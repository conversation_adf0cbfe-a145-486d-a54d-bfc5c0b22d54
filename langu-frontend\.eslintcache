[{"D:\\languworks\\langu-frontend\\components\\teacher-listing\\TeacherListingBanner.vue": "1", "D:\\languworks\\langu-frontend\\components\\teacher-listing\\TeacherListing.vue": "2", "D:\\languworks\\langu-frontend\\components\\TeacherFilterNew.vue": "3", "D:\\languworks\\langu-frontend\\lang\\en\\faq-page\\index.js": "4", "D:\\languworks\\langu-frontend\\pages\\faq\\index.vue": "5", "D:\\languworks\\langu-frontend\\lang\\es\\faq-page\\index.js": "6", "D:\\languworks\\langu-frontend\\lang\\pl\\faq-page\\index.js": "7", "D:\\languworks\\langu-frontend\\layouts\\error.vue": "8", "D:\\languworks\\langu-frontend\\lang\\en.js": "9", "D:\\languworks\\langu-frontend\\lang\\es.js": "10", "D:\\languworks\\langu-frontend\\lang\\pl.js": "11", "D:\\languworks\\langu-frontend\\store\\payments.js": "12", "D:\\languworks\\langu-frontend\\components\\payments\\PaymentItem.vue": "13", "D:\\languworks\\langu-frontend\\components\\payments\\PaymentLesson.vue": "14", "D:\\languworks\\langu-frontend\\components\\payments\\PaymentsPage.vue": "15", "D:\\languworks\\langu-frontend\\middleware\\confirmationPageAllowed.js": "16", "D:\\languworks\\langu-frontend\\middleware\\authenticated.js": "17", "D:\\languworks\\langu-frontend\\middleware\\paymentsPageClass.js": "18", "D:\\languworks\\langu-frontend\\middleware\\redirect.js": "19", "D:\\languworks\\langu-frontend\\middleware\\businessSuccessPageAllowed.js": "20", "D:\\languworks\\langu-frontend\\middleware\\thankYouPageAllowed.js": "21", "D:\\languworks\\langu-frontend\\middleware\\teacherListingRedirect.js": "22", "D:\\languworks\\langu-frontend\\middleware\\threadExisted.js": "23", "D:\\languworks\\langu-frontend\\middleware\\utm.js": "24", "D:\\languworks\\langu-frontend\\plugins\\axios.js": "25", "D:\\languworks\\langu-frontend\\plugins\\konva.js": "26", "D:\\languworks\\langu-frontend\\plugins\\vue-rating.js": "27", "D:\\languworks\\langu-frontend\\plugins\\stripe.js": "28", "D:\\languworks\\langu-frontend\\plugins\\socket.js": "29", "D:\\languworks\\langu-frontend\\plugins\\pluralization.js": "30", "D:\\languworks\\langu-frontend\\plugins\\tidio-chat.js": "31", "D:\\languworks\\langu-frontend\\plugins\\router.js": "32", "D:\\languworks\\langu-frontend\\plugins\\gtm.js": "33", "D:\\languworks\\langu-frontend\\layouts\\classroomLayout.vue": "34", "D:\\languworks\\langu-frontend\\layouts\\default.vue": "35", "D:\\languworks\\langu-frontend\\components\\BuzzDialog.vue": "36", "D:\\languworks\\langu-frontend\\components\\Calendar.vue": "37", "D:\\languworks\\langu-frontend\\components\\CalendarDate.vue": "38", "D:\\languworks\\langu-frontend\\components\\ConfirmDialog.vue": "39", "D:\\languworks\\langu-frontend\\components\\CheckEmailDialog.vue": "40", "D:\\languworks\\langu-frontend\\components\\CookiePopup.vue": "41", "D:\\languworks\\langu-frontend\\components\\FreeSlots.vue": "42", "D:\\languworks\\langu-frontend\\components\\Footer.vue": "43", "D:\\languworks\\langu-frontend\\components\\GoogleSignInButton.vue": "44", "D:\\languworks\\langu-frontend\\components\\LAvatar.vue": "45", "D:\\languworks\\langu-frontend\\components\\Header.vue": "46", "D:\\languworks\\langu-frontend\\components\\LDialog.vue": "47", "D:\\languworks\\langu-frontend\\components\\LChip.vue": "48", "D:\\languworks\\langu-frontend\\components\\LessonTimeNotice.vue": "49", "D:\\languworks\\langu-frontend\\components\\LExpansionPanels.vue": "50", "D:\\languworks\\langu-frontend\\components\\LoadMoreBtn.vue": "51", "D:\\languworks\\langu-frontend\\components\\LoginSidebar.vue": "52", "D:\\languworks\\langu-frontend\\components\\MessageDialog.vue": "53", "D:\\languworks\\langu-frontend\\components\\Loader.vue": "54", "D:\\languworks\\langu-frontend\\components\\Pagination.vue": "55", "D:\\languworks\\langu-frontend\\components\\SetPasswordDialog.vue": "56", "D:\\languworks\\langu-frontend\\components\\Snackbar.vue": "57", "D:\\languworks\\langu-frontend\\components\\StarRating.vue": "58", "D:\\languworks\\langu-frontend\\components\\Steps.vue": "59", "D:\\languworks\\langu-frontend\\components\\TeacherCard.vue": "60", "D:\\languworks\\langu-frontend\\components\\SummaryLessonDialog.vue": "61", "D:\\languworks\\langu-frontend\\components\\TeacherFilter.vue": "62", "D:\\languworks\\langu-frontend\\components\\TimePicker.vue": "63", "D:\\languworks\\langu-frontend\\components\\UserStatus.vue": "64", "D:\\languworks\\langu-frontend\\components\\TimePickerItem.vue": "65", "D:\\languworks\\langu-frontend\\components\\classroom\\video\\VideoActions.vue": "66", "D:\\languworks\\langu-frontend\\components\\classroom\\video\\Tokbox.vue": "67", "D:\\languworks\\langu-frontend\\components\\classroom\\vue-draggable-resizable\\VueDraggableResizable.vue": "68", "D:\\languworks\\langu-frontend\\components\\classroom\\video\\Twilio.vue": "69", "D:\\languworks\\langu-frontend\\pages\\index.vue": "70", "D:\\languworks\\langu-frontend\\pages\\teacher-listing\\_page\\_params\\index.vue": "71", "D:\\languworks\\langu-frontend\\pages\\lesson\\_id\\classroom\\index.vue": "72", "D:\\languworks\\langu-frontend\\pages\\teacher-listing\\_page\\index.vue": "73", "D:\\languworks\\langu-frontend\\pages\\teacher\\_slug\\index.vue": "74", "D:\\languworks\\langu-frontend\\store\\currency.js": "75", "D:\\languworks\\langu-frontend\\store\\auth.js": "76", "D:\\languworks\\langu-frontend\\store\\faq.js": "77", "D:\\languworks\\langu-frontend\\store\\language.js": "78", "D:\\languworks\\langu-frontend\\store\\business_page.js": "79", "D:\\languworks\\langu-frontend\\store\\education_page.js": "80", "D:\\languworks\\langu-frontend\\store\\review.js": "81", "D:\\languworks\\langu-frontend\\store\\socket.js": "82", "D:\\languworks\\langu-frontend\\store\\snackbar.js": "83", "D:\\languworks\\langu-frontend\\store\\teacher.js": "84", "D:\\languworks\\langu-frontend\\store\\index.js": "85", "D:\\languworks\\langu-frontend\\store\\message.js": "86", "D:\\languworks\\langu-frontend\\store\\lesson.js": "87", "D:\\languworks\\langu-frontend\\store\\purchase.js": "88", "D:\\languworks\\langu-frontend\\store\\teacher_profile.js": "89", "D:\\languworks\\langu-frontend\\store\\classroom.js": "90", "D:\\languworks\\langu-frontend\\store\\user.js": "91", "D:\\languworks\\langu-frontend\\store\\teacher_filter.js": "92", "D:\\languworks\\langu-frontend\\store\\settings.js": "93", "D:\\languworks\\langu-frontend\\components\\form\\TextInput.vue": "94", "D:\\languworks\\langu-frontend\\components\\classroom\\ClassroomContainer.vue": "95", "D:\\languworks\\langu-frontend\\locale-domains.js": "96", "D:\\languworks\\langu-frontend\\helpers\\navigationState.js": "97", "D:\\languworks\\langu-frontend\\helpers\\check_device.js": "98", "D:\\languworks\\langu-frontend\\helpers\\constants.js": "99", "D:\\languworks\\langu-frontend\\helpers\\index.js": "100", "D:\\languworks\\langu-frontend\\mixins\\SetTool.vue": "101", "D:\\languworks\\langu-frontend\\utils\\hash.js": "102", "D:\\languworks\\langu-frontend\\components\\classroom\\Konva.vue": "103", "D:\\languworks\\langu-frontend\\components\\classroom\\Toolbar.vue": "104", "D:\\languworks\\langu-frontend\\components\\classroom\\Library.vue": "105", "D:\\languworks\\langu-frontend\\components\\classroom\\VideoInput.vue": "106", "D:\\languworks\\langu-frontend\\components\\classroom\\ImageItem.vue": "107", "D:\\languworks\\langu-frontend\\components\\classroom\\PdfItem.vue": "108", "D:\\languworks\\langu-frontend\\components\\classroom\\AudioItem.vue": "109", "D:\\languworks\\langu-frontend\\components\\classroom\\VideoItem.vue": "110", "D:\\languworks\\langu-frontend\\components\\classroom\\TinymceVue.vue": "111", "D:\\languworks\\langu-frontend\\components\\classroom\\OtherCursor.vue": "112", "D:\\languworks\\langu-frontend\\components\\classroom\\Viewport.vue": "113", "D:\\languworks\\langu-frontend\\lang\\en\\about-us-page\\index.js": "114", "D:\\languworks\\langu-frontend\\lang\\pl\\education-page\\index.js": "115", "D:\\languworks\\langu-frontend\\components\\teacher-listing\\TeacherListingHeader.vue": "116", "D:\\languworks\\langu-frontend\\lang\\pl\\business-page\\index.js": "117", "D:\\languworks\\langu-frontend\\components\\classroom\\DropFileArea.vue": "118", "D:\\languworks\\langu-frontend\\helpers\\dom.js": "119", "D:\\languworks\\langu-frontend\\lang\\pl\\about-us-page\\index.js": "120", "D:\\languworks\\langu-frontend\\lang\\en\\education-page\\index.js": "121", "D:\\languworks\\langu-frontend\\lang\\en\\business-page\\index.js": "122", "D:\\languworks\\langu-frontend\\lang\\es\\education-page\\index.js": "123", "D:\\languworks\\langu-frontend\\lang\\es\\about-us-page\\index.js": "124", "D:\\languworks\\langu-frontend\\lang\\es\\business-page\\index.js": "125", "D:\\languworks\\langu-frontend\\components\\classroom\\ClassroomContainerHeader.vue": "126", "D:\\languworks\\langu-frontend\\mixins\\UploadFiles.vue": "127", "D:\\languworks\\langu-frontend\\helpers\\fns.js": "128", "D:\\languworks\\langu-frontend\\components\\form\\SelectInput.vue": "129", "D:\\languworks\\langu-frontend\\components\\form\\SearchInput.vue": "130", "D:\\languworks\\langu-frontend\\components\\user-lessons\\LessonEvaluationDialog.vue": "131", "D:\\languworks\\langu-frontend\\pages\\user\\messages\\_recipientId\\new\\index.vue": "132", "D:\\languworks\\langu-frontend\\pages\\user\\profile\\_slug\\index.vue": "133", "D:\\languworks\\langu-frontend\\pages\\user\\messages\\_threadId\\view\\index.vue": "134", "D:\\languworks\\langu-frontend\\pages\\user\\unscheduled-lessons\\_page\\index.vue": "135", "D:\\languworks\\langu-frontend\\pages\\user\\past-lessons\\_page\\index.vue": "136", "D:\\languworks\\langu-frontend\\pages\\user\\password\\_token\\index.vue": "137", "D:\\languworks\\langu-frontend\\pages\\user\\lessons\\_page\\index.vue": "138", "D:\\languworks\\langu-frontend\\pages\\checkout\\stripe-form\\_id\\index.vue": "139", "D:\\languworks\\langu-frontend\\pages\\checkout\\p24-payin\\_id\\index.vue": "140", "D:\\languworks\\langu-frontend\\pages\\user\\payments\\payouts\\_page\\index.vue": "141", "D:\\languworks\\langu-frontend\\pages\\user\\payments\\lessons\\_page\\index.vue": "142", "D:\\languworks\\langu-frontend\\pages\\user\\payments\\payouts\\index.vue": "143", "D:\\languworks\\langu-frontend\\mixins\\StatusOnline.vue": "144", "D:\\languworks\\langu-frontend\\pages\\user\\payments\\lessons\\index.vue": "145", "D:\\languworks\\langu-frontend\\pages\\user\\lessons\\thank-you\\index.vue": "146", "D:\\languworks\\langu-frontend\\pages\\user\\lessons\\confirmation\\index.vue": "147", "D:\\languworks\\langu-frontend\\pages\\user\\unscheduled-lessons\\index.vue": "148", "D:\\languworks\\langu-frontend\\pages\\user\\settings\\index.vue": "149", "D:\\languworks\\langu-frontend\\pages\\user\\register\\index.vue": "150", "D:\\languworks\\langu-frontend\\pages\\user\\payments\\index.vue": "151", "D:\\languworks\\langu-frontend\\pages\\user\\past-lessons\\index.vue": "152", "D:\\languworks\\langu-frontend\\pages\\user\\messages\\index.vue": "153", "D:\\languworks\\langu-frontend\\pages\\user\\lessons\\index.vue": "154", "D:\\languworks\\langu-frontend\\pages\\teacher-listing\\welcome\\index.vue": "155", "D:\\languworks\\langu-frontend\\pages\\business\\success\\index.vue": "156", "D:\\languworks\\langu-frontend\\pages\\teacher-listing\\index.vue": "157", "D:\\languworks\\langu-frontend\\pages\\education\\index.vue": "158", "D:\\languworks\\langu-frontend\\pages\\business\\index.vue": "159", "D:\\languworks\\langu-frontend\\components\\homepage\\ThinkingSection.vue": "160", "D:\\languworks\\langu-frontend\\components\\form\\Editor.vue": "161", "D:\\languworks\\langu-frontend\\components\\homepage\\FaqSection.vue": "162", "D:\\languworks\\langu-frontend\\components\\homepage\\ReviewSection.vue": "163", "D:\\languworks\\langu-frontend\\components\\homepage\\TutorsSection.vue": "164", "D:\\languworks\\langu-frontend\\components\\homepage\\LanguagesSection.vue": "165", "D:\\languworks\\langu-frontend\\components\\homepage\\AboutSection.vue": "166", "D:\\languworks\\langu-frontend\\components\\images\\EmailIcon.vue": "167", "D:\\languworks\\langu-frontend\\components\\form\\FormRate.vue": "168", "D:\\languworks\\langu-frontend\\components\\homepage\\HowWorksSection.vue": "169", "D:\\languworks\\langu-frontend\\components\\images\\LifeGradientIcon.vue": "170", "D:\\languworks\\langu-frontend\\components\\images\\EducationGradientIcon.vue": "171", "D:\\languworks\\langu-frontend\\components\\images\\CareerGradientIcon.vue": "172", "D:\\languworks\\langu-frontend\\components\\images\\CheckedGradientIcon.vue": "173", "D:\\languworks\\langu-frontend\\components\\teacher-profile\\TimePickerDialog.vue": "174", "D:\\languworks\\langu-frontend\\components\\teacher-profile\\FeedbackTags.vue": "175", "D:\\languworks\\langu-frontend\\components\\teacher-profile\\CourseItem.vue": "176", "D:\\languworks\\langu-frontend\\components\\teacher-profile\\TeacherProfileSidebar.vue": "177", "D:\\languworks\\langu-frontend\\components\\Youtube.vue": "178", "D:\\languworks\\langu-frontend\\components\\homepage\\StatSection.vue": "179", "D:\\languworks\\langu-frontend\\components\\homepage\\IntroSection.vue": "180", "D:\\languworks\\langu-frontend\\components\\images\\MoonGradientIcon.vue": "181", "D:\\languworks\\langu-frontend\\components\\images\\SunsetGradientIcon.vue": "182", "D:\\languworks\\langu-frontend\\components\\images\\SearchIcon.vue": "183", "D:\\languworks\\langu-frontend\\components\\images\\PlFlagIcon.vue": "184", "D:\\languworks\\langu-frontend\\components\\images\\EsFlagIcon.vue": "185", "D:\\languworks\\langu-frontend\\components\\images\\EnFlagIcon.vue": "186", "D:\\languworks\\langu-frontend\\components\\user-lessons\\LessonsPage.vue": "187", "D:\\languworks\\langu-frontend\\components\\user-messages\\MessagesPage.vue": "188", "D:\\languworks\\langu-frontend\\components\\images\\SunGradientIcon.vue": "189", "D:\\languworks\\langu-frontend\\components\\images\\AlarmGradientIcon.vue": "190", "D:\\languworks\\langu-frontend\\components\\business-page\\BusinessPage.vue": "191", "D:\\languworks\\langu-frontend\\mixins\\Avatars.vue": "192", "D:\\languworks\\langu-frontend\\components\\user-settings\\ReceiptInfo.vue": "193", "D:\\languworks\\langu-frontend\\components\\user-settings\\CalendarNotificationInfo.vue": "194", "D:\\languworks\\langu-frontend\\components\\user-settings\\TeachingQualificationsInfo.vue": "195", "D:\\languworks\\langu-frontend\\components\\user-settings\\LearningPreferencesInfo.vue": "196", "D:\\languworks\\langu-frontend\\components\\user-settings\\CoursesInfo.vue": "197", "D:\\languworks\\langu-frontend\\components\\user-settings\\TeachingPreferencesInfo.vue": "198", "D:\\languworks\\langu-frontend\\components\\user-settings\\PricingTableInfo.vue": "199", "D:\\languworks\\langu-frontend\\components\\user-settings\\AboutMeInfo.vue": "200", "D:\\languworks\\langu-frontend\\components\\landing-page\\TeachersSlider.vue": "201", "D:\\languworks\\langu-frontend\\components\\landing-page\\TestimonialsSlider.vue": "202", "D:\\languworks\\langu-frontend\\components\\user-settings\\BackgroundInfo.vue": "203", "D:\\languworks\\langu-frontend\\components\\user-settings\\LanguagesInfo.vue": "204", "D:\\languworks\\langu-frontend\\components\\user-settings\\SummaryInfo.vue": "205", "D:\\languworks\\langu-frontend\\components\\teacher-profile\\FindMoreTeachersButton.vue": "206", "D:\\languworks\\langu-frontend\\components\\teacher-profile\\PricePerLesson.vue": "207", "D:\\languworks\\langu-frontend\\components\\images\\HomePageIntroImage.vue": "208", "D:\\languworks\\langu-frontend\\components\\homepage\\SelectLanguage.vue": "209", "D:\\languworks\\langu-frontend\\components\\user-settings\\BasicInfo.vue": "210", "D:\\languworks\\langu-frontend\\pages\\blog\\index.vue": "211", "D:\\languworks\\langu-frontend\\pages\\about-us\\index.vue": "212", "D:\\languworks\\langu-frontend\\components\\business-page\\icons\\DotsIcon.vue": "213", "D:\\languworks\\langu-frontend\\components\\user-settings\\UserSettingTemplate.vue": "214", "D:\\languworks\\langu-frontend\\components\\user-settings\\UserSettingSelect.vue": "215", "D:\\languworks\\langu-frontend\\components\\user-settings\\QualificationSuccessDialog.vue": "216", "D:\\languworks\\langu-frontend\\components\\user-settings\\UserSettingAutocomplete.vue": "217", "D:\\languworks\\langu-frontend\\components\\user-settings\\SpecialityDialog.vue": "218", "D:\\languworks\\langu-frontend\\components\\user-settings\\PerLessonPrice.vue": "219", "D:\\languworks\\langu-frontend\\components\\user-settings\\LessonPrice.vue": "220", "D:\\languworks\\langu-frontend\\components\\user-settings\\CourseItem.vue": "221", "D:\\languworks\\langu-frontend\\components\\user-settings\\IllustrationDialog.vue": "222", "D:\\languworks\\langu-frontend\\components\\user-lessons\\UpcomingLesson.vue": "223", "D:\\languworks\\langu-frontend\\components\\user-settings\\AddQualificationDialog.vue": "224", "D:\\languworks\\langu-frontend\\components\\user-lessons\\UnscheduledLesson.vue": "225", "D:\\languworks\\langu-frontend\\components\\user-lessons\\PastLesson.vue": "226", "D:\\languworks\\langu-frontend\\components\\user-messages\\EmptyContent.vue": "227", "D:\\languworks\\langu-frontend\\components\\user-messages\\Conversation.vue": "228", "D:\\languworks\\langu-frontend\\components\\user-lessons\\TimePickerDialog.vue": "229", "D:\\languworks\\langu-frontend\\components\\user-lessons\\LessonItem.vue": "230", "D:\\languworks\\langu-frontend\\components\\user-messages\\ConversationItem.vue": "231", "D:\\languworks\\langu-frontend\\components\\payments\\WiseTransferModal.vue": "232", "D:\\languworks\\langu-frontend\\components\\payments\\SavedAccountsModal.vue": "233", "D:\\languworks\\langu-frontend\\components\\payments\\PayoutItem.vue": "234", "D:\\languworks\\langu-frontend\\components\\payments\\PaymentPayout.vue": "235", "D:\\languworks\\langu-frontend\\components\\payments\\PayoutModal.vue": "236", "D:\\languworks\\langu-frontend\\components\\payments\\PaymentDetailsModal.vue": "237", "D:\\languworks\\langu-frontend\\components\\images\\GoogleIcon.vue": "238", "D:\\languworks\\langu-frontend\\components\\images\\BusinessPageIntroMobileImage.vue": "239", "D:\\languworks\\langu-frontend\\components\\form\\SelectInputNew.vue": "240", "D:\\languworks\\langu-frontend\\components\\images\\BusinessPageIntroImage.vue": "241", "D:\\languworks\\langu-frontend\\components\\payments\\countries.js": "242", "D:\\languworks\\langu-frontend\\components\\classroom\\video\\Whereby.vue": "243", "D:\\languworks\\langu-frontend\\pages\\meet\\index.vue": "244", "D:\\languworks\\langu-frontend\\helpers\\whereby-api.js": "245", "D:\\languworks\\langu-frontend\\plugins\\sentry.client.js": "246", "D:\\languworks\\langu-frontend\\pages\\sentry-test.vue": "247"}, {"size": 6404, "mtime": 1748600170824, "results": "248", "hashOfConfig": "249"}, {"size": 12550, "mtime": 1748600201934, "results": "250", "hashOfConfig": "249"}, {"size": 72568, "mtime": 1749246271395, "results": "251", "hashOfConfig": "249"}, {"size": 26234, "mtime": 1748602370853, "results": "252", "hashOfConfig": "249"}, {"size": 13286, "mtime": 1748880254587, "results": "253", "hashOfConfig": "249"}, {"size": 25169, "mtime": 1748603228623, "results": "254", "hashOfConfig": "249"}, {"size": 29070, "mtime": 1748603301326, "results": "255", "hashOfConfig": "249"}, {"size": 4801, "mtime": 1748607980561, "results": "256", "hashOfConfig": "249"}, {"size": 39157, "mtime": 1752060195831, "results": "257", "hashOfConfig": "249"}, {"size": 42655, "mtime": 1752060211046, "results": "258", "hashOfConfig": "249"}, {"size": 41825, "mtime": 1752060224304, "results": "259", "hashOfConfig": "249"}, {"size": 20316, "mtime": 1750424737586, "results": "260", "hashOfConfig": "249"}, {"size": 11380, "mtime": 1749668071886, "results": "261", "hashOfConfig": "249"}, {"size": 2075, "mtime": 1749667839955, "results": "262", "hashOfConfig": "249"}, {"size": 19448, "mtime": 1750702455960, "results": "263", "hashOfConfig": "249"}, {"size": 144, "mtime": 1740508052928, "results": "264", "hashOfConfig": "249"}, {"size": 124, "mtime": 1740508052928, "results": "265", "hashOfConfig": "249"}, {"size": 515, "mtime": 1747934917027, "results": "266", "hashOfConfig": "249"}, {"size": 5438, "mtime": 1740508052929, "results": "267", "hashOfConfig": "249"}, {"size": 144, "mtime": 1740508052928, "results": "268", "hashOfConfig": "249"}, {"size": 141, "mtime": 1740508052929, "results": "269", "hashOfConfig": "249"}, {"size": 1177, "mtime": 1740508052929, "results": "270", "hashOfConfig": "249"}, {"size": 301, "mtime": 1740508052929, "results": "271", "hashOfConfig": "249"}, {"size": 926, "mtime": 1740508052930, "results": "272", "hashOfConfig": "249"}, {"size": 820, "mtime": 1740508052945, "results": "273", "hashOfConfig": "249"}, {"size": 74, "mtime": 1740508052945, "results": "274", "hashOfConfig": "249"}, {"size": 107, "mtime": 1740508052946, "results": "275", "hashOfConfig": "249"}, {"size": 414, "mtime": 1740508052946, "results": "276", "hashOfConfig": "249"}, {"size": 350, "mtime": 1740508052945, "results": "277", "hashOfConfig": "249"}, {"size": 505, "mtime": 1740508052945, "results": "278", "hashOfConfig": "249"}, {"size": 2955, "mtime": 1740508052946, "results": "279", "hashOfConfig": "249"}, {"size": 1383, "mtime": 1751393791949, "results": "280", "hashOfConfig": "249"}, {"size": 362, "mtime": 1740508052945, "results": "281", "hashOfConfig": "249"}, {"size": 1841, "mtime": 1741625770423, "results": "282", "hashOfConfig": "249"}, {"size": 7896, "mtime": 1752216867509, "results": "283", "hashOfConfig": "249"}, {"size": 2896, "mtime": 1740508052881, "results": "284", "hashOfConfig": "249"}, {"size": 3290, "mtime": 1740508052881, "results": "285", "hashOfConfig": "249"}, {"size": 2096, "mtime": 1740508052881, "results": "286", "hashOfConfig": "249"}, {"size": 1392, "mtime": 1740508052882, "results": "287", "hashOfConfig": "249"}, {"size": 1568, "mtime": 1745755044109, "results": "288", "hashOfConfig": "249"}, {"size": 2169, "mtime": 1740508052882, "results": "289", "hashOfConfig": "249"}, {"size": 10799, "mtime": 1740508052883, "results": "290", "hashOfConfig": "249"}, {"size": 25308, "mtime": 1740508052882, "results": "291", "hashOfConfig": "249"}, {"size": 4969, "mtime": 1742317819574, "results": "292", "hashOfConfig": "249"}, {"size": 5773, "mtime": 1740591162175, "results": "293", "hashOfConfig": "249"}, {"size": 56887, "mtime": 1752056921744, "results": "294", "hashOfConfig": "249"}, {"size": 4156, "mtime": 1740508052884, "results": "295", "hashOfConfig": "249"}, {"size": 4746, "mtime": 1753032583696, "results": "296", "hashOfConfig": "249"}, {"size": 2091, "mtime": 1740508052885, "results": "297", "hashOfConfig": "249"}, {"size": 2990, "mtime": 1740508052884, "results": "298", "hashOfConfig": "249"}, {"size": 1314, "mtime": 1740508052885, "results": "299", "hashOfConfig": "249"}, {"size": 14432, "mtime": 1747934917013, "results": "300", "hashOfConfig": "249"}, {"size": 5009, "mtime": 1740508052886, "results": "301", "hashOfConfig": "249"}, {"size": 826, "mtime": 1740591162176, "results": "302", "hashOfConfig": "249"}, {"size": 6602, "mtime": 1747340348976, "results": "303", "hashOfConfig": "249"}, {"size": 6784, "mtime": 1747934917015, "results": "304", "hashOfConfig": "249"}, {"size": 1310, "mtime": 1740508052886, "results": "305", "hashOfConfig": "249"}, {"size": 2820, "mtime": 1740508052886, "results": "306", "hashOfConfig": "249"}, {"size": 5623, "mtime": 1740591162180, "results": "307", "hashOfConfig": "249"}, {"size": 10471, "mtime": 1753976918383, "results": "308", "hashOfConfig": "249"}, {"size": 22558, "mtime": 1747934906618, "results": "309", "hashOfConfig": "249"}, {"size": 35850, "mtime": 1749246723492, "results": "310", "hashOfConfig": "249"}, {"size": 14061, "mtime": 1740508052888, "results": "311", "hashOfConfig": "249"}, {"size": 1538, "mtime": 1740508052888, "results": "312", "hashOfConfig": "249"}, {"size": 2593, "mtime": 1740508052888, "results": "313", "hashOfConfig": "249"}, {"size": 12836, "mtime": 1752607050056, "results": "314", "hashOfConfig": "249"}, {"size": 11955, "mtime": 1752606997994, "results": "315", "hashOfConfig": "249"}, {"size": 28377, "mtime": 1740508052894, "results": "316", "hashOfConfig": "249"}, {"size": 13869, "mtime": 1752606983793, "results": "317", "hashOfConfig": "249"}, {"size": 17947, "mtime": 1752684360330, "results": "318", "hashOfConfig": "249"}, {"size": 7337, "mtime": 1749244434396, "results": "319", "hashOfConfig": "249"}, {"size": 37367, "mtime": 1753973946424, "results": "320", "hashOfConfig": "249"}, {"size": 2864, "mtime": 1740591162226, "results": "321", "hashOfConfig": "249"}, {"size": 49377, "mtime": 1754419026267, "results": "322", "hashOfConfig": "249"}, {"size": 1896, "mtime": 1740591162232, "results": "323", "hashOfConfig": "249"}, {"size": 2098, "mtime": 1740591162232, "results": "324", "hashOfConfig": "249"}, {"size": 733, "mtime": 1740508052953, "results": "325", "hashOfConfig": "249"}, {"size": 651, "mtime": 1740508052953, "results": "326", "hashOfConfig": "249"}, {"size": 506, "mtime": 1740508052952, "results": "327", "hashOfConfig": "249"}, {"size": 907, "mtime": 1740508052952, "results": "328", "hashOfConfig": "249"}, {"size": 373, "mtime": 1740508052954, "results": "329", "hashOfConfig": "249"}, {"size": 289, "mtime": 1740508052954, "results": "330", "hashOfConfig": "249"}, {"size": 872, "mtime": 1740508052954, "results": "331", "hashOfConfig": "249"}, {"size": 3117, "mtime": 1749584748551, "results": "332", "hashOfConfig": "249"}, {"size": 4175, "mtime": 1747340900794, "results": "333", "hashOfConfig": "249"}, {"size": 7184, "mtime": 1740508052953, "results": "334", "hashOfConfig": "249"}, {"size": 7086, "mtime": 1753032582688, "results": "335", "hashOfConfig": "249"}, {"size": 7465, "mtime": 1747934906640, "results": "336", "hashOfConfig": "249"}, {"size": 5551, "mtime": 1740508052955, "results": "337", "hashOfConfig": "249"}, {"size": 10260, "mtime": 1753477427536, "results": "338", "hashOfConfig": "249"}, {"size": 8930, "mtime": 1751393795360, "results": "339", "hashOfConfig": "249"}, {"size": 12956, "mtime": 1748070349780, "results": "340", "hashOfConfig": "249"}, {"size": 24925, "mtime": 1740508052954, "results": "341", "hashOfConfig": "249"}, {"size": 3330, "mtime": 1740508052896, "results": "342", "hashOfConfig": "249"}, {"size": 12482, "mtime": 1753475997241, "results": "343", "hashOfConfig": "249"}, {"size": 285, "mtime": 1740508052928, "results": "344", "hashOfConfig": "249"}, {"size": 2828, "mtime": 1747934917023, "results": "345", "hashOfConfig": "249"}, {"size": 800, "mtime": 1740508052920, "results": "346", "hashOfConfig": "249"}, {"size": 1034, "mtime": 1740508052920, "results": "347", "hashOfConfig": "249"}, {"size": 5602, "mtime": 1748580007484, "results": "348", "hashOfConfig": "249"}, {"size": 993, "mtime": 1753476948627, "results": "349", "hashOfConfig": "249"}, {"size": 3959, "mtime": 1747934917034, "results": "350", "hashOfConfig": "249"}, {"size": 13505, "mtime": 1740508052891, "results": "351", "hashOfConfig": "249"}, {"size": 31915, "mtime": 1753413451910, "results": "352", "hashOfConfig": "249"}, {"size": 20526, "mtime": 1752686643831, "results": "353", "hashOfConfig": "249"}, {"size": 2873, "mtime": 1740508052893, "results": "354", "hashOfConfig": "249"}, {"size": 2314, "mtime": 1740508052891, "results": "355", "hashOfConfig": "249"}, {"size": 7407, "mtime": 1740508052892, "results": "356", "hashOfConfig": "249"}, {"size": 5617, "mtime": 1741725581044, "results": "357", "hashOfConfig": "249"}, {"size": 7718, "mtime": 1741725581044, "results": "358", "hashOfConfig": "249"}, {"size": 16532, "mtime": 1753477132375, "results": "359", "hashOfConfig": "249"}, {"size": 2321, "mtime": 1740508052892, "results": "360", "hashOfConfig": "249"}, {"size": 3819, "mtime": 1753727284278, "results": "361", "hashOfConfig": "249"}, {"size": 4706, "mtime": 1740508052922, "results": "362", "hashOfConfig": "249"}, {"size": 4610, "mtime": 1740508052926, "results": "363", "hashOfConfig": "249"}, {"size": 19387, "mtime": 1749246716957, "results": "364", "hashOfConfig": "249"}, {"size": 4657, "mtime": 1740508052926, "results": "365", "hashOfConfig": "249"}, {"size": 1618, "mtime": 1740508052890, "results": "366", "hashOfConfig": "249"}, {"size": 1308, "mtime": 1740508052921, "results": "367", "hashOfConfig": "249"}, {"size": 5348, "mtime": 1740508052926, "results": "368", "hashOfConfig": "249"}, {"size": 4112, "mtime": 1740508052923, "results": "369", "hashOfConfig": "249"}, {"size": 4499, "mtime": 1740508052923, "results": "370", "hashOfConfig": "249"}, {"size": 4504, "mtime": 1740508052925, "results": "371", "hashOfConfig": "249"}, {"size": 5242, "mtime": 1740508052924, "results": "372", "hashOfConfig": "249"}, {"size": 4923, "mtime": 1740508052924, "results": "373", "hashOfConfig": "249"}, {"size": 12482, "mtime": 1753475697156, "results": "374", "hashOfConfig": "249"}, {"size": 3053, "mtime": 1740508052931, "results": "375", "hashOfConfig": "249"}, {"size": 948, "mtime": 1740508052921, "results": "376", "hashOfConfig": "249"}, {"size": 3048, "mtime": 1740508052896, "results": "377", "hashOfConfig": "249"}, {"size": 4927, "mtime": 1740591162190, "results": "378", "hashOfConfig": "249"}, {"size": 11477, "mtime": 1740508052910, "results": "379", "hashOfConfig": "249"}, {"size": 88, "mtime": 1740508052940, "results": "380", "hashOfConfig": "249"}, {"size": 505, "mtime": 1740508052942, "results": "381", "hashOfConfig": "249"}, {"size": 1630, "mtime": 1740508052940, "results": "382", "hashOfConfig": "249"}, {"size": 1504, "mtime": 1740508052944, "results": "383", "hashOfConfig": "249"}, {"size": 1460, "mtime": 1740508052941, "results": "384", "hashOfConfig": "249"}, {"size": 564, "mtime": 1747934917029, "results": "385", "hashOfConfig": "249"}, {"size": 1472, "mtime": 1740508052938, "results": "386", "hashOfConfig": "249"}, {"size": 798, "mtime": 1745865597906, "results": "387", "hashOfConfig": "249"}, {"size": 4175, "mtime": 1740508052933, "results": "388", "hashOfConfig": "249"}, {"size": 1485, "mtime": 1748375485203, "results": "389", "hashOfConfig": "249"}, {"size": 1457, "mtime": 1748375471778, "results": "390", "hashOfConfig": "249"}, {"size": 1559, "mtime": 1748375475008, "results": "391", "hashOfConfig": "249"}, {"size": 843, "mtime": 1740508052930, "results": "392", "hashOfConfig": "249"}, {"size": 1549, "mtime": 1748375459509, "results": "393", "hashOfConfig": "249"}, {"size": 3715, "mtime": 1747934906629, "results": "394", "hashOfConfig": "249"}, {"size": 3681, "mtime": 1747934906628, "results": "395", "hashOfConfig": "249"}, {"size": 1431, "mtime": 1740508052944, "results": "396", "hashOfConfig": "249"}, {"size": 9981, "mtime": 1740508052944, "results": "397", "hashOfConfig": "249"}, {"size": 27323, "mtime": 1751657789931, "results": "398", "hashOfConfig": "249"}, {"size": 1316, "mtime": 1748375454705, "results": "399", "hashOfConfig": "249"}, {"size": 1387, "mtime": 1740508052941, "results": "400", "hashOfConfig": "249"}, {"size": 1491, "mtime": 1740508052940, "results": "401", "hashOfConfig": "249"}, {"size": 1414, "mtime": 1745755044133, "results": "402", "hashOfConfig": "249"}, {"size": 2482, "mtime": 1740508052936, "results": "403", "hashOfConfig": "249"}, {"size": 1718, "mtime": 1740508052932, "results": "404", "hashOfConfig": "249"}, {"size": 4851, "mtime": 1743794320612, "results": "405", "hashOfConfig": "249"}, {"size": 15165, "mtime": 1740508052933, "results": "406", "hashOfConfig": "249"}, {"size": 1815, "mtime": 1740508052932, "results": "407", "hashOfConfig": "249"}, {"size": 11736, "mtime": 1741434449092, "results": "408", "hashOfConfig": "249"}, {"size": 5867, "mtime": 1742058733796, "results": "409", "hashOfConfig": "249"}, {"size": 2965, "mtime": 1741434420294, "results": "410", "hashOfConfig": "249"}, {"size": 11688, "mtime": 1741434298604, "results": "411", "hashOfConfig": "249"}, {"size": 18387, "mtime": 1741434169130, "results": "412", "hashOfConfig": "249"}, {"size": 26017, "mtime": 1741434106917, "results": "413", "hashOfConfig": "249"}, {"size": 11118, "mtime": 1741434038035, "results": "414", "hashOfConfig": "249"}, {"size": 958, "mtime": 1740508052902, "results": "415", "hashOfConfig": "249"}, {"size": 1035, "mtime": 1740508052896, "results": "416", "hashOfConfig": "249"}, {"size": 9325, "mtime": 1741433895953, "results": "417", "hashOfConfig": "249"}, {"size": 8570, "mtime": 1740508052905, "results": "418", "hashOfConfig": "249"}, {"size": 2005, "mtime": 1740508052902, "results": "419", "hashOfConfig": "249"}, {"size": 1645, "mtime": 1740508052901, "results": "420", "hashOfConfig": "249"}, {"size": 1768, "mtime": 1740508052902, "results": "421", "hashOfConfig": "249"}, {"size": 10688, "mtime": 1745755044113, "results": "422", "hashOfConfig": "249"}, {"size": 1038, "mtime": 1740508052909, "results": "423", "hashOfConfig": "249"}, {"size": 13375, "mtime": 1740508052908, "results": "424", "hashOfConfig": "249"}, {"size": 4839, "mtime": 1745755044113, "results": "425", "hashOfConfig": "249"}, {"size": 1680, "mtime": 1740508052888, "results": "426", "hashOfConfig": "249"}, {"size": 3713, "mtime": 1751482705440, "results": "427", "hashOfConfig": "249"}, {"size": 8482, "mtime": 1740591162190, "results": "428", "hashOfConfig": "249"}, {"size": 7103, "mtime": 1740508052905, "results": "429", "hashOfConfig": "249"}, {"size": 6353, "mtime": 1740508052906, "results": "430", "hashOfConfig": "249"}, {"size": 646, "mtime": 1740591162193, "results": "431", "hashOfConfig": "249"}, {"size": 297, "mtime": 1740508052906, "results": "432", "hashOfConfig": "249"}, {"size": 160103, "mtime": 1740508052904, "results": "433", "hashOfConfig": "249"}, {"size": 1139, "mtime": 1740508052904, "results": "434", "hashOfConfig": "249"}, {"size": 10768, "mtime": 1753032585693, "results": "435", "hashOfConfig": "249"}, {"size": 14436, "mtime": 1740508052913, "results": "436", "hashOfConfig": "249"}, {"size": 9471, "mtime": 1740508052906, "results": "437", "hashOfConfig": "249"}, {"size": 4163, "mtime": 1740508052900, "results": "438", "hashOfConfig": "249"}, {"size": 42508, "mtime": 1740591162184, "results": "439", "hashOfConfig": "249"}, {"size": 476, "mtime": 1740508052930, "results": "440", "hashOfConfig": "249"}, {"size": 1595, "mtime": 1740508052918, "results": "441", "hashOfConfig": "249"}, {"size": 5610, "mtime": 1750785350695, "results": "442", "hashOfConfig": "249"}, {"size": 5508, "mtime": 1740508052918, "results": "443", "hashOfConfig": "249"}, {"size": 3128, "mtime": 1740508052916, "results": "444", "hashOfConfig": "249"}, {"size": 2780, "mtime": 1740508052915, "results": "445", "hashOfConfig": "249"}, {"size": 3155, "mtime": 1740508052918, "results": "446", "hashOfConfig": "249"}, {"size": 19201, "mtime": 1740591162200, "results": "447", "hashOfConfig": "249"}, {"size": 7447, "mtime": 1740508052914, "results": "448", "hashOfConfig": "249"}, {"size": 9473, "mtime": 1740508052907, "results": "449", "hashOfConfig": "249"}, {"size": 5807, "mtime": 1740508052907, "results": "450", "hashOfConfig": "249"}, {"size": 2657, "mtime": 1740508052914, "results": "451", "hashOfConfig": "249"}, {"size": 8582, "mtime": 1750184373199, "results": "452", "hashOfConfig": "249"}, {"size": 4623, "mtime": 1740508052918, "results": "453", "hashOfConfig": "249"}, {"size": 1741, "mtime": 1740508052909, "results": "454", "hashOfConfig": "249"}, {"size": 8700, "mtime": 1740591162196, "results": "455", "hashOfConfig": "249"}, {"size": 38916, "mtime": 1740508052905, "results": "456", "hashOfConfig": "249"}, {"size": 5164, "mtime": 1740591162192, "results": "457", "hashOfConfig": "249"}, {"size": 10346, "mtime": 1740508052914, "results": "458", "hashOfConfig": "249"}, {"size": 1565, "mtime": 1740508052932, "results": "459", "hashOfConfig": "249"}, {"size": 8845, "mtime": 1748456706570, "results": "460", "hashOfConfig": "249"}, {"size": 535, "mtime": 1740508052890, "results": "461", "hashOfConfig": "249"}, {"size": 4575, "mtime": 1740508052919, "results": "462", "hashOfConfig": "249"}, {"size": 2462, "mtime": 1750364306228, "results": "463", "hashOfConfig": "249"}, {"size": 727, "mtime": 1740508052917, "results": "464", "hashOfConfig": "249"}, {"size": 2769, "mtime": 1750363649188, "results": "465", "hashOfConfig": "249"}, {"size": 6793, "mtime": 1740508052918, "results": "466", "hashOfConfig": "249"}, {"size": 1024, "mtime": 1740591162197, "results": "467", "hashOfConfig": "249"}, {"size": 3312, "mtime": 1740591162197, "results": "468", "hashOfConfig": "249"}, {"size": 17822, "mtime": 1740591162197, "results": "469", "hashOfConfig": "249"}, {"size": 5277, "mtime": 1740508052916, "results": "470", "hashOfConfig": "249"}, {"size": 1411, "mtime": 1740508052912, "results": "471", "hashOfConfig": "249"}, {"size": 5610, "mtime": 1740508052914, "results": "472", "hashOfConfig": "249"}, {"size": 3161, "mtime": 1740508052912, "results": "473", "hashOfConfig": "249"}, {"size": 5320, "mtime": 1740508052911, "results": "474", "hashOfConfig": "249"}, {"size": 8196, "mtime": 1740508052913, "results": "475", "hashOfConfig": "249"}, {"size": 13197, "mtime": 1741725581044, "results": "476", "hashOfConfig": "249"}, {"size": 4488, "mtime": 1740508052911, "results": "477", "hashOfConfig": "249"}, {"size": 20299, "mtime": 1752775169620, "results": "478", "hashOfConfig": "249"}, {"size": 7773, "mtime": 1740508052913, "results": "479", "hashOfConfig": "249"}, {"size": 5748, "mtime": 1747934917022, "results": "480", "hashOfConfig": "249"}, {"size": 4242, "mtime": 1747934917021, "results": "481", "hashOfConfig": "249"}, {"size": 5105, "mtime": 1748369916195, "results": "482", "hashOfConfig": "249"}, {"size": 2684, "mtime": 1748579921843, "results": "483", "hashOfConfig": "249"}, {"size": 5669, "mtime": 1748457048301, "results": "484", "hashOfConfig": "249"}, {"size": 16691, "mtime": 1750703434233, "results": "485", "hashOfConfig": "249"}, {"size": 1409, "mtime": 1742026269842, "results": "486", "hashOfConfig": "249"}, {"size": 165929, "mtime": 1740508052901, "results": "487", "hashOfConfig": "249"}, {"size": 3051, "mtime": 1740591162190, "results": "488", "hashOfConfig": "249"}, {"size": 387011, "mtime": 1740508052900, "results": "489", "hashOfConfig": "249"}, {"size": 8991, "mtime": 1747934917023, "results": "490", "hashOfConfig": "249"}, {"size": 18250, "mtime": 1752838923741, "results": "491", "hashOfConfig": "249"}, {"size": 25857, "mtime": 1753412903710, "results": "492", "hashOfConfig": "249"}, {"size": 5526, "mtime": 1752666723597, "results": "493", "hashOfConfig": "249"}, {"size": 5461, "mtime": 1754123727593, "results": "494", "hashOfConfig": "249"}, {"size": 8413, "mtime": 1754121169522, "results": "495", "hashOfConfig": "249"}, {"filePath": "496", "messages": "497", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1fy5w0s", {"filePath": "498", "messages": "499", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "500"}, {"filePath": "501", "messages": "502", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "513"}, {"filePath": "514", "messages": "515", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "errorCount": 6, "warningCount": 0, "fixableErrorCount": 6, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "602", "messages": "603", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "errorCount": 12, "warningCount": 0, "fixableErrorCount": 12, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "620", "messages": "621", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "errorCount": 3, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "errorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "636", "messages": "637", "errorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "638", "messages": "639", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "errorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "652", "messages": "653", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "654", "messages": "655", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "656", "messages": "657", "errorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "658", "messages": "659", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "660", "messages": "661", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "errorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "672", "messages": "673", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "674", "messages": "675", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "680"}, {"filePath": "681", "messages": "682", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "errorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "685", "messages": "686", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "errorCount": 2, "warningCount": 0, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "693", "messages": "694", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "errorCount": 28, "warningCount": 3, "fixableErrorCount": 28, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "errorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "705", "messages": "706", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "707", "messages": "708", "errorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "errorCount": 7, "warningCount": 0, "fixableErrorCount": 7, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "errorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "777", "messages": "778", "errorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "779", "messages": "780", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "errorCount": 1, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "817", "messages": "818", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "825", "messages": "826", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "errorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "835", "messages": "836", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "errorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "851", "messages": "852", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "857", "messages": "858", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "873"}, {"filePath": "874", "messages": "875", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "errorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "882", "messages": "883", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "888", "messages": "889", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "900", "messages": "901", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "920", "messages": "921", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "errorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "924", "messages": "925", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "938", "messages": "939", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "942", "messages": "943", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "950", "messages": "951", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "952", "messages": "953", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "954", "messages": "955", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "956", "messages": "957", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "958", "messages": "959", "errorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "960", "messages": "961", "errorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "962", "messages": "963", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "988", "messages": "989", "errorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "990", "messages": "991", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "errorCount": 28, "warningCount": 0, "fixableErrorCount": 28, "fixableWarningCount": 0, "source": null}, "D:\\languworks\\langu-frontend\\components\\teacher-listing\\TeacherListingBanner.vue", [], "D:\\languworks\\langu-frontend\\components\\teacher-listing\\TeacherListing.vue", ["994"], "<template>\n  <v-col class=\"col-12 px-0\">\n    <div class=\"teacher-listing\">\n      <v-container fluid class=\"py-0\">\n        <v-row>\n          <v-col class=\"col-12\">\n            <div class=\"teacher-listing-wrap\">\n              <div class=\"teacher-listing-content\">\n                <v-navigation-drawer\n                  v-if=\"$vuetify.breakpoint.smAndDown\"\n                  id=\"teacher-filters\"\n                  v-model=\"drawer\"\n                  hide-overlay\n                  fixed\n                  color=\"darkLight\"\n                  width=\"375\"\n                >\n                  <teacher-filter\n                    @filters-loaded=\"filtersLoaded\"\n                  ></teacher-filter>\n                </v-navigation-drawer>\n\n                <!-- <div\n                  class=\"filter-button d-md-none\"\n                  @click=\"openFilterClickHandler\"\n                >\n                  {{ $t('filters') }}\n                  <span v-if=\"quantityActiveFilters\"\n                    >({{ quantityActiveFilters }})</span\n                  >\n                  <div class=\"filter-button-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n                      <use\n                        :xlink:href=\"`${require('~/assets/images/icon-sprite.svg')}#funnel`\"\n                      ></use>\n                    </svg>\n                  </div>\n                </div> -->\n\n                <div class=\"teacher-listing-header-title\">\n                  <div class=\"teacher-listing-header-title-text\">\n                    {{ $t('teacher_listing_header_title') }}\n                  </div>\n                  <span v-if=\"getBannerVisibility\"\n                    ><teacher-listing-banner></teacher-listing-banner\n                  ></span>\n                </div>\n\n                <teacher-filter-new></teacher-filter-new>\n\n                <teacher-listing-header></teacher-listing-header>\n\n                <section v-if=\"teachers.length\" class=\"teacher-listing-result\">\n                  <div class=\"teacher-listing-result-list\">\n                    <div\n                      v-for=\"teacher in teachers\"\n                      :key=\"teacher.id\"\n                      class=\"teacher-listing-result-item\"\n                    >\n                      <teacher-card :teacher=\"teacher\"></teacher-card>\n                    </div>\n                  </div>\n\n                  <div v-if=\"totalPages > 1\" class=\"mt-3 mt-md-5 text-center\">\n                    <pagination\n                      :current-page=\"page\"\n                      :total-pages=\"totalPages\"\n                      route=\"/teacher-listing\"\n                      :params=\"params\"\n                    ></pagination>\n                  </div>\n                </section>\n\n                <div\n                  v-if=\"!teachers.length && quantityActiveFilters\"\n                  class=\"teacher-listing-result--empty text-center\"\n                  v-html=\"\n                    $t('empty_teacher_list', {\n                      email: '<EMAIL>',\n                    })\n                  \"\n                ></div>\n                <section\n                  v-if=\"faqItems.length\"\n                  id=\"teacher-listing-page-faq-section\"\n                  ref=\"questions\"\n                  class=\"questions teacher-listing-page-faq-section\"\n                >\n                  <div\n                    v-if=\"backgroundImage\"\n                    ref=\"questionsSectionBg\"\n                    class=\"section-bg\"\n                  >\n                    <v-img\n                      :src=\"\n                        require('~/assets/images/homepage/questions-bg.png')\n                      \"\n                      position=\"center top\"\n                      :options=\"{ rootMargin: '50%' }\"\n                    ></v-img>\n                  </div>\n                  <v-container class=\"py-0 faq-custom-wrapper\">\n                    <v-row>\n                      <v-col class=\"col-12 py-0\">\n                        <div class=\"section-head section-head--decorated\">\n                          <h3 :class=\"['section-head-title', titleClass]\">\n                            {{ $t('home_page.questions_section_title') }}\n                          </h3>\n                        </div>\n                      </v-col>\n                    </v-row>\n                    <v-row>\n                      <v-col class=\"col-12 py-0\">\n                        <div class=\"questions-content\">\n                          <l-expansion-panels\n                            :items=\"faqItems\"\n                            flat\n                          ></l-expansion-panels>\n\n                          <div class=\"questions-button\">\n                            <v-btn to=\"/faq\" large color=\"primary\">\n                              {{ $t('see_our_full_faqs') }}\n                            </v-btn>\n                          </div>\n                        </div>\n                      </v-col>\n                    </v-row>\n                  </v-container>\n                </section>\n              </div>\n            </div>\n          </v-col>\n        </v-row>\n      </v-container>\n    </div>\n  </v-col>\n</template>\n\n<script>\nimport TeacherFilterNew from '~/components/TeacherFilterNew'\nimport TeacherListingHeader from '~/components/teacher-listing/TeacherListingHeader'\nimport TeacherListingBanner from '~/components/teacher-listing/TeacherListingBanner'\nimport TeacherCard from '~/components/TeacherCard'\nimport Pagination from '~/components/Pagination'\nimport LExpansionPanels from '~/components/LExpansionPanels'\n\nexport default {\n  name: 'TeacherListingPage',\n  components: {\n    TeacherFilterNew,\n    TeacherListingHeader,\n    TeacherListingBanner,\n    TeacherCard,\n    Pagination,\n    LExpansionPanels,\n  },\n  props: {\n    teachers: {\n      type: Array,\n      required: true,\n    },\n    faqItems: {\n      type: Array,\n      required: true,\n    },\n    page: {\n      type: Number,\n      default: 1,\n    },\n    params: {\n      type: String,\n      default: '',\n    },\n    items: {\n      type: Array,\n      required: true,\n    },\n    backgroundImage: {\n      type: Boolean,\n      default: false,\n    },\n    titleClass: {\n      type: String,\n      default: null,\n    },\n  },\n  data() {\n    return {\n      scrollContainer: null,\n      isCustomBreakpoint: false,\n    }\n  },\n  computed: {\n    quantityActiveFilters() {\n      return this.$store.getters['teacher_filter/quantityActiveFilters']\n    },\n    totalPages() {\n      return this.$store.getters['teacher/totalPages']\n    },\n    drawer: {\n      get() {\n        return this.$store.state.isShownTeacherFilter\n      },\n      set(value) {\n        this.$store.commit('SET_IS_TEACHER_FILTER', value)\n      },\n    },\n    breakpoint() {\n      return this.isCustomBreakpoint\n        ? this.$vuetify.breakpoint\n        : { mdAndUp: true }\n    },\n    filterScroll: {\n      get() {\n        return window.sessionStorage.getItem('filter-scroll')\n      },\n      set(value) {\n        window.sessionStorage.setItem('filter-scroll', value)\n      },\n    },\n    getBannerVisibility() {\n      return this.$vuetify.breakpoint.mdAndUp\n    },\n  },\n  mounted() {\n    this.scrollContainer = document\n      ?.getElementById('teacher-filters')\n      ?.getElementsByClassName('v-navigation-drawer__content')[0]\n    this.isCustomBreakpoint = true\n\n    if (this.scrollContainer) {\n      this.$nextTick(() => {\n        this.scrollContainer.addEventListener('scroll', this.onScroll)\n      })\n    }\n  },\n  beforeDestroy() {\n    if (this.scrollContainer) {\n      this.scrollContainer.removeEventListener('scroll', this.onScroll)\n    }\n  },\n  methods: {\n    openFilterClickHandler() {\n      this.$store.commit('SET_IS_TEACHER_FILTER', true)\n    },\n    filtersLoaded() {\n      if (\n        this.$vuetify.breakpoint.smAndDown &&\n        this.filterScroll &&\n        this.scrollContainer\n      ) {\n        this.scrollContainer.scroll({\n          top: this.filterScroll,\n          behavior: 'instant',\n        })\n      }\n    },\n    onScroll() {\n      this.filterScroll = this.scrollContainer.scrollTop\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import '~vuetify/src/styles/settings/_variables';\n@import './assets/styles/vars';\n\n.banner {\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  min-height: 125px;\n  padding: 5px 8px 0 32px;\n  line-height: 1.333;\n  margin-top: 8px;\n\n  @media #{map-get($display-breakpoints, 'md-only')} {\n    padding: 5px 15px 0 20px;\n  }\n\n  @media #{map-get($display-breakpoints, 'xs-only')} {\n    flex-direction: column;\n  }\n\n  @media only screen and (max-width: $xsm-and-down) {\n    padding: 16px 16px 0 16px;\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    // background: linear-gradient(97.6deg, #80b622 4.6%, #3c87f8 37.97%), #c4c4c4;\n    opacity: 0.1;\n    border-radius: 16px;\n  }\n\n  &-content {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    padding: 15px 10px 20px 0;\n\n    @media #{map-get($display-breakpoints, 'sm-and-up')} {\n      max-width: 600px;\n    }\n\n    @media only screen and (max-width: $xsm-and-down) {\n      padding: 0 0 15px 0;\n    }\n  }\n\n  &-title {\n    margin-bottom: 8px;\n    font-size: 24px;\n    font-weight: 700;\n\n    @media #{map-get($display-breakpoints, 'md-only')} {\n      font-size: 22px;\n    }\n\n    @media only screen and (max-width: $xsm-and-down) {\n      font-size: 20px;\n    }\n  }\n\n  &-text {\n    font-weight: 300;\n    font-size: 14px;\n    letter-spacing: -0.002em;\n  }\n\n  &-image {\n    display: flex;\n    align-items: flex-end;\n\n    @media #{map-get($display-breakpoints, 'xs-only')} {\n      justify-content: center;\n\n      .v-image {\n        max-height: 90px !important;\n      }\n    }\n\n    &-helper {\n      // width: 362px;\n    }\n  }\n}\n\n.teacher-listing-header-title {\n  font-size: 36px;\n  font-weight: 900;\n  border-bottom: 1px #dadada solid;\n  padding-bottom: 10px;\n  display: flex;\n  justify-content: space-between;\n  place-items: flex-end;\n}\n\n.teacher-listing-header-title-text {\n  min-width: 50%;\n}\n\n.teacher-listing-content {\n  width: 100%;\n}\n</style>\n\n<style lang=\"scss\">\n@import '~vuetify/src/styles/settings/_variables';\n@import './assets/styles/vars';\n.questions {\n  position: relative;\n  margin: 138px 0 82px;\n\n  @media #{map-get($display-breakpoints, 'sm-and-down')} {\n    margin: 95px 0 82px;\n  }\n\n  .section-bg {\n    top: 72px;\n  }\n\n  .section-head {\n    margin-bottom: 118px;\n\n    @media #{map-get($display-breakpoints, 'sm-and-down')} {\n      margin-bottom: 70px;\n    }\n\n    @media #{map-get($display-breakpoints, 'xs-only')} {\n      margin-bottom: 40px;\n    }\n  }\n\n  &-content {\n    max-width: 920px;\n    margin: 0 auto;\n\n    @media only screen and (max-width: $xxs-and-down) {\n      .v-expansion-panel-content__wrap {\n        padding: 0 16px 20px !important;\n      }\n    }\n  }\n\n  &-button {\n    display: flex;\n    justify-content: center;\n    margin-top: 45px;\n\n    .v-btn {\n      min-width: 202px !important;\n\n      @media only screen and (max-width: $xxs-and-down) {\n        min-width: 100% !important;\n        width: 100% !important;\n      }\n    }\n  }\n}\n\n.faq-custom-wrapper {\n  display: grid;\n  justify-content: center;\n}\n\n.section-head--decorated h3 {\n  color: var(--v-success-base);\n  background: -webkit-linear-gradient(\n    -75deg,\n    var(--v-success-base),\n    var(--v-primary-base)\n  );\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n\n.teacher-listing-page-faq-section {\n  padding-top: 50px;\n  margin-top: 50px;\n  padding-bottom: 70px;\n}\n\n.questions-content div div::before {\n  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2),\n    0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);\n  border-bottom: 1px solid #dadada;\n  border-radius: 0px !important;\n}\n\n.questions-content svg {\n  fill: #ef5a6f !important;\n}\n\n.teacher-listing-page-faq-section h3 {\n  color: var(--v-success-base);\n  background: -webkit-linear-gradient(\n    -75deg,\n    var(--v-success-base),\n    var(--v-primary-base)\n  );\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n\n.teacher-listing-page-faq-section\n  .v-expansion-panels\n  .v-expansion-panel::before {\n  box-shadow: none !important;\n}\n\n.teacher-listing-page-faq-section .v-expansion-panels .v-expansion-panel {\n  background-color: transparent !important;\n  margin-bottom: 0px !important;\n}\n.teacher-listing-header-image {\n  background-image: url('~/assets/images/banners/default.svg');\n  width: 250px;\n  height: 120px;\n  background-position: center center;\n}\n</style>\n", "D:\\languworks\\langu-frontend\\components\\TeacherFilterNew.vue", [], "D:\\languworks\\langu-frontend\\lang\\en\\faq-page\\index.js", [], "D:\\languworks\\langu-frontend\\pages\\faq\\index.vue", [], "D:\\languworks\\langu-frontend\\lang\\es\\faq-page\\index.js", [], "D:\\languworks\\langu-frontend\\lang\\pl\\faq-page\\index.js", [], "D:\\languworks\\langu-frontend\\layouts\\error.vue", ["995"], "<template>\n  <v-app light>\n    <div class=\"error-page\">\n      <v-container fluid class=\"fill-height\">\n        <v-row align=\"center\" justify=\"center\" class=\"fill-height\">\n          <v-col cols=\"12\" class=\"text-center\">\n            <!-- 404 Illustration -->\n            <div class=\"error-illustration\">\n              <v-img\n                :src=\"require('~/assets/images/404-Error-page-01.svg')\"\n                alt=\"404 Error\"\n                max-width=\"500\"\n                contain\n                class=\"mx-auto\"\n              ></v-img>\n            </div>\n\n            <!-- Error Content -->\n            <div class=\"error-content\">\n              <!-- <template v-if=\"error.statusCode === 404\"> -->\n              <h1 class=\"error-title\">{{ $t('error_404_title') }}</h1>\n              <h2 class=\"error-subtitle\">\n                {{ $t('error_404_subtitle') }}\n              </h2>\n              <p\n                class=\"error-description\"\n                v-html=\"$t('error_404_description')\"\n              ></p>\n              <nuxt-link\n                id=\"book-a-trail-button-header\"\n                :to=\"localePath({ path: '/teacher-listing' })\"\n                class=\"take-home-btn\"\n              >\n                {{ $t('error_404_button') }}\n              </nuxt-link>\n              <!-- </template> -->\n\n              <!-- <template v-else>\n                <h1 class=\"error-title mb-4\">\n                  {{ $t('error_general_title') }}\n                </h1>\n                <h2 class=\"error-subtitle mb-6\">\n                  {{ $t('error_general_subtitle') }}\n                </h2>\n                <p\n                  class=\"error-description mb-8\"\n                  v-html=\"$t('error_general_description')\"\n                ></p>\n                <v-btn\n                  :to=\"localePath('/')\"\n                  color=\"primary\"\n                  large\n                  rounded\n                  class=\"take-home-btn\"\n                  elevation=\"0\"\n                >\n                  {{ $t('error_general_button') }}\n                </v-btn>\n              </template> -->\n            </div>\n          </v-col>\n        </v-row>\n      </v-container>\n    </div>\n  </v-app>\n</template>\n\n<script>\nexport default {\n  layout: 'empty',\n  props: {\n    error: {\n      type: Object,\n      default: null,\n    },\n  },\n  head() {\n    const title = this.error.statusCode === 404 ? 'Page Not Found' : 'Error'\n    return {\n      title,\n      meta: [\n        {\n          hid: 'description',\n          name: 'description',\n          content:\n            this.error.statusCode === 404\n              ? 'The page you are looking for could not be found.'\n              : 'An error occurred while processing your request.',\n        },\n      ],\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.error-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.error-illustration {\n  .v-image {\n    max-width: 100%;\n    height: auto;\n    margin-top: -70px;\n  }\n}\n\n.error-content {\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n.error-title {\n  font-size: 42px;\n  font-weight: 700;\n  color: #000000;\n  line-height: 1.2;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n\n  @media (max-width: 480px) {\n    font-size: 2rem;\n  }\n}\n\n.error-subtitle {\n  font-size: 1.75rem;\n  font-weight: 600;\n  color: #000000;\n  line-height: 1.3;\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n\n  @media (max-width: 480px) {\n    font-size: 1.25rem;\n  }\n}\n\n.error-description {\n  font-size: 14px;\n  color: #000000;\n  line-height: 1.6;\n  margin-top: 10px;\n  margin-bottom: 20px;\n  @media (max-width: 768px) {\n    font-size: 1rem;\n  }\n\n  @media (max-width: 480px) {\n    font-size: 0.9rem;\n  }\n}\n\n.take-home-btn {\n  font-weight: 600;\n  font-size: 16px;\n  padding: 12px 32px;\n  text-transform: none;\n  letter-spacing: 0.5px;\n  border-radius: 50px;\n  background-color: #fbb03b !important;\n  color: white !important;\n  text-decoration-line: none;\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 1rem;\n    padding: 10px 24px;\n  }\n}\n\n// Responsive adjustments\n@media (max-width: 768px) {\n  .error-page {\n    padding: 15px;\n  }\n\n  .error-illustration {\n    margin-bottom: 1.5rem;\n\n    .v-image {\n      max-width: 400px;\n    }\n  }\n}\n\n@media (max-width: 480px) {\n  .error-page {\n    padding: 10px;\n  }\n\n  .error-illustration {\n    margin-bottom: 1rem;\n\n    .v-image {\n      max-width: 300px;\n    }\n  }\n\n  .error-content {\n    padding: 0 10px;\n  }\n}\n\n// Animation for the illustration\n.error-illustration {\n  animation: float 3s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%,\n  100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n</style>\n", "D:\\languworks\\langu-frontend\\lang\\en.js", [], "D:\\languworks\\langu-frontend\\lang\\es.js", [], "D:\\languworks\\langu-frontend\\lang\\pl.js", [], "D:\\languworks\\langu-frontend\\store\\payments.js", [], "D:\\languworks\\langu-frontend\\components\\payments\\PaymentItem.vue", [], "D:\\languworks\\langu-frontend\\components\\payments\\PaymentLesson.vue", [], "D:\\languworks\\langu-frontend\\components\\payments\\PaymentsPage.vue", [], "D:\\languworks\\langu-frontend\\middleware\\confirmationPageAllowed.js", [], "D:\\languworks\\langu-frontend\\middleware\\authenticated.js", [], "D:\\languworks\\langu-frontend\\middleware\\paymentsPageClass.js", [], "D:\\languworks\\langu-frontend\\middleware\\redirect.js", [], "D:\\languworks\\langu-frontend\\middleware\\businessSuccessPageAllowed.js", [], "D:\\languworks\\langu-frontend\\middleware\\thankYouPageAllowed.js", [], "D:\\languworks\\langu-frontend\\middleware\\teacherListingRedirect.js", [], "D:\\languworks\\langu-frontend\\middleware\\threadExisted.js", [], "D:\\languworks\\langu-frontend\\middleware\\utm.js", ["996"], "D:\\languworks\\langu-frontend\\plugins\\axios.js", [], "D:\\languworks\\langu-frontend\\plugins\\konva.js", [], "D:\\languworks\\langu-frontend\\plugins\\vue-rating.js", [], "D:\\languworks\\langu-frontend\\plugins\\stripe.js", ["997"], "D:\\languworks\\langu-frontend\\plugins\\socket.js", [], "D:\\languworks\\langu-frontend\\plugins\\pluralization.js", [], "D:\\languworks\\langu-frontend\\plugins\\tidio-chat.js", [], "D:\\languworks\\langu-frontend\\plugins\\router.js", ["998"], "D:\\languworks\\langu-frontend\\plugins\\gtm.js", ["999", "1000", "1001", "1002", "1003", "1004"], "D:\\languworks\\langu-frontend\\layouts\\classroomLayout.vue", [], "D:\\languworks\\langu-frontend\\layouts\\default.vue", [], "D:\\languworks\\langu-frontend\\components\\BuzzDialog.vue", [], "D:\\languworks\\langu-frontend\\components\\Calendar.vue", [], "D:\\languworks\\langu-frontend\\components\\CalendarDate.vue", [], "D:\\languworks\\langu-frontend\\components\\ConfirmDialog.vue", [], "D:\\languworks\\langu-frontend\\components\\CheckEmailDialog.vue", [], "D:\\languworks\\langu-frontend\\components\\CookiePopup.vue", ["1005"], "D:\\languworks\\langu-frontend\\components\\FreeSlots.vue", ["1006"], "D:\\languworks\\langu-frontend\\components\\Footer.vue", [], "D:\\languworks\\langu-frontend\\components\\GoogleSignInButton.vue", [], "D:\\languworks\\langu-frontend\\components\\LAvatar.vue", [], "D:\\languworks\\langu-frontend\\components\\Header.vue", ["1007"], "D:\\languworks\\langu-frontend\\components\\LDialog.vue", [], "D:\\languworks\\langu-frontend\\components\\LChip.vue", [], "D:\\languworks\\langu-frontend\\components\\LessonTimeNotice.vue", [], "D:\\languworks\\langu-frontend\\components\\LExpansionPanels.vue", ["1008"], "D:\\languworks\\langu-frontend\\components\\LoadMoreBtn.vue", [], "D:\\languworks\\langu-frontend\\components\\LoginSidebar.vue", ["1009"], "D:\\languworks\\langu-frontend\\components\\MessageDialog.vue", ["1010"], "D:\\languworks\\langu-frontend\\components\\Loader.vue", [], "D:\\languworks\\langu-frontend\\components\\Pagination.vue", [], "D:\\languworks\\langu-frontend\\components\\SetPasswordDialog.vue", ["1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022"], "D:\\languworks\\langu-frontend\\components\\Snackbar.vue", [], "D:\\languworks\\langu-frontend\\components\\StarRating.vue", [], "D:\\languworks\\langu-frontend\\components\\Steps.vue", [], "D:\\languworks\\langu-frontend\\components\\TeacherCard.vue", ["1023"], "D:\\languworks\\langu-frontend\\components\\SummaryLessonDialog.vue", ["1024"], "D:\\languworks\\langu-frontend\\components\\TeacherFilter.vue", [], "D:\\languworks\\langu-frontend\\components\\TimePicker.vue", [], "D:\\languworks\\langu-frontend\\components\\UserStatus.vue", [], "D:\\languworks\\langu-frontend\\components\\TimePickerItem.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\video\\VideoActions.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\video\\Tokbox.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\vue-draggable-resizable\\VueDraggableResizable.vue", ["1025", "1026", "1027", "1028"], "D:\\languworks\\langu-frontend\\components\\classroom\\video\\Twilio.vue", ["1029", "1030"], "D:\\languworks\\langu-frontend\\pages\\index.vue", ["1031", "1032"], "D:\\languworks\\langu-frontend\\pages\\teacher-listing\\_page\\_params\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\lesson\\_id\\classroom\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\teacher-listing\\_page\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\teacher\\_slug\\index.vue", ["1033", "1034", "1035", "1036", "1037", "1038", "1039"], "D:\\languworks\\langu-frontend\\store\\currency.js", [], "D:\\languworks\\langu-frontend\\store\\auth.js", [], "D:\\languworks\\langu-frontend\\store\\faq.js", ["1040"], "D:\\languworks\\langu-frontend\\store\\language.js", ["1041"], "D:\\languworks\\langu-frontend\\store\\business_page.js", ["1042"], "D:\\languworks\\langu-frontend\\store\\education_page.js", ["1043", "1044", "1045"], "D:\\languworks\\langu-frontend\\store\\review.js", ["1046"], "D:\\languworks\\langu-frontend\\store\\socket.js", [], "D:\\languworks\\langu-frontend\\store\\snackbar.js", [], "D:\\languworks\\langu-frontend\\store\\teacher.js", ["1047"], "D:\\languworks\\langu-frontend\\store\\index.js", [], "D:\\languworks\\langu-frontend\\store\\message.js", ["1048", "1049", "1050", "1051"], "D:\\languworks\\langu-frontend\\store\\lesson.js", ["1052"], "D:\\languworks\\langu-frontend\\store\\purchase.js", ["1053"], "D:\\languworks\\langu-frontend\\store\\teacher_profile.js", [], "D:\\languworks\\langu-frontend\\store\\classroom.js", [], "D:\\languworks\\langu-frontend\\store\\user.js", [], ["1054"], "D:\\languworks\\langu-frontend\\store\\teacher_filter.js", [], "D:\\languworks\\langu-frontend\\store\\settings.js", ["1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072"], "D:\\languworks\\langu-frontend\\components\\form\\TextInput.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\ClassroomContainer.vue", [], "D:\\languworks\\langu-frontend\\locale-domains.js", [], "D:\\languworks\\langu-frontend\\helpers\\navigationState.js", ["1073", "1074"], "D:\\languworks\\langu-frontend\\helpers\\check_device.js", [], "D:\\languworks\\langu-frontend\\helpers\\constants.js", [], "D:\\languworks\\langu-frontend\\helpers\\index.js", [], "D:\\languworks\\langu-frontend\\mixins\\SetTool.vue", [], "D:\\languworks\\langu-frontend\\utils\\hash.js", ["1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105"], "D:\\languworks\\langu-frontend\\components\\classroom\\Konva.vue", ["1106", "1107"], "D:\\languworks\\langu-frontend\\components\\classroom\\Toolbar.vue", ["1108"], "D:\\languworks\\langu-frontend\\components\\classroom\\Library.vue", ["1109", "1110"], "D:\\languworks\\langu-frontend\\components\\classroom\\VideoInput.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\ImageItem.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\PdfItem.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\AudioItem.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\VideoItem.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\TinymceVue.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\OtherCursor.vue", [], "D:\\languworks\\langu-frontend\\components\\classroom\\Viewport.vue", [], "D:\\languworks\\langu-frontend\\lang\\en\\about-us-page\\index.js", [], "D:\\languworks\\langu-frontend\\lang\\pl\\education-page\\index.js", [], "D:\\languworks\\langu-frontend\\components\\teacher-listing\\TeacherListingHeader.vue", [], "D:\\languworks\\langu-frontend\\lang\\pl\\business-page\\index.js", [], "D:\\languworks\\langu-frontend\\components\\classroom\\DropFileArea.vue", [], "D:\\languworks\\langu-frontend\\helpers\\dom.js", ["1111", "1112", "1113", "1114", "1115", "1116", "1117"], "D:\\languworks\\langu-frontend\\lang\\pl\\about-us-page\\index.js", [], "D:\\languworks\\langu-frontend\\lang\\en\\education-page\\index.js", [], "D:\\languworks\\langu-frontend\\lang\\en\\business-page\\index.js", [], "D:\\languworks\\langu-frontend\\lang\\es\\education-page\\index.js", [], "D:\\languworks\\langu-frontend\\lang\\es\\about-us-page\\index.js", [], "D:\\languworks\\langu-frontend\\lang\\es\\business-page\\index.js", [], "D:\\languworks\\langu-frontend\\components\\classroom\\ClassroomContainerHeader.vue", [], "D:\\languworks\\langu-frontend\\mixins\\UploadFiles.vue", [], "D:\\languworks\\langu-frontend\\helpers\\fns.js", [], "D:\\languworks\\langu-frontend\\components\\form\\SelectInput.vue", [], "D:\\languworks\\langu-frontend\\components\\form\\SearchInput.vue", [], "D:\\languworks\\langu-frontend\\components\\user-lessons\\LessonEvaluationDialog.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\messages\\_recipientId\\new\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\profile\\_slug\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\messages\\_threadId\\view\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\unscheduled-lessons\\_page\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\past-lessons\\_page\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\password\\_token\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\lessons\\_page\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\checkout\\stripe-form\\_id\\index.vue", ["1118", "1119", "1120"], "D:\\languworks\\langu-frontend\\pages\\checkout\\p24-payin\\_id\\index.vue", ["1121", "1122"], "D:\\languworks\\langu-frontend\\pages\\user\\payments\\payouts\\_page\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\payments\\lessons\\_page\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\payments\\payouts\\index.vue", [], "D:\\languworks\\langu-frontend\\mixins\\StatusOnline.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\payments\\lessons\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\lessons\\thank-you\\index.vue", ["1123"], "D:\\languworks\\langu-frontend\\pages\\user\\lessons\\confirmation\\index.vue", ["1124"], "D:\\languworks\\langu-frontend\\pages\\user\\unscheduled-lessons\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\settings\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\register\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\payments\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\past-lessons\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\messages\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\user\\lessons\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\teacher-listing\\welcome\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\business\\success\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\teacher-listing\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\education\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\business\\index.vue", ["1125", "1126"], "D:\\languworks\\langu-frontend\\components\\homepage\\ThinkingSection.vue", [], "D:\\languworks\\langu-frontend\\components\\form\\Editor.vue", [], "D:\\languworks\\langu-frontend\\components\\homepage\\FaqSection.vue", [], "D:\\languworks\\langu-frontend\\components\\homepage\\ReviewSection.vue", ["1127"], "D:\\languworks\\langu-frontend\\components\\homepage\\TutorsSection.vue", [], "D:\\languworks\\langu-frontend\\components\\homepage\\LanguagesSection.vue", ["1128", "1129", "1130", "1131"], "D:\\languworks\\langu-frontend\\components\\homepage\\AboutSection.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\EmailIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\form\\FormRate.vue", ["1132"], "D:\\languworks\\langu-frontend\\components\\homepage\\HowWorksSection.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\LifeGradientIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\EducationGradientIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\CareerGradientIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\CheckedGradientIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\teacher-profile\\TimePickerDialog.vue", [], "D:\\languworks\\langu-frontend\\components\\teacher-profile\\FeedbackTags.vue", [], "D:\\languworks\\langu-frontend\\components\\teacher-profile\\CourseItem.vue", ["1133", "1134", "1135", "1136"], "D:\\languworks\\langu-frontend\\components\\teacher-profile\\TeacherProfileSidebar.vue", [], "D:\\languworks\\langu-frontend\\components\\Youtube.vue", [], "D:\\languworks\\langu-frontend\\components\\homepage\\StatSection.vue", ["1137"], "D:\\languworks\\langu-frontend\\components\\homepage\\IntroSection.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\MoonGradientIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\SunsetGradientIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\SearchIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\PlFlagIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\EsFlagIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\EnFlagIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\user-lessons\\LessonsPage.vue", ["1138"], "<template>\n  <v-col class=\"col-12 px-0\">\n    <div\n      :class=\"[\n        'user-lessons',\n        `user-lessons--${isTeacher ? 'teacher' : 'student'}`,\n      ]\"\n    >\n      <v-container fluid class=\"pa-0\">\n        <v-row no-gutters>\n          <v-col class=\"col-12\">\n            <div class=\"user-lessons-wrap mx-auto\">\n              <div class=\"user-lessons-header mb-2 mb-md-4 mb-lg-5\">\n                <div class=\"user-lessons-title\">\n                  <h1 class=\"font-weight-medium\">{{ $t('my_lessons') }} 📚</h1>\n                  <div\n                    v-if=\"isStudent && userCredit\"\n                    class=\"user-lessons-credits text-no-wrap\"\n                  >\n                    {{ $t('langu_credit') }}: {{ currentCurrencySymbol\n                    }}{{ getPrice(userCredit) }}\n                  </div>\n                </div>\n                <div class=\"user-lessons-controls d-flex\">\n                  <div class=\"user-lessons-search\">\n                    <search-input\n                      v-model.trim=\"searchQuery\"\n                      :placeholder=\"\n                        isTeacher ? 'search_for_student' : 'search_for_teacher'\n                      \"\n                      class=\"search-input--inner-border\"\n                      @submit=\"searchSubmitHandler\"\n                    ></search-input>\n                  </div>\n                  <div class=\"user-lessons-nav d-flex\">\n                    <v-btn\n                      :to=\"localePath({ path: '/user/lessons' })\"\n                      class=\"nav-btn font-weight-medium\"\n                      height=\"48\"\n                    >\n                      {{ $t('upcoming_lessons') }}\n                    </v-btn>\n                    <v-btn\n                      :to=\"localePath({ path: '/user/past-lessons' })\"\n                      class=\"nav-btn font-weight-medium\"\n                      height=\"48\"\n                    >\n                      {{ $t('past_lessons') }}\n                    </v-btn>\n                    <v-btn\n                      :to=\"localePath({ path: '/user/unscheduled-lessons' })\"\n                      class=\"nav-btn font-weight-medium\"\n                      height=\"48\"\n                    >\n                      {{ $t('unscheduled_lessons') }} ({{\n                        totalNumberUnscheduledLessons\n                      }})\n                    </v-btn>\n                  </div>\n                </div>\n              </div>\n              <div class=\"user-lessons-body\">\n                <div class=\"user-lessons-content\">\n                  <div>\n                    <div class=\"user-lessons-filters d-flex\">\n                      <l-chip\n                        v-if=\"$route.query.search\"\n                        :clickable=\"true\"\n                        :label=\"$route.query.search\"\n                        light\n                        class=\"mb-1 mr-1\"\n                        @click:close=\"resetSearch\"\n                      ></l-chip>\n                      <l-chip\n                        v-if=\"selectedDate\"\n                        :label=\"\n                          $dayjs(selectedDate, 'YYYY-MM-DD').format('D MMMM')\n                        \"\n                        light\n                        class=\"mb-1 mr-1\"\n                        @click:close=\"resetSelectedDate\"\n                      ></l-chip>\n                    </div>\n\n                    <template v-if=\"lessons.length\">\n                      <div class=\"lessons-list\">\n                        <template v-for=\"lesson in lessons\">\n                          <component\n                            :is=\"lessonComponents[lesson.type]\"\n                            :key=\"lesson.id\"\n                            v-bind=\"{ item: lesson, userStatuses }\"\n                          ></component>\n                        </template>\n                      </div>\n                    </template>\n                    <template v-else>\n                      <div v-if=\"isStudent\" class=\"lessons-list-empty\">\n                        <steps\n                          :active-item-id=\"0\"\n                          :item-link=\"{ id: 1, path: '/teacher-listing' }\"\n                        ></steps>\n                      </div>\n                    </template>\n                  </div>\n\n                  <div v-if=\"totalPages > 1\" class=\"mt-3 mt-md-5 text-center\">\n                    <pagination\n                      :current-page=\"page\"\n                      :total-pages=\"totalPages\"\n                      :route=\"route\"\n                    ></pagination>\n                  </div>\n                </div>\n\n                <aside\n                  v-if=\"$vuetify.breakpoint.mdAndUp\"\n                  class=\"user-lessons-sidebar d-none d-md-block\"\n                >\n                  <div class=\"user-lessons-sidebar-helper\">\n                    <calendar\n                      v-for=\"i in 4\"\n                      :key=\"i\"\n                      :type=\"type\"\n                      :items=\"calendarItems\"\n                      :current-date=\"$dayjs().add(i - 1, 'month')\"\n                      @click-date=\"clickDate\"\n                    ></calendar>\n                  </div>\n                </aside>\n              </div>\n            </div>\n          </v-col>\n        </v-row>\n      </v-container>\n    </div>\n  </v-col>\n</template>\n\n<script>\nimport { hashUserData } from '@/utils/hash'\nimport { getPrice } from '~/helpers'\n\nimport Avatars from '~/mixins/Avatars'\nimport StatusOnline from '~/mixins/StatusOnline'\nimport SearchInput from '~/components/form/SearchInput'\nimport Calendar from '~/components/Calendar'\nimport UpcomingLesson from '~/components/user-lessons/UpcomingLesson'\nimport PastLesson from '~/components/user-lessons/PastLesson'\nimport UnscheduledLesson from '~/components/user-lessons/UnscheduledLesson'\nimport Steps from '~/components/Steps'\nimport Pagination from '~/components/Pagination'\nimport LChip from '~/components/LChip'\n\nexport default {\n  name: 'LessonsPage',\n  components: {\n    SearchInput,\n    Calendar,\n    Steps,\n    Pagination,\n    LChip,\n  },\n  mixins: [Avatars, StatusOnline],\n  props: {\n    page: {\n      type: Number,\n      default: 1,\n    },\n    route: {\n      type: String,\n      required: true,\n    },\n    type: {\n      type: String,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      getPrice,\n      lessonComponents: {\n        upcoming: UpcomingLesson,\n        past: PastLesson,\n        unscheduled: UnscheduledLesson,\n      },\n      searchQuery: '',\n      now: this.$dayjs(),\n      selectedDate: null,\n    }\n  },\n  computed: {\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    isStudent() {\n      return this.$store.getters['user/isStudent']\n    },\n    lessons() {\n      return this.$store.getters['lesson/lessons']\n    },\n    calendarItems() {\n      return this.$store.state.lesson.calendarItems\n    },\n    totalPages() {\n      return Math.ceil(\n        this.$store.state.lesson.totalQuantity / process.env.NUXT_ENV_PER_PAGE\n      )\n    },\n    totalNumberUnscheduledLessons() {\n      return this.$store.getters['user/totalNumberUnscheduledLessons']\n    },\n    currentCurrencySymbol() {\n      return this.$store.getters['currency/currentCurrencySymbol']\n    },\n    userCredit() {\n      return this.$store.getters['user/userCredit']\n    },\n  },\n  watch: {\n    selectedDate() {\n      this.setArrStatusId()\n    },\n    '$route.params.search': {\n      handler() {\n        this.setArrStatusId()\n      },\n      deep: true,\n    },\n  },\n  beforeMount() {\n    this.searchQuery = this.$route.query?.search ?? ''\n\n    // Check for paid trial event data\n    if (localStorage.getItem('event_data') !== null) {\n      try {\n        const eventData = JSON.parse(localStorage.getItem('event_data'))\n        if (eventData.event === 'purchase_paid_trial') {\n          // Get user data\n          const tidioData = this.$store.state.user.tidioData || {}\n          const userData = this.$store.state.user.item || {}\n          const userEmail = tidioData.email || ''\n          const userName = `${userData.firstName || ''} ${\n            userData.lastName || ''\n          }`.trim()\n\n          // Add hashed user data to the items array\n          if (\n            eventData.ecommerce &&\n            eventData.ecommerce.items &&\n            eventData.ecommerce.items.length > 0\n          ) {\n            eventData.ecommerce.items.forEach((item) => {\n              if (!item.user_name) {\n                item.user_name = hashUserData(userName)\n              }\n              if (!item.email_id) {\n                item.email_id = hashUserData(userEmail)\n              }\n            })\n          }\n\n          // Ensure dataLayer is initialized\n          window.dataLayer = window.dataLayer || []\n\n          // Push event to dataLayer\n          window.dataLayer.push({\n            ecommerce: null,\n          })\n          window.dataLayer.push(eventData)\n\n          // Remove event data from localStorage\n          localStorage.removeItem('event_data')\n        }\n      } catch (error) {\n        console.log(error)\n      }\n    }\n\n    this.setArrStatusId()\n    this.refreshStatusOnline()\n  },\n  methods: {\n    setArrStatusId() {\n      const prop = this.isTeacher ? 'studentId' : 'teacherId'\n\n      this.arrStatusId = this.lessons.map((item) => item[prop])\n    },\n    async searchSubmitHandler() {\n      if (this.selectedDate) {\n        await this.resetSelectedDate()\n      }\n\n      await this.$router.push({\n        name: this.$route.name,\n        params: { page: '1' },\n        query: this.searchQuery ? { search: this.searchQuery } : {},\n      })\n    },\n    async clickDate(date) {\n      if (this.searchQuery.length) {\n        await this.resetSearch()\n      }\n\n      // if (this.type === 'past' || this.type === 'upcoming') {\n      const data = { date }\n\n      // if (this.type === 'past') data.type = 'past'\n      if (this.type === 'upcoming') data.type = 'upcoming'\n\n      this.$store\n        .dispatch('lesson/getLessonsByDate', data)\n        .then(() => (this.selectedDate = date))\n      // }\n    },\n    resetSelectedDate() {\n      return new Promise((resolve) => {\n        const data = {\n          page: this.page,\n          perPage: process.env.NUXT_ENV_PER_PAGE,\n          type: this.type,\n        }\n\n        this.$store.dispatch('lesson/getUpcomingLessons', data).then(() => {\n          this.selectedDate = null\n\n          resolve()\n        })\n      })\n    },\n    resetSearch() {\n      return new Promise((resolve) => {\n        this.searchQuery = ''\n\n        this.$router.push({\n          name: this.$route.name,\n          params: { page: '1' },\n          query: {},\n        })\n\n        setTimeout(() => {\n          resolve()\n        })\n      })\n    },\n  },\n}\n</script>\n\n<style lang=\"scss\">\n@import './assets/styles/user-lessons.scss';\n</style>\n", "D:\\languworks\\langu-frontend\\components\\user-messages\\MessagesPage.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\SunGradientIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\AlarmGradientIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\business-page\\BusinessPage.vue", ["1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146"], "D:\\languworks\\langu-frontend\\mixins\\Avatars.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\ReceiptInfo.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\CalendarNotificationInfo.vue", ["1147"], "D:\\languworks\\langu-frontend\\components\\user-settings\\TeachingQualificationsInfo.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\LearningPreferencesInfo.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\CoursesInfo.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\TeachingPreferencesInfo.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\PricingTableInfo.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\AboutMeInfo.vue", ["1148"], "D:\\languworks\\langu-frontend\\components\\landing-page\\TeachersSlider.vue", [], "D:\\languworks\\langu-frontend\\components\\landing-page\\TestimonialsSlider.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\BackgroundInfo.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\LanguagesInfo.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\SummaryInfo.vue", [], "D:\\languworks\\langu-frontend\\components\\teacher-profile\\FindMoreTeachersButton.vue", [], "D:\\languworks\\langu-frontend\\components\\teacher-profile\\PricePerLesson.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\HomePageIntroImage.vue", [], "D:\\languworks\\langu-frontend\\components\\homepage\\SelectLanguage.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\BasicInfo.vue", ["1149"], "D:\\languworks\\langu-frontend\\pages\\blog\\index.vue", [], "D:\\languworks\\langu-frontend\\pages\\about-us\\index.vue", ["1150", "1151", "1152", "1153"], "D:\\languworks\\langu-frontend\\components\\business-page\\icons\\DotsIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\UserSettingTemplate.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\UserSettingSelect.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\QualificationSuccessDialog.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\UserSettingAutocomplete.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\SpecialityDialog.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\PerLessonPrice.vue", ["1154"], "D:\\languworks\\langu-frontend\\components\\user-settings\\LessonPrice.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\CourseItem.vue", ["1155"], "D:\\languworks\\langu-frontend\\components\\user-settings\\IllustrationDialog.vue", [], "D:\\languworks\\langu-frontend\\components\\user-lessons\\UpcomingLesson.vue", [], "D:\\languworks\\langu-frontend\\components\\user-settings\\AddQualificationDialog.vue", [], "D:\\languworks\\langu-frontend\\components\\user-lessons\\UnscheduledLesson.vue", ["1156"], "D:\\languworks\\langu-frontend\\components\\user-lessons\\PastLesson.vue", ["1157"], "D:\\languworks\\langu-frontend\\components\\user-messages\\EmptyContent.vue", ["1158"], "D:\\languworks\\langu-frontend\\components\\user-messages\\Conversation.vue", ["1159"], "D:\\languworks\\langu-frontend\\components\\user-lessons\\TimePickerDialog.vue", ["1160"], "D:\\languworks\\langu-frontend\\components\\user-lessons\\LessonItem.vue", ["1161", "1162"], "D:\\languworks\\langu-frontend\\components\\user-messages\\ConversationItem.vue", ["1163", "1164"], "D:\\languworks\\langu-frontend\\components\\payments\\WiseTransferModal.vue", [], "D:\\languworks\\langu-frontend\\components\\payments\\SavedAccountsModal.vue", [], "D:\\languworks\\langu-frontend\\components\\payments\\PayoutItem.vue", [], "D:\\languworks\\langu-frontend\\components\\payments\\PaymentPayout.vue", [], "D:\\languworks\\langu-frontend\\components\\payments\\PayoutModal.vue", [], "D:\\languworks\\langu-frontend\\components\\payments\\PaymentDetailsModal.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\GoogleIcon.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\BusinessPageIntroMobileImage.vue", [], "D:\\languworks\\langu-frontend\\components\\form\\SelectInputNew.vue", [], "D:\\languworks\\langu-frontend\\components\\images\\BusinessPageIntroImage.vue", [], "D:\\languworks\\langu-frontend\\components\\payments\\countries.js", [], "D:\\languworks\\langu-frontend\\components\\classroom\\video\\Whereby.vue", [], "D:\\languworks\\langu-frontend\\pages\\meet\\index.vue", ["1165"], "D:\\languworks\\langu-frontend\\helpers\\whereby-api.js", ["1166", "1167", "1168", "1169"], "D:\\languworks\\langu-frontend\\plugins\\sentry.client.js", [], "D:\\languworks\\langu-frontend\\pages\\sentry-test.vue", ["1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197"], {"ruleId": "1198", "severity": 1, "message": "1199", "line": 77, "column": 19, "nodeType": "1200", "endLine": 81, "endColumn": 20}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 27, "column": 17, "nodeType": "1200", "endLine": 27, "endColumn": 53}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 3, "column": 3, "nodeType": "1203", "messageId": "1204", "endLine": 3, "endColumn": 14}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 4, "column": 3, "nodeType": "1203", "messageId": "1204", "endLine": 4, "endColumn": 14}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 42, "column": 5, "nodeType": "1203", "messageId": "1204", "endLine": 42, "endColumn": 16}, {"ruleId": "1205", "severity": 2, "message": "1206", "line": 2, "column": 3, "nodeType": null, "endLine": 2, "endColumn": 5, "fix": "1207"}, {"ruleId": "1205", "severity": 2, "message": "1208", "line": 3, "column": 1, "nodeType": null, "endLine": 3, "endColumn": 57, "fix": "1209"}, {"ruleId": "1205", "severity": 2, "message": "1206", "line": 4, "column": 1, "nodeType": null, "endLine": 4, "endColumn": 3, "fix": "1210"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 7, "column": 6, "nodeType": null, "endLine": 7, "endColumn": 7, "fix": "1212"}, {"ruleId": "1205", "severity": 2, "message": "1213", "line": 8, "column": 1, "nodeType": null, "endLine": 8, "endColumn": 37, "fix": "1214"}, {"ruleId": "1205", "severity": 2, "message": "1215", "line": 9, "column": 2, "nodeType": null, "endLine": 9, "endColumn": 2, "fix": "1216"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 11, "column": 38, "nodeType": "1200", "endLine": 11, "endColumn": 74}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 93, "column": 13, "nodeType": "1200", "endLine": 93, "endColumn": 81}, {"ruleId": "1217", "severity": 1, "message": "1218", "line": 501, "column": 9, "nodeType": "1219", "messageId": "1220", "endLine": 501, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 37, "column": 14, "nodeType": "1200", "endLine": 37, "endColumn": 36}, {"ruleId": "1221", "severity": 2, "message": "1222", "line": 191, "column": 1, "nodeType": "1223", "endLine": 191, "endColumn": 64, "fix": "1224"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 37, "column": 20, "nodeType": "1200", "endLine": 37, "endColumn": 73}, {"ruleId": "1205", "severity": 2, "message": "1225", "line": 2, "column": 12, "nodeType": null, "endLine": 2, "endColumn": 89, "fix": "1226"}, {"ruleId": "1205", "severity": 2, "message": "1227", "line": 3, "column": 30, "nodeType": null, "endLine": 3, "endColumn": 30, "fix": "1228"}, {"ruleId": "1205", "severity": 2, "message": "1229", "line": 14, "column": 24, "nodeType": null, "endLine": 14, "endColumn": 105, "fix": "1230"}, {"ruleId": "1205", "severity": 2, "message": "1231", "line": 15, "column": 42, "nodeType": null, "endLine": 15, "endColumn": 42, "fix": "1232"}, {"ruleId": "1205", "severity": 2, "message": "1233", "line": 18, "column": 25, "nodeType": null, "endLine": 18, "endColumn": 96, "fix": "1234"}, {"ruleId": "1205", "severity": 2, "message": "1235", "line": 24, "column": 24, "nodeType": null, "endLine": 24, "endColumn": 65, "fix": "1236"}, {"ruleId": "1205", "severity": 2, "message": "1237", "line": 25, "column": 37, "nodeType": null, "endLine": 25, "endColumn": 78, "fix": "1238"}, {"ruleId": "1205", "severity": 2, "message": "1233", "line": 28, "column": 25, "nodeType": null, "endLine": 28, "endColumn": 96, "fix": "1239"}, {"ruleId": "1205", "severity": 2, "message": "1240", "line": 38, "column": 25, "nodeType": null, "endLine": 38, "endColumn": 97, "fix": "1241"}, {"ruleId": "1205", "severity": 2, "message": "1242", "line": 46, "column": 19, "nodeType": null, "endLine": 46, "endColumn": 83, "fix": "1243"}, {"ruleId": "1221", "severity": 2, "message": "1244", "line": 74, "column": 1, "nodeType": "1223", "endLine": 74, "endColumn": 65, "fix": "1245"}, {"ruleId": "1205", "severity": 2, "message": "1246", "line": 145, "column": 65, "nodeType": null, "endLine": 145, "endColumn": 83, "fix": "1247"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 46, "column": 9, "nodeType": "1200", "endLine": 46, "endColumn": 60}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 445, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 445, "endColumn": 22}, {"ruleId": "1248", "severity": 1, "message": "1249", "line": 18, "column": 14, "nodeType": "1250", "endLine": 18, "endColumn": 20}, {"ruleId": "1251", "severity": 2, "message": "1252", "line": 878, "column": 22, "nodeType": "1250", "messageId": "1253", "endLine": 878, "endColumn": 23}, {"ruleId": "1251", "severity": 2, "message": "1252", "line": 891, "column": 14, "nodeType": "1250", "messageId": "1253", "endLine": 891, "endColumn": 15}, {"ruleId": "1251", "severity": 2, "message": "1252", "line": 1033, "column": 24, "nodeType": "1250", "messageId": "1253", "endLine": 1033, "endColumn": 25}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 225, "column": 19, "nodeType": "1203", "messageId": "1204", "endLine": 225, "endColumn": 30}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 380, "column": 13, "nodeType": "1203", "messageId": "1204", "endLine": 380, "endColumn": 26}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 254, "column": 19, "nodeType": "1200", "endLine": 254, "endColumn": 59}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 258, "column": 19, "nodeType": "1200", "endLine": 258, "endColumn": 58}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 40, "column": 31, "nodeType": "1200", "endLine": 42, "endColumn": 32}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 125, "column": 23, "nodeType": "1200", "endLine": 125, "endColumn": 79}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 269, "column": 29, "nodeType": "1200", "endLine": 271, "endColumn": 30}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 281, "column": 29, "nodeType": "1200", "endLine": 283, "endColumn": 30}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 293, "column": 29, "nodeType": "1200", "endLine": 293, "endColumn": 76}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 303, "column": 29, "nodeType": "1200", "endLine": 307, "endColumn": 30}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 360, "column": 21, "nodeType": "1200", "endLine": 364, "endColumn": 22}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 19, "column": 21, "nodeType": "1203", "messageId": "1204", "endLine": 19, "endColumn": 32}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 31, "column": 21, "nodeType": "1203", "messageId": "1204", "endLine": 31, "endColumn": 32}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 7, "column": 21, "nodeType": "1203", "messageId": "1204", "endLine": 7, "endColumn": 32}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 7, "column": 21, "nodeType": "1203", "messageId": "1204", "endLine": 7, "endColumn": 32}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 20, "column": 21, "nodeType": "1203", "messageId": "1204", "endLine": 20, "endColumn": 32}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 33, "column": 21, "nodeType": "1203", "messageId": "1204", "endLine": 33, "endColumn": 32}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 15, "column": 21, "nodeType": "1203", "messageId": "1204", "endLine": 15, "endColumn": 32}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 36, "column": 21, "nodeType": "1203", "messageId": "1204", "endLine": 36, "endColumn": 32}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 155, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 155, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 194, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 194, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 224, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 224, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 249, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 249, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 223, "column": 20, "nodeType": "1203", "messageId": "1204", "endLine": 223, "endColumn": 32}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 111, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 111, "endColumn": 24}, {"ruleId": "1254", "replacedBy": "1255"}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 286, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 286, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 312, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 312, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 360, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 360, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 393, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 393, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 425, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 425, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 458, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 458, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 484, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 484, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 530, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 530, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 566, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 566, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 588, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 588, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 607, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 607, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 640, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 640, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 669, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 669, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 703, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 703, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 735, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 735, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 756, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 756, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 790, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 790, "endColumn": 21}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 828, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 828, "endColumn": 21}, {"ruleId": "1256", "severity": 2, "message": "1257", "line": 81, "column": 7, "nodeType": "1250", "messageId": "1258", "endLine": 81, "endColumn": 11, "fix": "1259"}, {"ruleId": "1256", "severity": 2, "message": "1260", "line": 82, "column": 7, "nodeType": "1250", "messageId": "1258", "endLine": 82, "endColumn": 12, "fix": "1261"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 2, "column": 81, "nodeType": null, "endLine": 2, "endColumn": 82, "fix": "1262"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 3, "column": 18, "nodeType": null, "endLine": 3, "endColumn": 19, "fix": "1263"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 5, "column": 69, "nodeType": null, "endLine": 5, "endColumn": 70, "fix": "1264"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 7, "column": 16, "nodeType": null, "endLine": 7, "endColumn": 17, "fix": "1265"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 24, "column": 42, "nodeType": null, "endLine": 24, "endColumn": 43, "fix": "1266"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 27, "column": 35, "nodeType": null, "endLine": 27, "endColumn": 36, "fix": "1267"}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 32, "column": 7, "nodeType": "1203", "messageId": "1204", "endLine": 32, "endColumn": 20}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 32, "column": 80, "nodeType": null, "endLine": 32, "endColumn": 81, "fix": "1268"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 33, "column": 36, "nodeType": null, "endLine": 33, "endColumn": 37, "fix": "1269"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 38, "column": 47, "nodeType": null, "endLine": 38, "endColumn": 48, "fix": "1270"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 41, "column": 40, "nodeType": null, "endLine": 41, "endColumn": 41, "fix": "1271"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 44, "column": 22, "nodeType": null, "endLine": 44, "endColumn": 23, "fix": "1272"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 46, "column": 80, "nodeType": null, "endLine": 46, "endColumn": 81, "fix": "1273"}, {"ruleId": "1205", "severity": 2, "message": "1274", "line": 48, "column": 52, "nodeType": null, "endLine": 48, "endColumn": 56, "fix": "1275"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 50, "column": 20, "nodeType": null, "endLine": 50, "endColumn": 21, "fix": "1276"}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 52, "column": 7, "nodeType": "1203", "messageId": "1204", "endLine": 52, "endColumn": 20}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 52, "column": 54, "nodeType": null, "endLine": 52, "endColumn": 55, "fix": "1277"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 53, "column": 36, "nodeType": null, "endLine": 53, "endColumn": 37, "fix": "1278"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 58, "column": 25, "nodeType": null, "endLine": 58, "endColumn": 26, "fix": "1279"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 60, "column": 76, "nodeType": null, "endLine": 60, "endColumn": 77, "fix": "1280"}, {"ruleId": "1205", "severity": 2, "message": "1274", "line": 62, "column": 55, "nodeType": null, "endLine": 62, "endColumn": 59, "fix": "1281"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 66, "column": 43, "nodeType": null, "endLine": 66, "endColumn": 44, "fix": "1282"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 69, "column": 38, "nodeType": null, "endLine": 69, "endColumn": 39, "fix": "1283"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 71, "column": 53, "nodeType": null, "endLine": 71, "endColumn": 54, "fix": "1284"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 74, "column": 30, "nodeType": null, "endLine": 74, "endColumn": 31, "fix": "1285"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 79, "column": 48, "nodeType": null, "endLine": 79, "endColumn": 49, "fix": "1286"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 80, "column": 48, "nodeType": null, "endLine": 80, "endColumn": 49, "fix": "1287"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 83, "column": 36, "nodeType": null, "endLine": 83, "endColumn": 37, "fix": "1288"}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 85, "column": 7, "nodeType": "1203", "messageId": "1204", "endLine": 85, "endColumn": 20}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 85, "column": 47, "nodeType": null, "endLine": 85, "endColumn": 48, "fix": "1289"}, {"ruleId": "1205", "severity": 2, "message": "1211", "line": 86, "column": 36, "nodeType": null, "endLine": 86, "endColumn": 37, "fix": "1290"}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 420, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 420, "endColumn": 23}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 516, "column": 13, "nodeType": "1203", "messageId": "1204", "endLine": 516, "endColumn": 25}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 854, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 854, "endColumn": 23}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 530, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 530, "endColumn": 22}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 629, "column": 25, "nodeType": "1203", "messageId": "1204", "endLine": 629, "endColumn": 36}, {"ruleId": "1205", "severity": 2, "message": "1291", "line": 3, "column": 48, "nodeType": null, "endLine": 3, "endColumn": 49, "fix": "1292"}, {"ruleId": "1205", "severity": 2, "message": "1293", "line": 11, "column": 23, "nodeType": null, "endLine": 11, "endColumn": 23, "fix": "1294"}, {"ruleId": "1205", "severity": 2, "message": "1295", "line": 12, "column": 10, "nodeType": null, "endLine": 12, "endColumn": 14, "fix": "1296"}, {"ruleId": "1205", "severity": 2, "message": "1291", "line": 25, "column": 32, "nodeType": null, "endLine": 25, "endColumn": 33, "fix": "1297"}, {"ruleId": "1205", "severity": 2, "message": "1293", "line": 30, "column": 53, "nodeType": null, "endLine": 30, "endColumn": 53, "fix": "1298"}, {"ruleId": "1205", "severity": 2, "message": "1291", "line": 34, "column": 25, "nodeType": null, "endLine": 34, "endColumn": 26, "fix": "1299"}, {"ruleId": "1205", "severity": 2, "message": "1291", "line": 47, "column": 28, "nodeType": null, "endLine": 47, "endColumn": 29, "fix": "1300"}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 25, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 25, "endColumn": 22}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 28, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 28, "endColumn": 22}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 31, "column": 7, "nodeType": "1203", "messageId": "1204", "endLine": 31, "endColumn": 18}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 10, "column": 19, "nodeType": "1200", "endLine": 14, "endColumn": 20}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 30, "column": 22, "nodeType": "1200", "endLine": 30, "endColumn": 59}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 74, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 74, "endColumn": 24}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 72, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 72, "endColumn": 24}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 21, "column": 5, "nodeType": "1203", "messageId": "1204", "endLine": 21, "endColumn": 16}, {"ruleId": "1205", "severity": 2, "message": "1301", "line": 26, "column": 13, "nodeType": null, "endLine": 26, "endColumn": 40, "fix": "1302"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 23, "column": 15, "nodeType": "1200", "endLine": 23, "endColumn": 64}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 56, "column": 23, "nodeType": "1200", "endLine": 56, "endColumn": 52}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 119, "column": 23, "nodeType": "1200", "endLine": 119, "endColumn": 58}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 228, "column": 29, "nodeType": "1200", "endLine": 228, "endColumn": 58}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 299, "column": 29, "nodeType": "1200", "endLine": 299, "endColumn": 64}, {"ruleId": "1205", "severity": 2, "message": "1303", "line": 14, "column": 12, "nodeType": null, "endLine": 14, "endColumn": 77, "fix": "1304"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 63, "column": 19, "nodeType": "1200", "endLine": 63, "endColumn": 53}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 113, "column": 15, "nodeType": "1200", "endLine": 113, "endColumn": 49}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 135, "column": 16, "nodeType": "1200", "endLine": 135, "endColumn": 50}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 145, "column": 16, "nodeType": "1200", "endLine": 145, "endColumn": 45}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 16, "column": 45, "nodeType": "1200", "endLine": 16, "endColumn": 67}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 275, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 275, "endColumn": 20}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 16, "column": 29, "nodeType": "1200", "endLine": 16, "endColumn": 69}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 172, "column": 31, "nodeType": "1200", "endLine": 172, "endColumn": 71}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 216, "column": 31, "nodeType": "1200", "endLine": 216, "endColumn": 71}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 256, "column": 33, "nodeType": "1200", "endLine": 256, "endColumn": 73}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 284, "column": 35, "nodeType": "1200", "endLine": 284, "endColumn": 79}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 402, "column": 35, "nodeType": "1200", "endLine": 402, "endColumn": 80}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 589, "column": 25, "nodeType": "1200", "endLine": 589, "endColumn": 74}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 808, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 808, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 79, "column": 13, "nodeType": "1200", "endLine": 79, "endColumn": 69}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 222, "column": 22, "nodeType": "1203", "messageId": "1204", "endLine": 222, "endColumn": 34}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 301, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 301, "endColumn": 20}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 16, "column": 13, "nodeType": "1200", "endLine": 16, "endColumn": 47}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 34, "column": 21, "nodeType": "1200", "endLine": 34, "endColumn": 65}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 76, "column": 21, "nodeType": "1200", "endLine": 76, "endColumn": 65}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 136, "column": 21, "nodeType": "1200", "endLine": 136, "endColumn": 65}, {"ruleId": "1305", "severity": 1, "message": "1306", "line": 6, "column": 5, "nodeType": "1200", "endLine": 6, "endColumn": 21, "fix": "1307"}, {"ruleId": "1305", "severity": 1, "message": "1306", "line": 93, "column": 19, "nodeType": "1200", "endLine": 93, "endColumn": 52, "fix": "1308"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 7, "column": 30, "nodeType": "1200", "endLine": 7, "endColumn": 62}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 165, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 165, "endColumn": 23}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 178, "column": 9, "nodeType": "1203", "messageId": "1204", "endLine": 178, "endColumn": 20}, {"ruleId": "1205", "severity": 2, "message": "1215", "line": 491, "column": 9, "nodeType": null, "endLine": 491, "endColumn": 9, "fix": "1309"}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 164, "column": 13, "nodeType": "1203", "messageId": "1204", "endLine": 164, "endColumn": 25}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 339, "column": 13, "nodeType": "1200", "endLine": 343, "endColumn": 14}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 597, "column": 11, "nodeType": "1203", "messageId": "1204", "endLine": 597, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 51, "column": 16, "nodeType": "1200", "endLine": 51, "endColumn": 34}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 166, "column": 22, "nodeType": "1203", "messageId": "1204", "endLine": 166, "endColumn": 34}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 505, "column": 13, "nodeType": "1203", "messageId": "1204", "endLine": 505, "endColumn": 24}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 42, "column": 5, "nodeType": "1203", "messageId": "1204", "endLine": 42, "endColumn": 18}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 64, "column": 5, "nodeType": "1203", "messageId": "1204", "endLine": 64, "endColumn": 18}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 88, "column": 5, "nodeType": "1203", "messageId": "1204", "endLine": 88, "endColumn": 18}, {"ruleId": "1201", "severity": 1, "message": "1202", "line": 181, "column": 5, "nodeType": "1203", "messageId": "1204", "endLine": 181, "endColumn": 18}, {"ruleId": "1205", "severity": 2, "message": "1310", "line": 10, "column": 1, "nodeType": null, "endLine": 10, "endColumn": 13, "fix": "1311"}, {"ruleId": "1205", "severity": 2, "message": "1312", "line": 32, "column": 65, "nodeType": null, "endLine": 32, "endColumn": 65, "fix": "1313"}, {"ruleId": "1205", "severity": 2, "message": "1314", "line": 33, "column": 76, "nodeType": null, "endLine": 33, "endColumn": 82, "fix": "1315"}, {"ruleId": "1205", "severity": 2, "message": "1316", "line": 34, "column": 40, "nodeType": null, "endLine": 34, "endColumn": 93, "fix": "1317"}, {"ruleId": "1205", "severity": 2, "message": "1312", "line": 35, "column": 74, "nodeType": null, "endLine": 35, "endColumn": 74, "fix": "1318"}, {"ruleId": "1205", "severity": 2, "message": "1319", "line": 36, "column": 43, "nodeType": null, "endLine": 36, "endColumn": 105, "fix": "1320"}, {"ruleId": "1205", "severity": 2, "message": "1321", "line": 47, "column": 1, "nodeType": null, "endLine": 47, "endColumn": 17, "fix": "1322"}, {"ruleId": "1205", "severity": 2, "message": "1323", "line": 49, "column": 25, "nodeType": null, "endLine": 52, "endColumn": 19, "fix": "1324"}, {"ruleId": "1205", "severity": 2, "message": "1325", "line": 55, "column": 1, "nodeType": null, "endLine": 55, "endColumn": 19, "fix": "1326"}, {"ruleId": "1205", "severity": 2, "message": "1327", "line": 56, "column": 25, "nodeType": null, "endLine": 59, "endColumn": 19, "fix": "1328"}, {"ruleId": "1205", "severity": 2, "message": "1325", "line": 62, "column": 1, "nodeType": null, "endLine": 62, "endColumn": 19, "fix": "1329"}, {"ruleId": "1205", "severity": 2, "message": "1330", "line": 63, "column": 25, "nodeType": null, "endLine": 68, "endColumn": 18, "fix": "1331"}, {"ruleId": "1205", "severity": 2, "message": "1325", "line": 69, "column": 1, "nodeType": null, "endLine": 69, "endColumn": 19, "fix": "1332"}, {"ruleId": "1205", "severity": 2, "message": "1333", "line": 70, "column": 25, "nodeType": null, "endLine": 73, "endColumn": 19, "fix": "1334"}, {"ruleId": "1205", "severity": 2, "message": "1335", "line": 106, "column": 75, "nodeType": null, "endLine": 106, "endColumn": 75, "fix": "1336"}, {"ruleId": "1205", "severity": 2, "message": "1337", "line": 116, "column": 63, "nodeType": null, "endLine": 117, "endColumn": 33, "fix": "1338"}, {"ruleId": "1205", "severity": 2, "message": "1339", "line": 126, "column": 80, "nodeType": null, "endLine": 126, "endColumn": 91, "fix": "1340"}, {"ruleId": "1205", "severity": 2, "message": "1341", "line": 127, "column": 17, "nodeType": null, "endLine": 127, "endColumn": 63, "fix": "1342"}, {"ruleId": "1205", "severity": 2, "message": "1343", "line": 136, "column": 72, "nodeType": null, "endLine": 136, "endColumn": 72, "fix": "1344"}, {"ruleId": "1205", "severity": 2, "message": "1345", "line": 173, "column": 17, "nodeType": null, "endLine": 173, "endColumn": 17, "fix": "1346"}, {"ruleId": "1205", "severity": 2, "message": "1347", "line": 187, "column": 1, "nodeType": null, "endLine": 187, "endColumn": 7, "fix": "1348"}, {"ruleId": "1205", "severity": 2, "message": "1349", "line": 193, "column": 1, "nodeType": null, "endLine": 193, "endColumn": 5, "fix": "1350"}, {"ruleId": "1205", "severity": 2, "message": "1351", "line": 197, "column": 39, "nodeType": null, "endLine": 197, "endColumn": 89, "fix": "1352"}, {"ruleId": "1205", "severity": 2, "message": "1349", "line": 206, "column": 1, "nodeType": null, "endLine": 206, "endColumn": 5, "fix": "1353"}, {"ruleId": "1205", "severity": 2, "message": "1354", "line": 210, "column": 39, "nodeType": null, "endLine": 210, "endColumn": 87, "fix": "1355"}, {"ruleId": "1205", "severity": 2, "message": "1349", "line": 219, "column": 1, "nodeType": null, "endLine": 219, "endColumn": 5, "fix": "1356"}, {"ruleId": "1205", "severity": 2, "message": "1357", "line": 223, "column": 39, "nodeType": null, "endLine": 223, "endColumn": 83, "fix": "1358"}, {"ruleId": "1205", "severity": 2, "message": "1349", "line": 232, "column": 1, "nodeType": null, "endLine": 232, "endColumn": 5, "fix": "1359"}, "vue/no-v-html", "'v-html' directive can lead to XSS attack.", "VAttribute", "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", "prettier/prettier", "Delete `··`", {"range": "1360", "text": "1361"}, "Replace `····const·noscript·=·document.createElement('noscript');` with `··const·noscript·=·document.createElement('noscript')`", {"range": "1362", "text": "1363"}, {"range": "1364", "text": "1361"}, "Delete `;`", {"range": "1365", "text": "1361"}, "Replace `····document.body.prepend(noscript);` with `··document.body.prepend(noscript)`", {"range": "1366", "text": "1367"}, "Insert `⏎`", {"range": "1368", "text": "1369"}, "vue/no-lone-template", "`<template>` require directive.", "VStartTag", "requireDirective", "import/order", "`@/helpers/navigationState` import should occur before import of `~/plugins/tidio-chat`", "ImportDeclaration", {"range": "1370", "text": "1371"}, "Replace `·:dialog=\"showSetPasswordDialog\"·:hide-close-button=\"isValid\"·max-width=\"388\"` with `⏎····:dialog=\"showSetPasswordDialog\"⏎····:hide-close-button=\"isValid\"⏎····max-width=\"388\"⏎···`", {"range": "1372", "text": "1373"}, "Insert `⏎··`", {"range": "1374", "text": "1375"}, "Replace `·v-model=\"password\"·type=\"password\"·:placeholder=\"$t('password')\"·name=\"password\"` with `⏎··············v-model=\"password\"⏎··············type=\"password\"⏎··············:placeholder=\"$t('password')\"⏎··············name=\"password\"⏎·············`", {"range": "1376", "text": "1377"}, "Insert `⏎············`", {"range": "1378", "text": "1379"}, "Replace `·:src=\"require('~/assets/images/lock-icon.svg')\"·width=\"14\"·height=\"21\"` with `⏎····················:src=\"require('~/assets/images/lock-icon.svg')\"⏎····················width=\"14\"⏎····················height=\"21\"⏎··················`", {"range": "1380", "text": "1381"}, "Replace `·v-model=\"passwordRepeat\"·type=\"password\"` with `⏎··············v-model=\"passwordRepeat\"⏎··············type=\"password\"⏎·············`", {"range": "1382", "text": "1383"}, "Replace `·hide-details·autocomplete=\"new-password\"` with `⏎··············hide-details⏎··············autocomplete=\"new-password\"⏎············`", {"range": "1384", "text": "1385"}, {"range": "1386", "text": "1381"}, "Replace `·:xlink:href=\"`${require('~/assets/images/icon-sprite.svg')}#attention`\"` with `⏎······················:xlink:href=\"`${require('~/assets/images/icon-sprite.svg')}#attention`\"⏎····················`", {"range": "1387", "text": "1388"}, "Replace `·color=\"orange\"·class=\"font-weight-medium\"·x-large·type=\"submit\"` with `⏎··············color=\"orange\"⏎··············class=\"font-weight-medium\"⏎··············x-large⏎··············type=\"submit\"⏎············`", {"range": "1389", "text": "1390"}, "`@/helpers/navigationState` import should occur before import of `~/components/LDialog`", {"range": "1391", "text": "1392"}, "Replace `this.$router,·true` with `⏎··············this.$router,⏎··············true⏎············`", {"range": "1393", "text": "1394"}, "vue/no-template-shadow", "Variable 'handle' is already declared in the upper scope.", "Identifier", "no-unused-vars", "'_' is assigned a value but never used.", "unusedVar", "unicorn/prefer-exponentiation-operator", ["1395"], "prefer-const", "'path' is never reassigned. Use 'const' instead.", "useConst", {"range": "1396", "text": "1397"}, "'query' is never reassigned. Use 'const' instead.", {"range": "1398", "text": "1399"}, {"range": "1400", "text": "1361"}, {"range": "1401", "text": "1361"}, {"range": "1402", "text": "1361"}, {"range": "1403", "text": "1361"}, {"range": "1404", "text": "1361"}, {"range": "1405", "text": "1361"}, {"range": "1406", "text": "1361"}, {"range": "1407", "text": "1361"}, {"range": "1408", "text": "1361"}, {"range": "1409", "text": "1361"}, {"range": "1410", "text": "1361"}, {"range": "1411", "text": "1361"}, "Replace `FF);` with `ff)`", {"range": "1412", "text": "1413"}, {"range": "1414", "text": "1361"}, {"range": "1415", "text": "1361"}, {"range": "1416", "text": "1361"}, {"range": "1417", "text": "1361"}, {"range": "1418", "text": "1361"}, {"range": "1419", "text": "1413"}, {"range": "1420", "text": "1361"}, {"range": "1421", "text": "1361"}, {"range": "1422", "text": "1361"}, {"range": "1423", "text": "1361"}, {"range": "1424", "text": "1361"}, {"range": "1425", "text": "1361"}, {"range": "1426", "text": "1361"}, {"range": "1427", "text": "1361"}, {"range": "1428", "text": "1361"}, "Delete `·`", {"range": "1429", "text": "1361"}, "Insert `,`", {"range": "1430", "text": "1431"}, "Replace `func` with `(func)`", {"range": "1432", "text": "1433"}, {"range": "1434", "text": "1361"}, {"range": "1435", "text": "1431"}, {"range": "1436", "text": "1361"}, {"range": "1437", "text": "1361"}, "Replace `·this.$i18n.locale·===·'en'` with `this.$i18n.locale·===·'en'⏎·········`", {"range": "1438", "text": "1439"}, "Replace `·2,·14,·17,·0,·19,·10,·34,·7,·50,·23,·43,·38,·50,·36,·34,·46,·19,` with `⏎········2,⏎········14,⏎········17,⏎········0,⏎········19,⏎········10,⏎········34,⏎········7,⏎········50,⏎········23,⏎········43,⏎········38,⏎········50,⏎········36,⏎········34,⏎········46,⏎········19,⏎·······`", {"range": "1440", "text": "1441"}, "vue/attributes-order", "Attribute \":length\" should go before \"@input\".", {"range": "1442", "text": "1443"}, {"range": "1444", "text": "1445"}, {"range": "1446", "text": "1369"}, "Delete `············`", {"range": "1447", "text": "1361"}, "Insert `·/`", {"range": "1448", "text": "1449"}, "Replace `·}}<br` with `⏎··················}}<br·/`", {"range": "1450", "text": "1451"}, "Replace `·{{·sentryStatus.dsn·?·'Configured'·:·'Not·Set'·}}<br` with `⏎··················{{·sentryStatus.dsn·?·'Configured'·:·'Not·Set'·}}<br·/`", {"range": "1452", "text": "1453"}, {"range": "1454", "text": "1449"}, "Replace `·{{·sentryStatus.isEnabled·?·'Enabled'·:·'Disabled·(Dev·Mode)'` with `⏎··················{{⏎····················sentryStatus.isEnabled·?·'Enabled'·:·'Disabled·(Dev·Mode)'⏎·················`", {"range": "1455", "text": "1456"}, "Delete `················`", {"range": "1457", "text": "1361"}, "Replace `⏎····················color=\"info\"⏎····················@click=\"testMessage\"⏎··················` with `·color=\"info\"·@click=\"testMessage\"`", {"range": "1458", "text": "1459"}, "Delete `··················`", {"range": "1460", "text": "1361"}, "Replace `⏎····················color=\"warning\"⏎····················@click=\"testWarning\"⏎··················` with `·color=\"warning\"·@click=\"testWarning\"`", {"range": "1461", "text": "1462"}, {"range": "1463", "text": "1361"}, "Replace `⏎····················color=\"error\"⏎····················@click=\"testError\"⏎··················>⏎····················Test·Error⏎·················` with `·color=\"error\"·@click=\"testError\">·Test·Error`", {"range": "1464", "text": "1465"}, {"range": "1466", "text": "1361"}, "Replace `⏎····················color=\"purple\"⏎····················@click=\"testException\"⏎··················` with `·color=\"purple\"·@click=\"testException\"`", {"range": "1467", "text": "1468"}, "Insert `⏎·················`", {"range": "1469", "text": "1470"}, "Replace `⏎················Only·staging·and` with `·Only·staging·and⏎···············`", {"range": "1471", "text": "1472"}, "Delete `·debugging.`", {"range": "1473", "text": "1361"}, "Replace `Use·<code>window.sentryTest</code>·helpers·for` with `debugging.·Use·<code>window.sentryTest</code>·helpers·for⏎···············`", {"range": "1474", "text": "1475"}, "Insert `⏎···············`", {"range": "1476", "text": "1477"}, "Insert `⏎·········`", {"range": "1478", "text": "1479"}, "Delete `······`", {"range": "1480", "text": "1361"}, "Delete `····`", {"range": "1481", "text": "1361"}, "Replace `'Test·info·message·from·langu-frontend-7b',·'info'` with `⏎············'Test·info·message·from·langu-frontend-7b',⏎············'info'⏎··········`", {"range": "1482", "text": "1483"}, {"range": "1484", "text": "1361"}, "Replace `'Test·warning·from·langu-frontend-7b',·'warning'` with `⏎············'Test·warning·from·langu-frontend-7b',⏎············'warning'⏎··········`", {"range": "1485", "text": "1486"}, {"range": "1487", "text": "1361"}, "Replace `'Test·error·from·langu-frontend-7b',·'error'` with `⏎············'Test·error·from·langu-frontend-7b',⏎············'error'⏎··········`", {"range": "1488", "text": "1489"}, {"range": "1490", "text": "1361"}, [38, 40], "", [81, 137], "  const noscript = document.createElement('noscript')", [138, 140], [322, 323], [324, 360], "  document.body.prepend(noscript)", [362, 362], "\n", [5846, 6128], "import { saveNavigationState } from '@/helpers/navigationState'\nimport { setTidioProperties, setVisitorData } from '~/plugins/tidio-chat'\nimport TextInput from '~/components/form/TextInput'\nimport EmailIcon from '~/components/images/EmailIcon'\n// Import the navigation state helper\n", [22, 99], "\n    :dialog=\"showSetPasswordDialog\"\n    :hide-close-button=\"isValid\"\n    max-width=\"388\"\n   ", [157, 157], "\n  ", [570, 651], "\n              v-model=\"password\"\n              type=\"password\"\n              :placeholder=\"$t('password')\"\n              name=\"password\"\n             ", [706, 706], "\n            ", [811, 882], "\n                    :src=\"require('~/assets/images/lock-icon.svg')\"\n                    width=\"14\"\n                    height=\"21\"\n                  ", [1023, 1064], "\n              v-model=\"passwordRepeat\"\n              type=\"password\"\n             ", [1138, 1179], "\n              hide-details\n              autocomplete=\"new-password\"\n            ", [1284, 1355], [1754, 1826], "\n                      :xlink:href=\"`${require('~/assets/images/icon-sprite.svg')}#attention`\"\n                    ", [2051, 2115], "\n              color=\"orange\"\n              class=\"font-weight-medium\"\n              x-large\n              type=\"submit\"\n            ", [2770, 2930], "import { applyNavigationState } from '@/helpers/navigationState'\nimport LDialog from '~/components/LDialog'\nimport TextInput from '~/components/form/TextInput'\n", [4568, 4586], "\n              this.$router,\n              true\n            ", "prefer-exponentiation-operator", [2309, 2330], "const path = state.path", [2333, 2347], "const query = {}", [118, 119], [137, 138], [244, 245], [265, 266], [815, 816], [924, 925], [1109, 1110], [1146, 1147], [1274, 1275], [1371, 1372], [1425, 1426], [1555, 1556], [1654, 1658], "ff)", [1686, 1687], [1759, 1760], [1796, 1797], [1899, 1900], [2021, 2022], [2123, 2127], [2206, 2207], [2318, 2319], [2410, 2411], [2499, 2500], [2628, 2629], [2677, 2678], [2761, 2762], [2827, 2828], [2864, 2865], [83, 84], [277, 277], ",", [287, 291], "(func)", [578, 579], [749, 749], [781, 782], [1057, 1058], [662, 689], "this.$i18n.locale === 'en'\n         ", [313, 378], "\n        2,\n        14,\n        17,\n        0,\n        19,\n        10,\n        34,\n        7,\n        50,\n        23,\n        43,\n        38,\n        50,\n        36,\n        34,\n        46,\n        19,\n       ", [69, 110], ":length=\"length\"\n    @input=\"updateValue\"", [3287, 3370], ":length=\"currentSelectedDuration\"\n                  @input=\"updatePriceTrialLesson\"", [13189, 13189], [292, 304], [1058, 1058], " /", [1135, 1141], "\n                  }}<br /", [1182, 1235], "\n                  {{ sentryStatus.dsn ? 'Configured' : 'Not Set' }}<br /", [1310, 1310], [1354, 1416], "\n                  {{\n                    sentryStatus.isEnabled ? 'Enabled' : 'Disabled (Dev Mode)'\n                 ", [1743, 1759], [1837, 1930], " color=\"info\" @click=\"testMessage\"", [1992, 2010], [2035, 2131], " color=\"warning\" @click=\"testWarning\"", [2193, 2211], [2236, 2378], " color=\"error\" @click=\"testError\"> Test Error", [2388, 2406], [2431, 2528], " color=\"purple\" @click=\"testException\"", [3863, 3863], "\n                 ", [4179, 4212], " Only staging and\n               ", [4580, 4591], [4608, 4654], "debugging. Use <code>window.sentryTest</code> helpers for\n               ", [4980, 4980], "\n               ", [5761, 5761], "\n         ", [6208, 6214], [6364, 6368], [6467, 6517], "\n            'Test info message from langu-frontend-7b',\n            'info'\n          ", [6817, 6821], [6920, 6968], "\n            'Test warning from langu-frontend-7b',\n            'warning'\n          ", [7256, 7260], [7357, 7401], "\n            'Test error from langu-frontend-7b',\n            'error'\n          ", [7705, 7709]]