# Discovered Components

This is an auto-generated list of components discovered by [nuxt/components](https://github.com/nuxt/components).

You can directly use them in pages and other components without the need to import them.

**Tip:** If a component is conditionally rendered with `v-if` and is big, it is better to use `Lazy` or `lazy-` prefix to lazy load.

- `<BuzzDialog>` | `<buzz-dialog>` (components/BuzzDialog.vue)
- `<Calendar>` | `<calendar>` (components/Calendar.vue)
- `<CalendarDate>` | `<calendar-date>` (components/CalendarDate.vue)
- `<CheckEmailDialog>` | `<check-email-dialog>` (components/CheckEmailDialog.vue)
- `<ConfirmDialog>` | `<confirm-dialog>` (components/ConfirmDialog.vue)
- `<CookiePopup>` | `<cookie-popup>` (components/CookiePopup.vue)
- `<Footer>` | `<footer>` (components/Footer.vue)
- `<FreeSlots>` | `<free-slots>` (components/FreeSlots.vue)
- `<GoogleSignInButton>` | `<google-sign-in-button>` (components/GoogleSignInButton.vue)
- `<Header>` | `<header>` (components/Header.vue)
- `<LAvatar>` | `<l-avatar>` (components/LAvatar.vue)
- `<LChip>` | `<l-chip>` (components/LChip.vue)
- `<LDialog>` | `<l-dialog>` (components/LDialog.vue)
- `<LessonTimeNotice>` | `<lesson-time-notice>` (components/LessonTimeNotice.vue)
- `<LExpansionPanels>` | `<l-expansion-panels>` (components/LExpansionPanels.vue)
- `<Loader>` | `<loader>` (components/Loader.vue)
- `<LoadMoreBtn>` | `<load-more-btn>` (components/LoadMoreBtn.vue)
- `<LoginSidebar>` | `<login-sidebar>` (components/LoginSidebar.vue)
- `<MessageDialog>` | `<message-dialog>` (components/MessageDialog.vue)
- `<Pagination>` | `<pagination>` (components/Pagination.vue)
- `<SetPasswordDialog>` | `<set-password-dialog>` (components/SetPasswordDialog.vue)
- `<Snackbar>` | `<snackbar>` (components/Snackbar.vue)
- `<StarRating>` | `<star-rating>` (components/StarRating.vue)
- `<Steps>` | `<steps>` (components/Steps.vue)
- `<SummaryLessonDialog>` | `<summary-lesson-dialog>` (components/SummaryLessonDialog.vue)
- `<TeacherCard>` | `<teacher-card>` (components/TeacherCard.vue)
- `<TeacherFilter>` | `<teacher-filter>` (components/TeacherFilter.vue)
- `<TeacherFilterNew>` | `<teacher-filter-new>` (components/TeacherFilterNew.vue)
- `<TimePicker>` | `<time-picker>` (components/TimePicker.vue)
- `<TimePickerItem>` | `<time-picker-item>` (components/TimePickerItem.vue)
- `<UserStatus>` | `<user-status>` (components/UserStatus.vue)
- `<Youtube>` | `<youtube>` (components/Youtube.vue)
- `<BusinessPage>` | `<business-page>` (components/business-page/BusinessPage.vue)
- `<ClassroomAudioItem>` | `<classroom-audio-item>` (components/classroom/AudioItem.vue)
- `<ClassroomContainer>` | `<classroom-container>` (components/classroom/ClassroomContainer.vue)
- `<ClassroomContainerHeader>` | `<classroom-container-header>` (components/classroom/ClassroomContainerHeader.vue)
- `<ClassroomDropFileArea>` | `<classroom-drop-file-area>` (components/classroom/DropFileArea.vue)
- `<ClassroomImageItem>` | `<classroom-image-item>` (components/classroom/ImageItem.vue)
- `<ClassroomKonva>` | `<classroom-konva>` (components/classroom/Konva.vue)
- `<ClassroomLibrary>` | `<classroom-library>` (components/classroom/Library.vue)
- `<ClassroomOtherCursor>` | `<classroom-other-cursor>` (components/classroom/OtherCursor.vue)
- `<ClassroomPdfItem>` | `<classroom-pdf-item>` (components/classroom/PdfItem.vue)
- `<ClassroomTinymceVue>` | `<classroom-tinymce-vue>` (components/classroom/TinymceVue.vue)
- `<ClassroomToolbar>` | `<classroom-toolbar>` (components/classroom/Toolbar.vue)
- `<ClassroomVideoInput>` | `<classroom-video-input>` (components/classroom/VideoInput.vue)
- `<ClassroomVideoItem>` | `<classroom-video-item>` (components/classroom/VideoItem.vue)
- `<ClassroomViewport>` | `<classroom-viewport>` (components/classroom/Viewport.vue)
- `<FormEditor>` | `<form-editor>` (components/form/Editor.vue)
- `<FormRate>` | `<form-rate>` (components/form/FormRate.vue)
- `<FormSearchInput>` | `<form-search-input>` (components/form/SearchInput.vue)
- `<FormSelectInput>` | `<form-select-input>` (components/form/SelectInput.vue)
- `<FormSelectInputNew>` | `<form-select-input-new>` (components/form/SelectInputNew.vue)
- `<FormTextInput>` | `<form-text-input>` (components/form/TextInput.vue)
- `<HomepageAboutSection>` | `<homepage-about-section>` (components/homepage/AboutSection.vue)
- `<HomepageFaqSection>` | `<homepage-faq-section>` (components/homepage/FaqSection.vue)
- `<HomepageHowWorksSection>` | `<homepage-how-works-section>` (components/homepage/HowWorksSection.vue)
- `<HomepageIntroSection>` | `<homepage-intro-section>` (components/homepage/IntroSection.vue)
- `<HomepageLanguagesSection>` | `<homepage-languages-section>` (components/homepage/LanguagesSection.vue)
- `<HomepageReviewSection>` | `<homepage-review-section>` (components/homepage/ReviewSection.vue)
- `<HomepageSelectLanguage>` | `<homepage-select-language>` (components/homepage/SelectLanguage.vue)
- `<HomepageStatSection>` | `<homepage-stat-section>` (components/homepage/StatSection.vue)
- `<HomepageThinkingSection>` | `<homepage-thinking-section>` (components/homepage/ThinkingSection.vue)
- `<HomepageTutorsSection>` | `<homepage-tutors-section>` (components/homepage/TutorsSection.vue)
- `<ImagesAlarmGradientIcon>` | `<images-alarm-gradient-icon>` (components/images/AlarmGradientIcon.vue)
- `<ImagesBusinessPageIntroImage>` | `<images-business-page-intro-image>` (components/images/BusinessPageIntroImage.vue)
- `<ImagesBusinessPageIntroMobileImage>` | `<images-business-page-intro-mobile-image>` (components/images/BusinessPageIntroMobileImage.vue)
- `<ImagesCareerGradientIcon>` | `<images-career-gradient-icon>` (components/images/CareerGradientIcon.vue)
- `<ImagesCheckedGradientIcon>` | `<images-checked-gradient-icon>` (components/images/CheckedGradientIcon.vue)
- `<ImagesEducationGradientIcon>` | `<images-education-gradient-icon>` (components/images/EducationGradientIcon.vue)
- `<ImagesEmailIcon>` | `<images-email-icon>` (components/images/EmailIcon.vue)
- `<ImagesEnFlagIcon>` | `<images-en-flag-icon>` (components/images/EnFlagIcon.vue)
- `<ImagesEsFlagIcon>` | `<images-es-flag-icon>` (components/images/EsFlagIcon.vue)
- `<ImagesGoogleIcon>` | `<images-google-icon>` (components/images/GoogleIcon.vue)
- `<ImagesHomePageIntroImage>` | `<images-home-page-intro-image>` (components/images/HomePageIntroImage.vue)
- `<ImagesLifeGradientIcon>` | `<images-life-gradient-icon>` (components/images/LifeGradientIcon.vue)
- `<ImagesMoonGradientIcon>` | `<images-moon-gradient-icon>` (components/images/MoonGradientIcon.vue)
- `<ImagesPlFlagIcon>` | `<images-pl-flag-icon>` (components/images/PlFlagIcon.vue)
- `<ImagesSearchIcon>` | `<images-search-icon>` (components/images/SearchIcon.vue)
- `<ImagesSunGradientIcon>` | `<images-sun-gradient-icon>` (components/images/SunGradientIcon.vue)
- `<ImagesSunsetGradientIcon>` | `<images-sunset-gradient-icon>` (components/images/SunsetGradientIcon.vue)
- `<LandingPageTeachersSlider>` | `<landing-page-teachers-slider>` (components/landing-page/TeachersSlider.vue)
- `<LandingPageTestimonialsSlider>` | `<landing-page-testimonials-slider>` (components/landing-page/TestimonialsSlider.vue)
- `<TeacherListing>` | `<teacher-listing>` (components/teacher-listing/TeacherListing.vue)
- `<TeacherListingBanner>` | `<teacher-listing-banner>` (components/teacher-listing/TeacherListingBanner.vue)
- `<TeacherListingHeader>` | `<teacher-listing-header>` (components/teacher-listing/TeacherListingHeader.vue)
- `<TeacherProfileCourseItem>` | `<teacher-profile-course-item>` (components/teacher-profile/CourseItem.vue)
- `<TeacherProfileFeedbackTags>` | `<teacher-profile-feedback-tags>` (components/teacher-profile/FeedbackTags.vue)
- `<TeacherProfileFindMoreTeachersButton>` | `<teacher-profile-find-more-teachers-button>` (components/teacher-profile/FindMoreTeachersButton.vue)
- `<TeacherProfilePricePerLesson>` | `<teacher-profile-price-per-lesson>` (components/teacher-profile/PricePerLesson.vue)
- `<TeacherProfileSidebar>` | `<teacher-profile-sidebar>` (components/teacher-profile/TeacherProfileSidebar.vue)
- `<TeacherProfileTimePickerDialog>` | `<teacher-profile-time-picker-dialog>` (components/teacher-profile/TimePickerDialog.vue)
- `<UserLessonsLessonEvaluationDialog>` | `<user-lessons-lesson-evaluation-dialog>` (components/user-lessons/LessonEvaluationDialog.vue)
- `<UserLessonsLessonItem>` | `<user-lessons-lesson-item>` (components/user-lessons/LessonItem.vue)
- `<UserLessonsPage>` | `<user-lessons-page>` (components/user-lessons/LessonsPage.vue)
- `<UserLessonsPastLesson>` | `<user-lessons-past-lesson>` (components/user-lessons/PastLesson.vue)
- `<UserLessonsTimePickerDialog>` | `<user-lessons-time-picker-dialog>` (components/user-lessons/TimePickerDialog.vue)
- `<UserLessonsUnscheduledLesson>` | `<user-lessons-unscheduled-lesson>` (components/user-lessons/UnscheduledLesson.vue)
- `<UserLessonsUpcomingLesson>` | `<user-lessons-upcoming-lesson>` (components/user-lessons/UpcomingLesson.vue)
- `<UserMessagesConversation>` | `<user-messages-conversation>` (components/user-messages/Conversation.vue)
- `<UserMessagesConversationItem>` | `<user-messages-conversation-item>` (components/user-messages/ConversationItem.vue)
- `<UserMessagesEmptyContent>` | `<user-messages-empty-content>` (components/user-messages/EmptyContent.vue)
- `<UserMessagesPage>` | `<user-messages-page>` (components/user-messages/MessagesPage.vue)
- `<PaymentsCountries>` | `<payments-countries>` (components/payments/countries.js)
- `<PaymentsPaymentDetailsModal>` | `<payments-payment-details-modal>` (components/payments/PaymentDetailsModal.vue)
- `<PaymentsPaymentItem>` | `<payments-payment-item>` (components/payments/PaymentItem.vue)
- `<PaymentsPaymentLesson>` | `<payments-payment-lesson>` (components/payments/PaymentLesson.vue)
- `<PaymentsPaymentPayout>` | `<payments-payment-payout>` (components/payments/PaymentPayout.vue)
- `<PaymentsPage>` | `<payments-page>` (components/payments/PaymentsPage.vue)
- `<PaymentsPayoutItem>` | `<payments-payout-item>` (components/payments/PayoutItem.vue)
- `<PaymentsPayoutModal>` | `<payments-payout-modal>` (components/payments/PayoutModal.vue)
- `<PaymentsSavedAccountsModal>` | `<payments-saved-accounts-modal>` (components/payments/SavedAccountsModal.vue)
- `<PaymentsWiseTransferModal>` | `<payments-wise-transfer-modal>` (components/payments/WiseTransferModal.vue)
- `<UserSettingsAboutMeInfo>` | `<user-settings-about-me-info>` (components/user-settings/AboutMeInfo.vue)
- `<UserSettingsAddQualificationDialog>` | `<user-settings-add-qualification-dialog>` (components/user-settings/AddQualificationDialog.vue)
- `<UserSettingsBackgroundInfo>` | `<user-settings-background-info>` (components/user-settings/BackgroundInfo.vue)
- `<UserSettingsBasicInfo>` | `<user-settings-basic-info>` (components/user-settings/BasicInfo.vue)
- `<UserSettingsCalendarNotificationInfo>` | `<user-settings-calendar-notification-info>` (components/user-settings/CalendarNotificationInfo.vue)
- `<UserSettingsCourseItem>` | `<user-settings-course-item>` (components/user-settings/CourseItem.vue)
- `<UserSettingsCoursesInfo>` | `<user-settings-courses-info>` (components/user-settings/CoursesInfo.vue)
- `<UserSettingsIllustrationDialog>` | `<user-settings-illustration-dialog>` (components/user-settings/IllustrationDialog.vue)
- `<UserSettingsLanguagesInfo>` | `<user-settings-languages-info>` (components/user-settings/LanguagesInfo.vue)
- `<UserSettingsLearningPreferencesInfo>` | `<user-settings-learning-preferences-info>` (components/user-settings/LearningPreferencesInfo.vue)
- `<UserSettingsLessonPrice>` | `<user-settings-lesson-price>` (components/user-settings/LessonPrice.vue)
- `<UserSettingsPerLessonPrice>` | `<user-settings-per-lesson-price>` (components/user-settings/PerLessonPrice.vue)
- `<UserSettingsPricingTableInfo>` | `<user-settings-pricing-table-info>` (components/user-settings/PricingTableInfo.vue)
- `<UserSettingsQualificationSuccessDialog>` | `<user-settings-qualification-success-dialog>` (components/user-settings/QualificationSuccessDialog.vue)
- `<UserSettingsReceiptInfo>` | `<user-settings-receipt-info>` (components/user-settings/ReceiptInfo.vue)
- `<UserSettingsSpecialityDialog>` | `<user-settings-speciality-dialog>` (components/user-settings/SpecialityDialog.vue)
- `<UserSettingsSummaryInfo>` | `<user-settings-summary-info>` (components/user-settings/SummaryInfo.vue)
- `<UserSettingsTeachingPreferencesInfo>` | `<user-settings-teaching-preferences-info>` (components/user-settings/TeachingPreferencesInfo.vue)
- `<UserSettingsTeachingQualificationsInfo>` | `<user-settings-teaching-qualifications-info>` (components/user-settings/TeachingQualificationsInfo.vue)
- `<UserSettingAutocomplete>` | `<user-setting-autocomplete>` (components/user-settings/UserSettingAutocomplete.vue)
- `<UserSettingSelect>` | `<user-setting-select>` (components/user-settings/UserSettingSelect.vue)
- `<UserSettingTemplate>` | `<user-setting-template>` (components/user-settings/UserSettingTemplate.vue)
- `<BusinessPageIconsDotsIcon>` | `<business-page-icons-dots-icon>` (components/business-page/icons/DotsIcon.vue)
- `<ClassroomVideoTokbox>` | `<classroom-video-tokbox>` (components/classroom/video/Tokbox.vue)
- `<ClassroomVideoTwilio>` | `<classroom-video-twilio>` (components/classroom/video/Twilio.vue)
- `<ClassroomVideoActions>` | `<classroom-video-actions>` (components/classroom/video/VideoActions.vue)
- `<ClassroomVideoWhereby>` | `<classroom-video-whereby>` (components/classroom/video/Whereby.vue)
- `<ClassroomVueDraggableResizable>` | `<classroom-vue-draggable-resizable>` (components/classroom/vue-draggable-resizable/VueDraggableResizable.vue)
