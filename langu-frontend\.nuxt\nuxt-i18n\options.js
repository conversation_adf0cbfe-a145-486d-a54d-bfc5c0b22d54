

export const Constants = {
  COMPONENT_OPTIONS_KEY: "nuxtI18n",
  STRATEGIES: {"PREFIX":"prefix","PREFIX_EXCEPT_DEFAULT":"prefix_except_default","PREFIX_AND_DEFAULT":"prefix_and_default","NO_PREFIX":"no_prefix"},
}
export const nuxtOptions = {
  isUniversalMode: true,
  trailingSlash: undefined,
}
export const options = {
  vueI18n: {},
  vueI18nLoader: false,
  locales: [{"code":"pl","iso":"pl-PL","file":"pl.js","domain":"pl.langu.loc:3000"},{"code":"es","iso":"es-ES","file":"es.js","domain":"es.langu.loc:3000"},{"code":"en","file":"en.js","iso":"en-GB","domain":"langu.loc:3000"}],
  defaultLocale: "",
  defaultDirection: "ltr",
  routesNameSeparator: "___",
  defaultLocaleRouteNameSuffix: "default",
  sortRoutes: true,
  strategy: "prefix_except_default",
  lazy: true,
  langDir: "D:\\languworks\\langu-frontend\\lang",
  rootRedirect: null,
  detectBrowserLanguage: {"alwaysRedirect":true,"cookieCrossOrigin":false,"cookieDomain":null,"cookieKey":"i18n_redirected","cookieSecure":false,"fallbackLocale":"","onlyOnNoPrefix":false,"onlyOnRoot":false,"useCookie":true},
  differentDomains: true,
  seo: false,
  baseUrl: "",
  vuex: false,
  parsePages: true,
  pages: {},
  skipSettingLocaleOnNavigate: false,
  beforeLanguageSwitch: () => null,
  onBeforeLanguageSwitch: () => {},
  onLanguageSwitched: () => null,
  normalizedLocales: [{"code":"pl","iso":"pl-PL","file":"pl.js","domain":"pl.langu.loc:3000"},{"code":"es","iso":"es-ES","file":"es.js","domain":"es.langu.loc:3000"},{"code":"en","file":"en.js","iso":"en-GB","domain":"langu.loc:3000"}],
  localeCodes: ["pl","es","en"],
}

export const localeMessages = {
  'pl.js': () => import('../..\\lang\\pl.js' /* webpackChunkName: "lang-pl.js" */),
  'es.js': () => import('../..\\lang\\es.js' /* webpackChunkName: "lang-es.js" */),
  'en.js': () => import('../..\\lang\\en.js' /* webpackChunkName: "lang-en.js" */),
}
