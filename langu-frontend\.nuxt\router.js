import Vue from 'vue'
import Router from 'vue-router'
import { normalizeURL, decode } from 'ufo'
import { interopDefault } from './utils'
import scrollBehavior from './router.scrollBehavior.js'

const _0c148fe7 = () => interopDefault(import('..\\pages\\about-us\\index.vue' /* webpackChunkName: "pages/about-us/index" */))
const _68e91023 = () => interopDefault(import('..\\pages\\blog\\index.vue' /* webpackChunkName: "pages/blog/index" */))
const _de854876 = () => interopDefault(import('..\\pages\\business\\index.vue' /* webpackChunkName: "pages/business/index" */))
const _7840ed07 = () => interopDefault(import('..\\pages\\education\\index.vue' /* webpackChunkName: "pages/education/index" */))
const _70449ece = () => interopDefault(import('..\\pages\\faq\\index.vue' /* webpackChunkName: "pages/faq/index" */))
const _2b2d425e = () => interopDefault(import('..\\pages\\meet\\index.vue' /* webpackChunkName: "pages/meet/index" */))
const _1cb4e673 = () => interopDefault(import('..\\pages\\sentry-test.vue' /* webpackChunkName: "pages/sentry-test" */))
const _418c54b6 = () => interopDefault(import('..\\pages\\teacher-listing\\index.vue' /* webpackChunkName: "pages/teacher-listing/index" */))
const _7654d40c = () => interopDefault(import('..\\pages\\business\\success\\index.vue' /* webpackChunkName: "pages/business/success/index" */))
const _9ca90508 = () => interopDefault(import('..\\pages\\teacher-listing\\welcome\\index.vue' /* webpackChunkName: "pages/teacher-listing/welcome/index" */))
const _703169df = () => interopDefault(import('..\\pages\\user\\lessons\\index.vue' /* webpackChunkName: "pages/user/lessons/index" */))
const _62bc000e = () => interopDefault(import('..\\pages\\user\\messages\\index.vue' /* webpackChunkName: "pages/user/messages/index" */))
const _3f45515a = () => interopDefault(import('..\\pages\\user\\past-lessons\\index.vue' /* webpackChunkName: "pages/user/past-lessons/index" */))
const _3ba1a8e6 = () => interopDefault(import('..\\pages\\user\\payments\\index.vue' /* webpackChunkName: "pages/user/payments/index" */))
const _dd343352 = () => interopDefault(import('..\\pages\\user\\register\\index.vue' /* webpackChunkName: "pages/user/register/index" */))
const _3fe7e8d7 = () => interopDefault(import('..\\pages\\user\\settings\\index.vue' /* webpackChunkName: "pages/user/settings/index" */))
const _bba483d0 = () => interopDefault(import('..\\pages\\user\\unscheduled-lessons\\index.vue' /* webpackChunkName: "pages/user/unscheduled-lessons/index" */))
const _a842636c = () => interopDefault(import('..\\pages\\user\\lessons\\confirmation\\index.vue' /* webpackChunkName: "pages/user/lessons/confirmation/index" */))
const _49bafc19 = () => interopDefault(import('..\\pages\\user\\lessons\\thank-you\\index.vue' /* webpackChunkName: "pages/user/lessons/thank-you/index" */))
const _339f0fe8 = () => interopDefault(import('..\\pages\\user\\payments\\lessons\\index.vue' /* webpackChunkName: "pages/user/payments/lessons/index" */))
const _7a12f37a = () => interopDefault(import('..\\pages\\user\\payments\\payouts\\index.vue' /* webpackChunkName: "pages/user/payments/payouts/index" */))
const _8a0aea4c = () => interopDefault(import('..\\pages\\user\\payments\\lessons\\_page\\index.vue' /* webpackChunkName: "pages/user/payments/lessons/_page/index" */))
const _3828f7ac = () => interopDefault(import('..\\pages\\user\\payments\\payouts\\_page\\index.vue' /* webpackChunkName: "pages/user/payments/payouts/_page/index" */))
const _4d38db77 = () => interopDefault(import('..\\pages\\checkout\\p24-payin\\_id\\index.vue' /* webpackChunkName: "pages/checkout/p24-payin/_id/index" */))
const _d1cfd0ee = () => interopDefault(import('..\\pages\\checkout\\stripe-form\\_id\\index.vue' /* webpackChunkName: "pages/checkout/stripe-form/_id/index" */))
const _ea5f14b2 = () => interopDefault(import('..\\pages\\user\\lessons\\_page\\index.vue' /* webpackChunkName: "pages/user/lessons/_page/index" */))
const _2641b1a5 = () => interopDefault(import('..\\pages\\user\\password\\_token\\index.vue' /* webpackChunkName: "pages/user/password/_token/index" */))
const _490b3dcc = () => interopDefault(import('..\\pages\\user\\past-lessons\\_page\\index.vue' /* webpackChunkName: "pages/user/past-lessons/_page/index" */))
const _26dc580e = () => interopDefault(import('..\\pages\\user\\profile\\_slug\\index.vue' /* webpackChunkName: "pages/user/profile/_slug/index" */))
const _69309f4e = () => interopDefault(import('..\\pages\\user\\unscheduled-lessons\\_page\\index.vue' /* webpackChunkName: "pages/user/unscheduled-lessons/_page/index" */))
const _78c86c0a = () => interopDefault(import('..\\pages\\user\\messages\\_recipientId\\new\\index.vue' /* webpackChunkName: "pages/user/messages/_recipientId/new/index" */))
const _6d77e4dd = () => interopDefault(import('..\\pages\\user\\messages\\_threadId\\view\\index.vue' /* webpackChunkName: "pages/user/messages/_threadId/view/index" */))
const _7156faf0 = () => interopDefault(import('..\\pages\\teacher-listing\\_page\\index.vue' /* webpackChunkName: "pages/teacher-listing/_page/index" */))
const _4adf585d = () => interopDefault(import('..\\pages\\teacher\\_slug\\index.vue' /* webpackChunkName: "pages/teacher/_slug/index" */))
const _3dee0214 = () => interopDefault(import('..\\pages\\lesson\\_id\\classroom\\index.vue' /* webpackChunkName: "pages/lesson/_id/classroom/index" */))
const _4aeb455f = () => interopDefault(import('..\\pages\\teacher-listing\\_page\\_params\\index.vue' /* webpackChunkName: "pages/teacher-listing/_page/_params/index" */))
const _1214f676 = () => interopDefault(import('..\\pages\\index.vue' /* webpackChunkName: "pages/index" */))

const emptyFn = () => {}

Vue.use(Router)

export const routerOptions = {
  mode: 'history',
  base: '/',
  linkActiveClass: 'active',
  linkExactActiveClass: 'nuxt-link-exact-active',
  scrollBehavior,

  routes: [{
    path: "/about-us",
    component: _0c148fe7,
    name: "about-us___pl"
  }, {
    path: "/about-us",
    component: _0c148fe7,
    name: "about-us___es"
  }, {
    path: "/about-us",
    component: _0c148fe7,
    name: "about-us___en"
  }, {
    path: "/blog",
    component: _68e91023,
    name: "blog___pl"
  }, {
    path: "/blog",
    component: _68e91023,
    name: "blog___es"
  }, {
    path: "/blog",
    component: _68e91023,
    name: "blog___en"
  }, {
    path: "/business",
    component: _de854876,
    name: "business___pl"
  }, {
    path: "/business",
    component: _de854876,
    name: "business___es"
  }, {
    path: "/business",
    component: _de854876,
    name: "business___en"
  }, {
    path: "/education",
    component: _7840ed07,
    name: "education___pl"
  }, {
    path: "/education",
    component: _7840ed07,
    name: "education___es"
  }, {
    path: "/education",
    component: _7840ed07,
    name: "education___en"
  }, {
    path: "/faq",
    component: _70449ece,
    name: "faq___pl"
  }, {
    path: "/faq",
    component: _70449ece,
    name: "faq___es"
  }, {
    path: "/faq",
    component: _70449ece,
    name: "faq___en"
  }, {
    path: "/meet",
    component: _2b2d425e,
    name: "meet___pl"
  }, {
    path: "/meet",
    component: _2b2d425e,
    name: "meet___es"
  }, {
    path: "/meet",
    component: _2b2d425e,
    name: "meet___en"
  }, {
    path: "/sentry-test",
    component: _1cb4e673,
    name: "sentry-test___pl"
  }, {
    path: "/sentry-test",
    component: _1cb4e673,
    name: "sentry-test___es"
  }, {
    path: "/sentry-test",
    component: _1cb4e673,
    name: "sentry-test___en"
  }, {
    path: "/teacher-listing",
    component: _418c54b6,
    name: "teacher-listing___pl"
  }, {
    path: "/teacher-listing",
    component: _418c54b6,
    name: "teacher-listing___es"
  }, {
    path: "/teacher-listing",
    component: _418c54b6,
    name: "teacher-listing___en"
  }, {
    path: "/business/success",
    component: _7654d40c,
    name: "business-success___pl"
  }, {
    path: "/business/success",
    component: _7654d40c,
    name: "business-success___es"
  }, {
    path: "/business/success",
    component: _7654d40c,
    name: "business-success___en"
  }, {
    path: "/teacher-listing/welcome",
    component: _9ca90508,
    name: "teacher-listing-welcome___pl"
  }, {
    path: "/teacher-listing/welcome",
    component: _9ca90508,
    name: "teacher-listing-welcome___es"
  }, {
    path: "/teacher-listing/welcome",
    component: _9ca90508,
    name: "teacher-listing-welcome___en"
  }, {
    path: "/user/lessons",
    component: _703169df,
    name: "user-lessons___pl"
  }, {
    path: "/user/lessons",
    component: _703169df,
    name: "user-lessons___es"
  }, {
    path: "/user/lessons",
    component: _703169df,
    name: "user-lessons___en"
  }, {
    path: "/user/messages",
    component: _62bc000e,
    name: "user-messages___pl"
  }, {
    path: "/user/messages",
    component: _62bc000e,
    name: "user-messages___es"
  }, {
    path: "/user/messages",
    component: _62bc000e,
    name: "user-messages___en"
  }, {
    path: "/user/past-lessons",
    component: _3f45515a,
    name: "user-past-lessons___pl"
  }, {
    path: "/user/past-lessons",
    component: _3f45515a,
    name: "user-past-lessons___es"
  }, {
    path: "/user/past-lessons",
    component: _3f45515a,
    name: "user-past-lessons___en"
  }, {
    path: "/user/payments",
    component: _3ba1a8e6,
    name: "user-payments___pl"
  }, {
    path: "/user/payments",
    component: _3ba1a8e6,
    name: "user-payments___es"
  }, {
    path: "/user/payments",
    component: _3ba1a8e6,
    name: "user-payments___en"
  }, {
    path: "/user/register",
    component: _dd343352,
    name: "user-register___pl"
  }, {
    path: "/user/register",
    component: _dd343352,
    name: "user-register___es"
  }, {
    path: "/user/register",
    component: _dd343352,
    name: "user-register___en"
  }, {
    path: "/user/settings",
    component: _3fe7e8d7,
    name: "user-settings___pl"
  }, {
    path: "/user/settings",
    component: _3fe7e8d7,
    name: "user-settings___es"
  }, {
    path: "/user/settings",
    component: _3fe7e8d7,
    name: "user-settings___en"
  }, {
    path: "/user/unscheduled-lessons",
    component: _bba483d0,
    name: "user-unscheduled-lessons___pl"
  }, {
    path: "/user/unscheduled-lessons",
    component: _bba483d0,
    name: "user-unscheduled-lessons___es"
  }, {
    path: "/user/unscheduled-lessons",
    component: _bba483d0,
    name: "user-unscheduled-lessons___en"
  }, {
    path: "/user/lessons/confirmation",
    component: _a842636c,
    name: "user-lessons-confirmation___pl"
  }, {
    path: "/user/lessons/confirmation",
    component: _a842636c,
    name: "user-lessons-confirmation___es"
  }, {
    path: "/user/lessons/confirmation",
    component: _a842636c,
    name: "user-lessons-confirmation___en"
  }, {
    path: "/user/lessons/thank-you",
    component: _49bafc19,
    name: "user-lessons-thank-you___pl"
  }, {
    path: "/user/lessons/thank-you",
    component: _49bafc19,
    name: "user-lessons-thank-you___es"
  }, {
    path: "/user/lessons/thank-you",
    component: _49bafc19,
    name: "user-lessons-thank-you___en"
  }, {
    path: "/user/payments/lessons",
    component: _339f0fe8,
    name: "user-payments-lessons___pl"
  }, {
    path: "/user/payments/lessons",
    component: _339f0fe8,
    name: "user-payments-lessons___es"
  }, {
    path: "/user/payments/lessons",
    component: _339f0fe8,
    name: "user-payments-lessons___en"
  }, {
    path: "/user/payments/payouts",
    component: _7a12f37a,
    name: "user-payments-payouts___pl"
  }, {
    path: "/user/payments/payouts",
    component: _7a12f37a,
    name: "user-payments-payouts___es"
  }, {
    path: "/user/payments/payouts",
    component: _7a12f37a,
    name: "user-payments-payouts___en"
  }, {
    path: "/user/payments/lessons/:page",
    component: _8a0aea4c,
    name: "user-payments-lessons-page___pl"
  }, {
    path: "/user/payments/lessons/:page",
    component: _8a0aea4c,
    name: "user-payments-lessons-page___es"
  }, {
    path: "/user/payments/lessons/:page",
    component: _8a0aea4c,
    name: "user-payments-lessons-page___en"
  }, {
    path: "/user/payments/payouts/:page",
    component: _3828f7ac,
    name: "user-payments-payouts-page___pl"
  }, {
    path: "/user/payments/payouts/:page",
    component: _3828f7ac,
    name: "user-payments-payouts-page___es"
  }, {
    path: "/user/payments/payouts/:page",
    component: _3828f7ac,
    name: "user-payments-payouts-page___en"
  }, {
    path: "/checkout/p24-payin/:id",
    component: _4d38db77,
    name: "checkout-p24-payin-id___pl"
  }, {
    path: "/checkout/p24-payin/:id",
    component: _4d38db77,
    name: "checkout-p24-payin-id___es"
  }, {
    path: "/checkout/p24-payin/:id",
    component: _4d38db77,
    name: "checkout-p24-payin-id___en"
  }, {
    path: "/checkout/stripe-form/:id",
    component: _d1cfd0ee,
    name: "checkout-stripe-form-id___pl"
  }, {
    path: "/checkout/stripe-form/:id",
    component: _d1cfd0ee,
    name: "checkout-stripe-form-id___es"
  }, {
    path: "/checkout/stripe-form/:id",
    component: _d1cfd0ee,
    name: "checkout-stripe-form-id___en"
  }, {
    path: "/user/lessons/:page",
    component: _ea5f14b2,
    name: "user-lessons-page___pl"
  }, {
    path: "/user/lessons/:page",
    component: _ea5f14b2,
    name: "user-lessons-page___es"
  }, {
    path: "/user/lessons/:page",
    component: _ea5f14b2,
    name: "user-lessons-page___en"
  }, {
    path: "/user/password/:token",
    component: _2641b1a5,
    name: "user-password-token___pl"
  }, {
    path: "/user/password/:token",
    component: _2641b1a5,
    name: "user-password-token___es"
  }, {
    path: "/user/password/:token",
    component: _2641b1a5,
    name: "user-password-token___en"
  }, {
    path: "/user/past-lessons/:page",
    component: _490b3dcc,
    name: "user-past-lessons-page___pl"
  }, {
    path: "/user/past-lessons/:page",
    component: _490b3dcc,
    name: "user-past-lessons-page___es"
  }, {
    path: "/user/past-lessons/:page",
    component: _490b3dcc,
    name: "user-past-lessons-page___en"
  }, {
    path: "/user/profile/:slug",
    component: _26dc580e,
    name: "user-profile-slug___pl"
  }, {
    path: "/user/profile/:slug",
    component: _26dc580e,
    name: "user-profile-slug___es"
  }, {
    path: "/user/profile/:slug",
    component: _26dc580e,
    name: "user-profile-slug___en"
  }, {
    path: "/user/unscheduled-lessons/:page",
    component: _69309f4e,
    name: "user-unscheduled-lessons-page___pl"
  }, {
    path: "/user/unscheduled-lessons/:page",
    component: _69309f4e,
    name: "user-unscheduled-lessons-page___es"
  }, {
    path: "/user/unscheduled-lessons/:page",
    component: _69309f4e,
    name: "user-unscheduled-lessons-page___en"
  }, {
    path: "/user/messages/:recipientId/new",
    component: _78c86c0a,
    name: "user-messages-recipientId-new___pl"
  }, {
    path: "/user/messages/:recipientId/new",
    component: _78c86c0a,
    name: "user-messages-recipientId-new___es"
  }, {
    path: "/user/messages/:recipientId/new",
    component: _78c86c0a,
    name: "user-messages-recipientId-new___en"
  }, {
    path: "/user/messages/:threadId/view",
    component: _6d77e4dd,
    name: "user-messages-threadId-view___pl"
  }, {
    path: "/user/messages/:threadId/view",
    component: _6d77e4dd,
    name: "user-messages-threadId-view___es"
  }, {
    path: "/user/messages/:threadId/view",
    component: _6d77e4dd,
    name: "user-messages-threadId-view___en"
  }, {
    path: "/teacher-listing/:page",
    component: _7156faf0,
    name: "teacher-listing-page___pl"
  }, {
    path: "/teacher-listing/:page",
    component: _7156faf0,
    name: "teacher-listing-page___es"
  }, {
    path: "/teacher-listing/:page",
    component: _7156faf0,
    name: "teacher-listing-page___en"
  }, {
    path: "/teacher/:slug",
    component: _4adf585d,
    name: "teacher-slug___pl"
  }, {
    path: "/teacher/:slug",
    component: _4adf585d,
    name: "teacher-slug___es"
  }, {
    path: "/teacher/:slug",
    component: _4adf585d,
    name: "teacher-slug___en"
  }, {
    path: "/lesson/:id?/classroom",
    component: _3dee0214,
    name: "lesson-id-classroom___pl"
  }, {
    path: "/lesson/:id?/classroom",
    component: _3dee0214,
    name: "lesson-id-classroom___es"
  }, {
    path: "/lesson/:id?/classroom",
    component: _3dee0214,
    name: "lesson-id-classroom___en"
  }, {
    path: "/teacher-listing/:page?/:params",
    component: _4aeb455f,
    name: "teacher-listing-page-params___pl"
  }, {
    path: "/teacher-listing/:page?/:params",
    component: _4aeb455f,
    name: "teacher-listing-page-params___es"
  }, {
    path: "/teacher-listing/:page?/:params",
    component: _4aeb455f,
    name: "teacher-listing-page-params___en"
  }, {
    path: "/",
    component: _1214f676,
    name: "index___pl"
  }, {
    path: "/",
    component: _1214f676,
    name: "index___es"
  }, {
    path: "/",
    component: _1214f676,
    name: "index___en"
  }],

  fallback: false
}

export function createRouter (ssrContext, config) {
  const base = (config._app && config._app.basePath) || routerOptions.base
  const router = new Router({ ...routerOptions, base  })

  // TODO: remove in Nuxt 3
  const originalPush = router.push
  router.push = function push (location, onComplete = emptyFn, onAbort) {
    return originalPush.call(this, location, onComplete, onAbort)
  }

  const resolve = router.resolve.bind(router)
  router.resolve = (to, current, append) => {
    if (typeof to === 'string') {
      to = normalizeURL(to)
    }
    return resolve(to, current, append)
  }

  return router
}
