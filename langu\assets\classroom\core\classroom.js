import '../css/style.css';
import '../css/cursor.css';
import "regenerator-runtime/runtime.js";
import Vue from 'vue';
import Vuex from 'vuex';
import VueSocketIO from 'vue-socket.io';
import * as Sentry from '@sentry/vue'
import VueKonva from 'vue-konva'
import VuePlyr from 'vue-plyr'
import {TOOL_ERASER, TOOL_PEN, TOOL_POINTER, TOOL_SQUARE, TOOL_CIRCLE, TOOL_TRIANGLE, TOOL_LINE} from "./imports/tools";
// import {STUDENT_CURSOR_POINTER, TEACHER_CURSOR_POINTER} from "./imports/cursors";
import drag from 'v-drag';
import axios from 'axios';
import {
    ADD_COMPONENT,
    FORCE_PUSH_COMPONENTS,
    SEND_SOCKET_UPDATE,
    UPDATE_COMPONENT_DATA
} from "../../vue/import/mutation-types";
import set from "lodash/set";
import FileManager from '../vue/mixins/FileManager'
// import VueHtml2Canvas from 'vue-html2canvas';
// import {UpdateBuilder} from "../../js/classroom/11_transmitter";

import VueDraggableResizable from '../vue/vue-draggable-resizable/vue-draggable-resizable'

// optionally import default styles
import '../vue/vue-draggable-resizable/vue-draggable-resizable.css'

Vue.component('vue-draggable-resizable', VueDraggableResizable)

Sentry.init({
  Vue: Vue,
  dsn: 'https://<EMAIL>/52',
})

Vue.use(Vuex);
Vue.use(VueKonva);
Vue.use(new VueSocketIO({
    debug: true,
    connection: `${location.host}/`,
    // @TODO: Integrate vuex on Vue side
    // vuex: {
    //     store,
    //     actionPrefix: 'SOCKET_',
    //     mutationPrefix: 'SOCKET_'
    // },
    options: {
        path: window.ws.endpoint,
        secure: window.ws.secure,
        reconnectionAttempts: 5,
    }
}));
Vue.use(VuePlyr, {
  plyr: {
    fullscreen: { enabled: false }
  },
  emit: ['ended']
})
Vue.use(drag);

Vue.prototype.$axios = axios

// Vue.use(VueHtml2Canvas);
// Vue.component('v-style', {
//   render: function (createElement) {
//     return createElement('style', this.$slots.default)
//   }
// });

Vue.mixin(FileManager)

Vue.component('main-component', require('../vue/components/MainComponent').default);
Vue.component('componentdispatcher', require('../../vue/components/ComponentDispatcher').default);
Vue.component('toolbar', require('../vue/components/ToolbarComponent').default);
Vue.component('summernote', require('../vue/components/SummernoteComponent').default);
Vue.component('videoinput', require('../vue/components/VideoComponent').default);
Vue.component('videoitem', require('../vue/components/VideoItem').default);
Vue.component('othercursor', require('../vue/components/OtherCursorComponent').default);
Vue.component('library', require('../vue/components/LibraryComponent').default);
Vue.component('pdfcomponent', require('../vue/components/pdfComponent').default);
Vue.component('imagecomponent', require('../vue/components/ImageComponent').default);
Vue.component('tokboxcomponent', require('../vue/components/Video/TokboxComponent').default);
Vue.component('voxeetcomponent', require('../vue/components/Video/VoxeetComponent').default);
// Vue.component('videoitemvue', require('../vue/components/VideoItemVue').default);
// Vue.component('component-other-cursor', require('./components/OtherCursorComponent').default);
Vue.component('dropfilearea', require('../vue/components/DropFileAreaComponent').default);
Vue.component('konva-component', require('../vue/components/KonvaComponent').default);
Vue.component('container-component', require('../vue/components/ContainerComponent').default);
Vue.component('container-header-component', require('../vue/components/ContainerHeaderComponent').default);
Vue.component('viewport-component', require('../vue/components/ViewportComponent').default);


// Configurations
// require('./bootstrap');

// const classroomStore = new Vuex.Store({
//     state: {
//         components: [],
//     },
//     mutations: {},
//     getters: {},
//     actions: {}
// });
const getRoomIdFromURL = () => {
  const url = window.location.href;
  let partial = url.split('/lesson/')[1];
  partial = partial.split('/classroom')[0];

  return partial;
};
const changeCoordinatesResolution = (oldX, oldY, screenWidth, screenHeight) => {
  const xPercentage = oldX / screenWidth * 100;
  const yPercentage = oldY / screenHeight * 100;
  const x = xPercentage / 100 * window.screen.width;
  const y = yPercentage / 100 * window.screen.height;
  // const x = oldX * window.screen.width / screenWidth;
  // const y = oldY * window.screen.height / screenHeight;

  return {x, y};
}

const store = new Vuex.Store({
    state: {
        user_id: window.langu_role === 'teacher' ? window.teacher_id : window.student_id,
        other_user_id: window.langu_role !== 'teacher' ? window.teacher_id : window.student_id,
        role: window.langu_role,
        other_user_role: window.langu_role === 'teacher' ? 'student' : 'teacher',
        users_joined_classroom: [],
        lesson_id: window.lesson_id,
        lessonStatusId: null,
        components: [Vue.component('summernote', require('../vue/components/SummernoteComponent').default)],
        isVideoInputOpened: false,
        isVideoOpened: false,
        videoId: null,
        isLibraryOpened: false,
        newAssets: [],
        userParams: {
          tool: TOOL_POINTER,
          cursor: 'cursor-pointer',
          color: window.langu_role === 'teacher' ? '#7FB802' : '#3C87F8',
          role: window.langu_role
        },
        maxIndex: 5,
        assets: [],
        containerComponentEnabled: true,
        otherCursor: {
          username: null,
          tool: TOOL_POINTER,
          cursor: 'pointer',
          bgColorTooltip: window.langu_role === 'teacher' ? '#3C87F8' : '#7FB802',
          coords: {
            x: 0,
            y: 0,
          }
        },
        resizeVideoData: {
          x: 500,
          y: 350
        },
        moveVideoData: {
          x: '40%',
          y: '20%'
        },
        moveTextBoxData: {
          x: '40%',
          y: '20%'
        },
        resizeTextBoxData: {
          w: '60vw',
          h: '80vh'
        },
        isDragging: false,
        opponentScreen: {
          width: window.screen.width,
          height: window.screen.height
        },
        toolNameBeforeChange: null,
        cursorNameBeforeChange: null,
        isScrollMainComponent: true,
        isSocketConnected: false,
        // isVoxeetPlayerActive: true,
    },
    mutations: {
        [ADD_COMPONENT](state, component) {
            state.components.push(component);
        },
        [FORCE_PUSH_COMPONENTS](state, components) {
            state.components = components;
        },
        [UPDATE_COMPONENT_DATA](state, {index, path, data}) {
            Vue.set(state.components, index, set(state.components[index], path, data));
        },
        toggleVideoInput(state) {
          state.isVideoInputOpened = !state.isVideoInputOpened
        },
        closeVideoInput(state) {
          state.isVideoInputOpened = false
        },
        toggleVideo(state, v) {
          state.isVideoOpened = v;
        },
        isDraggingTrigger(state, value) {
          state.isDragging = value
        },
        updateAssets(state, v) {
          state.newAssets = v
        },
        updateOtherCursor(state, data) {
          state.otherCursor = { ...state.otherCursor, ...data }
        },
        setResizeCoords(state, v) {
          state.resizeVideoData = {
            x: v.x,
            y: v.y
          }
        },
        setMoveVideoCoords(state, v) {
          state.moveVideoData = {
            x: v.x,
            y: v.y
          }
        },
        setMoveTextBoxCoords(state, data) {
          const coords = changeCoordinatesResolution(data.loc.x, data.loc.y, state.opponentScreen.width, state.opponentScreen.height);
          state.moveTextBoxData = {
            x: coords.x,
            y: coords.y,
          }
        },
        setResizeTextBoxData(state, v) {
          const coords = changeCoordinatesResolution(v.w, v.h, state.opponentScreen.width, state.opponentScreen.height)
          state.resizeTextBoxData = {
            w: coords.x,
            h: coords.y
          }
        },
        toggleLibrary(state) {
          state.isLibraryOpened = !state.isLibraryOpened;
        },
        setLessonStatusId(state, value) {
          state.lessonStatusId = value
        },
      enableContainerComponent: (state, enabled) => {
          state.containerComponentEnabled = enabled
      },
      addAssets: (state, assets) => {
        state.assets = [...state.assets, ...assets];

        assets.forEach(asset => {
          if (
            asset.asset &&
            asset.asset.index &&
            asset.asset.index > state.maxIndex
          ) {
            state.maxIndex = asset.asset.index
          }
        })
      },
      setAssets: (state, assets) => {
        state.assets = assets
      },
      deleteAsset: (state, deleted) => {
        state.assets = state.assets.filter(asset => asset.id !== deleted.id)
      },
      moveAsset: (state, moved) => {
        const item = state.assets.find(asset => asset.id === moved.id)

        if (item) {
          item.asset = { ...item.asset, ...moved.asset }

          if (moved.asset.index && moved.asset.index > state.maxIndex) {
            state.maxIndex = moved.asset.index
          }
        }
      },
      updateMoveAsset: (state, { id, asset }) => {
        const item = state.assets.find(asset => asset.id === id)

        if (item) {
          item.asset = { ...item.asset, ...asset }
        }
      },
      setMaxIndex: (state, maxIndex) => {
          if (maxIndex > state.maxIndex) {
            state.maxIndex = maxIndex
          }
      },
      setOpponentScreen: (state, screen) => {state.opponentScreen = screen},

      setMouseMovePosition: (state, position) => {
          state.mouseMovePosition = position
      },
      setUserTool: (state, tool) => {
          state.userParams.tool = tool
      },
      setUserCursor: (state, cursor) => {
          state.userParams.cursor = cursor
      },
      setToolNameBeforeChange: (state, payload) => {
        state.toolNameBeforeChange = payload
      },
      setCursorNameBeforeChange: (state, payload) => {
        state.cursorNameBeforeChange = payload
      },
      setIsScrollMainComponent: (state, payload) => {
        state.isScrollMainComponent = payload
      },
      setIsSocketConnected: (state, payload) => {
        state.isSocketConnected = payload
      },
      setUsersJoinedClassroom: (state, payload) => {
        state.users_joined_classroom = payload
      },
      // toggleVideoPlayer: (state, payload) => {state.isVoxeetPlayerActive = payload}
    },
    getters: {
        isOtherUserJoinedClassroom: state => state.users_joined_classroom.includes(+state.other_user_id),
        activeComponents: state => {
            return state.components.filter(component => component.is_active);
        },
        getIndexByRef: state => ref => {
            return state.components.findIndex(component => component.ref === ref);
        },
        getVideoState: state => {
          return state.isVideoOpened
        },
        pdf: state =>  {
          return state.pdf
        },
        getZoomAsset: state => {
          return state.assets.find(asset => asset.asset.type === 'zoom' && asset.asset.user_id === (window.langu_role === 'teacher' ? window.teacher_id : window.student_id))
        },
        getOtherZoomAsset: state => {
          return state.assets.find(asset => asset.asset.type === 'zoom' && asset.asset.user_id === (window.langu_role !== 'teacher' ? window.teacher_id : window.student_id)) ?? {}
        },
        // getToolbarAsset: state =>  {
        //   return state.assets.find(asset => asset.asset.type === 'toolbar' && asset.asset.user_id === (window.langu_role === 'teacher' ? window.teacher_id : window.student_id))
        // },
        getOtherCursor: state => {
          return state.otherCursor
        },
        getUserParams: state => {
          return state.userParams
        },
        role: state => state.userParams?.role,
        isLocked: state => state.assets.find(asset => asset.asset.type === 'lock')?.asset?.isLocked,
        isLessonFinished: state => state.lessonStatusId === '2',
        // getActiveVideoPlayerStatus: state => state.isVoxeetPlayerActive,
    },
    actions: {
      createAsset({ commit }, asset) {
        return axios.post(`/api/classroom/${getRoomIdFromURL()}`, asset)
          .then(res => {
            commit('addAssets', [res.data])
            this._vm.$socket.emit('asset-added', res.data)
          })
      },
      deleteAsset({ commit }, asset) {
        return axios.delete(`/api/classroom/${asset.id}`)
          .then(() => {
            commit('deleteAsset', asset)
            this._vm.$socket.emit('asset-deleted', asset)
          })
      },
      moveAsset({ commit }, asset) {
        return axios.put(`/api/classroom/${asset.id}`, asset)
          .then(() => {
            this._vm.$socket.emit('asset-moved', asset)
          })
      },
      updateAssetWithoutSync({ commit }, asset) {
        return axios.put(`/api/classroom/${asset.id}`, asset)
      },
        async [UPDATE_COMPONENT_DATA]({commit, getters, dispatch}, {type, ref, path, data}) {
            if (type === null) {
                return commit(UPDATE_COMPONENT_DATA, {
                    path: path,
                    data: data,
                    index: getters.getIndexByRef(ref),
                });
            } else {
                return dispatch(SEND_SOCKET_UPDATE, {
                    type: type,
                    ref: ref,
                    path: path,
                    data: data,
                }).then(() => {
                    commit(UPDATE_COMPONENT_DATA, {
                        path: path,
                        data: data,
                        index: getters.getIndexByRef(ref),
                    });
                });
            }
        },
        async [SEND_SOCKET_UPDATE]({commit}, {type, ref, path, data}) {
            return new Promise((resolve, reject) => {
                (new UpdateBuilder()).type(type).name(UpdateBuilder.VUE_COMPONENT_UPDATE)
                  .componentId(ref)
                  .updateData({
                      path: path,
                      target: data,
                  })
                  .send();

                resolve();
            });
        }
    }
});

global.store = store

new Vue({
    store,
    el: '.middle-inner-classroom',
})
