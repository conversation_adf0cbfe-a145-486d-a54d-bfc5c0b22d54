/* Cursors */
body.teacher-cursor-pointer,
body.teacher-cursor-pointer * {
  cursor: url('../../../web/images/classroom/teacher-pointer.svg') 0 0, auto;
}

body.student-cursor-pointer,
body.student-cursor-pointer * {
  cursor: url('../../../web/images/classroom/student-pointer.svg') 0 0, auto;
}

body.teacher-cursor-pointer .container-header-title:hover,
body.teacher-cursor-pointer .panel-heading:hover,
body.teacher-grabber-hover-cursor-pointer,
body.teacher-grabber-hover-cursor-pointer *,
body.teacher-role .cursor-before-grab:hover,
body.teacher-role .cursor-before-grab:hover * {
  cursor: url('../../../web/images/classroom/teacher-beforeGrab.svg') 21 16, auto;
}

body.student-cursor-pointer:not(.room-is-disabled) .container-header-title:hover,
body.student-cursor-pointer:not(.room-is-disabled) .panel-heading:hover,
body.student-grabber-hover-cursor-pointer,
body.student-grabber-hover-cursor-pointer *,
body.student-role .cursor-before-grab:hover,
body.student-role .cursor-before-grab:hover * {
  cursor: url('../../../web/images/classroom/student-beforeGrab.svg') 21 16, auto;
}

body.student-cursor-pointer.dragging * {
  cursor: url('../../../web/images/classroom/student-dragging.svg') 21 16, auto !important;
}

body.teacher-cursor-pointer.dragging * {
  cursor: url('../../../web/images/classroom/teacher-dragging.svg') 21 16, auto !important;
}

body.teacher-eraser-cursor,
body.teacher-eraser-cursor * {
  cursor: url('../../../web/images/classroom/teacher-eraser.svg') 10 20, auto;
}

body.teacher-pencil-cursor,
body.teacher-pencil-cursor * {
  cursor: url('../../../web/images/classroom/teacher-pencil.svg') 0 22, auto;
}

body.student-eraser-cursor,
body.student-eraser-cursor * {
  cursor: url('../../../web/images/classroom/student-eraser.svg') 10 20, auto;
}

body.student-pencil-cursor,
body.student-pencil-cursor * {
  cursor: url('../../../web/images/classroom/student-pencil.svg') 0 22, auto;
}

.summernote-teacher .note-editable:hover,
.summernote-teacher .note-editable:hover * {
  cursor: url('../../../web/images/classroom/teacher-text-cursor.svg') 32 32, auto;
}

.summernote-student .note-editable:hover,
.summernote-student .note-editable:hover * {
  cursor: url('../../../web/images/classroom/student-text-cursor.svg') 32 32, auto;
}
.teacher .plyr__controls__item:hover,
.summernote-teacher note-popover:hover,
.summernote-teacher a:hover,
.summernote-teacher a:hover * {
  cursor: url('../../../web/images/classroom/teacher-cursor-link.svg') 32 32, auto;
}

.student .plyr__controls__item:hover,
.summernote-student note-popover:hover,
.summernote-student a:hover,
.summernote-student a:hover * {
  cursor: url('../../../web/images/classroom/student-cursor-link.svg') 32 32, auto;
}

/*body.teacher-cursor-hand,*/
/*body.teacher-cursor-hand * {*/
/*    cursor: url('../../../web/images/classroom/cursor_hand_teacher.svg') 10 8, auto;*/
/*}*/

body.teacher-cursor-pointer.handle-mr *,
body.teacher-cursor-pointer.handle-ml *,
.vdr.teacher .handle-mr,
.vdr.teacher .handle-ml {
  cursor: url('../../../web/images/classroom/cursor-teacher-right.svg') 25 19, auto !important;
}

body.teacher-cursor-pointer.handle-tm *,
body.teacher-cursor-pointer.handle-bm *,
.vdr.teacher .handle-tm,
.vdr.teacher .handle-bm {
  cursor: url('../../../web/images/classroom/cursor-teacher-down.svg') 19 25, auto !important;
}

body.teacher-cursor-pointer.handle-tl *,
body.teacher-cursor-pointer.handle-br *,
.vdr.teacher .handle-tl,
.vdr.teacher .handle-br {
  cursor: url('../../../web/images/classroom/teacher-arrow.svg') 18 16, auto !important;
}

body.teacher-cursor-pointer.handle-tr *,
body.teacher-cursor-pointer.handle-bl *,
.vdr.teacher .handle-tr,
.vdr.teacher .handle-bl {
  cursor: url('../../../web/images/classroom/teacher-arrow-2.svg') 16 18, auto !important;
}

body.student-cursor-pointer.handle-mr *,
body.student-cursor-pointer.handle-ml *,
.vdr.student .handle-mr,
.vdr.student .handle-ml {
  cursor: url('../../../web/images/classroom/cursor-student-right.svg') 25 19, auto !important;
}

body.student-cursor-pointer.handle-tm *,
body.student-cursor-pointer.handle-bm *,
.vdr.student .handle-tm,
.vdr.student .handle-bm {
  cursor: url('../../../web/images/classroom/cursor-student-down.svg') 19 25, auto !important;
}

body.student-cursor-pointer.handle-tl *,
body.student-cursor-pointer.handle-br *,
.vdr.student .handle-tl,
.vdr.student .handle-br {
  cursor: url('../../../web/images/classroom/student-arrow.svg') 18 16, auto !important;
}

body.student-cursor-pointer.handle-tr *,
body.student-cursor-pointer.handle-bl *,
.vdr.student .handle-tr,
.vdr.student .handle-bl {
  cursor: url('../../../web/images/classroom/student-arrow-2.svg') 16 18, auto !important;
}
