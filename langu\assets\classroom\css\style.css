html {
  font-size: 1em !important;
}

body {
  overscroll-behavior-x: none;
}

.classroom {
  background: #f2f2f2 !important;
}

.classroom div, .classroom image, .classroom iframe {
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    -khtml-user-select: none; /* Konqueror HTML */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
}

.note-editable p {
    -webkit-touch-callout: default ; /* iOS Safari */
    -webkit-user-select: auto; /* Safari */
}

.note-editable[contenteditable="false"] {
  position: relative;
}

.note-editable[contenteditable="false"]::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255,255,255,.5);
}

.transparent .konvajs-content > canvas {
    background: transparent !important;
}

.container-fluid {
    padding: 0;
    margin: 0;
}

.columns-wrapper .middle .middle-inner {
    padding: 0;
    margin: 0;
}

.img-classroom-preview {
    position: absolute;
    top: 2px;
    right: 2px;
    border: 1px solid grey;
    background-color: #e4e4e4;
    z-index: 1;
    width: 250px;
}

.moveable-control-box,
.moveable-control-box .moveable-line{
    display: none !important;
}

.note-editable a {
  color: blue !important;
}

.note-editable a:hover {
  text-decoration: underline;
}

.body-classroom  {
    position: relative;
}

.plyr__video-embed iframe {
  top: -50%;
  height: 200%;
}
.plyr--video.plyr--menu-open {
    overflow: hidden;
}

.cursor-pointer,
.cursor-pointer * {
  cursor: pointer !important;
}

.cursor-auto {
  cursor: auto !important;
}

.selected {
    border-bottom: none;
}

.selected.button-student svg{
    color: #3C87F8;
}

.selected.button-teacher svg{
    color: #80B723;
}

.hide {
    display: none;
}

.toolbar-buttons {
  position: relative;
  width: 40px;
  z-index: 1000;
  padding-left: 0;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.toolbar-buttons li {
    list-style: none;
}

.toolbar-button-wrapper {
  width: 40px;
  display: flex;
  height: 40px;
  justify-content: center;
  position: relative;
}

 .toolbar-button-wrapper-replace, .toolbar-button-wrapper-undo {
     padding: 0;
 }

.toolbar-button-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 8px;
  position: relative;
  outline: none;
  border: none;
  background: transparent;
}

.toolbar-button-undo {
    padding: 8px 0;
    margin-bottom: 8px;
    width: 100%;
    border-radius: 6px;
    box-shadow: 0 4px 2px -2px rgba(0, 0, 0, 0.15);
}

.toolbar-button-item[disabled],
.toolbar-button-item:disabled,
.toolbar-button-item[disabled] *,
.toolbar-button-item:disabled * {
  cursor: default !important;
}

.toolbar-button-hand::before,
.toolbar-button-file::before {
  content: '';
  position: absolute;
  left: 2px;
  bottom: 2px;
  border: 2px solid transparent;
  border-bottom: 2px solid black;
  border-left: 2px solid black;
}

.toolbar-button-item[disabled]::before,
.toolbar-button-item:disabled::before {
  opacity: 0.3;
}

.toolbar-button-icon {
  width: auto;
  height: auto;
  max-height: 100%;
  max-width: 100%;
}

.toolbar-buttons-horizontal {
  display: none;
  position: absolute;
  top: 0;
  right: 40px;
  padding-right: 5px;
  border-radius: 6px;
}

.toolbar-button-item-draw-line{
    padding-right: 0;
}

.toolbar-button-item-hand{
    padding-left: 0;
}

 .toolbar-button-item-horizontal .toolbar-button-icon{
     height: auto;
     width: auto;
 }

.toolbar-buttons-horizontal .toolbar-button-wrapper:first-child .toolbar-button-item-horizontal {
    border-bottom-left-radius: 4px!important;
    border-top-left-radius: 4px!important;
}

.stream-controls .toolbar-button-item svg {
  color: #2D2D2D;
}

.stream-controls .toolbar-button-item[disabled] svg,
.stream-controls .toolbar-button-item:disabled svg {
  color: #c6c6c6;
}

#toolbar-switch {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.toolbar-button-wrapper:last-child .toolbar-button-item {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom: none!important;
}

.toolbar-button-item:hover + .hover-btn-info,
.toolbar-button-item:active + .hover-btn-info {
    display: block;
}

.button-teacher:hover svg {
    color: #80B723!important;
}

.button-student:hover svg {
    color: #3C87F8!important;
}

.toolbar-button-item svg {
    color: #2D2D2D;
}

.hover-btn-info {
  position: absolute;
  display: none;
  width: auto;
  top: 50%;
  transform: translateY(-50%);
  right: 50px;
  color: #fff;
  background: #444444;
  border-radius: 5px;
  padding: 5px 10px;
  white-space: nowrap;
  font-size: 13px;
}

.hover-horizontal-button {
  top: auto;
  right: auto;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
}

.hover-btn-info::after {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: -9px;
  border: 5px solid transparent;
  border-left: 5px solid #444444;
}

.hover-btn-info-horizontal {
    top: -40px;
    right: -60%;
}

.hover-horizontal-button::after {
  border-top: 5px solid #444444;
  border-left: 5px solid transparent;
  top: auto;
  right: auto;
  bottom: -9px;
  left: 50%;
  transform: translateX(-50%);
}

.toolbar-button-replace + .hover-btn-info {
    top: 40%;
}

input.video-load-input {
    padding: 5px;
    border-radius: 3px;
    margin-bottom: 15px;
}

.video-buttons-wrap {
    display: flex;
    align-items: center;
}

.video-load-btn {
    background: #5aac44;
    color: #fff;
    border: none;
    outline: none;
    padding: 6px 12px;
    border-radius: 3px;
}

.video-load-cross {
    margin-left: 10px;
    font-size: 30px;
    color: #6b778c;
    vertical-align: middle;
}

/* .cursor-first {
    cursor: url('/images/classroom/cursor_first_css.svg') 11 0, auto ;
}

.cursor-second {
    cursor: url('/images/classroom/cursor_second_css.svg') 11 0, auto ;
} */

.video_annotations {
    width: 2px;
    height: 12px;
    position: absolute;
    top: 4px;
    background: red;
}

#video-window {
    position: absolute;
    /*min-width: 240px;*/
    /*min-height: 178px;*/
    width: 100%;
    height: 100%;
    background: black;
    /*z-index: 1001;*/
    /*top: 10px;*/
    /*right: 2%;*/
}

.local-stream {
    z-index: 3;
    width: 100px;
    height: 75px;
    top: 0;
    position: absolute;
    background-color: #000;
}
.remote-stream {
    z-index: 2;
    position: absolute;
    width: 100%;
    height: 100%;
}

.remote-screenshare {
  background-color: #000;
}

.hr-flip {
  transform: scaleX(-1);
}

.stream-controls {
    position: absolute;
    width: 180px;
    left: 50%;
    bottom: -36px;
    z-index: 10;
    transform: translateX(-50%);
}

.student-role .stream-controls {
    bottom: -20px;
}

.video-window--is-fullscreen .stream-controls {
    bottom: 15px;
}

.student-role .video-window--is-fullscreen .stream-controls {
    bottom: 38px;
}

.embed-responsive {
  position: static;
}

.video-window--is-fullscreen .embed-responsive-4by3 {
    height: 100%;
    padding-bottom: 0;
}

.video-window-buttons-wrap {
    height: 100%;
    display: inline-flex;
    flex-direction: column;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
    border-radius: 6px;
}

#screenshare::-webkit-media-controls-fullscreen-button {}
#screenshare::-webkit-media-controls-play-button {
    display: none !important;
}
#screenshare::-webkit-media-controls-timeline {
    display: none !important;
}
#screenshare::-webkit-media-controls-current-time-display {
    display: none !important;
}
#screenshare::-webkit-media-controls-time-remaining-display {
    display: none !important;
}
#screenshare::-webkit-media-controls-mute-button {
    display: none !important;
}
#screenshare::-webkit-media-controls-toggle-closed-captions-button {
    display: none !important;
}
#screenshare::-webkit-media-controls-volume-slider {
    display: none !important;
}
#screenshare::-webkit-media-controls-panel {
    background-image: linear-gradient(transparent, transparent) !important;
}
#screenshare::-webkit-media-controls-panel {
    display: flex !important;
    opacity: 1 !important;
}

.OT_publisher, .OT_subscriber {
    min-width: 100% !important;
    min-height: 100% !important;
}

#video-share-catch {
    z-index: 91;
}

.note-popover .popover-content>.btn-group, .panel-heading.note-toolbar>.btn-group {
    margin-right: 0;
}

.btn {
    box-shadow: none;
}

.note-btn-group.note-style, .note-btn-group.note-insert, .note-btn-group.note-para {
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.12);
}

.note-editor {
  height: 100%;
}

.note-btn-group.note-style {
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
     position: relative;
    z-index: 14;
}

.note-btn-group.note-insert {
     position: relative;
    z-index: 12;
}

.note-popover .popover-content, .panel-heading.note-toolbar {
    padding: 5px 0 10px 17px;
}

.note-btn-group.note-style .btn:first-child {
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
}

.note-btn-group.note-para .note-btn-group, .note-btn-group.note-para .note-btn-group .btn{
     border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
}

.note-btn {
    padding: 8px 10px;
}

.note-toolbar-wrapper {
    height: auto !important;
}
.panel{
    box-shadow: 0 2px 30px rgba(0, 0, 0, 0.25);
    margin-bottom: 0;
}
.stream-controls-wrapper {
    position: relative;
    display: inline-flex;
    justify-content: center;
    flex-wrap: wrap;
    background: #FFFFFF;
    padding: 0 10px;
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
    z-index: 12;
}

 .stream-controls-wrapper button:first-child {
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
 }

 .stream-controls-wrapper button:last-child {
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
 }

.stream-controls-switch {
  display: inline-block;
  margin-top: -6px;
  padding: 10px 6px 4px;
  background: #FFFFFF;
  border: none;
  border-radius: 0 0 6px 6px;
  font-size: 12px;
  font-weight: 500;
  outline: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
}

  #play-pause img{
      margin-top: 5px;
        max-height: 27px;
        max-width: 28px;
 }

 #play-pause, #mute {
     position: relative;
     height: 100%;
     transition: all ease-in-out 0.5s;
 }

#play-pause::after, #mute::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    display: block;
    width: 2px;
    height: 80%;
    background: #4a4a4a;
    opacity: 0;
     transition: all ease-in-out 0.5s;
}

/* #play-pause.active::after, #mute.active::after, #play-pause:hover::after, #mute:hover::after {
}

#summernote_host.teacher .ui-resizable-se{
    cursor: url('/images/classroom/teacher-arrow.svg'), auto;
}

#summernote_host.student .ui-resizable-se{
    cursor: url('/images/classroom/student-arrow.svg'), auto;
}

#summernote_host.teacher .ui-resizable-e{
    cursor: url('/images/classroom/cursor-teacher-right.svg') 30 0, auto;
}

#summernote_host.student .ui-resizable-e{
    cursor: url('/images/classroom/cursor-student-right.svg') 30 0, auto;
}
#summernote_host.teacher .ui-resizable-s{
    cursor: url('/images/classroom/cursor-teacher-down.svg') 0 30, auto;
}

#summernote_host.student .ui-resizable-s{
    cursor: url('/images/classroom/cursor-student-down.svg') 0 30, auto;
} */

.popup-load-files {
    /* display: none; */
    cursor: auto !important;
    position: absolute;
    min-height: 483px;
    right: 13%;
    top: 50px;
    max-width: 650px;
    min-width: 650px;
    border-radius: 8px;
    box-shadow: 0px 2px 30px rgba(0, 0, 0, 0.25);
    z-index: 1002;
}

.popup-load-files.active {
    cursor: auto !important;
    display: block;
}

.popup-load-files-header {
  height: 65px;
    cursor: auto !important;
    padding: 10px 12px;
    background: #F5F5F5;
    display: flex;
    align-items: center;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
}

.popup-load-files-header-selected-files {
  height: 65px;
    cursor: auto !important;
    display: none;
    justify-content: space-between;
  align-items: center;
    background: #505050;
    padding: 10px 12px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
}

.popup-load-files-header-selected-files.active {
    cursor: auto !important;
    display: flex;
}

.popup-load-files-header-cross {
    cursor: auto !important;
    width: 28px;
    height: 35px;
    border: none;
    background: none;
}

.popup-load-files-header-cross-icon {
    cursor: auto !important;
    max-width: 100%;
    max-height: 100%;
}

.popup-load-files-header.seleceted-files.active {
    cursor: auto !important;
    display: block;
}

.popup-load-files-title {
    cursor: auto !important;
    font-weight: bold;
    font-size: 23px;
    margin-bottom: 0;
    margin-right: 20px;
    color: #2D2D2D;
}

input[type=file].popup-load-files-btn-upload {
    cursor: auto !important;
    display: none;
}

label.popup-load-files-label-upload, .popup-load-files-input {
    cursor: auto !important;
    padding: 9px 17px;
    margin-right: 17px;
    margin-bottom: 0;
    border: none;
    outline: none;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    font-weight: bold;
    font-size: 13px;
    color: #2D2D2D;
}
label.popup-load-files-label-upload.popup-load-files-label-upload-laptop {
  display: flex;
  justify-content: center;
  align-items: center;
    cursor: auto !important;
    padding: 0;
    box-shadow: none;
}

.popup-load-files .popup-load-files-label-upload {
  white-space: nowrap;
  min-width: 120px;
  text-align: center;
}

.popup-load-files-input:last-child {
    cursor: auto !important;
    margin-right: 0;
}

.popup-load-files-input::-webkit-input-placeholder {
    color: #2D2D2D;
}
.popup-load-files-input::-moz-placeholder {
    color: #2D2D2D;
}
.popup-load-files-input:-ms-input-placeholder {
    color: #2D2D2D;
}
.popup-load-files-input:-moz-placeholder {
    color: #2D2D2D;
}

.popup-load-files-select-wrap,  .popup-load-files-label-search {
    position: relative;
}

.popup-load-files-select {
    position: relative;
    width: 166px;
    margin-right: 0;
}

.popup-load-files-select-options::after  {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    display: block;
    border: 5px solid transparent;
    border-bottom: 5px solid #fff;
}

.popup-load-files-select-wrap {
    position: relative;
}

.popup-load-files-select-wrap::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 90%;
    transform: translateY(-20%);
    display: block;
    border: 5px solid transparent;
    border-top: 5px solid #000;
    z-index: 12;
}

.popup-load-files-select-options {
    position: absolute;
    display: none;
    width: 100%;
    top: 130%;
    left: 0;
    background: #fff;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
    z-index: 19;
}

.popup-load-files-select-options.active {
    display: block;
}

.popup-load-files-select-option {
    position: relative;
    font-size: 16px;
    padding: 6px 0;
}

.popup-load-files-select-option:last-child {
    margin-bottom: 0;
}

.popup-load-files-select-option::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -13px;
    transform: translateY(-20%);
    display: block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #000;
}

.popup-load-files-search-wrap {
    position: relative;
}

.library-add-search-img {
    background: url('../../../web/images/classroom/search.svg');
    height: 20px;
    width: 25px;
    cursor: pointer;
}

.popup-load-files-search {
    padding-right: 38px;
}

.popup-load-files-search-icon {
    position: absolute;
    top: 47%;
    transform: translateY(-40%);
    right: 23px;
    border: none;
    background: none;
    outline: none;
}

.popup-load-files-body {
    padding: 15px 0 0 20px;
    display: flex;
    flex-wrap: wrap;
    max-width: 873px;
}

.popup-load-files-item {
    width: calc(16% - 20px);
    margin-right: 20px;
    display: flex;
    justify-content:center;
    align-items: center;
    flex-direction: column;
    margin-bottom: 15px;
}

.popup-load-files-item-img {
    cursor: auto !important;
    width: 100%;
    height: 80px;
    border-radius: 8px;
    background: #C4C4C4;
    display: flex;
    justify-content: center;
    align-items: center;
}

.popup-load-files-footer {
    cursor: auto !important;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 17px 17px 17px;
}

.popup-load-files-item-name p{
    cursor: auto !important;
    font-size: 12px;
    width: 80px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


.popup-load-files-nav-wrap {
    cursor: auto !important;
    margin: 0 30px;
}

.popup-load-files-nav-number {
    cursor: auto !important;
    position: relative;
    display: inline-block;
    padding: 5px;
    font-size: 14px;
}

.popup-load-files-nav-number::after {
    cursor: auto !important;
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-40%);
    display: none;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background: #000;
}

.popup-load-files-nav-number.active::after {
    cursor: auto !important;
    display: block;
}

.popup-load-files-btn-nav {
    cursor: auto !important;
    border: none;
    outline: none;
    background: none;
}

 .popup-load-files-btn-nav span{
     cursor: auto !important;
     font-size: 14px;
 }

.popup-load-files-nav-icon-next {
    cursor: auto !important;
    display: inline-block;
    background-position: top left;
    background: url('../../../web/images/classroom/Arrow 2.svg');
    transform: rotate(-180deg);
    margin-left: 6px;
    height: 20px;
    width: 20px;
    background-repeat: no-repeat;
}

.popup-load-files-nav-icon-prev {
    cursor: auto !important;
    display: inline-block;
    background: url('../../../web/images/classroom/Arrow 2.svg') bottom left;
    margin-right: 6px;
    height: 20px;
    width: 20px;
    background-repeat: no-repeat;
}

.popup-load-files-close {
    cursor: auto !important;
    position: absolute;
    right: 20px;
}

.popup-load-files-item {
    cursor: auto !important;
    position: relative;
}

.popup-load-files-item-tick {
    cursor: auto !important;
    position: absolute;
    top: 3px;
    left: 3px;
    width: 16px;
    height: 16px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid rgba(0,0,0,0.5)
}

.popup-load-files-item-tick img {
    display: block;
}

.popup-load-files-item-tick.active .popup-load-files-tick-icon {
    cursor: auto !important;
    display: block;
}

.popup-load-files-search:hover,  .popup-load-files-search:focus {
    cursor: auto !important;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}

.popup-load-files-tick-icon {
    cursor: auto !important;
    position: absolute;
    top: 50%;
    left: 50%;
    display: none;
    transform: translate(-50%,-50%);
}

.whiteboard_video_el {
    z-index: 20;
}

.popup-load-files-wrap {
    min-height: 425px;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #fff;
}

.popup-load-files-drop-wrap {
    /*position: absolute;*/
    top: 0;
    left: 0;
    height: 100vh;
    width: 100%;
    background: none;
}

.popup-load-files-drop-wrap.active {
    /*background:  url('../../../web/images/classroom/dropfiles.svg');*/
    background-color: rgba(255, 255, 255, 0.9);
    /*background-position: center;*/
    /*background-repeat: no-repeat;*/

    z-index: 999999;
    display: flex;
}
.drop-area--wrapper {
    width: 50%;
    height: 65%;
    margin: auto;
    pointer-events: none;
    position: relative;
}
.drop-area--wrapper__upload-meta {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
}
.drop-area--wrapper__dropbox-img {
    width: 100%;
    height: auto;
}
/*.video-item, .image-wrap-classroom {*/
    /*position: absolute !important;*/
    /*width: 500px;*/
    /*height: 350px;*/
/*    top: 20%;*/
/*    left: 40%;*/
/*    z-index: 1110;*/
/*}*/

/*.video-item, .image-wrap-classroom {*/
/*  display: none;*/
/*}*/

.image-wrap-classroom {
    background: #fff;
    box-shadow: 0px 2px 30px rgba(0, 0, 0, 0.25);
}

/*.video-item.active, .image-wrap-classroom.active {*/
/*    display: block;*/
/*}*/

.video-item-description, .image-classroom-description {
    /*position: absolute;*/
    /*bottom: 100%;*/
    /*left: 0;*/
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background: #5e5e5e;
    /* padding: 5px; */
    color: #fff;
}

.video-item-cross, .image-classroom-cross {
    font-size: 20px;
    padding: 0 10px;
}

.video-item-name {
  width: 100%;
  padding: 10px 10px;
}

.image-classroom-item-icon {
    width: 100%;
    height: 100%;
}

#player {
   width: 100%;
   height: 100%;
}

#video-item-wrap {
   position: absolute;
   left: 0;
   top: 0;
   bottom: 0;
   right: 0;
}

.plyr__video-wrapper {
   position: relative;
}

.plyr__controls__item, .plyr__controls__item span, .plyr__progress, .plyr__control, .plyr__progress input, .plyr__progress svg, .plyr__volume input {
   cursor: pointer!important;
}

.canvas-video {
   height: 100%;
   width: 100%;
}

.drag-move {
    cursor: url('../../../web/images/classroom/teacher-dragging.svg') 32 24, auto !important;
  }

.note-modal .modal-dialog {
  width: 100% !important;
  max-width: 1100px !important;
}

.sn-checkbox-open-in-new-window,
.sn-checkbox-use-protocol {
  display: none !important;
}
