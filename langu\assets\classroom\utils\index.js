var aryCurPressedKeys = new Array();

const onKeyDown = (evt) => {

  if (evt.keyCode == 16) {
    cursor.shift_pressed = true;
  }
  //alert(String.fromCharCode(evt.which||evt.keyCode));
  var key = String.fromCharCode(evt.which || evt.keyCode);

  // If key not already down then add to our list
  if (!isKeyPressed(key)) {
    aryCurPressedKeys[aryCurPressedKeys.length] = key;
  }
}

const onKeyUp = (evt)  => {
  var key = String.fromCharCode(evt.which || evt.keyCode);
  if (evt.keyCode == 16) {
    cursor.shift_pressed = true;
  }

  // If the key released is in our list then remove it
  for (var i = 0; i < aryCurPressedKeys.length; i++) {
    if (key == aryCurPressedKeys[i]) {
      removeArrayItem(aryCurPressedKeys, i);
    }
  }
}

const isKeyPressed = (key) => {
  for (var i = 0; i < aryCurPressedKeys.length; i++) {
    if (aryCurPressedKeys[i] == key) {
      return true;
    }
  }

  return false;
}

//Move all items in the array above the point down and then
//delete the last item.
const removeArrayItem = (_array, nItem) => {
  for (var i = nItem; i < _array.length; i++) {
    _array[i] = _array[i + 1];

    if (i == _array.length - 1) {
      delete _array[_array.length];
      return;
    }
  }
}

const removeAllKeysFromArray = () => {
  aryCurPressedKeys = new Array();
}

function Location(_x, _y) {
  this.x = _x;
  this.y = _y;
}

const getParameterByName = (name, url) => {
  if (!url) url = window.location.href;
  name = name.replace(/[\[\]]/g, '\\$&');
  var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
    results = regex.exec(url);
  if (!results) return null;
  if (!results[2]) return '';
  return decodeURIComponent(results[2].replace(/\+/g, ' '));
}

const getParameterFromURL = (type_param) => {
  var url = window.location.href;
  if (type_param == "roomid") {
    // Extract room id from the url we are currently at
    var partial = url.split('/lesson/')[1];
    partial = partial.split('/classroom')[0];

    return partial;
  }
}

// From stack overflow
const uuidv4 = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

const is_float = (mixedVar) => {
  return +mixedVar === mixedVar && (!isFinite(mixedVar) || !!(mixedVar % 1))
}

/**
 * This function includes unified state for modules
 */
const State = () => {
  this.hovered = false;
  this.focused = false;
  this.resized = false;
}

const getHoverColor = (reverse = false) => {
  return window.langu_role == (!reverse ? 'teacher' : 'student') ? '#7FB802' : '#3C87F8';
}

/**
 * Checks if variable is Object.
 *
 * @param variable
 * @returns {boolean}
 */
function is_object(variable) {
  return typeof variable === 'object';
}

const ucfirst = (string) => {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

/**
 * @param assets
 * @param type
 * @returns {Promise<*>}
 */
const findAssetByType = async (assets, type) => {
  return assets.find(asset => asset.type === type)
}

/**
 * Make an unified dispatch request
 *
 * @param action {String} const of called action
 * @param type {String|null} const (temporary or permanent)
 * @param ref {String} ref of a component
 * @param path {String} divided with dotes path to key in Object that will be replaced
 * @param data {Object|String} Object with update data
 * @return {Promise<any>}
 */
const dispatch = (action, type, ref, path, data = '') => {
  return store.dispatch(action, {
    type: type,
    ref: ref,
    path: path,
    data: data,
  });
}

const setPropertyByKeypath = (object, keypath, data) => {
  keypath = (typeof keypath === 'string') ? keypath.split('.') : keypath;
  let i = 0,
    len = keypath.length - 1;

  for (; i < len; i++) {
    object = object[keypath[i]];
  }

  object[keypath[i]] = data;
}

const getCaretCharacterOffsetWithin = (element) => {
  var caretOffset = 0;
  var doc = element.ownerDocument || element.document;
  var win = doc.defaultView || doc.parentWindow;
  var sel;
  if (typeof win.getSelection != "undefined") {
    sel = win.getSelection();
    if (sel.rangeCount > 0) {
      var range = win.getSelection().getRangeAt(0);
      var preCaretRange = range.cloneRange();
      preCaretRange.selectNodeContents(element);
      preCaretRange.setEnd(range.endContainer, range.endOffset);
      caretOffset = preCaretRange.toString().length;
    }
  } else if ((sel = doc.selection) && sel.type != "Control") {
    var textRange = sel.createRange();
    var preCaretTextRange = doc.body.createTextRange();
    preCaretTextRange.moveToElementText(element);
    preCaretTextRange.setEndPoint("EndToEnd", textRange);
    caretOffset = preCaretTextRange.text.length;
  }
  return caretOffset;
}

const setCaretPosition = (element, offset) => {
  var range = document.createRange();
  var sel = window.getSelection();

  //select appropriate node
  var currentNode = null;
  var previousNode = null;

  for (var i = 0; i < element.childNodes.length; i++) {
    //save previous node
    previousNode = currentNode;

    //get current node
    currentNode = element.childNodes[i];
    //if we get span or something else then we should get child node
    while(currentNode.childNodes.length > 0){
      currentNode = currentNode.childNodes[0];
    }

    //calc offset in current node
    if (previousNode != null) {
      offset -= previousNode.length;
    }
    //check whether current node has enough length
    if (offset <= currentNode.length) {
      break;
    }
  }
  //move caret to specified offset
  if (currentNode != null) {
    range.setStart(currentNode, offset);
    range.collapse(true);
    sel.removeAllRanges();
    sel.addRange(range);
  }
}




const createRange = (node, chars, range) => {
  if (!range) {
    range = document.createRange()
    range.selectNode(node);
    range.setStart(node, 0);
  }

  if (chars.count === 0) {
    range.setEnd(node, chars.count);
  } else if (node && chars.count >0) {
    if (node.nodeType === Node.TEXT_NODE) {
      if (node.textContent.length < chars.count) {
        chars.count -= node.textContent.length;
      } else {
        range.setEnd(node, chars.count);
        chars.count = 0;
      }
    } else {
      for (var lp = 0; lp < node.childNodes.length; lp++) {
        range = createRange(node.childNodes[lp], chars, range);

        if (chars.count === 0) {
          break;
        }
      }
    }
  }

  return range;
}

const setCurrentCursorPosition = (chars) => {
  if (chars >= 0) {
    var selection = window.getSelection();

    range = createRange(document.getElementById("test").parentNode, { count: chars });

    if (range) {
      range.collapse(false);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }
}

const isChildOf = (node, parentId) => {
  while (node !== null) {
    if (node.id === parentId) {
      return true;
    }
    node = node.parentNode;
  }

  return false;
}

const getCurrentCursorPosition = (parentId) => {
  var selection = window.getSelection(),
    charCount = -1,
    node;

  if (selection.focusNode) {
    if (isChildOf(selection.focusNode, parentId)) {
      node = selection.focusNode;
      charCount = selection.focusOffset;

      while (node) {
        if (node.id === parentId) {
          break;
        }

        if (node.previousSibling) {
          node = node.previousSibling;
          charCount += node.textContent.length;
        } else {
          node = node.parentNode;
          if (node === null) {
            break
          }
        }
      }
    }
  }

  return charCount;
}

export {
  onKeyDown,
  onKeyUp,
  isKeyPressed,
  removeArrayItem,
  removeAllKeysFromArray,
  getParameterByName,
  getParameterFromURL,
  uuidv4,
  is_float,
  State,
  getHoverColor,
  is_object,
  ucfirst,
  findAssetByType,
  dispatch,
  setPropertyByKeypath
}
