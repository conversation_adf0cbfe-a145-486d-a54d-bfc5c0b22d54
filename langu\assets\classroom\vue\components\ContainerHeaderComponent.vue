<template>
  <div>
    <div
      :class="['container-header', { 'container-header--close': isCloseBtn }]"
      @mouseenter.prevent="mouseenterHandler"
      @mouseleave.prevent="mouseleaveHandler"
    >
      <div class="container-header-title">{{ title }}</div>
      <div
        v-if="isCloseBtn"
        class="container-header-close"
        @click="close"
      >×</div>
    </div>

    <slot></slot>
  </div>
</template>

<script>
import Tool from '../mixins/Tool'

export default {
  name: 'ContainerHeaderComponent',
  mixins: [Tool],
  props: {
    file: {
      type: Object,
      required: true
    },
    title: {
      type: String,
      required: true
    }
  },
  computed: {
    role() {
      return this.$store.getters.role
    },
    isCloseBtn() {
      return (
        this.role === null ||
        this.role === 'teacher' ||
        (this.role === 'student' && this.file.asset?.owner === 'student')
      )
    }
  },
  methods: {
    close() {
      this.$store.dispatch('deleteAsset', this.file)
    },
    mouseenterHandler() {
      this.$store.commit(
        'setCursorNameBeforeChange',
        this.$store.state?.userParams?.cursor || 'cursor-pointer'
      )
      this.$store.commit(
        'setToolNameBeforeChange',
        this.$store.state?.userParams?.tool || 'pointer'
      )

      this.setTool('pointer', 'cursor-pointer')
    },
    mouseleaveHandler() {
      this.setTool(
        this.$store.state.toolNameBeforeChange,
        this.$store.state.cursorNameBeforeChange
      )
    },
  }
}
</script>

<style scoped>
.container-header {
  position: relative;
  height: 40px;
  color: #fff;
  background: #5e5e5e;
  line-height: normal;
}

.container-header-title {
  padding: 10px;
  font-size: 16px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.container-header--close .container-header-title {
  width: calc(100% - 40px);
  padding-right: 0;
}

.container-header-close {
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  line-height: 0.8;
}
</style>
