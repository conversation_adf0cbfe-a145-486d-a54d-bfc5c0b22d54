<template>
  <div @drop.prevent="drop" @dragleave.prevent="dragleave">
    <div class="popup-load-files-drop-wrap active" v-show="isDragging">
      <div class="drop-area--wrapper">
        <img src="/images/classroom/dropfiles.svg" class="drop-area--wrapper__dropbox-img" alt="">
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'DropFileAreaComponent',
    computed: {
      isDragging() {
        return this.$store.state.isDragging
      }
    },
    mounted() {
      document.addEventListener('dragover', this.dragover)
    },
    methods: {
      dragover(e) {
        e.preventDefault()
        store.commit('isDraggingTrigger', true)
      },
      dragleave() {
        store.commit('isDraggingTrigger', false)
      },
      drop(e) {
        store.commit('isDraggingTrigger', false)
        this.uploadFiles(e.dataTransfer.files)
      }
    }
  }
</script>

<style scoped>
 .popup-load-files-drop-wrap {
     pointer-events: none;
 }
</style>
