<template>
  <container-component :asset="file" :child-header-height="40">
    <container-header-component
      :file="file"
      :title="file.asset.displayName"
    >
      <div ref="childComponent" class="image-wrap-classroom active">
        <img
          :src="file.asset.path"
          class="image-classroom-item-icon"
          alt=""
          @load="resize"
        />
      </div>
      <div class="transparent">
        <konva-component
          v-if="file && width && height"
          :file="file"
          :width="width"
          :height="height"
          :scale="scale"
          :style="{
            marginTop: `-${height}px`
          }"
        ></konva-component>
      </div>
    </container-header-component>
  </container-component>
</template>

<script>
import { defaultWidth } from '../../core/helpers/constants'

export default {
  props: {
    file: {
      type: Object,
      required: true
    }
  },
  computed: {
    scale() {
      if (this.width) {
        return this.width / defaultWidth
      }

      return 1
    },
    width() {
      return this.file.asset.width
    },
    height() {
      return this.file.asset.height
    },
    zoomIndex() {
      return this.$store.getters.getZoomAsset?.asset?.zoomIndex ?? 1
    },
  },
  methods: {
    resize() {
      if (!this.width) {
        const width = this.$refs.childComponent.getBoundingClientRect().width / this.zoomIndex
        const height = this.$refs.childComponent.getBoundingClientRect().height / this.zoomIndex
        const asset = { width, height, originalWidth: defaultWidth }

        this.$store.commit('moveAsset', {
          id: this.file.id,
          asset,
        })

        this.$store.dispatch('moveAsset', {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset,
        })
      }
    }
  },
  // watch: {
  //   'zoom.zoomIndex': function(old, newv) {
  //     this.resize()
  //     console.log(this.height, old, newv)
  //   }
  // }
}
</script>
