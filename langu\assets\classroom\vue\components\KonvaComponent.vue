<template lang="html">
  <v-stage
    ref="stage"
    :config="config"
    @mousedown="mousedown"
    @touchstart="mousedown"
    @touchmove="move"
    @touchend="mouseup"
    @mouseup="mouseup"
    @mousemove="move"
  >
    <v-layer ref="globalLayer">
      <template v-for="(shape, index) in shapes">
        <component
          v-if="
          (!shape.hasOwnProperty('time') && !shape.hasOwnProperty('page')) ||
          (shape.hasOwnProperty('time') && currentTime + 1 >= shape.time && shape.time >= currentTime - 1) ||
          (shape.hasOwnProperty('page') && currentPage === shape.page)
        "
          :key="index"
          :is="shape.type"
          :config="shape"
        ></component>
      </template>
    </v-layer>
  </v-stage>
</template>

<script>
import {
  TOOL_CIRCLE,
  TOOL_ERASER,
  TOOL_LINE,
  TOOL_PEN,
  TOOL_POINTER,
  TOOL_SQUARE,
  TOOL_TRIANGLE
} from '../../core/imports/tools'
import {
  mainCanvasWidth,
  mainCanvasHeight,
  mainCanvasOffsetX,
  mainCanvasOffsetY
} from '../../core/helpers/constants'
import _ from 'lodash'

export default {
  props: {
    file: {
      type: Object,
      required: true,
    },
    width: {
      type: Number,
      required: true,
    },
    height: {
      type: Number,
      required: true,
    },
    scale: {
      type: Number,
      default: 1
    },
    currentTime: {
      type: Number,
      default: null
    },
    currentPage: {
      type: Number,
      default: null
    },
    isMainKonva: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      assetShapes: [],
      shapeData: null,
      beginPoint: null,
      konvaEl: null,
      konvaOverlayREl: null,
      konvaOverlayBEl: null,
    }
  },
  computed: {
    isCanvasOversizeX() {
      return mainCanvasWidth > this.width
    },
    isScaledCanvasOversizeX() {
      return mainCanvasWidth * this.scale > this.width
    },
    isCanvasOversizeY() {
      return mainCanvasHeight > this.viewportHeight
    },
    isScaledCanvasOversizeY() {
      return mainCanvasHeight * this.scale > this.height
    },
    assetType() {
      return this.file.asset.type
    },
    userParams() {
      return this.$store.getters.getUserParams
    },
    shapes() {
      const assetShapes = [...this.assetShapes]
      const _shapes = []

      if (this.shapeData) {
        assetShapes.push(this.shapeData)
      }

      assetShapes.forEach(shape => {
        const _shape = { ...shape }

        _shape.x = shape.x - this.zoomX
        _shape.y = shape.y - this.zoomY

        if (this.file.asset?.originalWidth) {
          _shape.strokeWidth = shape.strokeWidth * this.file.asset.originalWidth / this.file.asset.width
        }

        _shapes.push(_shape)
      })

      // if (this.isMainKonva) {
      //   _shapes.push({
      //     type: 'v-line',
      //     stroke: '#f8ae3c',
      //     strokeWidth: 5,
      //     x: 0,
      //     y: 0,
      //     points: [
      //       mainCanvasOffsetX, mainCanvasOffsetY,
      //       mainCanvasOffsetX, mainCanvasHeight + mainCanvasOffsetY,
      //       mainCanvasWidth + mainCanvasOffsetX, mainCanvasHeight + mainCanvasOffsetY,
      //       mainCanvasWidth + mainCanvasOffsetX, mainCanvasOffsetY,
      //       mainCanvasOffsetX, mainCanvasOffsetY,
      //     ],
      //   })
      // }

      return _shapes
    },
    zoomX() {
      return this.isMainKonva ? this.zoom.x : 0
    },
    zoomY() {
      return this.isMainKonva ? this.zoom.y : 0
    },
    zoom() {
      return this.$store.getters.getZoomAsset.asset
    },
    config() {
      return {
        scale: {
          x: this.scale,
          y: this.scale,
        },
        width: this.width,
        height: this.height,
        draggable: false,
      }
    }
  },
  watch: {
    'file.asset.shapes': function(shapes) {
      this.assetShapes = shapes
    },
    width(width) {
      this.stage.setWidth(width)

      if (this.isMainKonva) {
        if (this.konvaOverlayREl) {
          this.setStyleForHorizontalMainKonvaOverlays()
        }

        if (this.konvaOverlayBEl) {
          this.setStyleForVerticalMainKonvaOverlays()
        }
      }
    },
    height(height) {
      this.stage.setHeight(height)

      if (this.isMainKonva) {
        if (this.konvaOverlayREl) {
          this.setStyleForHorizontalMainKonvaOverlays()
        }

        if (this.konvaOverlayBEl) {
          this.setStyleForVerticalMainKonvaOverlays()
        }
      }
    },
    scale(scale) {
      this.stage.setScale({
        x: scale,
        y: scale
      })

      if (this.isMainKonva) {
        if (this.konvaOverlayREl) {
          this.setStyleForHorizontalMainKonvaOverlays()
        }

        if (this.konvaOverlayBEl) {
          this.setStyleForVerticalMainKonvaOverlays()
        }
      }
    },
    isScaledCanvasOversizeX(newValue) {
      if (this.isMainKonva) {
        if (newValue) {
          this.konvaEl.removeChild(this.konvaOverlayREl)

          this.konvaOverlayREl = null
        } else {
          this.addHorizontalMainKonvaOverlays()
        }
      }
    },
    isScaledCanvasOversizeY(newValue) {
      if (this.isMainKonva) {
        if (newValue) {
          this.konvaEl.removeChild(this.konvaOverlayBEl)

          this.konvaOverlayBEl = null
        } else {
          this.addVerticalMainKonvaOverlays()
        }
      }
    },
  },
  mounted() {
    this.assetShapes = this.file.asset.shapes || []
    this.stage = this.$refs.stage.getStage()
    this.throttledMove = _.throttle(this.move, 0)

    this.konvaEl = document.getElementById('konva')

    if (this.isMainKonva) {
      if (!this.isScaledCanvasOversizeX) {
        this.addHorizontalMainKonvaOverlays()
      }

      if (!this.isScaledCanvasOversizeY) {
        this.addVerticalMainKonvaOverlays()
      }
    }
  },
  methods: {
    addHorizontalMainKonvaOverlays() {
      if (!this.konvaOverlayREl) {
        this.konvaOverlayREl = document.createElement('div')

        this.konvaOverlayREl.classList.add('konva-overlay-r')

        this.setStyleForHorizontalMainKonvaOverlays()

        this.konvaOverlayREl.addEventListener('mouseenter', this.mouseup)

        this.konvaEl.appendChild(this.konvaOverlayREl)
      }
    },
    addVerticalMainKonvaOverlays() {
      if (!this.konvaOverlayBEl) {
        this.konvaOverlayBEl = document.createElement('div')

        this.konvaOverlayBEl.classList.add('konva-overlay-b')

        this.setStyleForVerticalMainKonvaOverlays()

        this.konvaOverlayBEl.addEventListener('mouseenter', this.mouseup)

        this.konvaEl.appendChild(this.konvaOverlayBEl)
      }
    },
    setStyleForHorizontalMainKonvaOverlays() {
      this.konvaOverlayREl.style.position = 'absolute'
      this.konvaOverlayREl.style.top = '0'
      this.konvaOverlayREl.style.right = '0'
      this.konvaOverlayREl.style.width = `${this.width - mainCanvasWidth * this.scale}px`
      this.konvaOverlayREl.style.height = `${this.height}px`
      this.konvaOverlayREl.style.backgroundColor = '#DCDCDD'
    },
    setStyleForVerticalMainKonvaOverlays() {
      this.konvaOverlayBEl.style.position = 'absolute'
      this.konvaOverlayBEl.style.bottom = '0'
      this.konvaOverlayBEl.style.left = '0'
      this.konvaOverlayBEl.style.width = `${this.width}px`
      this.konvaOverlayBEl.style.height = `${this.height - mainCanvasHeight * this.scale}px`
      this.konvaOverlayBEl.style.backgroundColor = '#DCDCDD'
    },
    mousedown(event) {
      let layer = this.$refs.globalLayer.getNode()
      let position = event.target.getStage().getPointerPosition()

      this.beginPoint = { x: position.x / this.scale + this.zoomX, y: position.y / this.scale + this.zoomY }

      switch (this.userParams.tool) {
        case TOOL_POINTER:
          let ripple = new Konva.Circle({
            x: position.x / this.scale,
            y: position.y / this.scale,
            radius: 2,
            stroke: this.userParams.color,
            strokeWidth: 1,
          });

          layer.add(ripple);
          (new Konva.Tween({
            node: ripple,
            duration: 1,
            radius: 24,
            onFinish: () => ripple.destroy()
          })).play();
          break

        case TOOL_PEN:
          this.shapeData = {
            type: 'v-line',
            stroke: this.userParams.color,
            strokeWidth: 5,
            x: 0,
            y: 0,
            points: [position.x / this.scale + this.zoomX, position.y / this.scale + this.zoomY],
            lineCap: 'round',
            lineJoin: 'round',
            tension: 0,
            bezier: true,
            perfectDrawEnabled: false,
          }
          break

        case TOOL_SQUARE:
          this.shapeData = {
            type: 'v-rect',
            x: position.x / this.scale + this.zoomX,
            y: position.y / this.scale + this.zoomY,
            width: 1,
            height: 1,
            stroke: this.userParams.color,
            strokeWidth: 5,
          }
          break;

        case TOOL_CIRCLE:
          this.shapeData = {
            type: 'v-circle',
            x: position.x / this.scale + this.zoomX,
            y: position.y / this.scale + this.zoomY,
            radius: 1,
            stroke: this.userParams.color,
            strokeWidth: 5,
          }
          break

        case TOOL_TRIANGLE:
          this.shapeData = {
            type: 'v-line',
            stroke: this.userParams.color,
            strokeWidth: 5,
            x: position.x / this.scale + this.zoomX,
            y: position.y / this.scale + this.zoomY,
            points: [0, 0, 0, 0, 0, 0],
            tension: 0,
            closed: true,
          }
          break

        case TOOL_LINE:
          this.shapeData = {
            type: 'v-line',
            stroke: this.userParams.color,
            strokeWidth: 5,
            x: 0,
            y: 0,
            points: [position.x / this.scale + this.zoomX, position.y / this.scale + this.zoomY],
          }
          break

        case TOOL_ERASER:
          this.shapeData = {
            type: 'v-line',
            stroke: '#f2f2f2',
            strokeWidth: 30,
            x: 0,
            y: 0,
            points: [position.x / this.scale + this.zoomX, position.y / this.scale + this.zoomY],
            globalCompositeOperation: 'destination-out',
          }
          break

        default:
          console.warn('Requested action doesnt found for selected cursor - ' + this.userParams.tool);
      }

      if (this.userParams.tool !== TOOL_POINTER) {
        if (this.assetType === 'video') {
          this.shapeData.time = this.currentTime
        }

        if (this.assetType === 'pdf') {
          this.shapeData.page = this.currentPage
        }
      }
    },
    mouseup() {
      if (!this.shapeData || !this.shapeData.type) return

      this.assetShapes.push(this.shapeData)
      this.beginPoint = null
      this.shapeData = null

      this.$store.dispatch('moveAsset', {
        id: this.file.id,
        lessonId: this.file.lessonId,
        asset: {
          shapes: this.assetShapes
        }
      })
    },
    move(event) {
      if (this.shapeData) {
        const position = event.target.getStage().getPointerPosition()

        this.drawing(position)
      }
    },
    drawing(position) {
      if (this.shapeData) {
        let offsetWidth
        let offsetHeight

        switch (this.userParams.tool) {
          case TOOL_PEN:
          case TOOL_ERASER:
            this.shapeData.points = [
              ...this.shapeData.points,
              position.x / this.scale + this.zoomX,
              position.y / this.scale + this.zoomY
            ]
            break

          case TOOL_SQUARE:
            this.shapeData.width = position.x / this.scale + this.zoomX - this.beginPoint.x
            this.shapeData.height = position.y / this.scale + this.zoomY - this.beginPoint.y
            break;

          case TOOL_CIRCLE:
            offsetWidth = Math.abs(position.x / this.scale + this.zoomX - this.beginPoint.x)
            offsetHeight = Math.abs(position.y / this.scale + this.zoomY - this.beginPoint.y)
            this.shapeData.radius = Math.max(offsetWidth, offsetHeight);
            break;

          case TOOL_TRIANGLE:
            this.shapeData.points =
              [
                -(position.x / this.scale + this.zoomX - this.beginPoint.x),
                position.y / this.scale + this.zoomY - this.beginPoint.y,
                0,
                0,
                position.x / this.scale + this.zoomX - this.beginPoint.x,
                position.y / this.scale + this.zoomY - this.beginPoint.y,
              ]
            break

          case TOOL_LINE:
            this.shapeData.points = [
              this.beginPoint.x,
              this.beginPoint.y,
              position.x / this.scale + this.zoomX,
              position.y / this.scale + this.zoomY,
            ]
            break

          default:
            console.warn('Requested action doesnt found for selected cursor')
        }
      }
    },
  },
}
</script>
