<template lang="html">
  <div
    class="popup-load-files"
    :style="{ zIndex }"
    @dragover.stop.prevent="isDraggingTrigger(true)"
  >
    <div
      class="popup-load-files-header"
      ref="loadFilesHeader"
      :class="{ inactive: tickedFiles.length > 0 }"
    >
      <p class="popup-load-files-title">Library</p>
      <label class="popup-load-files-label-upload cursor-pointer">
        Upload new file
        <input
          type="file"
          id="upload-library-files"
          multiple class="popup-load-files-btn-upload"
          ref="file"
          @change="addFiles"
        />
      </label>
      <div class="popup-load-files-search-wrap cursor-auto">
        <input
          v-model="queryStr"
          class="popup-load-files-search popup-load-files-input"
          placeholder="Search"
        />
        <button id="library-add-search" class="popup-load-files-search-icon cursor-auto">
          <div class="library-add-search-img cursor-auto"></div>
        </button>
      </div>
      <div class="popup-load-files-select-wrap cursor-auto">
        <div
          class="popup-load-files-input popup-load-files-select cursor-pointer"
          @click="toggleSortOptionsList"
        >
          Sort by
        </div>
        <div :class="{ 'active': sortListDisplay }" class="popup-load-files-select-options cursor-auto">
          <div
            v-for="(option, idx) in sortOptions"
            :key="idx"
            class="cursor-pointer"
            :class="{ 'popup-load-files-select-option': requestBody.sort_type === option.value }"
            @click="changeSortType(option)"
          >
            {{ option.label }}
          </div>
        </div>
      </div>
    </div>

    <div
      ref="headerSelectedFiles"
      class="popup-load-files-header-selected-files"
      :class="{'active': tickedFiles.length > 0}"
    >
      <button
        id="add-to-classroom"
        class="popup-load-files-input"
        @click="addToClassroom"
      >
        Add to classroom
      </button>
      <div class="popup-load-files-buttons-wrap">
        <button
          id="popup-load-files-download"
          class="popup-load-files-input"
          @click="downloadFiles"
        >
          Download
        </button>
        <button
          class="popup-load-files-input"
          @click="deleteFiles"
        >
          Delete
        </button>
        <button
          class="popup-load-files-header-cross"
          @click="untickFiles"
        >
          <img
            src="/images/classroom/library-cross.svg"
            class="popup-load-files-header-cross-icon"
            alt=""
          >
        </button>
      </div>
    </div>

    <div class="popup-load-files-wrap cursor-auto">
      <div
        v-show="isDragging"
        class="popup-load-files-drop-wrap active"
        @dragleave.stop.prevent="isDraggingTrigger(false)"
        @drop.stop.prevent="handleFileDrop"
      >
        <div class="drop-area--wrapper">
          <img
            src="/images/classroom/dropfiles.svg"
            class="drop-area--wrapper__dropbox-img"
            alt=""
          >
        </div>
      </div>

      <div v-show="!isDragging">
        <div id="popup-load-files-list" class="popup-load-files-body cursor-auto">
          <div
            v-for="file in files"
            :key="file.id"
            class="popup-load-files-item cursor-auto"
          >
            <div class="popup-load-files-item-img cursor-auto">
              <template v-if="isPdf(file)">
                <canvas :id="`pdf-thumb--${file.id}`"></canvas>
              </template>
              <template v-else>
                <img
                  :src="file.path"
                  class="preview-fluid cursor-auto popup-load-files-img-icon"
                  alt=""
                >
              </template>
            </div>
            <div class="popup-load-files-item-name cursor-auto">
              <p>{{ file.displayName }}</p>
            </div>
            <div
              class="popup-load-files-item-tick cursor-pointer"
              :class="{'active': tickedFiles.find(item => item.id === file.id)}"
              @click="toggleFileMark(file)"
            >
              <div class="popup-load-files-tick-icon cursor-auto">
                <img src="/images/classroom/tick2.svg" alt="" />
              </div>
            </div>
          </div>
        </div>
        <div class="popup-load-files-footer">
          <button
            class="popup-load-files-btn-nav popup-load-files-btn-nav-prev cursor-pointer"
            @click="prevPage"
          >
            <div class="popup-load-files-nav-icon popup-load-files-nav-icon-prev"></div>
            <span>Previous</span>
          </button>
          <div id="popup-load-files-navigation" class="popup-load-files-nav-wrap">
            <span
              v-for="page in arrayPages"
              class="popup-load-files-nav-number"
              :class="{'active': requestBody.page === page}"
              @click="goToPage(page)"
            >
              {{ page }}
            </span>
          </div>
          <button
            class="popup-load-files-btn-nav popup-load-files-btn-nav-next cursor-pointer"
            @click="nextPage"
          >
            <span>Next</span>
            <div class="popup-load-files-nav-icon popup-load-files-nav-icon-next"></div>
          </button>
          <button
            class="popup-load-files-input popup-load-files-close cursor-pointer"
            @click="closeLibrary"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { createImage } from '../../../js/vueFiles/classroom/imageItem'
import { defaultWidth } from '../../core/helpers/constants'

// const SORT_TYPES = {
//     'file_name_asc': 1,
//     'file_name_desc' : 2,
//     'last_added': 3
//
// }
const axios = require('axios')
const debounce = function debounce(fn, delay) {
  let timeoutID = null

  return function() {
    clearTimeout(timeoutID)

    let args = arguments
    let that = this

    timeoutID = setTimeout(function() {
      fn.apply(that, args)
    }, delay)
  }
}

export default {
  data() {
    return {
      files: [],
      totalPages: 1,
      sortListDisplay: false,
      queryStr: '',
      requestBody: {
        query: '',
        sort_direction: 'DESC',
        page: 1,
        sort_type: 3,
      },
      sortOptions: [
        {
          label: 'Last added',
          value: 3,
        },
        {
          label: 'Last opened',
          value: 'last_opened',
        },
        {
          label: 'File name (A-Z)',
          value: 2,
        },
        {
          label: 'File name (Z-A)',
          value: 1,
        }
      ],
      tickedFiles: [],
      arrayPages: [],
      uploadPercentage: 0,
      isDragging: false,
      isLoad: false,
      cancelToken: null,
      source: null,
    };
  },
  computed: {
    zIndex() {
      return this.$store.state.maxIndex + 1
    },
    getZoom() {
      return this.$store.getters.getZoomAsset.asset
    },
    lessonId() {
      return this.$store.state.lesson_id
    }
  },
  watch: {
    queryStr: debounce(function(value) {
      this.requestBody.query = value

      this.getListOfFiles()
    }, 500)
  },
  mounted() {
    this.getListOfFiles()
  },
  methods: {
    isDraggingTrigger(value) {
      this.isDragging = value
    },
    getPager(currentPage = 1, pageSize = 10, maxPages = 10) {
      // calculate total pages
      let totalPages = this.totalPages

      // ensure current page isn't out of range
      if (currentPage < 1) {
        currentPage = 1
      } else if (currentPage > totalPages) {
        currentPage = totalPages
      }

      let startPage, endPage
      if (totalPages <= maxPages) {
        // total pages less than max so show all pages
        startPage = 1
        endPage = totalPages
      } else {
        // total pages more than max so calculate start and end pages
        let maxPagesBeforeCurrentPage = Math.floor(maxPages / 2)
        let maxPagesAfterCurrentPage = Math.ceil(maxPages / 2) - 1

        if (currentPage <= maxPagesBeforeCurrentPage) {
          // current page near the start
          startPage = 1
          endPage = maxPages
        } else if (currentPage + maxPagesAfterCurrentPage >= totalPages) {
          // current page near the end
          startPage = totalPages - maxPages + 1
          endPage = totalPages
        } else {
          // current page somewhere in the middle
          startPage = currentPage - maxPagesBeforeCurrentPage
          endPage = currentPage + maxPagesAfterCurrentPage
        }
      }

      // create an array of pages to ng-repeat in the pager control
      // return array of pages required by the view
      return Array.from(Array(endPage + 1 - startPage).keys()).map(
        (i) => startPage + i
      )
    },
    untickFiles() {
      this.tickedFiles = []
    },
    toggleFileMark(file) {
      if (this.tickedFiles.find(item => item.id === file.id)) {
        this.tickedFiles = this.tickedFiles.filter(item => item.id !== file.id)
      } else {
        this.tickedFiles.push(file)
      }
    },
    downloadFiles() {
      this.tickedFiles.forEach((file) => {
        const link = document.createElement('a')

        link.href = file.path
        link.download = file.name

        link.click()
      })
    },
    deleteFiles() {
      const data = this.tickedFiles.map((file) => file.id)

      axios.post('/api/lesson/classroom/delete/library', { data })
        .then((response) => {
          this.getListOfFiles()

          this.tickedFiles = []
        })
    },
    addFile(e) {
      console.warn(e)
    },
    toggleSortOptionsList() {
      return (this.sortListDisplay = !this.sortListDisplay)
    },
    changeSortType(option) {
      this.requestBody.sort_type = option.value

      return this.getListOfFiles()
    },
    nextPage() {
      if (this.requestBody.page === this.totalPages) return false

      this.requestBody.page++

      return this.getListOfFiles()
    },
    prevPage() {
      if (this.requestBody.page === 1) return false

      this.requestBody.page--

      return this.getListOfFiles()
    },
    goToPage(page) {
      this.requestBody.page = page

      return this.getListOfFiles()
    },
    isPdf(file) {
      return this.getFileExtension(file.path)[0].toLowerCase() === 'pdf'
    },
    getListOfFiles() {
      let formData = new FormData()

      for (const key in this.requestBody) {
        formData.append(key, this.requestBody[key])
      }

      axios.post(
          `/api/lesson/classroom/library/${ window.classroom.voxeet.userId }/${ this.requestBody.page }`,
          formData
        )
        .then((response) => {
          this.totalPages = response.data.length ? response.data[0].pages : 1
          this.files = response.data
          this.arrayPages = this.getPager(this.requestBody.page, 18, 5)

          this.files.forEach(file => {
            if (this.isPdf(file)) {
              this.makePdfThumb(file)
            }
          })
        })
    },
    makePdfThumb(file) {
      pdfjsLib.getDocument(file.path).promise
        .then(pdfDoc => {
          pdfDoc.getPage(1).then(page => {
            if (page) {
              const vp = page.getViewport({ scale: 1 })
              const canvas = document.querySelector(`#pdf-thumb--${file.id}`)

              if (canvas && vp.width > 0 && vp.height > 0) {
                const pageScale = vp.width / vp.height

                canvas.height = canvas.width = 78

                if (vp.width < vp.height) {
                  canvas.width = 78 * pageScale
                } else {
                  canvas.height = 78 / pageScale
                }

                const scale = Math.min(canvas.width / vp.width, canvas.height / vp.height)

                page.render({
                  canvasContext: canvas.getContext('2d'),
                  viewport: page.getViewport({ scale })
                })
              }
            }
          })
        })
        .catch(err => {
          console.log(err.message)
        })
    },
    handleFileDrop(e) {
      const droppedFiles = e.dataTransfer.files

      if (!droppedFiles) return

      this.uploadFile(droppedFiles)
    },
    addFiles(e) {
      const files = this.$refs.file.files

      this.uploadFile(files)
    },
    cancelUpload() {
      this.source.cancel('Operation canceled due to new request.')
    },
    uploadFile(files) {
      files = [...files]

      const formData = new FormData()

      for (let i = 0; i <= files.length - 1; i++) {
        formData.append(i.toString(), files[i])
      }

      this.isLoad = true
      this.cancelToken = axios.CancelToken
      this.source = this.cancelToken.source()

      axios.post(`/api/lesson/classroom/upload/library/${ this.lessonId }`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          cancelToken: this.source.token,
          onUploadProgress: function(progressEvent) {
            this.uploadPercentage = parseInt(
              Math.round((progressEvent.loaded / progressEvent.total) * 100)
            )
          }.bind(this)
        })
        .then(response => {
          this.files = [...response.data, ...this.files]
          this.tickedFiles = response.data
          this.isDragging = false
          this.isLoad = false

          response.data.forEach(file => {
            if (this.isPdf(file)) {
              this.makePdfThumb(file)
            }
          })
        })
        .catch((e) => {
          this.isDragging = false
          this.isLoad = false
        })
    },
    closeLibrary() {
      store.commit('toggleLibrary')
    },
    async addToClassroom() {
      for (const file of this.tickedFiles) {
        let type

        switch (this.getFileExtension(file.path)[0].toLowerCase()) {
          case 'pdf':
            type = 'pdf'

            break
          case 'png':
          case 'jpg':
          case 'jpeg':
          case 'bmp':
          case 'svg':
            type = 'image'

            break
          default:
            return
        }

        await this.$store.dispatch('createAsset', {
          type,
          path: file.path,
          displayName: file.displayName,
          top: this.getZoom.y + 200,
          left: this.getZoom.x + 650,
          index: this.$store.state.maxIndex
        })
      }

      this.untickFiles()
      this.closeLibrary()
    },
    loadImage(id, src, name, isCreated) {
      createImage(id, src, name, isCreated)

      $('.image-classroom').addClass(window.role === 'teacher' ? 'teacher' : 'student')
    }
  }
};
</script>

<style lang="css" scoped>
.popup-load-files-drop-wrap {
  cursor: default !important;
  height: 425px !important;
}

.popup-load-files-footer {
  bottom: 0;
  position: absolute;
  width: 100%;
}

.cursor-pointer,
.cursor-pointer * {
  cursor: pointer !important;
}

.popup-load-files-item-img .preview-fluid {
  max-width: 75%;
  max-height: 75%;
}

.popup-load-files-header.inactive {
  display: none;
}
</style>
