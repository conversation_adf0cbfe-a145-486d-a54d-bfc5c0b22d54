<template>
  <div
    v-if="zoomAsset"
    id="main-component"
    class="classroom"
    @mouseleave="onUp"
    @wheel="onwheel"
    @gesturestart.prevent="gestureStart"
    @gesturechange.prevent="gestureChange"
    @gestureend.prevent="gestureEnd"
    @mouseup="onUp"
    @mousemove="mouseMoved"
  >
    <dropfilearea></dropfilearea>
    <div v-show="!isDragging">
      <div
        v-if="timer"
        class="classroom-timer"
        :style="timeRemainingStyle"
      >
        Time remaining: {{ timer }}
      </div>
      <div :class="['zoom-percent', { 'zoom-percent--show': zoomPercent !== 100 }]">
        Zoom: {{ zoomPercent }}%
      </div>

      <viewport-component
        :zoom-other-asset="zoomOtherAsset"
        :zoom-asset="zoomAsset"
        :viewport-width="viewportWidth"
        :viewport-height="viewportHeight"
      ></viewport-component>

      <othercursor></othercursor>

      <summernote v-if="editorAsset" :key="key" :file="editorAsset"></summernote>
      <videoitem v-for="videoAsset in videoAssets" :key="videoAsset.id" :file="videoAsset"></videoitem>
      <pdfcomponent v-for="pdfAsset in pdfAssets" :key="pdfAsset.id" :file="pdfAsset"></pdfcomponent>
      <imagecomponent v-for="imageAsset in imageAssets" :key="imageAsset.id" :file="imageAsset"></imagecomponent>

      <voxeetcomponent v-if="voxeetAsset" :file="voxeetAsset" :screen-share-asset="screenShareAsset"></voxeetcomponent>
      <tokboxcomponent v-if="tokboxAsset" :file="tokboxAsset" :screen-share-asset="screenShareAsset"></tokboxcomponent>

      <videoinput v-if="isVideoInputOpened"></videoinput>
      <library v-if="isLibraryOpened"></library>

      <toolbar
        :file="lockAsset"
        :buzz-url="buzzUrl"
        :student-id="studentId"
        :finish-lesson-url="finishLessonUrl"
        :scale="zoomIndex"
        :width="viewportWidth"
        :height="viewportHeight"
      ></toolbar>
      <div
        id="konva"
        @mousedown="konvaMouseDownHandler"
        @mouseup="konvaMouseUpHandler"
      >
        <konva-component
          v-if="shapeAsset"
          :scale="zoomIndex"
          :file="shapeAsset"
          :width="viewportWidth"
          :height="viewportHeight"
          is-main-konva
        ></konva-component>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import debounce from 'debounce'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import { addEvent, removeEvent } from '../utils/dom'
import { TOOL_POINTER}  from '../../core/imports/tools'
import { isIOS, isDevice }  from '../../core/helpers/check_device'
import Tool from '../mixins/Tool'
import {
  mainCanvasWidth,
  mainCanvasHeight,
  mainCanvasOffsetX,
  mainCanvasOffsetY
} from '../../core/helpers/constants'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(advancedFormat)

export default {
  name: 'MainComponent',
  mixins: [Tool],
  props: {
    buzzUrl: {
      type: String,
      required: true
    },
    studentId: {
      type: String,
      required: true
    },
    studentName: {
      type: String,
      required: true
    },
    finishLessonUrl: {
      type: String,
      required: true
    },
    lessonStartTime: {
      type: String,
      required: true
    },
    lessonDuration: {
      type: String,
      required: true
    },
    lessonStatusId: {
      type: String,
      required: true
    },
  },
  data() {
    return {
      user_id: window.langu_role === 'teacher' ? window.teacher_id : window.student_id,
      // role: window.langu_role,
      other_user_role: window.langu_role === 'teacher' ? 'student' : 'teacher',
      lesson_id: window.lesson_id,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
      timeOut: null,
      startScale: 1,
      mouseClickPosition : { x: 0, y: 0 },
      offset: {
        x: 0,
        y: 0
      },
      components: [],
      element: null,
      beginPoint: null,
      shapeData: null,
      throttledMove: () => {},
      evCache: [],
      prevDiff: -1,
      pointerMoveTimeout: null,
      dragTimeout: null,
      mouseMoveTimeout: null,
      screenResizeTimeout: null,
      touchStatus: null,
      otherCursorPosition : { x: 0, y: 0 },
      timer: null,
      keysPressed: {},
      key: 1,
      isDevice: isDevice(),
    }
  },
  computed: {
    isCanvasOversizeX() {
      return mainCanvasWidth > this.viewportWidth
    },
    isScaledCanvasOversizeX() {
      return mainCanvasWidth * this.zoomIndex > this.viewportWidth
    },
    isCanvasOversizeY() {
      return mainCanvasHeight > this.viewportHeight
    },
    isScaledCanvasOversizeY() {
      return mainCanvasHeight * this.zoomIndex > this.viewportHeight
    },
    shapeAsset() {
      return this.$store.state.assets.find(asset => asset.asset.type === 'shape')
    },
    editorAsset() {
      return this.$store.state.assets.find(asset => asset.asset.type === 'editor')
    },
    voxeetAsset() {
      return this.$store.state.assets.find(asset => asset.asset.type === 'voxeet')
    },
    tokboxAsset() {
      return this.$store.state.assets.find(asset => asset.asset.type === 'tokbox')
    },
    imageAssets() {
      return this.$store.state.assets.filter(asset => asset.asset.type === 'image')
    },
    pdfAssets() {
      return this.$store.state.assets.filter(asset => asset.asset.type === 'pdf')
    },
    videoAssets() {
      return this.$store.state.assets.filter(asset => asset.asset.type === 'video')
    },
    lockAsset() {
      return this.$store.state.assets.find(asset => asset.asset.type === 'lock')
    },
    screenShareAsset() {
      return this.$store.state.assets.find(asset => asset.asset.type === 'screenShare')
    },
    isLocked() {
      return this.$store.getters.isLocked
    },
    // toolbarAsset() {
    //   return this.$store.getters.getToolbarAsset
    // },
    currentVideoPlayer() {
      return localStorage.getItem('activeVideoPlayer') || 'voxeet'
    },
    isVideoInputOpened() {
      return this.$store.state.isVideoInputOpened
    },
    isLibraryOpened() {
      return this.$store.state.isLibraryOpened
    },
    isDragging() {
      return this.$store.state.isDragging;
    },
    userParams() {
      return this.$store.getters.getUserParams
    },
    zoomAsset() {
      return this.$store.getters.getZoomAsset
    },
    zoomOtherAsset() {
      return this.$store.getters.getOtherZoomAsset
    },
    zoomIndex() {
      return this.zoomAsset?.asset?.zoomIndex ?? 1
    },
    zoomPercent() {
      return Math.round(this.zoomIndex * 100)
    },
    minSizeX() {
      return mainCanvasOffsetX
    },
    minSizeY() {
      return mainCanvasOffsetY
    },
    maxSizeXPoint() {
      return mainCanvasWidth + mainCanvasOffsetX
    },
    maxSizeYPoint() {
      return mainCanvasHeight + mainCanvasOffsetY
    },
    maxSizeX() {
      return this.isScaledCanvasOversizeX ?
        this.maxSizeXPoint - window.innerWidth / this.zoomIndex :
        0
    },
    maxSizeY() {
      return this.isScaledCanvasOversizeY ?
        this.maxSizeYPoint - window.innerHeight / this.zoomIndex :
        0
    },
    minZoom() {
      return this.isCanvasOversizeX && this.isCanvasOversizeY ?
        Math.max(
    window.innerWidth / (this.maxSizeXPoint - this.minSizeX),
           window.innerHeight / (this.maxSizeYPoint - this.minSizeY)
        ) :
        window.innerWidth / this.maxSizeXPoint
    },
    maxZoom() {
      return 2
    },
    lessonEndTime() {
      return dayjs(this.lessonStartTime, 'Y-m-d H:i:sO').add(this.lessonDuration, 'minute')
    },
    userTz() {
      return window.classroom?.user?.tz
    },
    isScrollMainComponent() {
      return this.$store.state.isScrollMainComponent
    },
    timeRemainingStyle() {
      return {
        bottom: this.isScaledCanvasOversizeY ?
          '15px' :
          `${this.viewportHeight - mainCanvasHeight * this.zoomIndex + 15}px`,
        right: this.isScaledCanvasOversizeX ?
          '75px' :
          `${this.viewportWidth - mainCanvasWidth * this.zoomIndex + 75}px`,
      }
    },
  },
  watch: {
    'userParams.cursor': function(cursor) {
      this.toggleBodyClass('addClass', cursor)
    },
    isLocked(newValue, oldValue) {
      if (this.role === 'student') {
        const $body = $('body')
        const bodyClass = 'room-is-disabled'

        if (newValue) {
          this.setTool('pointer', 'cursor-pointer')
          $body.addClass(bodyClass)
        } else {
          $body.removeClass(bodyClass)
        }
      }
    },
  },
  async mounted() {
    setInterval(() => {
      const currentTime = dayjs().tz(this.userTz).format()
      const diff = dayjs(this.lessonEndTime).diff(currentTime, 'second')

      if (diff > 0) {
        if (this.lessonDuration * 60 > diff) {
          let minutes = Math.abs(Math.floor(diff / 60)).toString()
          minutes = minutes.length < 2 ? `0${minutes}` : minutes

          let seconds = Math.abs(diff % 60).toString()
          seconds = seconds.length < 2 ? `0${seconds}` : seconds

          this.timer = `${minutes}:${seconds}`
        } else {
          this.timer = `${this.lessonDuration}:00`
        }
      } else {
        this.timer = '0:00'
      }
    }, 1000)

    this.updateScreenSize()

    this.$store.commit('setLessonStatusId', this.lessonStatusId)

    this.toggleBodyClass('addClass', 'role')
    this.toggleBodyClass('addClass', this.userParams.cursor)

    await axios.get(`/api/classroom/${this.getParameterFromURL('roomid')}`).then(res => {
      this.$store.commit('addAssets', res.data)
    })

    if (!this.editorAsset) {
      await this.$store.dispatch('createAsset', {
        type: 'editor',
      })
    }

    if (!this.shapeAsset) {
      await this.$store.dispatch('createAsset', { type: 'shape', shapes: [] })
    }

    if (!this.lockAsset) {
      await this.$store.dispatch('createAsset', { type: 'lock', isLocked: false })
    }

    if (!this.voxeetAsset && !this.tokboxAsset) {
      await this.$store.dispatch('createAsset', {
        type: this.currentVideoPlayer,
        width: 400,
        height: 300,
        settings: {
          teacher: {
            isMuted: false,
            isVideoEnabled: true,
          },
          student: {
            isMuted: false,
            isVideoEnabled: true,
          },
        },
      })
    }

    // if (!this.toolbarAsset) {
    //   await this.$store.dispatch('createAsset', {
    //     type: 'toolbar',
    //     user_id: window.langu_role === 'teacher' ? window.teacher_id : window.student_id,
    //     resizable: false,
    //     synchronizeable: false
    //   })
    // }

    if (!this.screenShareAsset) {
      await this.$store.dispatch('createAsset', {
        type: 'screenShare',
        top: 5,
        left: 5,
        width: 400,
        height: 300,
      })
    }

    if (!this.zoomAsset) {
      await this.$store.dispatch('createAsset', {
        type: 'zoom',
        user_id: window.langu_role === 'teacher' ? window.teacher_id : window.student_id,
        zoomIndex: this.minZoom > 1 ? this.minZoom : 1,
        x: 0,
        y: 0,
        username: this.studentName,
        screen: {
          width: Math.min(this.viewportWidth, mainCanvasWidth),
          height: Math.min(this.viewportHeight, mainCanvasHeight),
        },
      })
    } else {
      this.updateScreenSize()
    }

    this.$nextTick(() => {
      window.addEventListener('resize', this.updateViewportSizes)
      window.addEventListener('keydown', this.keyZoom)
      window.addEventListener('keyup', this.keyZoomUp)

      // Install event handlers for the pointer target
      const mainComponentEl = document.getElementById('main-component')
      const konvaEl = document.getElementById('konva')

      konvaEl.addEventListener( 'touchmove', this.touchMove, false )
      konvaEl.addEventListener( 'touchend', this.touchUp, false )

      mainComponentEl.onpointerdown = this.pointerDownHandler
      mainComponentEl.onpointermove = this.pointerMoveHandler

      mainComponentEl.onpointerup = this.pointerUpHandler
      mainComponentEl.onpointercancel = this.pointerUpHandler
      mainComponentEl.onpointerout = this.pointerUpHandler
      mainComponentEl.onpointerleave = this.pointerUpHandler
    })
  },
  methods: {
    pointerDownHandler(event) {
      // The pointerdown event signals the start of a touch interaction.
      // This event is cached to support 2-finger gestures
      this.evCache.push(event)

      if (this.evCache.length < 2) {
        const x = event.offsetX
        const y = event.offsetY

        this.mouseClickPosition.x = x + this.zoomAsset.asset.x
        this.mouseClickPosition.y = y + this.zoomAsset.asset.y
      }

      if (this.evCache.length === 2) {
        this.$store.commit(
          'setCursorNameBeforeChange',
          this.$store.state?.userParams?.cursor || 'cursor-pointer'
        )
        this.$store.commit(
          'setToolNameBeforeChange',
          this.$store.state?.userParams?.tool || 'pointer'
        )

        this.setTool('pointer', 'cursor-pointer')
      }
    },
    pointerMoveHandler(event) {
      if (!this.pointerMoveTimeout) {
        this.pointerMoveTimeout = setTimeout(() => {
          this.pointerMoveTimeout = null

          // This function implements a 2-pointer horizontal pinch/zoom gesture.
          // Find this event in the cache and update its record with this event
          for (let i = 0; i < this.evCache.length; i++) {
            if (event.pointerId === this.evCache[i].pointerId) {
              this.evCache[i] = event

              break
            }
          }

          // If two pointers are down, check for pinch gestures
          if (this.evCache.length === 2) {
            this.touchStatus = 'zooming'

            const oldZoomIndex = this.zoomIndex
            const step = isIOS() ? 0.005 : 0.025

            // Calculate the distance between the two pointers
            const curDiffX = Math.abs(this.evCache[0].clientX - this.evCache[1].clientX)
            const curDiffY = Math.abs(this.evCache[0].clientY - this.evCache[1].clientY)
            const curDiff = curDiffX > curDiffY ? curDiffX : curDiffY

            if (this.prevDiff > 0) {
              if (curDiff > this.prevDiff) {
                // The distance between the two pointers has increased
                let zoomIndex = Math.min(Math.max(this.minZoom, oldZoomIndex + step), this.maxZoom)

                this.zoomChange(oldZoomIndex, zoomIndex)
              }
              if (curDiff < this.prevDiff) {
                // The distance between the two pointers has decreased
                let zoomIndex = Math.min(Math.max(this.minZoom, oldZoomIndex - step), this.maxZoom)

                this.zoomChange(oldZoomIndex, zoomIndex)
              }
            }

            // Cache the distance for the next move event
            this.prevDiff = curDiff
          }
        }, 100)
      }
    },
    pointerUpHandler(event) {
      if (this.evCache.length === 2) {
        this.zoomStop()
        this.setTool(
            this.$store.state.toolNameBeforeChange,
            this.$store.state.cursorNameBeforeChange
        )
      }

      // Remove this pointer from the cache and reset the target's
      this.removeEvent(event)

      // If the number of pointers down is less than two then reset diff tracker
      if (this.evCache.length < 2) {
        this.prevDiff = -1
      }

      if (this.evCache.length === 0) {
        this.touchStatus = null
      }
    },
    removeEvent(event) {
      // Remove this event from the target's cache
      for (let i = 0; i < this.evCache.length; i++) {
        if (this.evCache[i].pointerId === event.pointerId) {
          this.evCache.splice(i, 1)

          break
        }
      }
    },
    getEventDeltaX(event) {
      if (event.deltaMode === WheelEvent.DOM_DELTA_PIXEL) {
        return event.deltaX / this.zoomIndex
      }

      return event.deltaX * 10 / this.zoomIndex
    },
    getEventDeltaY(event) {
      if (event.deltaMode === WheelEvent.DOM_DELTA_PIXEL) {
        return event.deltaY / this.zoomIndex
      }

      return event.deltaY * 10 / this.zoomIndex
    },
    getEventDeltaZoom(event) {
      let deltaY = event.deltaY

      const isFloat = Number(deltaY) === deltaY && deltaY % 1 !== 0

      if (isFloat) {
        deltaY = event.deltaY >= 0 ? Math.ceil(event.deltaY + 7) : Math.floor(event.deltaY - 7)
      }

      if (event.deltaMode === WheelEvent.DOM_DELTA_PIXEL) {
        return  deltaY * -0.0005
      }

      return deltaY * -0.01
    },
    updateViewportSizes() {
      this.viewportWidth = window.innerWidth
      this.viewportHeight = window.innerHeight

      this.updateScreenSize()
    },
    updateScreenSize() {
      if (this.zoomAsset && !this.screenResizeTimeout) {
        this.screenResizeTimeout = setTimeout(() => {
          this.screenResizeTimeout = null

          const data = {
            id: this.zoomAsset.id,
            lessonId: this.zoomAsset.lessonId,
            asset: {
              username: this.studentName,
              screen: {
                width: Math.min(this.viewportWidth, mainCanvasWidth),
                height: Math.min(this.viewportHeight, mainCanvasHeight),
              },
            },
          }

          // this.$socket.emit('asset-moved', data)

          this.$store.commit('moveAsset', data)

          this.$store.dispatch('moveAsset', data)
        }, 100)
      }
    },
    keyZoomUp(event) {
      delete this.keysPressed[event.keyCode]
    },
    keyZoom(event) {
      this.keysPressed[event.keyCode] = true

      if (this.keysPressed[17] && (this.keysPressed[187] || this.keysPressed[189] || this.keysPressed[107] || this.keysPressed[109])) {
        event.preventDefault()

        const oldZoomIndex = this.zoomIndex

        let zoomIndex = [107, 187].includes(event.keyCode) ?
            Math.min(Math.max(this.minZoom, oldZoomIndex + 0.1), this.maxZoom) :
            Math.min(Math.max(this.minZoom, oldZoomIndex - 0.1), this.maxZoom)

        const x =  window.innerWidth * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex / 2
        const y =  window.innerHeight * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex / 2
        const asset = {
          zoomIndex,
          x: mainCanvasWidth * zoomIndex > this.viewportWidth ?
            Math.min(Math.max(this.minSizeX, this.zoomAsset.asset.x + x), this.maxSizeXPoint - window.innerWidth / zoomIndex) :
            0,
          y: mainCanvasHeight * zoomIndex > this.viewportHeight ?
            Math.min(Math.max(this.minSizeY, this.zoomAsset.asset.y + y), this.maxSizeYPoint - window.innerHeight / zoomIndex) :
            0,
        }

        this.$store.commit('moveAsset', {
          id: this.zoomAsset.id,
          asset,
        })

        this.$store.dispatch('moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset,
        })
      }
    },
    zoomChange(oldZoomIndex, zoomIndex) {
      const x = window.innerWidth * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex / 2
      const y = window.innerHeight * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex / 2

      this.$store.commit('moveAsset', {
        id: this.zoomAsset.id,
        asset: {
          zoomIndex,
          x: mainCanvasWidth * zoomIndex > this.viewportWidth ?
            Math.min(Math.max(this.minSizeX, this.zoomAsset.asset.x + x), this.maxSizeXPoint - window.innerWidth / zoomIndex) :
            0,
          y: mainCanvasHeight * zoomIndex > this.viewportHeight ?
            Math.min(Math.max(this.minSizeY, this.zoomAsset.asset.y + y), this.maxSizeYPoint - window.innerHeight / zoomIndex) :
            0,
        },
      })

      this.updateOtherCursor()
    },
    zoomStop() {
      this.$store.dispatch('moveAsset', {
        id: this.zoomAsset.id,
        lessonId: this.zoomAsset.lessonId,
        asset: {
          zoomIndex: this.zoomIndex,
          x: this.zoomAsset.asset.x,
          y: this.zoomAsset.asset.y,
        }
      })
    },
    gestureStart() {
      this.startScale = this.zoomIndex
    },
    gestureChange(event) {
      const oldZoomIndex = this.zoomIndex

      let zoomIndex = this.startScale * event.scale
      zoomIndex = Math.min(Math.max(this.minZoom, zoomIndex), this.maxZoom)

      const x =  event.clientX * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex
      const y =  event.clientY * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex

      this.$store.commit('moveAsset', {
        id: this.zoomAsset.id,
        asset: {
          zoomIndex,
          x: mainCanvasWidth * zoomIndex > this.viewportWidth ?
            Math.min(Math.max(this.minSizeX, this.zoomAsset.asset.x + x), this.maxSizeXPoint - window.innerWidth / zoomIndex) :
            0,
          y: mainCanvasHeight * zoomIndex > this.viewportHeight ?
            Math.min(Math.max(this.minSizeY, this.zoomAsset.asset.y + y), this.maxSizeYPoint - window.innerHeight / zoomIndex) :
            0,
        }
      })
    },
    gestureEnd() {
      this.$store.dispatch('moveAsset', {
        id: this.zoomAsset.id,
        lessonId: this.zoomAsset.lessonId,
        asset: {
          zoomIndex: this.zoomIndex,
          x: this.zoomAsset.asset.x,
          y: this.zoomAsset.asset.y
        }
      })
    },
    onwheel(event) {
      if (event.ctrlKey) {
        event.preventDefault()

        const oldZoomIndex = this.zoomIndex

        let zoomIndex = oldZoomIndex + this.getEventDeltaZoom(event)
        zoomIndex = Math.min(Math.max(this.minZoom, zoomIndex), this.maxZoom)

        const x =  event.clientX * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex
        const y =  event.clientY * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex

        this.$store.commit('moveAsset', {
          id: this.zoomAsset.id,
          asset: {
            zoomIndex,
            x: mainCanvasWidth * zoomIndex > this.viewportWidth ?
              Math.min(Math.max(this.minSizeX, this.zoomAsset.asset.x + x), this.maxSizeXPoint - window.innerWidth / zoomIndex) :
              0,
            y: mainCanvasHeight * zoomIndex > this.viewportHeight ?
              Math.min(Math.max(this.minSizeY, this.zoomAsset.asset.y + y), this.maxSizeYPoint - window.innerHeight / zoomIndex) :
              0,
          }
        })

        this.onwheelStop(event)
      } else {
        if (this.isScrollMainComponent) {
          event.preventDefault()

          this.$store.commit('moveAsset', {
            id: this.zoomAsset.id,
            asset: {
              x: Math.min(Math.max(this.minSizeX, this.zoomAsset.asset.x + this.getEventDeltaX(event)), this.maxSizeX),
              y: Math.min(Math.max(this.minSizeY, this.zoomAsset.asset.y + this.getEventDeltaY(event)), this.maxSizeY),
            }
          })

          this.onwheelStop(event)
        }
      }

      this.updateOtherCursor()
    },
    onwheelStop: debounce(function(event) {
      if (event.ctrlKey) {
        const oldZoomIndex = this.zoomIndex

        let zoomIndex = oldZoomIndex + this.getEventDeltaZoom(event)
        zoomIndex = Math.min(Math.max(this.minZoom, zoomIndex), this.maxZoom)

        const x =  event.clientX * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex
        const y =  event.clientY * (zoomIndex - oldZoomIndex) / zoomIndex / oldZoomIndex

        this.$store.dispatch('moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset: {
            zoomIndex,
            x: mainCanvasWidth * zoomIndex > this.viewportWidth ?
              Math.min(Math.max(this.minSizeX, this.zoomAsset.asset.x + x), this.maxSizeXPoint - window.innerWidth / zoomIndex) :
              0,
            y: mainCanvasHeight * zoomIndex > this.viewportHeight ?
              Math.min(Math.max(this.minSizeY, this.zoomAsset.asset.y + y), this.maxSizeYPoint - window.innerHeight / zoomIndex) :
              0,
          }
        })
      } else {
        this.$store.dispatch('moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset: {
            // x: Math.min(Math.max(this.minSizeX, this.zoomAsset.asset.x + this.getEventDeltaX(event)), this.maxSizeX),
            y: Math.min(Math.max(this.minSizeY, this.zoomAsset.asset.y + this.getEventDeltaY(event)), this.maxSizeY),
          }
        })
      }
    }, 500),
    konvaMouseUpHandler() {
      $('body').removeClass('dragging')
    },
    touchMove(event) {
      event.preventDefault()

      if (
          this.isDevice &&
          this.touchStatus !== 'zooming' &&
          this.userParams.tool === TOOL_POINTER &&
          this.evCache.length === 1
      ) {
        const x = event.targetTouches[0].clientX
        const y = event.targetTouches[0].clientY

        this.$store.commit('moveAsset', {
          id: this.zoomAsset.id,
          asset: {
            x: Math.min(Math.max(this.minSizeX, this.mouseClickPosition.x - x), this.maxSizeX),
            y: Math.min(Math.max(this.minSizeY, this.mouseClickPosition.y - y), this.maxSizeY)
          }
        })

        this.updateOtherCursor()
      }
    },
    touchUp() {
      if (this.touchStatus !== 'zooming' && this.userParams.tool === TOOL_POINTER) {
        this.mouseClickPosition.x = 0
        this.mouseClickPosition.y = 0

        this.$store.dispatch('moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset: {
            zoomIndex: this.zoomIndex,
            x: this.zoomAsset.asset.x,
            y: this.zoomAsset.asset.y,
          }
        })
      }
    },
    konvaMouseDownHandler(e) {
      if (this.userParams.tool === TOOL_POINTER) {
        this.mouseClickPosition.x = e.x + this.zoomAsset.asset.x
        this.mouseClickPosition.y = e.y + this.zoomAsset.asset.y

        addEvent(document.documentElement, 'mousemove', this.drag)

        $('body').addClass('dragging')
      }
    },
    onUp() {
      if (this.userParams.tool === TOOL_POINTER) {
        this.mouseClickPosition.x = 0
        this.mouseClickPosition.y = 0

        removeEvent(document.documentElement, 'mousemove', this.drag)

        this.$store.dispatch('moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset: {
            zoomIndex: this.zoomIndex,
            x: this.zoomAsset.asset.x,
            y: this.zoomAsset.asset.y,
          }
        })
      }
    },
    drag(event) {
      if (!this.dragTimeout) {
        this.dragTimeout = setTimeout(() => {
          this.dragTimeout = null

          this.$store.commit('moveAsset', {
            id: this.zoomAsset.id,
            asset: {
              x: Math.min(Math.max(this.minSizeX, this.mouseClickPosition.x - event.x), this.maxSizeX),
              y: Math.min(Math.max(this.minSizeY, this.mouseClickPosition.y - event.y), this.maxSizeY)
            }
          })

          this.updateOtherCursor()
        }, 100)
      }
    },
    mouseMoved(event) {
      if (!this.mouseMoveTimeout) {
        this.mouseMoveTimeout = setTimeout(() => {
          this.mouseMoveTimeout = null

          this.$socket.emit('cursor-moved', {
            username: this.studentName,
            coords: {
              x: event.clientX / this.zoomIndex + this.zoomAsset.asset.x,
              y: event.clientY / this.zoomIndex + this.zoomAsset.asset.y,
            },
            lessonId: this.$store.state.lesson_id,
          })
        }, 20)
      }
    },
    toggleBodyClass(addRemoveClass, className) {
      const el = document.body;

      if (addRemoveClass === 'addClass') {
        el.classList.add(`${this.role}-${className}`);
      } else {
        el.classList.remove(`${this.role}-${className}`);
      }
    },
    updateOtherCursor() {
      store.commit('updateOtherCursor', {
        coords: {
          x: this.otherCursorPosition.x,
          y: this.otherCursorPosition.y,
        },
      })
    },
    getConnectedUsers() {
      this.$socket.emit('get-connected-users', {
        lessonId: this.$store.state.lesson_id,
      })
    },
  },
  sockets: {
    connect: function() {
      // console.log('socket connected')
      this.$store.commit('setIsSocketConnected', true)

      this.getConnectedUsers()
    },
    disconnect: function() {
      // console.log('socket disconnected')
      this.$store.commit('setIsSocketConnected', false)

      this.getConnectedUsers()
    },
    reconnect: async function() {
      // console.log('socket reconnected')
      this.$store.commit('setIsSocketConnected', true)

      await axios.get(`/api/classroom/${this.getParameterFromURL('roomid')}`)
        .then(res => {
          this.$store.commit('setAssets', res.data)
          this.key++
        })

      this.getConnectedUsers()
    },
    ['cursor-moved']: async function(data) {
      if (data.coords?.x && data.coords?.y) {
        this.otherCursorPosition.x = data.coords.x
        this.otherCursorPosition.y = data.coords.y
      }

      store.commit('updateOtherCursor', data)
    },
    ['asset-added']: async asset => {
      // console.log('asset-added', asset)
      store.commit('addAssets', [asset])
    },
    ['asset-deleted']: async asset => {
      // console.log('asset-deleted', asset)
      store.commit('deleteAsset', asset)
    },
    ['asset-moved']: async asset => {
      // console.log('asset-moved', asset)
      store.commit('moveAsset', asset)
    },
    ['user-connected']: async asset => {
      // console.log('user-connected', asset)
      store.commit('setUsersJoinedClassroom', asset.joinedUsers)
    },
    ['user-disconnected']: function() {
      // console.log('user-disconnected')
      this.getConnectedUsers()
    },
  },
}
</script>

<style scoped>
.zoom-percent,
.classroom-timer {
  position: fixed;
  color: #80b723;
  line-height: 0.8;
  z-index: 99999;
}

.classroom-timer {
  font-size: 16px;
}

.zoom-percent {
  font-weight: 700;
  font-size: 18px;
  transition: opacity 0.5s;
  opacity: 0;
  top: 15px;
  right: 15px;
}

.zoom-percent--show {
  opacity: 1;
}

@media (max-width: 600px) {
  .zoom-percent {
    top: 5px;
  }

  .zoom-percent {
    right: 5px;
    font-size: 16px;
  }

  .classroom-timer {
    font-size: 14px;
  }
}
</style>
