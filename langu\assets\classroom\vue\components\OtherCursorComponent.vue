<template>
  <div
    v-show="isVisible"
    id="other_cursor"
    ref="other_cursor"
    :class="['other_cursor', otherCursor.cursor]"
    :style="{
      top: `${top}px`,
      left: `${left}px`,
      background: `url('/images/classroom/${role}-${otherCursor.cursor}.svg')`
    }"
  >
    <div class="cursor-name" :style="{ backgroundColor: otherCursor.bgColorTooltip }">
      {{ otherCursor.username }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      left: 0,
      top: 0,
    }
  },
  computed: {
    role() {
      return store.state.other_user_role
    },
    otherCursor() {
      return this.$store.getters.getOtherCursor
    },
    isVisible() {
      return !!this.otherCursor.username
    },
    zoom() {
      return this.$store.getters.getZoomAsset.asset
    }
  },
  watch: {
    'otherCursor': {
      handler(data) {
        this.left = (data.coords.x - this.zoom.x) * this.zoom.zoomIndex
        this.top = (data.coords.y - this.zoom.y) * this.zoom.zoomIndex
      }
    }
  },
}
</script>

<style scoped>
.other_cursor {
  position: absolute;
  z-index: 1000000;
  background-size: contain !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

.other_cursor.pointer {
  width: 22px;
  height: 23px;
}

.other_cursor.pencil {
  width: 23px;
  height: 23px;
  margin: -22px 0 0;
}

.other_cursor.eraser {
  width: 25px;
  height: 23px;
  margin: -20px 0 0 -10px;
}

.cursor-name {
  position: absolute;
  display: flex;
  align-items: center;
  height: 20px;
  padding: 0 8px;
  white-space: nowrap;
  color: #fff;
  font-size: 13px;
  line-height: 0.8;
  font-weight: 600;
}

.other_cursor .cursor-name {
  left: 35px;
  bottom: -20px;
}
</style>
