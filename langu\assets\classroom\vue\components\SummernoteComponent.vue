<template>
  <container-component :asset="file" :lock-aspect-ratio="false" :is-draggable-prop="isDraggableProp">
    <textarea id="text_summernote" ref="textarea"></textarea>
  </container-component>
</template>

<script>
import debounce from 'debounce'

export default {
  props: {
    file: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      // text: '',
      // Internal vars
      _summernote: null,
      _noteEditor: null,
      _toolbarWrapperHeight: null,
      _noteEditorMarginBottom: null,
      _noteEditable: null,
      _selection: null,
      changedBySocket: false,
      changedByDatabase: true,
      text: '',
      startCursorPos: null,
      endCursorPos: null,
      pressButtonKey: null,
      currentNode: null,
      previousNode: null,
      offset: null,
      isCaretPositionFound: false,
      isDraggableProp: true,

      // isTextChanged: false,
      // Throttle protectors
      // throttledResizeNoteEditor: null,
      // throttledPositionNoteEditor: null,

      // State handlers
      // isHovered: false,
      // isFocused: false,
      // range: null,
      // areToolbarButtonsHovered: false,
      // asyncChange: false
    }
  },
  computed: {
    editorHeight() {
      return this.file.asset.height
    },
    editorWidth() {
      return this.file.asset.width
    },
    isSocketConnected() {
      return this.$store.state.isSocketConnected
    },
  },
  mounted() {
    this._summernote = $(this.$refs.textarea)

    this.initSummernote()
    this.postInit()

    setTimeout(() => {
      this.setEditableAreaHeight()
    }, 100)
  },
  methods: {
    initSummernote() {
      this._summernote.summernote({
        placeholder: 'Welcome to the new Langu classroom!',
        // height: '100%',
        // height: '60vh',
        // maxHeight: '75vh',
        // minHeight: '15vh',
        width: '100%',
        // minWidth: '20vw',
        // maxWidth:'75vw',
        // zIndex: 20,
        // focus: false,
        airMode: false,
        disableDragAndDrop: true,
        disableResizeEditor: true,
        toolbar: [
          ['style', ['bold', 'underline', 'strikethrough', 'color']],
          ['insert', ['link', 'hr', 'table']],
          ['para', ['ul', 'ol', 'paragraph']]
        ],
        popover: {
          air: [
            ['style', ['bold', 'underline', 'strikethrough', 'color']],
            ['insert', ['link', 'hr', 'picture', 'table']],
            ['para', ['ul', 'ol', 'paragraph']]
          ],
          image: [
            ['float', ['floatLeft', 'floatRight', 'floatNone']],
            ['remove', ['removeMedia']]
          ],
          link: [
            ['link', ['linkDialogShow', 'unlink']]
          ]
        },
        dialogsFade: true,
        dialogsInBody: true,
        callbacks: {
          onInit: () => {
            $('.note-statusbar').hide()

            this._noteEditor = $('.note-editor')
            this._toolbarWrapperHeight = $('.note-toolbar-wrapper').height()
            this._noteEditorMarginBottom = parseInt(this._noteEditor.css('marginBottom'))
            this._noteEditable = $('.note-editable')
            this._toolbar = $('.note-toolbar')
            this._toolbarButtons = $('.note-btn-group')

            this._noteEditable.on('mouseup', this.setStartCursorPos)
            this._noteEditable.on('mouseenter touchstart', () => {
              this.isDraggableProp = false

              if (this._noteEditable.get(0).scrollHeight > this._noteEditable.outerHeight()) {
                this.$store.commit('setIsScrollMainComponent', false)
              }
            })
            this._noteEditable.on('mouseleave touchend', () => {
              this.isDraggableProp = true

              this.$store.commit('setIsScrollMainComponent', true)
            })

            $('.note-insert button').each(function() {
              const tooltip = $(this).attr('title')

              if (tooltip?.includes('Rule')) {
                const newTooltip = tooltip.replace('Rule', 'Line')

                $(this).attr({
                  title: newTooltip,
                  'aria-label': newTooltip,
                })
              }
            })
          },
          onChange: this.textChanged,
          onKeydown: e => {
            this.pressButtonKey = e.keyCode
          },
          onKeyup: e => {
            if ([37, 38, 39, 40].includes(e.keyCode)) {
              this.setStartCursorPos()
            }
          },
          onFocus: () => {
            if (!this.file.asset.text) {
              this.setStartCursorPos()
            }
          },
        },
      })
    },
    postInit() {
      this.text = this.file.asset.text

      this._summernote.summernote('code', this.text)

      // $.each(this._toolbarButtons, (index, btn) => {
      //   btn.addEventListener("mouseover", () => {
      //     this.areToolbarButtonsHovered = true;
      //     $("body").removeClass(`${this.$root.$data.role}-grabber-hover-cursor-pointer`).addClass(`${this.$root.$data.role}-cursor-pointer`)
      //   }, false);
      //   btn.addEventListener("mouseleave", () => {
      //     this.areToolbarButtonsHovered = false;
      //   })
      // })
      //
      //
      // $.each($('a'), (index, link) => {
      //   link.addEventListener("mouseover", () => {
      //     $("body").removeClass(`${this.$root.$data.role}-cursor-pointer`).addClass(`${this.$root.$data.role}-cursor-link`)
      //   }, false);
      //   link.addEventListener("mouseleave", () => {
      //     // cursorsList.forEach(c => $("body").addClass(c))
      //     // cursorsList = [];
      //   })
      // })

      // this._summernote.on('summernote.change', debounce(this.textChanged, 1000));

      // this._noteEditable.on('click focus', () => {
      //   this.isFocused = true;
      //   // let classList = $("body").attr('class').split(/\s+/);
      //   // $.each(classList, function(index, item) {
      //   //     if (item.includes('cursor')) {
      //   //       $("body").removeClass(item);
      //   //     }
      //   // });
      //   // $("body").addClass(`${this.$root.$data.role}-text-cursor`)
      //   // this.range = rangy.saveSelection(this._noteEditable.get(0)); // @TODO: What's next?
      //   this._summernote.summernote('saveRange');
      //
      // });
      // let disabledCursors = [];
      // this._toolbar.on("mouseover", () => {
      //   if(!this.areToolbarButtonsHovered) {
      //     let classList = $("body").attr('class').split(/\s+/);
      //     $.each(classList, function(index, item) {
      //
      //         if (item.includes('cursor')) {
      //           $("body").removeClass(item);
      //           if(!disabledCursors.includes(item)) disabledCursors.push(item)
      //         }
      //     });
      //     $("body").removeClass('teacher-cursor-pointer').addClass(`${this.$root.$data.role}-grabber-hover-cursor-pointer`);
      //   }
      // } )
      // this._toolbar.on("mouseleave", () => {
      //   disabledCursors = disabledCursors.filter(cursor => cursor !== `${this.$root.$data.role}-dragging-cursor-pointer` &&
      //     cursor !== `${this.$root.$data.role}-grabber-hover-cursor-pointer` )
      //   $("body").removeClass(`${this.$root.$data.role}-grabber-hover-cursor-pointer`);
      //   disabledCursors.forEach(cursor => $("body").addClass(cursor));
      //   disabledCursors = [];
      // } )
      // this._noteEditable.on('blur', () => {
      //   this.isFocused = false;
      //   // this.range = null;
      // });
    },
    setStartCursorPos() {
      this.startCursorPos = this.getCaretCharacterOffsetWithin()
    },
    setEditableAreaHeight() {
      const [noteEditorEl] = document.getElementsByClassName('note-editor')
      const [noteToolbarEl] = document.getElementsByClassName('note-toolbar')
      const [noteEditableEl] = document.getElementsByClassName('note-editable')

      if (noteToolbarEl && noteEditableEl) {
        noteEditableEl.style.height = `${noteEditorEl.offsetHeight - noteToolbarEl.offsetHeight - 2}px`
      }
    },
    /**
     * Resize note editor
     *
     * @param height
     * @param width
     * @param type {null|String} if set - will be dispatched emit to sockets
     */
    // resizeNoteEditor(height, width, type = null) {
    //   dispatch(UPDATE_COMPONENT_DATA, type, this.$vnode.data.ref, 'properties.size', {
    //     height: height - this._toolbarWrapperHeight - this._noteEditorMarginBottom,
    //     width: width,
    //   });
    // },
    /**
     * Set position of component and emit to sockets
     *
     * @param left
     * @param top
     */
    // positionNoteEditor(left, top) {
    //   dispatch(UPDATE_COMPONENT_DATA, PERMANENT_UPDATE, this.$vnode.data.ref, 'properties.position', {
    //     left: left,
    //     top: top,
    //   });
    // },
    /**
     * Handle text updates and dispatch them to sockets
     */
    textChanged(text) {
      if (!this.changedBySocket && !this.changedByDatabase) {
        this.text = text

        this.endCursorPos = this.getCaretCharacterOffsetWithin()

        this.$socket.emit('text-editor-updated', {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset: {
            text,
            startPos: this.startCursorPos,
            endPos: this.endCursorPos,
            pressButtonKey: this.pressButtonKey,
          }
        })

        this.startCursorPos = this.endCursorPos

        debounce(() => {
          this.$store.dispatch('updateAssetWithoutSync', {
            id: this.file.id,
            lessonId: this.file.lessonId,
            asset: {
              text,
            }
          })
        }, 100)()
      } else {
        this.changedBySocket = false
        this.changedByDatabase = false
      }
    },
    getCaretCharacterOffsetWithin() {
      const element = this._noteEditable.get(0).childNodes[0]

      if (element) {
        const doc = element.ownerDocument || element.document
        const win = doc.defaultView || doc.parentWindow

        let caretOffset = 0
        let sel

        if (typeof win.getSelection != 'undefined') {
          sel = win.getSelection()

          if (sel.rangeCount > 0) {
            let range = win.getSelection().getRangeAt(0)
            let preCaretRange = range.cloneRange()

            preCaretRange.selectNodeContents(element)
            preCaretRange.setEnd(range.endContainer, range.endOffset)

            caretOffset = preCaretRange.toString().length
          }
        } else if ((sel = doc.selection) && sel.type !== 'Control') {
          let textRange = sel.createRange()
          let preCaretTextRange = doc.body.createTextRange()

          preCaretTextRange.moveToElementText(element)
          preCaretTextRange.setEndPoint('EndToEnd', textRange)

          caretOffset = preCaretTextRange.text.length
        }

        return caretOffset
      }

      return 0
    },
    getCurrentNodeWithPosition(element) {
      for (let i = 0; i < element.length; i++) {
        if (element[i].childNodes.length > 0) {
          this.getCurrentNodeWithPosition(element[i].childNodes)
        } else {
          if (!this.isCaretPositionFound) {
            this.previousNode = this.currentNode
            this.currentNode = element[i]

            if (this.previousNode?.textContent.length) {
              this.offset -= this.previousNode?.textContent.length
            }

            if (this.offset <= this.currentNode?.textContent.length) {
              this.isCaretPositionFound = true

              break
            }
          }
        }
      }
    },
    setCaretPosition(element, position) {
      const range = document.createRange()
      const sel = window.getSelection()

      this.offset = position
      this.currentNode = null
      this.previousNode = null
      this.isCaretPositionFound = false

      this.getCurrentNodeWithPosition(element)

      //move caret to specified offset
      if (this.currentNode != null) {
        range.setStart(this.currentNode, this.offset)
        range.collapse(true)
        sel.removeAllRanges()
        sel.addRange(range)
      }
    },
  },
  sockets: {
    ['text-editor-updated']: function(data) {
      this.changedBySocket = true

      if (data.asset.text !== this._noteEditable.html()) {
        this.text = data.asset.text

        this._summernote.summernote('code', this.text)

        if (this.startCursorPos != null) {
          if (
            (data.asset.endPos < data.asset.startPos && this.startCursorPos >= data.asset.startPos) ||
            (data.asset.endPos > data.asset.startPos && this.startCursorPos > data.asset.startPos)
          ) {
            this.startCursorPos = this.startCursorPos + data.asset.endPos - data.asset.startPos
          }

          if (
              this.startCursorPos > 0 &&
              this.startCursorPos > data.asset.startPos &&
              data.asset.endPos === data.asset.startPos &&
              data.asset.pressButtonKey !== 13
          ) {
            this.startCursorPos = this.startCursorPos - 1
          }

          this.setCaretPosition(this._noteEditable.get(0).childNodes, this.startCursorPos)
        }
      }
    }
  },
  watch: {
    editorHeight() {
      this.setEditableAreaHeight()
    },
    editorWidth() {
      this.setEditableAreaHeight()
    },
    text() {
      if (!this.isDraggableProp) {
        this.$store.commit(
          'setIsScrollMainComponent',
          !(this._noteEditable.get(0).scrollHeight > this._noteEditable.outerHeight())
        )
      }
    },
    isSocketConnected(newValue) {
      this._summernote.summernote(newValue ? 'enable' : 'disable')
    },
  }
}
</script>

<style>
.note-editing-area,
.note-editing-area * {
  cursor: auto !important;
}

.note-editing-area a {
  cursor: pointer !important;
}

.note-editing-area p {
  margin-bottom: 0 !important;
}
</style>
