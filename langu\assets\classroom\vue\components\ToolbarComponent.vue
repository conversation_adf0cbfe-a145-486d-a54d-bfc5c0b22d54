<template>
  <div :class="['toolbar', `toolbar--${role}`]" :style="style">
    <ul class="toolbar-buttons" id="toolbar-buttons" ref="toolbar_buttons">
<!--      <li class="toolbar-button-wrapper toolbar-button-wrapper-replace">-->
<!--        <button class="toolbar-button-item toolbar-button-replace cursor-pointer">-->
<!--          <svg class="toolbar-button-icon" width="19" height="20" viewBox="0 0 19 20">-->
<!--            <use xlink:href="/images/classroom/toolbar.svg#squareline"></use>-->
<!--          </svg>-->
<!--        </button>-->
<!--      </li>-->
      <li class="toolbar-button-wrapper">
        <button
          id="toolbar-switch"
          data-toolbar-default-cursor
          :class="[
            'toolbar-button-item toolbar-button-pointer cursor-pointer',
            { selected: currentTool === 'pointer' },
          ]"
          @click="selectToolClickHandler('pointer', `cursor-pointer`)"
        >
          <svg class="toolbar-button-icon" width="32" height="34" viewBox="0 0 32 34">
            <use xlink:href="/images/classroom/toolbar.svg#pointer"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Default cursor</div>
      </li>
      <li
        class="toolbar-button-wrapper toolbar-button-wrapper-pencil"
        @mouseleave.stop="currentHorizontalMenu = null"
      >
        <button
          :class="[
            'toolbar-button-item toolbar-button-hand cursor-pointer',
             { selected: (
                 currentTool === 'line' ||
                 currentTool === 'circle' ||
                 currentTool === 'triangle' ||
                 currentTool === 'square' ||
                 currentTool === 'pen'
               )
             }
            ]"
          :disabled="isLockedForStudent"
          @click="currentHorizontalMenu = 'toolbar-horizontal'"
        >
          <svg class="toolbar-button-icon" width="33" height="33" viewBox="0 0 33 33">
            <use xlink:href="/images/classroom/toolbar.svg#pencil"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Drawing</div>

        <div
          :class="[
            'toolbar-buttons-horizontal',
            { 'toolbar-show': currentHorizontalMenu === 'toolbar-horizontal' },
          ]"
        >
          <ul>
            <li class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-line">
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'line' },
                ]"
                data-toolbar-tool-line
                @click="selectToolClickHandler('line', 'pencil-cursor')"
              >
                <svg class="toolbar-button-icon" width="39" height="37" viewBox="0 0 39 37">
                  <use xlink:href="/images/classroom/toolbar.svg#draw-line"></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">Draw a line</div>
            </li>
            <li class="toolbar-button-wrapper-horizontal">
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'circle' },
                ]"
                data-toolbar-tool-circle
                @click="selectToolClickHandler('circle', 'pencil-cursor')"
              >
                <svg class="toolbar-button-icon" width="36" height="37" viewBox="0 0 39 40">
                  <use xlink:href="/images/classroom/toolbar.svg#draw-circle"></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">Draw a circle</div>
            </li>
            <li class="toolbar-button-wrapper-horizontal">
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'triangle' },
                ]"
                data-toolbar-tool-triangle
                @click="selectToolClickHandler('triangle', 'pencil-cursor')"
              >
                <svg class="toolbar-button-icon" width="41" height="34" viewBox="0 0 41 34">
                  <use
                    xlink:href="/images/classroom/toolbar.svg#draw-triangle"
                  ></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">Draw a triangle</div>
            </li>
            <li class="toolbar-button-wrapper-horizontal">
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'square' },
                ]"
                data-toolbar-tool-square
                @click="selectToolClickHandler('square', 'pencil-cursor')"
              >
                <svg class="toolbar-button-icon" width="36" height="38" viewBox="0 0 36 38">
                  <use xlink:href="/images/classroom/toolbar.svg#draw-square"></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">Draw a square</div>
            </li>
            <li class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-pencil">
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'pen' },
                ]"
                data-toolbar-tool-pen
                @click="selectToolClickHandler('pen', 'pencil-cursor')"
              >
                <svg class="toolbar-button-icon" width="33" height="33" viewBox="0 0 33 33">
                  <use xlink:href="/images/classroom/toolbar.svg#pencil"></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">Enable drawing tool</div>
            </li>
<!--            <li class="toolbar-button-wrapper-horizontal">-->
<!--              <button-->
<!--                class="toolbar-button-item toolbar-button-item-hand toolbar-button-item-horizontal"-->
<!--                @click="$root.$data.user.tool = 'hand';"-->
<!--              >-->
<!--                <svg class="toolbar-button-icon">-->
<!--                  <use xlink:href="/images/classroom/toolbar.svg#hand"></use>-->
<!--                </svg>-->
<!--              </button>-->
<!--              <div class="hover-btn-info hover-horizontal-button">Navigation tool</div>-->
<!--            </li>-->
          </ul>
        </div>
      </li>
      <li class="toolbar-button-wrapper">
        <button
          :class="[
            'toolbar-button-item cursor-pointer',
            { selected: currentTool === 'eraser' },
          ]"
          data-toolbar-eraser
          :disabled="isLockedForStudent"
          @click="selectToolClickHandler('eraser', 'eraser-cursor')"
        >
          <svg class="toolbar-button-icon" width="35" height="31" viewBox="0 0 35 31">
            <use xlink:href="/images/classroom/toolbar.svg#lastic"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Enable erasing tool</div>
      </li>
      <li class="toolbar-button-wrapper">
        <button
          id="toolbar-button-video"
          class="toolbar-button-item cursor-pointer"
          data-toolbar-add-video
          :disabled="isLockedForStudent"
          @click="toggleVideoInput"
        >
          <svg class="toolbar-button-icon" width="39" height="31" viewBox="0 0 39 31">
            <use xlink:href="/images/classroom/toolbar.svg#play"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Add video</div>
      </li>
<!--      <li class="toolbar-button-wrapper">-->
<!--        <button class="toolbar-button-item" @click="clearLines()">-->
<!--          <svg class="toolbar-button-icon">-->
<!--            <use xlink:href="/images/classroom/toolbar.svg#trash"></use>-->
<!--          </svg>-->
<!--        </button>-->
<!--        <div class="hover-btn-info">Remove all lines</div>-->
<!--      </li>-->
<!--      <li class="toolbar-button-wrapper toolbar-button-wrapper-undo">-->
<!--        <button class="toolbar-button-item toolbar-button-undo">-->
<!--          <svg class="toolbar-button-icon">-->
<!--            <use xlink:href="/images/classroom/toolbar.svg#undo"></use>-->
<!--          </svg>-->
<!--        </button>-->
<!--        <div class="hover-btn-info">Undo</div>-->
<!--      </li>-->
      <li
        v-if="role === 'teacher'"
        class="toolbar-button-wrapper"
      >
        <button
          class="toolbar-button-item cursor-pointer"
          data-toolbar-buzz-student
          :disabled="alertDisabled"
          @click.prevent="buzz"
        >
          <svg class="toolbar-button-icon" width="35" height="38" viewBox="0 0 35 38">
            <use xlink:href="/images/classroom/toolbar.svg#ring"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Buzz student</div>
      </li>
<!--      <li class="toolbar-button-wrapper">-->
<!--        <button-->
<!--          ref="toolbar-button-file"-->
<!--          class="toolbar-button-item toolbar-button-file"-->
<!--          @click="showHorizontalMenu('toolbar-horizontal-file', 'toolbar-button-file')"-->
<!--        >-->
<!--          <svg class="toolbar-button-icon">-->
<!--            <use xlink:href="/images/classroom/toolbar.svg#file"></use>-->
<!--          </svg>-->
<!--        </button>-->
<!--      </li>-->
      <li
        class="toolbar-button-wrapper"
        @mouseleave.stop="currentHorizontalMenu = null"
      >
        <button
          class="toolbar-button-item toolbar-button-hand cursor-pointer"
          @click="currentHorizontalMenu = 'toolbar-horizontal-file'"
        >
<!--          <label-->
<!--            class="popup-load-files-label-upload popup-load-files-label-upload-laptop"-->
<!--          >-->
            <svg class="toolbar-button-icon" width="29" height="38" viewBox="0 0 29 38">
              <use xlink:href="/images/classroom/toolbar.svg#library"></use>
            </svg>
<!--            <input-->
<!--              type="file"-->
<!--              id="upload-library-files-laptop"-->
<!--              multiple-->
<!--              class="popup-load-files-btn-upload"-->
<!--            />-->
<!--          </label>-->
        </button>
        <div class="hover-btn-info">Library</div>

        <div
          :class="[
            'toolbar-buttons-horizontal toolbar-buttons-horizontal-file',
            { 'toolbar-show': currentHorizontalMenu === 'toolbar-horizontal-file' },
          ]"
        >
          <ul>
            <li class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-books">
              <button
                id="load-files-library"
                class="toolbar-button-item toolbar-button-item-horizontal cursor-pointer"
                data-toolbar-library
                @click="openLibrary"
              >
                <svg class="toolbar-button-icon" width="38" height="38" viewBox="0 0 38 38">
                  <use xlink:href="/images/classroom/toolbar.svg#books"></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">Select from library</div>
            </li>
            <li class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-laptop">
              <button
                class="toolbar-button-item toolbar-button-item-horizontal cursor-pointer"
                data-toolbar-computer
              >
                <label
                  class="popup-load-files-label-upload popup-load-files-label-upload-laptop"
                >
                  <svg class="toolbar-button-icon" width="41" height="34" viewBox="0 0 41 34">
                    <use xlink:href="/images/classroom/toolbar.svg#laptop"></use>
                  </svg>
                  <input
                    type="file"
                    id="upload-library-files-laptop"
                    multiple
                    class="popup-load-files-btn-upload"
                    @change="uploadFromComputer"
                  />
                </label>
              </button>
              <div class="hover-btn-info hover-horizontal-button">Upload from computer</div>
            </li>
          </ul>
        </div>
      </li>
      <li
        v-if="role === 'teacher'"
        class="toolbar-button-wrapper"
      >
        <button
          class="toolbar-button-item cursor-pointer"
          data-toolbar-lock
          @click.prevent="toggleStudentRoomStatus"
        >
          <svg class="toolbar-button-icon" width="38" height="50" viewBox="0 0 38 50">
            <use :xlink:href="`/images/classroom/toolbar.svg#${isLocked ? 'lock' : 'unlock'}`"></use>
          </svg>
        </button>
        <div class="hover-btn-info">
          <template v-if="isLocked">
            Enable moving, resizing & drawing for student
          </template>
          <template v-else>
            Disable moving, resizing & drawing for student
          </template>
        </div>
      </li>
      <li class="toolbar-button-wrapper toolbar-button-wrapper-reset">
        <button
          class="toolbar-button-item cursor-pointer"
          data-toolbar-reset
          :disabled="isLockedForStudent"
          @click="reset"
        >
          <svg class="toolbar-button-icon" width="36" height="36" viewBox="0 0 37 37">
            <use xlink:href="/images/classroom/toolbar.svg#restore"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Restore whiteboard & video to original positions</div>
      </li>
      <li class="toolbar-button-wrapper toolbar-button-wrapper-exit">
        <button
          class="toolbar-button-item cursor-pointer"
          data-toolbar-exit
          @click="exitClass"
        >
          <svg class="toolbar-button-icon" width="36" height="36" viewBox="0 0 37 37">
            <use xlink:href="/images/classroom/toolbar.svg#exit"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Exit class</div>
      </li>
      <li
        v-if="role === 'teacher'"
        class="toolbar-button-wrapper"
        @mouseleave.stop="currentHorizontalMenu = null"
      >
        <button
          class="toolbar-button-item toolbar-button-hand cursor-pointer"
          :disabled="isLessonFinished"
          @click="currentHorizontalMenu = 'toolbar-horizontal-finish'"
        >
          <svg class="toolbar-button-icon" width="43" height="38" viewBox="0 0 43 38">
            <use xlink:href="/images/classroom/toolbar.svg#tick"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Finish class</div>

        <div
          :class="[
          'toolbar-buttons-horizontal toolbar-buttons-horizontal-file',
          { 'toolbar-show': currentHorizontalMenu === 'toolbar-horizontal-finish' },
        ]"
        >
          <ul>
            <li class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-finish">
              <form
                id="lesson-finish-form"
                v-bind:action="finishLessonUrl"
                method="POST"
              >
                <input
                  :value="lessonId"
                  type="hidden"
                  name="lessonId"
                />
                <button
                  class="toolbar-button-item toolbar-button-item-horizontal cursor-pointer"
                  data-toolbar-finish
                  type="submit"
                >
                  <svg class="toolbar-button-icon" width="43" height="38" viewBox="0 0 43 38">
                    <use xlink:href="/images/classroom/toolbar.svg#tick"></use>
                  </svg>
                </button>
                <div class="hover-btn-info hover-horizontal-button">Finish class</div>
              </form>
            </li>
          </ul>
        </div>
      </li>
      <li
        v-if="role === 'student'"
        class="toolbar-button-wrapper"
      >
        <button
          class="toolbar-button-item cursor-pointer"
          disabled="disabled"
        >
          <svg class="toolbar-button-icon" width="38" height="50" viewBox="0 0 38 50">
            <use :xlink:href="`/images/classroom/toolbar.svg#${isLocked ? 'lock' : 'unlock'}`"></use>
          </svg>
        </button>
        <div class="hover-btn-info">
          <template v-if="isLocked">
            Moving, resizing & drawing are disabled
          </template>
          <template v-else>
            Classroom controls are unlocked
          </template>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import Tool from '../mixins/Tool'
import {
  defaultWidth,
  mainCanvasWidth,
  mainCanvasHeight
} from '../../core/helpers/constants'

export default {
  mixins: [Tool],
  props: {
    buzzUrl: {
      type: String,
      required: true,
    },
    finishLessonUrl: {
      type: String,
      required: true,
    },
    studentId: {
      type: String,
      required: true,
    },
    file: {
      type: Object,
      required: true,
    },
    width: {
      type: Number,
      required: true,
    },
    height: {
      type: Number,
      required: true,
    },
    scale: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      buzzed: false,
      studentStatus: null,
      currentTool: 'pointer',
      currentHorizontalMenu: null,
      offset: 5,
    }
  },
  computed: {
    isCanvasOversizeX() {
      return mainCanvasWidth > this.width
    },
    isScaledCanvasOversizeX() {
      return mainCanvasWidth * this.scale > this.width
    },
    isCanvasOversizeY() {
      return mainCanvasHeight > this.height
    },
    isScaledCanvasOversizeY() {
      return mainCanvasHeight * this.scale > this.height
    },
    style() {
      return {
        bottom: this.isScaledCanvasOversizeY ?
          '10px' :
          `${this.height - mainCanvasHeight * this.scale + this.offset * 2}px`,
        right: this.isScaledCanvasOversizeX ?
          '10px' :
          `${this.width - mainCanvasWidth * this.scale + this.offset * 2}px`,
      }
    },
    alertDisabled() {
      return this.buzzed || this.studentStatus !== 'online'
    },
    maxIndex() {
      return store.state.maxIndex + 100
    },
    role() {
      return this.$store.getters.role
    },
    isLocked() {
      return this.file?.asset?.isLocked
    },
    isLockedForStudent() {
      return this.isLocked && this.role === 'student'
    },
    lessonId() {
      return this.file?.lessonId
    },
    isLessonFinished() {
      return this.$store.getters.isLessonFinished
    },
  },
  watch: {
    isLockedForStudent(newValue, oldValue) {
      if (newValue) {
        this.resetCurrentValues()
        store.commit('closeVideoInput')
      }
    },
  },
  mounted() {
    this.refreshStatusOnline()
    setInterval(this.refreshStatusOnline, 10000)
  },
  beforeDestroy() {
    this.resetCurrentValues()
  },
  methods: {
    selectToolClickHandler(toolName, cursorName) {
      this.currentTool = toolName
      this.currentHorizontalMenu = null

      this.setTool(toolName, cursorName)
    },
    uploadFromComputer(event) {
      this.currentHorizontalMenu = null

      this.uploadFiles(event.target.files)
    },
    refreshStatusOnline() {
      $.ajax({
        url: '/user/statusonline',
        method: 'post',
        dataType: 'json',
        data : { users : [this.studentId] },
        success: result => {
          this.studentStatus = result[this.studentId]
        },
        error: result => {
          console.log(result)
        }
      })
    },
    exitClass() {
      window.location.pathname = '/user/lessons'
    },
    reset() {
      let i = 1
      this.$store.state.assets.forEach(asset => {
        i++
        switch (asset.asset.type) {
          case 'shape':
          case 'lock':
            break
          case 'editor':
            asset.asset.width = (this.isCanvasOversizeX ? this.width : mainCanvasWidth) * 0.66

            let height = (this.isCanvasOversizeY ? this.height : mainCanvasHeight) * 0.8

            if (height > 1200) {
              height = 1200
            }

            if (height < 400) {
              height = 400
            }

            asset.asset.height = height - this.offset * 2
            asset.asset.top = this.offset
            asset.asset.left = this.offset
            asset.asset.index = 1
            break
          case 'voxeet':
          case 'tokbox':
            asset.asset.width = 400
            asset.asset.height = 300
            asset.asset.top = this.offset
            asset.asset.left = (this.isCanvasOversizeX ? this.width : mainCanvasWidth ) - asset.asset.width - this.offset
            asset.asset.index = i
            break
          case 'image':
            const ratio1 = defaultWidth / asset.asset.width
            asset.asset.width = defaultWidth
            asset.asset.height = asset.asset.height * ratio1
            asset.asset.top = 200
            asset.asset.left = 650
            asset.asset.index = i
            break
          case 'pdf':
            const ratio2 = defaultWidth / asset.asset.width
            asset.asset.width = defaultWidth
            asset.asset.height = asset.asset.height * ratio2
            asset.asset.top = 200
            asset.asset.left = 650
            asset.asset.index = i
            break
          case 'video':
            const ratio3 = defaultWidth / asset.asset.width
            asset.asset.width = defaultWidth
            asset.asset.height = asset.asset.height * ratio3
            asset.asset.top = 50
            asset.asset.left = 550
            asset.asset.index = i
            break
          case 'zoom':
            asset.asset.zoomIndex = 1
            asset.asset.x = 0
            asset.asset.y = 0
            break
          default:
        }

        this.$store.commit('moveAsset', {
          id: asset.id,
          asset: asset.asset
        })
        this.$store.dispatch('moveAsset', {
          id: asset.id,
          lessonId: asset.lessonId,
          asset: asset.asset
        })
      })
    },
    buzz() {
      this.buzzed = true

      setTimeout(() => {
        this.buzzed = false
      }, 30000)

      $.ajax(this.buzzUrl, {
        cache: false,
      })
    },
    toggleVideoInput() {
      store.commit('toggleVideoInput')
    },
    openLibrary() {
      this.currentHorizontalMenu = null

      store.commit('toggleLibrary')
    },
    toggleStudentRoomStatus() {
      const asset = { isLocked: !this.isLocked }

      this.$store.commit('moveAsset', {
        id: this.file.id,
        asset,
      })

      this.$store.dispatch('moveAsset', {
        id: this.file.id,
        lessonId: this.lessonId,
        asset,
      })
    },
    resetCurrentValues() {
      this.currentTool = 'pointer'
      this.currentHorizontalMenu = null
    },
  },
}
</script>

<style scoped>
.toolbar {
  position: fixed;
  z-index: 999999999 !important;
}

label.popup-load-files-label-upload {
  margin-right: 0;
}
.toolbar-buttons-wrapper {
  position: absolute;
  top: 50%;
  right: 2%;
  transform: translateY(-50%);
}

.cursor-pointer,
.cursor-pointer * {
  cursor: pointer !important;
}

.toolbar-buttons {
  margin-bottom: 0;
  padding: 8px 0;
}

.toolbar-buttons-horizontal.toolbar-show,
.toolbar-buttons-horizontal-file.toolbar-show {
  display: flex !important;
}

.toolbar-buttons li {
  list-style: none;
  z-index: 2000;
}

.toolbar-button-wrapper form {
  display: inline-block;
  width: 100%;
}

.toolbar-button-wrapper-horizontal {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  position: relative;
}

.toolbar-button-wrapper-pencil > button {
  padding: 9px;
}

.toolbar-button-wrapper-exit button {
  padding-left: 7px;
  padding-right: 10px;
}

.toolbar-button-wrapper-reset button {
  padding-left: 10px;
  padding-right: 10px;
}

.toolbar-button-wrapper-finish button {
  padding-right: 7px;
}

.toolbar-button-wrapper-horizontal-books button {
  padding: 9px;
}

.toolbar-button-wrapper-horizontal-laptop button {
  padding-top: 10px;
}

/*.toolbar-button-wrapper-replace,*/
/*.toolbar-button-wrapper-undo {*/
/*  transform: none;*/
/*  padding: 0;*/
/*}*/

/*.toolbar-button-wrapper-replace {*/
/*  position: absolute;*/
/*  height: 24px;*/
/*  top: -6px;*/
/*  left: 0;*/
/*  background-color: #fff;*/
/*  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);*/
/*  border-radius: 6px;*/
/*  z-index: 2001 !important;*/
/*}*/

/*.toolbar-button-replace {*/
/*  width: 100%;*/
/*  height: 100%;*/
/*  max-height: 100%;*/
/*  margin-bottom: 0;*/
/*  padding: 5px 0;*/
/*}*/

/*.toolbar-button-undo {*/
/*  padding: 8px 0;*/
/*  margin-bottom: 8px;*/
/*  width: 100%;*/
/*  border-radius: 6px;*/
/*  box-shadow: 0 4px 2px -2px rgba(0, 0, 0, 0.15);*/
/*}*/

.toolbar-buttons-horizontal > ul {
  display: flex;
  padding: 0 10px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
}

.toolbar-button-wrapper-horizontal-draw-line button {
  padding: 10px 5px 6px 10px;
}

.toolbar-button-wrapper-horizontal-draw-pencil button {
  padding: 9px;
}

.toolbar-button-item-hand {
  padding-left: 0;
}

.toolbar-buttons-horizontal > ul li:first-child,
.toolbar-buttons-horizontal > ul li:first-child button {
  border-bottom-left-radius: 6px !important;
  border-top-left-radius: 6px !important;
}

.toolbar-buttons-horizontal > ul li:last-child,
.toolbar-buttons-horizontal > ul li:last-child button {
  border-bottom-right-radius: 6px !important;
  border-top-right-radius: 6px !important;
}

#toolbar-switch {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.toolbar-button-wrapper .toolbar-button-item:disabled svg {
  color: #c6c6c6 !important;
  fill: #c6c6c6 !important;
  stroke: #c6c6c6 !important;
}

.toolbar--student .toolbar-button-wrapper:hover > button:enabled > svg,
.toolbar--student .toolbar-button-wrapper:hover > form > button:enabled > svg,
.toolbar--student .toolbar-button-wrapper-horizontal:hover svg,
.toolbar--student .toolbar-button-item.selected svg {
  color: #3c87f8 !important;
}

.toolbar--teacher .toolbar-button-wrapper:hover > button:enabled > svg,
.toolbar--teacher .toolbar-button-wrapper:hover > form > button:enabled > svg,
.toolbar--teacher .toolbar-button-wrapper-horizontal:hover svg,
.toolbar--teacher .toolbar-button-item.selected svg {
  color: #80b723 !important;
}

.toolbar-button-item svg {
  color: #2d2d2d;
}

.hover-btn-info-horizontal {
  top: -20px;
  right: -60%;
}

/*.toolbar-button-replace + .hover-btn-info {*/
/*  top: 40%;*/
/*}*/

/*.selected {*/
/*  border-bottom: none;*/
/*}*/
</style>

<style module>
/* .teacher_horizontal_hover {
  box-shadow: rgb(127, 184, 2) 0 2px 0 0, rgb(127, 184, 2) -2px -2px 0 0, rgb(127, 184, 2) -2px 2px 0 0, rgb(127, 184, 2) 2px -2px 0 0;
} */

.teacher_toolbar_hover {
  box-shadow: 0 0 2px 2px rgb(127, 184, 2) !important;
}

/* .student_horizontal_hover {
  box-shadow: rgb(60, 135, 248) 0 2px 0 0, rgb(60, 135, 248) -2px -2px 0 0, rgb(60, 135, 248) -2px 2px 0 0, rgb(60, 135, 248) 2px -2px 0 0;
} */

.student_toolbar_hover {
  box-shadow: 0 0 2px 2px rgb(60, 135, 248) !important;
}
</style>
