<template>
  <div
    class="video-component"
    :style="{ position: 'absolute', zIndex: maxIndex }"
  >
<!--    <slot v-if="!this.url">-->
      <div class="video-load-wrap">
        <form @submit.prevent="submit">
          <div class="input-wrap">
            <input
                id="video-load-value"
                v-model="inputUrl"
                ref="input"
                class="video-load-input cursor-auto"
                placeholder="Enter a YouTube URL"
                type="text"
            />
            <div v-if="!isValid" class="error">
              Invalid URL or video ID
            </div>
          </div>
          <div class="video-buttons-wrap">
            <button
              id="video-load-btn"
              class="video-load-btn"
              type="submit"
            >
              Load Video
            </button>
            <span
              id="close-video-load"
              v-on:click="closePopup"
              class="video-load-cross"
            >&times;</span>
          </div>
        </form>
      </div>
<!--    </slot>-->
<!--    <slot v-else>-->
<!--      <h1>Video loaded!</h1>-->
<!--    </slot>-->
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputUrl: null,
      isValid: true,
    }
  },
  computed: {
    isVideoInputOpened() {
      return store.state.isVideoInputOpened
    },
    maxIndex() {
      return store.state.maxIndex + 2
    },
    role() {
      return this.$store.getters.role
    },
  },
  mounted() {
    this.$refs.input.focus()
  },
  watch: {
    inputUrl() {
      this.isValid = true
    },
  },
  methods: {
    closePopup() {
      store.state.isVideoInputOpened = false
    },
    async submit() {
      let video_id = this.inputUrl

      if (video_id?.includes('v=')) {
        video_id = this.inputUrl.split('v=')[1]

        const ampersandPosition = video_id.indexOf('&')

        if (ampersandPosition !== -1) {
          video_id = video_id.substring(0, ampersandPosition)
        }
      }

      if (!video_id || !video_id.match(/[^"&=\s?]{11}/)) {
        this.isValid = false

        return false
      }

      await store.dispatch('createAsset', {
        type: 'video',
        videoId: video_id,
        index: this.maxIndex + 1,
        shapes: [],
        owner: this.role,
      })

      store.commit('toggleVideoInput')
    }
  }
}
</script>

<style scoped>
.video-component,
.video-component * {
  cursor: auto !important;
}

.video-component button,
.video-component .video-load-cross {
  cursor: pointer !important;
}

.video-load-wrap {
  position: absolute;
  top: 20px;
  left: 50vw;
  transform: translateX(-50%);
  background: #ebecf0;
  border-radius: 3px;
  padding: 7px 10px;
  min-width: 500px;
  z-index: 1999;
}

@media (max-width: 600px) {
  .video-load-wrap {
    min-width: 450px;
  }
}

@media (max-width: 480px) {
  .video-load-wrap {
    min-width: 300px;
  }
}

.input-wrap {
  position: relative;
  padding-bottom: 10px;
}

input.video-load-input {
  padding: 5px;
  border-radius: 3px;
}

.input-wrap .error {
  position: absolute;
  left: 0;
  bottom: 8px;
  padding-left: 6px;
  font-size: 12px;
  color: #f84f3c;
}

.video-buttons-wrap {
  display: flex;
  align-items: center;
}

.video-load-btn {
  background: #5aac44;
  color: #fff;
  border: none;
  outline: none;
  padding: 6px 12px;
  border-radius: 3px;
}

.video-load-cross {
  margin-left: 10px;
  font-size: 30px;
  color: #6b778c;
  vertical-align: middle;
}

.video_annotations {
  width: 2px;
  height: 12px;
  position: absolute;
  top: 4px;
  background: red;
}

#video-window {
  min-width: 240px;
  min-height: 178px;
  width: 400px;
  height: 300px;
  background: black;
  z-index: 1001;
}
</style>
