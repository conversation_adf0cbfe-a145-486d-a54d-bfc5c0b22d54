<template lang="html">
  <container-component :asset="file" :child-header-height="80">
    <container-header-component
      :file="file"
      :title="player.config.title"
    >
      <div :id="`video-${file.id}`" ref="childComponent">
        <div
          :id="`player-${file.id}`"
          :data-plyr-embed-id="file.asset.videoId"
          data-plyr-provider="youtube"
        ></div>
      </div>
      <div class="transparent">
        <konva-component
          v-if="file && width && height"
          :file="file"
          :width="width"
          :height="height"
          :scale="scale"
          :current-time="currentTime"
          :style="{
            marginTop: `-${height}px`
          }"
        ></konva-component>
      </div>
    </container-header-component>
  </container-component>
</template>

<script>
import Plyr from 'plyr'
import { defaultWidth } from '../../core/helpers/constants'

export default {
  props: {
    file: {
      type: Object,
      required: true,
    }
  },
  data() {
    return {
      timeMarkerContainer: null,
      currentTime: 0,
      duration: null,
      loaded: false,
      changedBySocket: false,
      player: {
        config: {
          title: '',
        },
      },
    }
  },
  computed: {
    scale() {
      if (this.width) {
        return this.width / defaultWidth
      }

      return 1
    },
    width() {
      return this.file.asset.width
    },
    height() {
      return this.file.asset.height
    },
    shapes() {
      return [ ...this.file.asset.shapes ]
    },
    zoomIndex() {
      return this.$store.getters.getZoomAsset?.asset?.zoomIndex ?? 1
    },
    isSocketConnected() {
      return this.$store.state.isSocketConnected
    },
  },
  beforeDestroy() {
    this.player.destroy()
  },
  mounted() {
    const options = {
      controls: ['play', 'progress', 'current-time', 'mute', 'volume', 'captions', 'settings', 'pip'],
      clickToPlay: false,
      hideControls: false,
      resetOnEnd: true,
      volume: this.file.asset.volume || 1,
      muted: this.file.asset.muted || false,
      storage: {
        enabled: false,
      },
      speed: {
        selected: this.file.asset.speed || 1,
        options: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
      },
    }

    this.player = new Plyr(`#player-${this.file.id}`, options)
    this.player.on('ready', () => {
      this.player.on('play', this.playVideo)
      this.player.on('pause', this.pauseVideo)
      this.player.on('ratechange', this.changeSpeed)
      this.player.on('timeupdate', this.timeupdate)

      this.currentTime = this.file.asset.currentTime || 0
      this.player.currentTime = this.currentTime
      this.player.pause()
      this.duration = this.player.duration

      this.resize()
      this.createTimeMarketContainer()

      this.loaded = true
    })

    new ResizeObserver(this.resize).observe(this.$refs.childComponent)
  },
  methods: {
    resize() {
      if (!this.width) {
        const width = this.$refs.childComponent.getBoundingClientRect().width / this.zoomIndex
        const height = this.$refs.childComponent.getBoundingClientRect().height / this.zoomIndex
        const asset = { width, height, originalWidth: defaultWidth }

        this.$store.commit('moveAsset', {
          id: this.file.id,
          asset,
        })

        this.$store.dispatch('moveAsset', {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset,
        })
      }
    },
    onPlayerChange(e, asset) {
      if (!this.changedBySocket) {
        const data = {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset
        }

        if (this.isSocketConnected) {
          this.$socket.emit('video-updated', data)
        }

        this.$store.dispatch('updateAssetWithoutSync', data)
      } else {
        this.changedBySocket = false
      }
    },
    playVideo(e) {
      this.onPlayerChange(e, {
        currentTime: e.detail.plyr.media.currentTime,
        play: true
      })
    },
    pauseVideo(e) {
      this.onPlayerChange(e, {
        currentTime: e.detail.plyr.media.currentTime,
        play: false
      })
    },
    timeupdate() {
      this.currentTime = this.player.currentTime
    },
    changeSpeed(e) {
      this.onPlayerChange(e, {
        speed: e.detail.plyr.config.speed.selected
      })
    },
    createTimeMarketContainer() {
      this.timeMarkerContainer = document.getElementById(`time-markers-${this.file.id}`)

      if (!this.timeMarkerContainer) {
        const el = document.getElementById(`video-${this.file.id}`).getElementsByClassName('plyr__progress__buffer')[0]
        this.timeMarkerContainer = document.createElement('div')

        this.timeMarkerContainer.setAttribute('id', `time-markers-${this.file.id}`)
        this.timeMarkerContainer.setAttribute('class', 'time-markers')

        el.before(this.timeMarkerContainer)

        if (this.shapes.length) {
          this.shapes.forEach(shape => this.addMarker(shape))
        }
      }
    },
    addMarker(shape) {
      if (shape) {
        const position = (shape.time * 100 / this.duration).toFixed(2).toString()
        const span = document.createElement('span')

        span.setAttribute('class', 'time-markers-item')
        span.setAttribute('style', `left: ${position}%;`)

        span.addEventListener('click', () => {
          this.currentTime = shape.time
          this.player.currentTime = shape.time

          this.player.play()
        }, false)

        this.timeMarkerContainer.appendChild(span)
      }
    }
  },
  sockets: {
    ['video-updated']: function(data) {
      this.changedBySocket = true

      if (data.asset.speed) {
        this.player.speed = data.asset.speed
      }

      if (data.asset.play) {
        this.player.muted = true
        this.player.currentTime = data.asset.currentTime
        this.player.play()
      } else {
        this.player.pause()
      }
    }
  },
  watch: {
    'shapes': function(newShapes, oldShapes) {
      if (newShapes.length) {
        const diff = newShapes.filter(x => !oldShapes.includes(x))

        this.addMarker(diff[0])
      }
    }
  }
}
</script>

<style>
.plyr__controls {
  background: #5e5e5e !important;
  padding: 4px !important;
  position: fixed !important;
}

.plyr__progress .time-markers {
  position: absolute;
  top: 50%;
  left: 0;
  width: calc(100% + var(--plyr-range-thumb-height,13px));
  height: var(--plyr-range-track-height,5px);
  margin-top: calc((var(--plyr-range-track-height,5px)/ 2) * -1);
  margin-left: calc(var(--plyr-range-thumb-height,13px) * -.5);
}

.plyr__progress .time-markers span {
  position: absolute;
  top: 0;
  display: block;
  width: 1px;
  height: 100%;
  background-color: #fff;
  z-index: 2;
}
</style>
