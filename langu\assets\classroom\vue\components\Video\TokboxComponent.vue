<template>
  <div>
    <container-component
      :asset="file"
      :hover-enabled="false"
    >
      <div
        id="video-window"
        :class="[
        'tokbox-component cursor-before-grab',
        { 'video-window--is-fullscreen': settings.isFullscreenEnabled },
      ]"
    >
      <div id="tokbox-my-stream-placeholder" class="local-stream hr-flip"></div>
      <div
        id="tokbox-other-stream-placeholder"
        class="remote-stream hr-flip"
      ></div>

        <video-actions-component
          :is-joined="isJoined"
          :settings="{ ...settings, isMuted, isVideoEnabled }"
          switch-label="Switch to primary video player"
          @switch-video-player="switchVideoPlayer"
          @toggle-video="toggleVideo"
          @toggle-audio="toggleAudio"
          @toggle-full-screen="toggleFullScreen"
          @toggle-screen-share="toggleScreenShare"
        ></video-actions-component>
      </div>
    </container-component>

    <container-component
      v-show="isScreenShareEnabled"
      :asset="screenShareAsset"
      :hover-enabled="false"
    >
      <div class="tokbox-component cursor-before-grab">
        <div
          id="tokbox-screenshare-stream-placeholder"
          class="remote-stream"
        ></div>
      </div>
    </container-component>
  </div>
</template>

<script>
import OT from '@opentok/client'
import VideoActionsComponent from './VideoActionsComponent'
import VoxeetSDK from "@voxeet/voxeet-web-sdk";

export default {
  name: 'TokboxComponent',
  components: { VideoActionsComponent },
  props: {
    file: {
      type: Object,
      required: true,
    },
    screenShareAsset: {
      type: Object,
      required: true
    },
  },
  data: () => ({
    isJoined: false,
    isScreenShareEnabled: false,
    settings: {
      isScreenShareEnabled: false,
      isFullscreenEnabled: false
    },
    session: null,
    publisher: null,
    screenSharingPublisher: null,
    pubOptions: {
      insertMode: 'append',
      width: '100%',
      height: '100%',
      usePreviousDeviceSelection: true,
      name: classroom.user.name,
      style: {
        nameDisplayMode: 'off'
      },
      fitMode: 'contain',
      mirror: false,
      showControls: false,
      frameRate: 15
    },
    isMediaChanging: false,
  }),
  computed: {
    apiKey() {
      return window.classroom.ot.apiKey
    },
    sessionId() {
      return window.classroom.ot.sessionId
    },
    token() {
      return window.classroom.ot.token
    },
    isVideoEnabled() {
      return this.file.asset?.settings?.[window.langu_role]?.isVideoEnabled ?? true
    },
    isMuted() {
      return this.file.asset?.settings?.[window.langu_role]?.isMuted ?? false
    },
  },
  created() {
    this.init()

    const beforeUnloadListener = (event) => {
      event.preventDefault()
      this.closeStream()
    }

    document.addEventListener('beforeunload', beforeUnloadListener, { once: true })
    document.addEventListener('fullscreenchange', () => {
      if (!document.fullscreenElement) {
        this.settings.isFullscreenEnabled = false
      }
    })
  },
  beforeDestroy() {
    this.closeStream()
  },
  methods: {
    init() {
      if (!OT.checkSystemRequirements()) {
        return alert('Sorry. Your browser does not support our streaming protocols. Please try latest version of Google Chrome or Mozilla Firefox.')
      }

      this.session = OT.initSession(this.apiKey, this.sessionId)

      this.session.connect(this.token, err => {
        if (err) {
          return this.errorHandler(err)
        }

        this.isJoined = true
        this.publisher = OT.initPublisher(
        'tokbox-my-stream-placeholder',
          {
            ...this.pubOptions,
            publishAudio: !this.isMuted,
            publishVideo: this.isVideoEnabled
          })
        this.session.publish(this.publisher, this.errorHandler)

        this.publisher.on({
          streamDestroyed: event => {
            event.preventDefault()

            if (this.session && this.publisher) {
              this.session.disconnect()
              this.session.unpublish(this.publisher)
              this.session.off()
            }
          }
        })

        this.session.on({
          streamCreated: event => {
            let el = 'tokbox-other-stream-placeholder'

            if (event.stream.videoType === 'screen') {
              el = 'tokbox-screenshare-stream-placeholder'
              this.isScreenShareEnabled = true
            }

            this.session.subscribe(event.stream, el, {
              insertMode: 'append',
              fitMode: 'contain',
              style: {
                audioLevelDisplayMode: 'off',
                buttonDisplayMode: 'off',
                nameDisplayMode: 'off'
              },
              mirror: false
            }, error => {
              if (error) {
                alert('Sorry, we encountered an error trying to connect you to the remote stream. Please report to Langu Team. Error code: ' + error.code + ', details: ' + error.message)
              }
            })
          },
          streamPropertyChanged: event => {
            const asset = {
              settings: {
                ...this.file.asset.settings,
                [window.langu_role]: {
                  isVideoEnabled: this.isVideoEnabled,
                  isMuted: this.isMuted,
                }
              }
            }

            if (
              this.isMediaChanging &&
              (event.changedProperty === 'hasVideo' || event.changedProperty === 'hasAudio')
            ) {
              if (event.changedProperty === 'hasVideo') {
                asset.settings[window.langu_role].isVideoEnabled = event.newValue
              }

              if (event.changedProperty === 'hasAudio') {
                asset.settings[window.langu_role].isMuted = !event.newValue
              }

              this.$store.commit('moveAsset', {
                id: this.file.id,
                asset,
              })
              this.$store.dispatch('moveAsset', {
                id: this.file.id,
                lessonId: this.file.lessonId,
                asset,
              }).finally(() => this.isMediaChanging = false)
            }
          },
          streamDestroyed: async event => {
            if (event.stream.videoType === 'screen') {
              this.isScreenShareEnabled = false
            }
          }
        })
      })
    },
    errorHandler(e) {
      if (e) {
        this.isJoined = false

        const message = e.code === 1500 ?
          'We are unable to access your video camera. Either the browser has not been given permissions or the camera is in use by another program.' :
          e.message

        alert(message)
      }
    },
    toggleVideo() {
      this.isMediaChanging = true
      this.publisher.publishVideo(!this.isVideoEnabled)
    },
    toggleAudio() {
      this.isMediaChanging = true
      this.publisher.publishAudio(this.isMuted)
    },
    toggleFullScreen() {
      this.settings.isFullscreenEnabled = !this.settings.isFullscreenEnabled

      if (this.settings.isFullscreenEnabled) {
        let elem = document.getElementById('video-window')

        if (elem.requestFullscreen) {
          elem.requestFullscreen()
        } else if (elem.mozRequestFullScreen) { /* Firefox */
          elem.mozRequestFullScreen()
        } else if (elem.webkitRequestFullscreen) { /* Chrome, Safari & Opera */
          elem.webkitRequestFullscreen()
        } else if (elem.msRequestFullscreen) { /* IE/Edge */
          elem = window.top.document.body //To break out of frame in IE
          elem.msRequestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen()
        } else if (document.msExitFullscreen) {
          window.top.document.msExitFullscreen()
        }
      }
    },
    toggleScreenShare() {
      this.settings.isScreenShareEnabled = !this.settings.isScreenShareEnabled

      if (this.settings.isScreenShareEnabled) {
        this.screenSharingPublisher = OT.initPublisher('tokbox-my-screenshare-placeholder', {
          videoSource: 'screen',
          showControls: false,
          resolution: '640x480',
          frameRate: 7
        })

        this.session.publish(this.screenSharingPublisher, this.errorHandler)

        this.screenSharingPublisher.on({
          streamDestroyed: event => {
            event.preventDefault()

            this.screenSharingPublisher.disconnect()
            this.session.unpublish(this.screenSharingPublisher)
          }
        })
      } else {
        this.screenSharingPublisher.destroy()
      }
    },
    async closeStream() {
      if (this.publisher) {
        await this.publisher.destroy()
      }

      if (this.screenSharingPublisher) {
        await this.screenSharingPublisher.destroy()
      }
    },
    switchVideoPlayer() {
      this.$store.dispatch('deleteAsset', this.file)
      this.$store.dispatch('createAsset', {
        ...this.file.asset,
        type: 'voxeet'
      })
    },
  }
}
</script>
