<template>
  <div class="stream-controls">
    <div
      id="video-window-buttons"
      class="video-window-buttons-wrap"
    >
      <div class="stream-controls-wrapper cursor-auto">
        <div class="toolbar-button-wrapper">
          <button
              class="toolbar-button-item cursor-pointer"
              data-stream-toggle-video
              type="button"
              :disabled="!isJoined"
              @click="$emit('toggle-video')"
          >
            <svg
                v-if="settings.isVideoEnabled"
                class="toolbar-button-icon"
                width="51"
                height="33"
                viewBox="0 0 51 33"
            >
              <use
                  xlink:href="/images/classroom/videocam.svg#videocam"
              ></use>
            </svg>
            <svg
                v-else-if="!settings.isVideoEnabled"
                class="toolbar-button-icon"
                width="39"
                height="33"
                viewBox="0 0 39 33"
            >
              <use
                  xlink:href="/images/classroom/videocam.svg#videocam_off"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="settings.isVideoEnabled">Turn off camera</template>
            <template v-else>Turn on camera</template>
          </div>
        </div>
        <div class="toolbar-button-wrapper">
          <button
              class="toolbar-button-item cursor-pointer"
              data-stream-toggle-audio
              type="button"
              :disabled="!isJoined"
              @click="$emit('toggle-audio')"
          >
            <svg
                v-if="!settings.isMuted"
                class="toolbar-button-icon"
                width="23"
                height="33"
                viewBox="0 0 23 33"
            >
              <use
                  xlink:href="/images/classroom/microphone.svg#microphone"
              ></use>
            </svg>
            <svg
                v-if="settings.isMuted"
                class="toolbar-button-icon"
                width="31"
                height="34"
                viewBox="0 0 31 34"
            >
              <use
                  xlink:href="/images/classroom/microphone.svg#microphone-off"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="!settings.isMuted">Mute microphone</template>
            <template v-else>Unmute microphone</template>
          </div>
        </div>
        <div class="toolbar-button-wrapper toolbar-button-wrapper-full-screen">
          <button
              class="toolbar-button-item cursor-pointer"
              data-stream-toggle-full-screen
              type="button"
              :disabled="!isJoined"
              @click="$emit('toggle-full-screen')"
          >
            <svg
                v-if="!settings.isFullscreenEnabled"
                class="toolbar-button-icon"
                width="31"
                height="31"
                viewBox="0 0 31 31"
            >
              <use
                  xlink:href="/images/classroom/full_screen.svg#full_screen"
              ></use>
            </svg>
            <svg
                v-if="settings.isFullscreenEnabled"
                class="toolbar-button-icon"
                width="31"
                height="31"
                viewBox="0 0 31 31"
            >
              <use
                  xlink:href="/images/classroom/full_screen.svg#window_screen"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="!settings.isFullscreenEnabled">Full screen video</template>
            <template v-else>Leave full-screen mode</template>
          </div>
        </div>
        <div class="toolbar-button-wrapper">
          <button
              class="toolbar-button-item cursor-pointer"
              data-stream-toggle-screen-share
              type="button"
              :disabled="!isScreenShareAllowed && !isJoined"
              @click="$emit('toggle-screen-share')"
          >
            <svg
                v-if="settings.isScreenShareEnabled"
                class="toolbar-button-icon"
                width="38"
                height="35"
                viewBox="0 0 38 35"
            >
              <use
                  xlink:href="/images/classroom/not_share.svg#not_share"
              ></use>
            </svg>
            <svg
                v-if="!settings.isScreenShareEnabled"
                class="toolbar-button-icon"
                width="37"
                height="28"
                viewBox="0 0 37 28"
            >
              <use
                  xlink:href="/images/classroom/not_share.svg#share"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="!settings.isScreenShareEnabled">Share my screen</template>
            <template v-else>Stop screenshare</template>
          </div>
        </div>
      </div>
      <button
        v-if="role === 'teacher'"
        class="stream-controls-switch cursor-pointer"
        id="provider-switch-button"
        data-stream-switch-video-player
        type="button"
        @click="$emit('switch-video-player')"
      >
        {{ switchLabel }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoActionsComponent',
  props: {
    settings: {
      type: Object,
      required: true
    },
    isJoined: {
      type: Boolean,
      required: true
    },
    switchLabel: {
      type: String,
      required: true
    },
    isScreenShareAllowed: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    role() {
      return this.$store.getters.getUserParams?.role
    },
  },
}
</script>

<style scoped>
.toolbar-button-wrapper {
  width: 40px;
  height: 40px;
}

.toolbar-button-wrapper button:focus {
  outline: none;
}

.toolbar-button-wrapper button svg {
  margin: 0 auto;
}

.toolbar-button-wrapper-full-screen button {
  padding: 9px;
}

.hover-btn-info {
  left: 50%;
  right: auto;
  transform: translateX(-50%);
  top: auto;
  bottom: -36px;
}

.hover-btn-info::after {
  left: 50%;
  transform: translateX(-50%);
  top: -9px;
  right: auto;
  border: 5px solid transparent;
  border-bottom: 5px solid #444444;
}
</style>
