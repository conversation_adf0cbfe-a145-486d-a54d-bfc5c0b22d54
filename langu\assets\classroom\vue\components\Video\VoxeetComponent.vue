<template>
  <div>
    <container-component :asset="file" :hover-enabled="false">
      <div
        id="video-window"
        :class="[
          'voxeet-component cursor-before-grab',
          { 'video-window--is-fullscreen': settings.isFullscreenEnabled },
        ]"
      >
        <video
          v-for="localStream in localStreams"
          :key="localStream.id"
          :src-object.prop.camel="localStream"
          autoplay
          muted
          width="100"
          height="75"
          class="local-stream hr-flip"
        ></video>

        <div
          v-show="hasVideoRemote"
          class="remote-stream hr-flip"
          :key="key"
        >
          <div class="stream-container embed-responsive embed-responsive-4by3">
            <video
              v-for="remoteVideoStream in remoteVideoStreams"
              :src-object.prop.camel="remoteVideoStream"
              autoplay
              muted
              width="320"
              height="240"
              :key="remoteVideoStream.id"
              :id="remoteVideoStream.id"
            ></video>
          </div>
        </div>

        <video-actions-component
          :is-joined="isJoined"
          :settings="{ ...settings, isMuted, isVideoEnabled }"
          :is-screen-share-allowed="isScreenShareAllowed"
          switch-label="Switch to backup video player"
          @switch-video-player="switchVideoPlayer"
          @toggle-video="toggleVideo"
          @toggle-audio="toggleAudio"
          @toggle-full-screen="toggleFullScreen"
          @toggle-screen-share="toggleScreenShare"
        ></video-actions-component>
      </div>
    </container-component>

    <container-component
      v-if="remoteScreenShareStreams.length"
      :asset="screenShareAsset"
      :hover-enabled="false"
    >
      <div
        class="voxeet-component cursor-before-grab"
      >
        <div class="remote-stream remote-screenshare">
          <div class="stream-container embed-responsive embed-responsive-4by3">
            <video
              v-for="remoteScreenShareStream in remoteScreenShareStreams"
              :key="remoteScreenShareStream.id"
              :src-object.prop.camel="remoteScreenShareStream"
              autoplay
              muted
              width="320"
              height="240"
            ></video>
          </div>
        </div>
      </div>
    </container-component>
  </div>
</template>

<script>
import VoxeetSDK from "@voxeet/voxeet-web-sdk"
import VideoActionsComponent from './VideoActionsComponent'

export default {
  name: 'VoxeetComponent',
  components: { VideoActionsComponent },
  props: {
    file: {
      type: Object,
      required: true
    },
    screenShareAsset: {
      type: Object,
      required: true
    },
  },
  data: () => ({
    localStreams: [],
    remoteVideoStreams: [],
    remoteScreenShareStreams: [],
    isJoined: false,
    settings: {
      isScreenShareEnabled: false,
      isFullscreenEnabled: false,
    },
    key: 1,
  }),
  computed: {
    consumer_key() {
      return window.classroom.voxeet.consumer_key
    },
    sessionName() {
      return window.classroom.voxeet.userId
    },
    consumer_secret_key() {
      return window.classroom.voxeet.consumer_secret_key
    },
    noCamera() {
      return navigator.webkitGetUserMedia === null
    },
    hasVideoRemote() {
      let isVideo = false

      for (let i = 0; i < this.remoteVideoStreams.length; i++) {
        if (
          !this.localStreams.map(item => item.id).includes(this.remoteVideoStreams[i].id) &&
          this.remoteVideoStreams[i].getVideoTracks().length
        ) {
          isVideo = true

          break
        }
      }

      return isVideo

      //  this.remoteVideoStreams.forEach((item) => {
      //    console.log(item.getVideoTracks().length)
      // })
      //
      // return false
    },
    isScreenShareAllowed() {
      return !this.remoteScreenShareStreams.length
    },
    isVideoEnabled() {
      return this.file.asset?.settings?.[window.langu_role]?.isVideoEnabled ?? true
    },
    isMuted() {
      return this.file.asset?.settings?.[window.langu_role]?.isMuted ?? false
    },
  },
  created() {
    this.init()

    const beforeUnloadListener = (event) => {
      event.preventDefault()
      this.closeStream()
    }

    document.addEventListener('beforeunload', beforeUnloadListener, { once: true })
    document.addEventListener('fullscreenchange', () => {
      if (!document.fullscreenElement) {
        this.settings.isFullscreenEnabled = false
      }
    })
  },
  beforeDestroy() {
    this.closeStream()
  },
  methods: {
    async init() {
      VoxeetSDK.initialize(this.consumer_key, this.consumer_secret_key)

      try {
        await VoxeetSDK.session.open({ name: this.sessionName })

        VoxeetSDK.conference.on('streamAdded', this.streamAdded)
        VoxeetSDK.conference.on('streamUpdated', this.streamAdded)
        VoxeetSDK.conference.on('streamRemoved', this.streamRemoved)

        VoxeetSDK.conference
          .create({ alias: 'classroom_' + this.getParameterFromURL('roomid') })
          .then(conference => {
            VoxeetSDK.conference
              .join(conference, {
                video: this.isVideoEnabled,
                constraints: { audio: true, video: this.isVideoEnabled },
              })
              .then(() => {
                this.isJoined = true

                VoxeetSDK.conference.mute(VoxeetSDK.session.participant, this.isMuted)
              })
              .catch((err) => {
                const message = err.name === 'NotReadableError' || err.name === 'NotAllowedError' ?
                  'We are unable to access your video camera. Either the browser has not been given permissions or the camera is in use by another program.' :
                  err

                alert(message)
              })
          }).catch((err) => console.log(err))
      } catch (e) {
        alert('Something went wrong : ' + e)
      }
    },
    streamAdded(participant, stream) {
      if (+participant.info?.name === this.sessionName) {
        if (stream.type !== 'ScreenShare') {
          this.localStreams = [...this.localStreams.filter(item => item.id !== stream.id), stream]
        }
      } else {
        if (stream.type === 'ScreenShare') {
          this.remoteScreenShareStreams = [...this.remoteScreenShareStreams.filter(item => item.id !== stream.id), stream]
        } else {
          this.remoteVideoStreams = [...this.remoteVideoStreams.filter(item => item.id !== stream.id), stream]
        }
      }

      this.key++
    },
    streamRemoved(participant, stream) {
      this.localStreams = this.localStreams.filter(item => item.id !== stream.id)
      this.remoteVideoStreams = this.remoteVideoStreams.filter(item => item.id !== stream.id)
      this.remoteScreenShareStreams = this.remoteScreenShareStreams.filter(item => item.id !== stream.id)

      this.key++
    },
    toggleVideo() {
      const asset = {
        settings: {
          ...this.file.asset.settings,
          [window.langu_role]: {
            isVideoEnabled: !this.isVideoEnabled,
            isMuted: this.isMuted,
          }
        }
      }

      this.$store.commit('moveAsset', {
        id: this.file.id,
        asset,
      })
      this.$store.dispatch('moveAsset', {
        id: this.file.id,
        lessonId: this.file.lessonId,
        asset,
      })

      if (this.isVideoEnabled) {
        VoxeetSDK.conference.startVideo(VoxeetSDK.session.participant).catch((err) => console.log(err))
      } else {
        VoxeetSDK.conference.stopVideo(VoxeetSDK.session.participant).catch((err) => console.log(err))
      }
    },
    toggleAudio() {
      VoxeetSDK.conference.mute(VoxeetSDK.session.participant, !VoxeetSDK.conference.isMuted())

      const asset = {
        settings: {
          ...this.file.asset.settings,
          [window.langu_role]: {
            isVideoEnabled: this.isVideoEnabled,
            isMuted: VoxeetSDK.conference.isMuted(),
          }
        }
      }

      this.$store.commit('moveAsset', {
        id: this.file.id,
        asset,
      })
      this.$store.dispatch('moveAsset', {
        id: this.file.id,
        lessonId: this.file.lessonId,
        asset,
      })
    },
    toggleFullScreen() {
      this.settings.isFullscreenEnabled = !this.settings.isFullscreenEnabled

      if (this.settings.isFullscreenEnabled) {
        let elem = document.getElementById('video-window')

        if (elem.requestFullscreen) {
          elem.requestFullscreen()
        } else if (elem.mozRequestFullScreen) { /* Firefox */
          elem.mozRequestFullScreen()
        } else if (elem.webkitRequestFullscreen) { /* Chrome, Safari & Opera */
          elem.webkitRequestFullscreen()
        } else if (elem.msRequestFullscreen) { /* IE/Edge */
          elem = window.top.document.body //To break out of frame in IE
          elem.msRequestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen()
        } else if (document.msExitFullscreen) {
          window.top.document.msExitFullscreen()
        }
      }
    },
    toggleScreenShare() {
      if (!this.settings.isScreenShareEnabled) {
        VoxeetSDK.conference.startScreenShare().then(() => this.settings.isScreenShareEnabled = true)
      } else {
        VoxeetSDK.conference.stopScreenShare().then(() => this.settings.isScreenShareEnabled = false)
      }
    },
    async closeStream() {
      if (this.isScreenShareEnabled) {
        await VoxeetSDK.conference.stopScreenShare()
      }

      await VoxeetSDK.conference.leave()
      await VoxeetSDK.session.close()
    },
    switchVideoPlayer() {
      this.$store.dispatch('deleteAsset', this.file)
      this.$store.dispatch('createAsset', {
        ...this.file.asset,
        type: 'tokbox'
      })
    },
  }
}
</script>
