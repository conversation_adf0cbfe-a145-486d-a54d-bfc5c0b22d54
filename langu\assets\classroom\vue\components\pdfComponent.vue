<template lang="html">
  <container-component
    :asset="file"
    :child-height="childHeight"
    :child-header-height="83"
  >
    <div ref="childComponent" class="image-wrap-classroom active">
      <container-header-component
        :file="file"
        :title="file.asset.displayName"
      >
        <canvas :id="`pdf-render--${file.id}`"></canvas>
        <div class="transparent">
          <konva-component
            v-if="file && width && height"
            :file="file"
            :width="width"
            :height="height"
            :scale="scale"
            :current-page="page"
            :style="{
              marginTop: `-${height}px`
            }"
          ></konva-component>
        </div>
        <div
          class="controls"
          @mouseenter.prevent="mouseenterHandler"
          @mouseleave.prevent="mouseleaveHandler"
        >
          <button @click="showPrevPage" :disabled="!isPrevPageEnabled">
            <img src="/images/classroom/arrow-left.svg" alt="">
          </button>
          <span class="page-info">
            Page <span>{{ page }}</span> of {{ pageTotal }}<span></span>
          </span>
          <button @click="showNextPage" :disabled="!isNextPageEnabled">
            <img src="/images/classroom/arrow-right.svg" alt="">
          </button>
        </div>
      </container-header-component>
    </div>
  </container-component>
</template>

<script>
import debounce from 'debounce'
import { defaultWidth } from '../../core/helpers/constants'
import Tool from '../mixins/Tool'

export default {
  mixins: [Tool],
  props: {
    file: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      renderInProgress: false,
      viewport: null,
      page: 1,
      pageTotal: 1,
      childHeight: undefined,
      pdfDoc: null,
      pdfPage: null,
    }
  },
  computed: {
    scale() {
      if (this.width) {
        return this.width / defaultWidth
      }

      return 1
    },
    canvas() {
      return document.querySelector(`#pdf-render--${this.file.id}`)
    },
    zoom() {
      return this.$store.getters.getZoomAsset.asset
    },
    width() {
      return this.file.asset.width
    },
    height() {
      return this.file.asset.height
    },
    role() {
      return this.$store.getters.role
    },
    isPrevPageEnabled() {
      return this.pageTotal > 1 && this.page > 1
    },
    isNextPageEnabled() {
      return this.pageTotal > 1 && this.page < this.pageTotal
    },
  },
  mounted() {
    if (this.file.asset.page) {
      this.page = this.file.asset.page
    }

    pdfjsLib.getDocument(this.file.asset.path).promise
      .then(pdfDoc => {
        this.pdfDoc = pdfDoc
        this.pageTotal = this.pdfDoc.numPages

        this.renderPage()

        const asset = { originalWidth: defaultWidth }

        this.$store.commit('moveAsset', {
          id: this.file.id,
          asset,
        })

        this.$store.dispatch('moveAsset', {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset,
        })
      })
      .catch(err => {
        alert(err.message)
      })

    new ResizeObserver(debounce(this.resize, 200)).observe(this.$refs.childComponent)
  },
  watch: {
    'file.asset.page': function(page) {
      this.page = page

      this.renderPage()
    },
  },
  methods: {
    showPage() {
      this.$store.dispatch('moveAsset', {
        id: this.file.id,
        lessonId: this.file.lessonId,
        asset: {
          page: this.page
        }
      })
    },
    showPrevPage() {
      if (this.page <= 1) {
        return
      }

      this.page -= 1

      this.renderPage()
      this.showPage()
    },
    showNextPage () {
      if (this.page >= this.pageTotal) {
        return
      }

      this.page += 1

      this.renderPage()
      this.showPage()
    },
    resize() {
      if (this.pdfPage) {
        if (this.renderInProgress) {
          return
        }

        this.renderInProgress = true

        const canvasContext = this.canvas.getContext('2d')

        if (this.$refs.childComponent) {
          const childComponentSize = this.$refs.childComponent.getBoundingClientRect()
          const scale = childComponentSize.width / this.zoom.zoomIndex / this.pdfPage.getViewport({ scale: 1 }).width

          this.viewport = this.pdfPage.getViewport({ scale })

          this.canvas.height = this.viewport.height
          this.canvas.width = this.viewport.width

          const renderTask = this.pdfPage.render({ canvasContext, viewport: this.viewport })

          renderTask.promise.then(() => {
            this.childHeight = this.$refs.childComponent.getBoundingClientRect().height

            this.$store.commit('moveAsset', {
              id: this.file.id,
              asset: {
                width: this.canvas.width,
                height: this.canvas.height,
              },
            })

            this.renderInProgress = false
          })
        }
      }
    },
    renderPage () {
      this.pdfDoc.getPage(this.page).then(pdfPage => {
        this.pdfPage = pdfPage

        this.resize()
      })
    },
    mouseenterHandler() {
      this.$store.commit(
        'setCursorNameBeforeChange',
        this.$store.state?.userParams?.cursor || 'cursor-pointer'
      )
      this.$store.commit(
        'setToolNameBeforeChange',
        this.$store.state?.userParams?.tool || 'pointer'
      )

      this.setTool('pointer', 'cursor-pointer')
    },
    mouseleaveHandler() {
      this.setTool(
        this.$store.state.toolNameBeforeChange,
        this.$store.state.cursorNameBeforeChange
      )
    },
  },
}
</script>

<style lang="css" scoped>
  .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.25);
    position: relative;
    padding: 10px 13px 11px;
    background: #fff;
  }

  .controls button {
    border: none;
    background: none;
  }

  .controls button[disabled] {
    opacity: 0;
    visibility: hidden;
  }
</style>
