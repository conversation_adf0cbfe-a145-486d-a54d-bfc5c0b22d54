<script>
export default {
  computed: {
    role() {
      return this.$store.getters.role
    },
  },
  methods: {
    getParameterFromURL(type_param) {
      const url = window.location.href;
      if (type_param == "roomid") {
        // Extract room id from the url we are currently at
        let partial = url.split('/lesson/')[1];
        partial = partial.split('/classroom')[0];

        return partial;
      }
    },
    getFileExtension(file) {
      return (/[.]/.exec(file)) ? /[^.]+$/.exec(file) : undefined;
    },
    uploadFiles(files) {
      files = [...files];

      let formData = new FormData();
      for (let i = 0; i <= files.length -1; i++) {
        formData.append(i, files[i]);
      }

      this.$axios.post(`/api/lesson/classroom/upload/canvas/${this.getParameterFromURL('roomid')}`, formData,{
        headers: {
          'Content-Type': 'multipart/form-data'
        },
      }).then(response => {
        const assets = response.data

        this.$store.commit('addAssets', assets)

        assets.forEach(asset => {
          let index = store.state.maxIndex + 1
          asset.asset = {
            ...asset.asset,
            index: index,
            owner: this.role,
            top: this.$store.getters.getZoomAsset.asset.y + 200,
            left: this.$store.getters.getZoomAsset.asset.x + 650
          }

          this.$store.commit('setMaxIndex', index);
          this.$store.dispatch('moveAsset', asset);

          this.$socket.emit('asset-added', asset)
        })
      }).catch((e) => {
        Bugsnag.notify(e)
        throw e
      });
    }
  },
}
</script>
