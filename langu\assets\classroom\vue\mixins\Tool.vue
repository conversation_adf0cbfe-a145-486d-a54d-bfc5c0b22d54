<script>
export default {
  methods: {
    removeCursors(classList) {
      $.each(classList, function(index, item) {
        if (item.includes('cursor')) {
          $('body').removeClass(item)
        }
      })
    },
    setTool(toolName, cursorName) {
      this.$store.commit('enableContainerComponent', toolName === 'pointer')
      this.$socket.emit('cursor-moved', {
        tool: toolName,
        cursor: cursorName.replace(/(-cursor|cursor-)/i, ''),
        lessonId: this.$store.state.lesson_id,
      })

      // this.$socket.emit(
      //   'testFrontend',
      //   JSON.stringify({
      //     id: this.getParameterFromURL('roomid'),
      //     data: {
      //       cursor: { x: 619, y: 205, offset: 0 },
      //       screen: {
      //         width: window.screen.width,
      //         height: window.screen.height,
      //       },
      //     },
      //     role: 'teacher',
      //     assets: [],
      //     commands: [],
      //   })
      // );
      // this.$socket.emit(
      //   'classroom update',
      //   JSON.stringify(
      //     {
      //       'id':'197',
      //       'data': {
      //         'cursor':{ 'x':619,'y':205,'offset':0},
      //         'screen':{'width':896,'height':732}},
      //         'role':'teacher',
      //         'assets':[],
      //         'commands':[]
      //       }
      //   )
      // )

      this.$store.commit('setUserTool', toolName)
      this.$store.commit('setUserCursor', cursorName)
      // this.$root.$data.user.tool = toolName
      // this.$root.$data.user.cursor = cursorName

      const $body = $('body')
      let classList = $body.attr('class').split(/\s+/)

      this.removeCursors(classList)
      $body.addClass(`${ this.role }-${ cursorName }`)

      this.classList = $body.attr('class').split(/\s+/)
    },
  }
}
</script>
