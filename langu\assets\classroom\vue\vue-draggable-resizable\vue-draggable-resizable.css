.vdr {
  touch-action: none;
  position: absolute;
  box-sizing: border-box;
  border: none;
}

.handle {
  display: block !important;
}

.resizable.vdr::after {
  content: '';
  position: absolute;
  bottom: 4px;
  right: 2px;
  width: 8px;
  height: 8px;
  background-image: url('../../../../web/images/classroom/corner-resize-marker.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  opacity: 0.9;
  z-index: 99;
}

.vdr .handle {
  box-sizing: border-box;
  position: absolute;
  width: 12px;
  height: 12px;
  border: none;
  background-color: transparent;
  z-index: 10;
}

.vdr .handle-tl {
  top: -7px;
  left: -7px;
}

.vdr .handle-tm {
  top: -7px;
  margin-left: -5px;
}

.vdr .handle-tr {
  top: -7px;
  right: -7px;
}

.vdr .handle-ml {
  margin-top: -5px;
  left: -7px;
}

.vdr .handle-mr {
  margin-top: -5px;
  right: -7px;
}

.vdr .handle-bl {
  bottom: -7px;
  left: -7px;
}

.vdr .handle-bm {
  bottom: -7px;
  margin-left: -5px;
}

.vdr .handle-br {
  bottom: -7px;
  right: -7px;
}

@media only screen and (max-width: 768px) {
  [class*="handle-"]:before {
    content: '';
    left: -10px;
    right: -10px;
    bottom: -10px;
    top: -10px;
    position: absolute;
  }
}

.vdr .handle-tm,
.vdr .handle-bm {
  width: 100%;
  left: 5px;
}

.vdr .handle-mr,
.vdr .handle-ml {
  height: 100%;
  top: 5px;
}

.vdr .handle-tl,
.vdr .handle-tr,
.vdr .handle-bl,
.vdr .handle-br {
  width: 14px;
  height: 14px;
  z-index: 11;
}

@media (max-width: 1199px) {
  .vdr .handle-tm,
  .vdr .handle-bm {
    height: 15px;
  }

  .vdr .handle-ml,
  .vdr .handle-mr {
    width: 15px;
  }

  .vdr .handle-tl,
  .vdr .handle-tr,
  .vdr .handle-bl,
  .vdr .handle-br {
    width: 15px;
    height: 15px;
  }

  .vdr .handle-tl {
    top: -10px;
    left: -10px;
  }

  .vdr .handle-tm {
    top: -10px;
    margin-left: -5px;
  }

  .vdr .handle-tr {
    top: -10px;
    right: -10px;
  }

  .vdr .handle-ml {
    margin-top: -5px;
    left: -10px;
  }

  .vdr .handle-mr {
    margin-top: -5px;
    right: -10px;
  }

  .vdr .handle-bl {
    bottom: -10px;
    left: -10px;
  }

  .vdr .handle-bm {
    bottom: -10px;
    margin-left: -5px;
  }

  .vdr .handle-br {
    bottom: -10px;
    right: -10px;
  }
}
