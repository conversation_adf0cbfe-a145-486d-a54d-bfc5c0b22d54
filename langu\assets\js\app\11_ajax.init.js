;
(function ($, undefined) {
    var timeout;
    var dialog;
    var prevXhr = null;

    $.blockUI.defaults.css = {};
    $.blockUI.defaults.blockMsgClass = 'block-msg';
    $.blockUI.defaults.message = '<span class="glyphicon glyphicon-repeat spin"></span>';

    $(document).ajaxSend(function (event, xhr, options) {
        var ignoreIfPrevious = options.ignoreIfPrevious || false;
        
        if(ignoreIfPrevious && null !== prevXhr) {
            xhr.abort();

            return false;
        }
        
        var abortPrevious = options.abortPrevious || true;

        if (abortPrevious) {
            if (null !== prevXhr) {
                prevXhr.abort();
                clearTimeout(timeout);

                if (dialog) {
                    dialog.modal('hide');
                }

                dialog = null;
            }
        }

        prevXhr = xhr;

        var bootboxLoader = options.bootbox || false;
        var bootboxLoaderMessage = options.bootboxMessage || '<p class="text-center">Loading...</p>';

        if (false !== bootboxLoader) {
            timeout = setTimeout(function () {
                dialog = bootbox.dialog({
                    message: bootboxLoaderMessage,
                    closeButton: false
                });
                dialog.css('z-index', 99999);
            }, 300);
        }

        var blk = options.block || false;

        if (false !== blk) {
            var context = blk.context || null;
            var blkOptions = blk.options || {};

            timeout = setTimeout(function () {
                if (null === context) {
                    $.blockUI(blkOptions);
                } else {
                    $(context).block(blkOptions);
                }
            }, 300);
        }
    });

    $(document).ajaxComplete(function (event, xhr, options) {
        if (xhr.status === 0 && xhr.readystate === 0) {
            return;
        }

        prevXhr = null;
        clearTimeout(timeout);

        if (dialog) {
            dialog.modal('hide');
        }

        dialog = null;

        var blk = options.block || false;

        if (false !== blk) {
            var context = blk.context || null;

            if (null === context) {
                $.unblockUI(blk.options);
            } else {
                $(context).unblock(blk.options);
            }
        }

    });
    
    $(document).ajaxSuccess(function(event, xhr, options, data){
        if(options.dataType === 'json' && xhr.hasOwnProperty('responseJSON')) {
            if(xhr.responseJSON.hasOwnProperty('redirect') && xhr.responseJSON.redirect != null) {
                if(!xhr.responseJSON.hasOwnProperty('mock') || xhr.responseJSON.mock === false) {
                    window.location.replace(xhr.responseJSON.redirect);
                } else {
                    console.log('redirect mocked to: ');
                    console.log(xhr.responseJSON.redirect);
                }
            }
        }
    });
})(jQuery);
