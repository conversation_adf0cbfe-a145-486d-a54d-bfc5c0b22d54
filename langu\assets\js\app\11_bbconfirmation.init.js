;
(function ($) {
    langu = window.langu || {};

    var bbConfirmation = {
        instance: function (title, message, callback, buttons) {
            if (buttons) {
                bootbox.confirm({
                    title: Translator.trans(title),
                    message: Translator.trans(message),
                    callback: callback,
                    buttons: {
                        confirm: {
                            className: 'btn-primary',
                            label: Translator.trans(buttons.confirm)
                        },
                        cancel: {
                            className: 'btn-default',
                            label: Translator.trans(buttons.cancel)
                        }
                    }
                });
            } else {
                bootbox.confirm({
                    title: Translator.trans(title),
                    message: Translator.trans(message),
                    callback: callback
                });
            }
        }
    };

    langu.bbConfirmation = bbConfirmation;
})(jQuery);