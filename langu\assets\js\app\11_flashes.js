;
(function ($) {
    var FLASHES = (function () {
        var types = {
            'notice': {
                name: 'info',
                icon: 'glyphicon-info-sign'
            },
            'warning': {
                name: 'warning',
                icon: 'glyphicon-alert'
            },
            'error': {
                name: 'danger',
                icon: 'glyphicon-exclamation-sign'
            },
            'success': {
                name: 'success',
                icon: 'glyphicon-ok-sign'
            }
        };

        var $container = $('.flash-container');
        var flashContainerTemplate = '<div class="alert fade alert-%type% alert-dismissable" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button><ul class="list-unstyled"></ul></div>';
        var flashMessageTemplate = '<li><span class="glyphicon %icon%"></span> %message%</li>';

        function nl2br(str, is_xhtml) {
            var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';
            return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
        }

        function addFlash(type, message) {
            var $flashContainer = $container.children('.alert-' + type);
            var typeObj = types[type];
            // create new container if does not exist
            if ($flashContainer.length === 0) {
                $flashContainer = $(flashContainerTemplate.replace('%type%', typeObj.name));
                $container.append($flashContainer);
            }

            var msgProcessed = nl2br(Translator.trans(message));

            var $flashMessage = $(flashMessageTemplate.replace('%icon%', typeObj.icon).replace('%message%', msgProcessed));

            $flashContainer.children('ul').first().append($flashMessage);
            $flashContainer.addClass('in');
            setTimeout(removeFlash, 3000, $flashMessage);
        }

        function removeFlash(self) {
            var container = self.closest('.alert');

            if (self.siblings().length > 1) {
                self.remove();
            } else {
                container.removeClass('in');
                container.on('transitionend webkitTransitionEnd oTransitionEnd MSTransitionEnd', function () {
                    container.remove();
                });
            }
        }
        
        function initializeFlashes() {
            var counter = 0;
            var alerts = $container.find('.alert.fade-out');
            
            $.each(alerts, function(i, element){
                var $el = $(element);
                var items = $el.children('ul').first().children('li');
                
                $.each(items, function(j, subElement){
                    var $subEl = $(subElement);
                    setTimeout(removeFlash, 3000 + counter * 1000, $subEl);
                    counter++;
                });
            });
        }

        // define the public API
        var PUBLIC = {};
        PUBLIC.addFlash = addFlash;
        
        $(function(){
            initializeFlashes();
        });
        
        return PUBLIC;
    }());

    window.FLASHES = FLASHES;
})(jQuery);