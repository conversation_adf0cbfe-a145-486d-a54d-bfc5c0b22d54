;(function($){
    var body = $(document.body);
    var overlay = null;
    var activityIndicator = null;
    var closeButton = null;
    
    var 
        overlayOn = function(){
            if(null === overlay) {
                overlay = $('<div class="image-lightbox-overlay"></div>');
            }
        
            overlay.appendTo(body);
        },
        overlayOff = function(){
            overlay.detach();
        },
        activityIndicatorOn = function(){
            if(null === activityIndicator) {
                activityIndicator = $('<div class="image-lightbox-activity-indicator"><div></div></div>');
            }
            
            activityIndicator.appendTo(body);
        },
        activityIndicatorOff = function(){
            activityIndicator.detach();
        },
        closeButtonClicked = function(e, instance){
            e.preventDefault();
            closeButton.detach();
            instance.quitImageLightbox();
            return false;
        },
        closeButtonOn = function(instance){
            if(null === closeButton) {
                closeButton = $('<button type="button" class="image-lightbox-close-button"></button>');
            }
            
            closeButton.appendTo(body);
            closeButton.on('click touchend', {instance: instance}, closeButtonClicked);
        },
        closeButtonOff = function(){
            closeButton.detach();
            closeButton.off('click touchend', closeButtonClicked);
        };
    
    var inst = $('.image-lightbox').imageLightbox({
        onLoadStart: function(){
            activityIndicatorOn();
        },
        onLoadEnd: function(){
            activityIndicatorOff();
        },
        onStart: function(){
            overlayOn();
            closeButtonOn(inst);
        },
        onEnd: function(){
            overlayOff();
            activityIndicatorOff();
            closeButtonOff();
        },
        selector: 'class="image-lightbox-full"',
        quitOnEnd: true,
        enableKeyboard: false,
    });
})(jQuery);