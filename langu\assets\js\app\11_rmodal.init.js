;
(function($) {
  langu = window.langu || {};

  var modal = {
    instance: function(content, _class, options) {
      _class = _class || 'langu-modal-dialog';

      var wrapper = $('<div class="langu-modal langu-modal--active"><div class="'
        + _class
        + '"><a href="#" class="langu-modal-dialog-close">&times;</a><div class="langu-modal-dialog-content"></div></div></div>');

      var defaultOptions = {
        content: $(content).html(),
        dialogClass: 'langu-modal-dialog-content',
        escapeClose: true,
        afterClose: function() {
          wrapper.remove();
        }
      };

      options = $.extend({}, options, defaultOptions);
      var modal = new RModal(wrapper[0], options);
      $('body').append(wrapper);
      wrapper.on('click', '.langu-modal-dialog-close, .modal-close', function (e) {
        e.preventDefault();
        modal.close();
      });
      return modal;
    }
  };
  langu.modal = modal;
})(jQuery);
