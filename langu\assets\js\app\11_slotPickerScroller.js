;
(function ($, window, document, undefined) {
    var slidePicker = function (e) {
        var self = $(this);
        var direction = self.data('picker-slide');
        var slots = self.closest('.slots-picker').children('.slots-picker-slots').children();

        switch (direction) {
            case "up":
                var preSlots = [];
                for (var i = 0; i < slots.length; i++) {
                    var $element = $(slots[i]);

                    if (!$element.hasClass('concealed')) {
                        break;
                    }

                    preSlots.push(slots[i]);
                }

                if (preSlots.length === 0) {
                    return;
                }

                var revealSlots = $(preSlots).slice(-2);
                var concealSlots = slots.filter(':not(.concealed)').slice(-2);

                var all = revealSlots.add(concealSlots);

                var $window = $(window);
                var scrollTop = $window.scrollTop();
                $(all).toggleClass('concealed');
                $window.scrollTop(scrollTop);

                break;
            case "down":
                var postSlots = [];
                for (var i = slots.length - 1; i >= 0; i--) {
                    var $element = $(slots[i]);

                    if (!$element.hasClass('concealed')) {
                        break;
                    }

                    postSlots.push(slots[i]);
                }

                if (postSlots.length === 0) {
                    return;
                }

                var revealSlots = $(postSlots).slice(-2);
                var concealSlots = slots.filter(':not(.concealed)').slice(0, 2);
                var all = concealSlots.add(revealSlots);

                var $window = $(window);
                var scrollTop = $window.scrollTop();
                $(all).toggleClass('concealed');
                $window.scrollTop(scrollTop);

                break;
        }
    };
    
    window.slidePicker = slidePicker;
})(jQuery, window, document);