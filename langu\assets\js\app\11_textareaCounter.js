;(function($){
    var counterUpdate = function(counter) {
        var self = $(this);
        var length = self.val().length;
        
        counter.text(length);
    };
    
    var charCounter = function(parent) {
        parent = parent || document;
        $(parent).find('.char-counter').each(function(index, element){
            var $el = $(element);
            var counter = $el.data('counter') || null;

            if(null !== counter) {
                return;
            }

            var length = $el.val().length;

            var $counter = $('<span class="char-counter-display"></span>');
            $counter.text(length);

            $el.data('counter', $counter);
            $el.after($counter);
            $el.on('input propertychange', counterUpdate.bind($el, $counter));
        });        
    };

    window.charCounter = charCounter;
    
    charCounter();
})(jQuery);
