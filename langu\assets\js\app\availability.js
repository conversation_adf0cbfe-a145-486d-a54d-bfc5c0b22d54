;
(function ($) {
    $('.slots-container').on('click', '.close', function (e) {
        e.preventDefault();
        
        var self = $(this);

        if (self.hasClass('removing')) {
            return false;
        }

        var slotInner = self.closest('.slots-slot');
        var slotId = slotInner.data('slot-id');

        self.addClass('removing');
        slotInner.addClass('removing');
        
        var call = $.ajax(self.attr('href'), {
            dataType: 'json',
            data: {slot_id: slotId},
            method: 'DELETE'
        });

        call.fail(function (xhr, status, error) {
            slotInner.removeClass('removing');
            self.removeClass('removing');
            
            if(xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = 'error';
                
                if(xhr.responseJSON.hasOwnProperty('payload') && xhr.responseJSON.payload.hasOwnProperty('messageType')) {
                    type = xhr.responseJSON.payload.messageType;
                }
                
                var message = xhr.responseJSON.message;
                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            slotInner.remove();
        });
        
        return false;
    });

    $('.copy-slots-form').on('submit', function (e) {
        e.preventDefault();
        
        var self = $(this);
        var button = self.find('.copy-slots-container-button');

        if (button.hasClass('disabled')) {
            e.stopPropagation();
            return false;
        }

        var data = self.serializeArray();
        var call = $.ajax(self.attr('action'), {
            dataType: 'json',
            data: data,
            method: 'POST',
            block: {
                context: '.copy-slots-form'
            }
        });

        call.fail(function (xhr, status, error) {
            if(xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.payload.messageType || 'error';
                var message = xhr.responseJSON.message;
                
                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            if(null !== data) {
                var messagesContainer = data.payload.messages;
                
                for(var type in messagesContainer) {
                    var messages = messagesContainer[type];
                    
                    for(var i = 0; i < messages.length; i++) {
                        FLASHES.addFlash(type, messages[i]);
                    }
                }
            }
        });

        e.stopPropagation();
        return false;
    });

    $('.copy-slots-form').on('change', '.copy-slots-item-checkbox', function () {
        var self = $(this);
        var form = self.closest('.copy-slots-form');
        var button = form.find('.copy-slots-container-button');
        var checked = form.find('.copy-slots-item-checkbox:checked');

        if (checked.length) {
            button.removeClass('disabled');
        } else {
            button.addClass('disabled');
        }
    });
})(jQuery);
