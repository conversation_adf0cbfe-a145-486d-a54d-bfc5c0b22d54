;
(function ($) {
    // check to avoid processing
    
    window.langu = window.langu || {};
    
    // methods / classes
    var CalendarScroller = function(calendarContainer) {
        var arrows = calendarContainer.find('.slots-picker-page.active .arrow-row .arrow-row-slide');
        var arrowUp = arrows.filter('[data-picker-slide="up"]');
        var arrowDown = arrows.filter('[data-picker-slide="down"]');
        var scrollable = calendarContainer.find('.slots-picker-page.active .calendar-row .calendar-row-inner');
        var scrollPosition = parseInt(scrollable.css('marginTop'));
        var scrollRow = 7;
        
        var doScroll = function() {
            if(scrollRow === 0) {
                scrollPosition = 0;
            } else if(scrollRow === 7) {
                scrollPosition = '';
            } else {
                var val = scrollRow * 3;
                scrollPosition = 'calc(-' + val + 'em - ' + val + 'px)'; 
            }
            
            scrollable.css('marginTop', scrollPosition);
        };
        
        arrowUp.on('click', function() {
            if(scrollRow === 0) {
                return;
            }
            
            scrollRow--;
            doScroll();
        });
        
        arrowDown.on('click', function() {
            if(scrollRow === 9) {
                return;
            }
            
            scrollRow++;
            doScroll();
        });
    };
    window.langu.CalendarScroller = CalendarScroller;

    var SelectionUpdater = function(bookingForm) {
        var formId = bookingForm.attr('id');
        var changeHandlers = [];
        var lessonTimeRadios = bookingForm.find('input[name="' + formId + '[length]"]');
        var servicesContainer = bookingForm.find('.booking-selection-services').first();
        var servicesRadios = servicesContainer.find('input[name="' + formId + '[service]"]');
        
        var executeHandlers = function(length, lessons){
            for(var i = 0; i < changeHandlers.length; i++) {
                changeHandlers[i](length, lessons);
            }
        };
        
        lessonTimeRadios.on('change', function(e){
            servicesRadios.parent().addClass('hidden');
            var length = parseInt(this.value);
            var lessons = null;
            
            if(length === 0) {
                servicesContainer.addClass('hidden');
                servicesRadios.prop('checked', false);
            } else {
                var visible = servicesRadios.filter('[data-length="' + length + '"]');
                var service = visible.first();
                service.prop('checked', true);
                lessons = service.data('lessons');
                visible.parent().removeClass('hidden');
                servicesContainer.removeClass('hidden');
            }
            
            executeHandlers(length ? length : 30, lessons ? lessons : 1);
        });
        
        servicesRadios.on('change', function(e){
            var self = $(this);
            var length = self.data('length');
            var lessons = self.data('lessons');
            
            executeHandlers(length ? length : 30, lessons ? lessons : 1);
        });
        
        this.getCurrentState = function() {
            var length = parseInt(lessonTimeRadios.filter(':checked').first().val());
            var lessons = 1;
            
            var service = servicesRadios.filter(':checked');
            if(service.length) {
                lessons = parseInt(service.first().data('lessons'));
            }
            
            return {
                lessons: lessons ? lessons : 1,
                length: length ? length : 30
            };
        };
        
        this.addStateChangeListener = function(f) {
            var index = $.inArray(f, changeHandlers);
            if(-1 === index) {
                changeHandlers.push(f);
            }
        };
        
        this.removeStateChangeListener = function(f) {
            var index = $.inArray(f, changeHandlers);
            if(-1 === index) {
                return;
            }
            
            changeHandlers.splice(index, 1);
        };
    };
    window.langu.SelectionUpdater = SelectionUpdater;
    var CalendarPicker = function(calendarContainer, initialState) {
        var container = calendarContainer;
        var slotsContainerSelector = '.slots-picker-page.active .calendar-row .calendar-row-inner .calendar-row-inner-slots-column';
        var slotsContainer;
        var length = initialState.selection.length;
        var lessons = initialState.selection.lessons;
        var state;
        
        var cache = {
            state: {
                
            }
        };
        
        var calendarPageChange = function() {
            updateEvents();
        };
        var updateEvents = function() {
            if(slotsContainer) {
                slotsContainer.off('change', 'input.slot-picker-checkbox', selectionChanged);
                slotsContainer.off('mouseenter', 'label.slot.slot-label.free', slotEnter);
                slotsContainer.off('mouseleave', 'label.slot.slot-label.free', slotLeave);
            }
            
            slotsContainer = container.find(slotsContainerSelector);
            slotsContainer.on('change', 'input.slot-picker-checkbox', selectionChanged);
            slotsContainer.on('mouseenter', 'label.slot.slot-label.free', slotEnter);
            slotsContainer.on('mouseleave', 'label.slot.slot-label.free', slotLeave);
        };
        var slotEnter = function(e) {
            var self = $(this);
            if(lessons <= state.selection.selectedCount || self.siblings().first().prop('checked')) {
                return;
            }
            
            var range = length / 30;
            var selection = querySelect(this, range);
            
            if(null === selection) {
                return false;
            }
            
            self.addClass('active');
            $.each(selection, function(){
                this.siblings().first().addClass('active');
            });
        };
        var slotLeave = function(e) {
            var self = $(this);
            if(lessons <= state.selection.selectedCount || self.siblings().first().prop('checked')) {
                return;
            }
            
            var range = length / 30;
            var selection = querySelect(this, range);
            
            if(null === selection) {
                return false;
            }
            
            self.removeClass('active');
            $.each(selection, function(){
                this.siblings().first().removeClass('active');
            });
        };
        var selectionChanged = function(e) {
            if(this.checked) {
                return addSelection(this, e);
            } else {
                return removeSelection(this, e);
            }
        };     
        var noListener = function(f) {
            slotsContainer.off('change', 'input.slot-picker-checkbox', selectionChanged);
            f();
            slotsContainer.on('change', 'input.slot-picker-checkbox', selectionChanged);
        };
        var querySelect = function(element, range) {
            if(range === 1) {
                return [];
            };
            
            var $initialElement = $(element);
            var elements = [];
            
            var $parent = $initialElement.parent();
            while(true) {
                $parent = $parent.next();
                var $input = $parent.children('input');
                if(!$input.length || $input.prop('checked')) {
                    break;
                }
                
                elements.push($input.first());
                
                if(range <= elements.length + 1) {
                    return elements;
                }
            };
            
            $parent = $initialElement.parent();
            while(true) {
                $parent = $parent.prev();
                var $input = $parent.children('input');
                if(!$input.length || $input.prop('checked')) {
                    break;
                }
                
                elements.push($input.first());
                
                if(range <= elements.length + 1) {
                    return elements;
                }
            };
            
            return null;
        };     
        var addSelection = function (element, event) {
            if(state.selection.selectedCount + 1 > lessons) {
                event.preventDefault();
                
                noListener(function(){
                    element.checked = !element.checked;
                });
                
                return false;
            }
            
            var range = length / 30;
            var selection = querySelect(element, range);
            
            if(null === selection) {
                return false;
            }
            
            noListener(function(){
                var groupId = state.selection.selectedCount;
                
                var newGroup = [
                    {
                        id: parseInt(element.value),
                        element: $(element)
                    }
                ];
                
                newGroup[0].element.siblings().first().removeClass('active');
                state.selection.groupMappings[newGroup[0].id] = groupId;
                
                $.each(selection, function(){
                    this.prop('checked', true);
                    this.siblings().first().removeClass('active');
                    
                    var id = parseInt(this.val());
                    
                    newGroup.push({
                        id: id,
                        element: this
                    });
                    
                    state.selection.groupMappings[id] = groupId;
                });
                
                state.selection.groups[groupId] = newGroup;
            });
            
            state.selection.selectedCount++;
            
            return true;
        };       
        var removeSelection = function (element, event) {
            var $element = $(element);
            var id = parseInt($element.val());
            
            var groupId = state.selection.groupMappings[id];
            
            if(!state.selection.groups.hasOwnProperty(groupId)) {
                throw Error('No selection group found');
            }
            
            var group = state.selection.groups[groupId];
            
            noListener(function(){
                for(var i = 0; i < group.length; i++) {
                    if(group[i].id === id) {
                        continue;
                    }
                    
                    group[i].element.prop('checked', false);
                };
            });
            
            delete state.selection.groupMappings[id];
            delete state.selection.groups[groupId];
            state.selection.selectedCount--;
            
            slotEnter.call($element.siblings().get(0));
            
            return true;
        };      
        var generateStateCacheKey = function(length, lessons) {
            return length + '_' + lessons;
        };       
        var clearSelection = function() {
            noListener(function(){
                for(var key in state.selection.groups) {
                    var group = state.selection.groups[key];

                    for(var element in group) {
                        var item = group[element];
                        item.element.prop('checked', false);
                    }
                }
            });
        };       
        var updateSelection = function() {
            noListener(function(){
                for(var key in state.selection.groups) {
                    var group = state.selection.groups[key];

                    for(var element in group) {
                        var item = group[element];
                        item.element.prop('checked', true);
                    }
                }
            });            
        };
        var init = function () {
            updateEvents();
            reset();
            updateState();
            // INIT WITH PREVIOUS STATE
        };
        var reset = function () {
            cache = {
                state: {

                }
            };
        };
        var updateState = function () {
            state = {
                length: length,
                lessons: lessons,
                selection: {
                    selectedCount: 0,
                    groups: {},
                    groupMappings: {}
                } 
            };
            
            var cacheKey = generateStateCacheKey(length, lessons);
            
            if(cache.state.hasOwnProperty(cacheKey)) {
                state = cache.state[cacheKey];
                updateSelection();
            }
        };
        var update = function() {
            if(state.length === length && state.lessons === lessons) {
                return;
            }
            
            var cacheKey = generateStateCacheKey(state.length, state.lessons);
            cache.state[cacheKey] = state;
            clearSelection();
            updateState();
        };
        this.changeStateHandler = function(_length, _lessons) {
            length = _length;
            lessons = _lessons;
            
            update();
        };
        this.changePageHandler = function () {
            updateEvents();
        };
        this.getCurrentSelection = function() {
            var flat = [];
            
            for(var i in state.selection.groups) {
                var group = state.selection.groups[i];
                
                for(var j = 0; j < group.length; j++) {
                    flat.push(group[j].id);
                }
            }
            
            return flat;
        };
        this.init = init.bind(this);
        
        updateEvents();
        reset();
        init();
    };
    window.langu.CalendarPicker = CalendarPicker;
    var CalendarPageSwitcher = function(calendarContainer) {
        var container = calendarContainer;
        var changeHandlers = [];
        var changing = false;
        
        this.addPageChangeListener = function(f) {
            var index = $.inArray(f, changeHandlers);
            if(-1 === index) {
                changeHandlers.push(f);
            }
        };
        this.removePageChangeListener = function(f) {
            var index = $.inArray(f, changeHandlers);
            if(-1 === index) {
                return;
            }
            
            changeHandlers.splice(index, 1);
        };    
        var executeHandlers = function(){
            for(var i = 0; i < changeHandlers.length; i++) {
                changeHandlers[i]();
            }
        }; 
        var changePage = function(e) {
            e.preventDefault();
            
            if(changing) {
                return;
            }
            
            changing = true;
            
            var self = $(this);
            var currentPage = container.find('.slots-picker-page.active');
            var dir = self.data('dir');
            var newPage;
            
            switch(dir) {
                case 'prev':
                    newPage = currentPage.prev('.slots-picker-page');
                    break;
                case 'next':
                    newPage = currentPage.next('.slots-picker-page');
                    break;
            };
            
            if(currentPage.hasClass('first') && dir === 'prev') {
                changing = false;
                return;
            }
            
            if(newPage.length) {
                newPage.addClass('active');
                currentPage.removeClass('active');
                executeHandlers();

                changing = false;
                return;
            } else {
                newPage = null;
            }
            
            var opt = {
                url: self.attr('href'),
                method: 'GET',
                cache: false,
            };

            changing = true;
            var call = $.ajax(opt);
            call.always(function(){
                changing = false;
            });
            call.fail(function(xhr, status, error){
                // handle on failure
                // with 410 -> xhr.responseText being error
                currentPage.addClass('first');
            });
            call.done(function(data, status, xhr){
                newPage = $(data);
                
                switch(dir) {
                    case 'prev':
                        newPage.insertBefore(currentPage);
                        break;
                    case 'next':
                        newPage.insertAfter(currentPage);
                        break;
                };
                
                newPage.addClass('active');
                currentPage.removeClass('active');
                new CalendarScroller(calendarContainer);
                executeHandlers();
            });
            
            return false;
        };
        
        container.on('click', '.calendar-page-switcher', changePage);
    };
    window.langu.CalendarPageSwitcher = CalendarPageSwitcher;
    var BookingSelection = function(bookingForm, getSelectedSlots, selectionReset) {
        var form = bookingForm;
        var formId = bookingForm.attr('id');
        var getSelectedSlotsFunc = getSelectedSlots;
        var selectionResetFunc = selectionReset;
        
        var submitHandler = function(e){

            var data = form.serializeArray();
            var minSlots = 1, maxSlots = 1;
            var newData = [];
            
            for(var i = 0; i < data.length; i++) {
                var el = data[i];
                
                if(el.name === formId + '[length]') {
                    minSlots = parseInt(el.value) / 30;
                    minSlots = minSlots > 0 ? minSlots : 1;
                }
                
                newData.push(el);
            }
            
            var service = form.find('input[name="' + formId + '[service]"]:checked');
            if(service.length) {
                maxSlots = parseInt(service.data('lessons')) * minSlots;
            }
            
            var slots = getSelectedSlotsFunc();
            
            if(slots.length < minSlots || slots.length > maxSlots || slots.length % minSlots) {
                // handle slots error
                window.FLASHES.addFlash('error', 'slots.incorrect_amount');
                return false;
            }
            
            for(var i = 0; i < slots.length; i++) {
                newData.push({
                    name: formId + '[slots][]',
                    value: slots[i]
                });
            }
            
            var promise = $.ajax({
                url: form.attr('action'),
                method: form.attr('method'),
                data: newData,
                dataType: 'json',
                cache: false
            });
            
            promise.fail(function(xhr){
                // handle something here, but we should never reach this point of code
                console.log(xhr.responseText);
            });
            
            promise.done(function(data, status, xhr){
                var json = xhr.responseJSON;
                
                if(json.error) {
                    // new calendar page ?
                    switch(json.error.type) {
                        case 'reservation':
                            var $page = $(json.calendar);
                            calendarContainer.find('.slots-picker-page').remove();
                            calendarContainer.append($page);
                            
                            window.FLASHES.addFlash('error', json.error.message);
                            selectionResetFunc();
                            break;
                        case 'slots':
                            calendarContainer.find('.slots-picker-page').find('input[type="checkbox"]:checked').prop('checked', false);
                            
                            window.FLASHES.addFlash('error', json.error.message);
                            selectionResetFunc();
                            // display error message
                            break;
                        case 'form':
                            
                            break;
                        default:
                            window.FLASHES.addFlash('error', json.error.message);
                            break;
                    }
                } else if(json.form) {
                    var $form = $(json.form);
                    form.after($form);
                    form.hide();
                    
                    $('html, body').animate({
                            scrollTop: form.closest('.sidebar').offset().top
                    }, 450);
                    
                    var trialForm = $form.find('form.trial-confirm-form');
                    if(trialForm.length) {
                        autosize(trialForm.find('textarea'));
                    }
                    
                    StepTwoForm($form);
                }
            });
            
            return false;
        };
        
        form.on('submit', submitHandler);
    };
    var StepTwoForm = function(formContainer) {
        var container = formContainer;
        var attachHandlers = function() {
            var form = formContainer.find('form');
            form.on('submit', validation);
            form.on('submit', submitHandler);
            form.on('click', 'a.back-button', back);
        };
        
        var back = function(e) {
            var self = $(this);
            
            var promise = $.ajax({
                url: self.attr('href'),
                method: 'GET',
                dataType: 'json',
                cache: false
            });
            
            promise.fail(function(xhr){
                // handle something here, but we should never reach this point of code
                console.log(xhr.responseText);
            });
            
            promise.done(function(data, status, xhr){
                var json = xhr.responseJSON;
                
                if(json.error) {
                    switch(json.error.type) {
                        case 'generic':
                        default:
                            window.FLASHES.addFlash('error', json.error.message);
                            // just show message
                            break;
                    }
                } else if(json.form) {
                    var outer = container.parent();
                    outer.html(json.form);
                     
                   $('html, body').animate({
                            scrollTop: outer.closest('.sidebar').offset().top
                    }, 450);
                    
                    initialize();
                }
            });
            
            return false;
        };
        
        var validation = function(e) {
            var self = $(this); // form
            
            if(self.hasClass('payment-method-form')) {
                // nothing to validate
                return true;
            } else if(self.hasClass('trial-confirm-form')) {
                var formId = self.attr('id');
                
                // validate if terms checked
                if(0 === self.find('input[name="'+ formId + '[terms]"]:checked').length) {
                    // form invalid, terms agreement checkbox not checked
//                    e.preventDefault();
                    e.stopImmediatePropagation();
                    window.FLASHES.addFlash('error', 'trial.booking.terms.not_checked');
                    
                    return false;
                }
                
                var message = self.find('textarea[name="'+ formId + '[message]"]');
                var min = message.attr('minlength');
                var max = message.attr('maxlength');
                var len = message.val().length;
                if(len < min) {
//                    e.preventDefault();
                    e.stopImmediatePropagation();
                    // form invalid, message is too short
                    window.FLASHES.addFlash('error', 'trial.booking.message.too_short');
                    
                    return false;
                } else if(len > max) {
                    e.stopImmediatePropagation();
                    // form invalid, message is too short
                    window.FLASHES.addFlash('error', 'trial.booking.message.too_long');
                    
                    return false;
                }
            }
        };
        
        var submitHandler = function (e) {            
            var form = $(this);
            
            var promise = $.ajax({
                url: form.attr('action'),
                method: form.attr('method'),
                data: form.serializeArray(),
                dataType: 'json',
                cache: false
            });
            
            promise.fail(function(xhr){
                // handle something here, but we should never reach this point of code
                console.log(xhr.responseText);
            });
            
            promise.done(function(data, status, xhr){
                var json = xhr.responseJSON;
                
                if(json.error) {
                    switch(json.error.type) {
                        case 'generic':
                        default:
                            window.FLASHES.addFlash('error', json.error.message);
                            // just show message
                            break;
                    }
                } else if(json.form) {
                    var $form = $(json.form);
                    form.replaceWith($form);
                     
                   $('html, body').animate({
                            scrollTop: $form.closest('.sidebar').offset().top
                    }, 450);
                    
                    StepTwoForm($form);
                }
            });
        };
        
        attachHandlers();
    };
    
    var initialize = function(){
        var bookingForm = $(document.querySelector('.selection-form'));
        if(!bookingForm.length) {
            return;
        }

        // global-ish variables
        var calendarContainer = bookingForm.find('.slots-picker').first();

        // actual stuff
        new CalendarScroller(calendarContainer);
        var selectionUpdater = new SelectionUpdater(bookingForm);

        var initialState = {
            selection: selectionUpdater.getCurrentState()
        };
        var calendarPicker = new CalendarPicker(calendarContainer, initialState);
        var calendarPageSwitcher = new CalendarPageSwitcher(calendarContainer);
        var bookingSelection = new BookingSelection(bookingForm, calendarPicker.getCurrentSelection, calendarPicker.init);

        selectionUpdater.addStateChangeListener(calendarPicker.changeStateHandler);
        selectionUpdater.addStateChangeListener(function(length, lessons){
            var $info = $('#multi-lesson-info');
            var $single = $('#slots-label-single');
            var $multi = $('#slots-label-multi');
            
            if(lessons === 1) { 
                $info.addClass('hidden');
                $single.removeClass('hidden');
                $multi.addClass('hidden');
            } else { 
                $info.removeClass('hidden'); 
                $multi.removeClass('hidden');
                $single.addClass('hidden');
            }
        });
        calendarPageSwitcher.addPageChangeListener(calendarPicker.changePageHandler);
    };
    
    initialize();
})(jQuery);
