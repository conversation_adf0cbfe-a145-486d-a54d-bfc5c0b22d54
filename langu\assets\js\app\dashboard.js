;
(function($) {
  var cancelLesson = function() {
    var self = $(this);
    var lessonId = self.data('id');

    $.blockUI();

    var call = $.ajax(self.attr('href'), {
      method: 'POST',
      dataType: "json",
      data: {
        'lessonId': lessonId
      }
    });

    call.fail(function(xhr, status, error) {
      $.unblockUI();

      if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
        var type = xhr.responseJSON.payload.messageType || 'error';
        var message = xhr.responseJSON.message;

        FLASHES.addFlash(type, message);
      }
    });

    call.done(function(data, status, xhr) {
      document.location.reload();
    });
  };


  var saveResources = function(e, modal) {
    e.preventDefault();

    var $form = $(this);
    var data = $form.serializeArray();

    var call = $.ajax($form.attr('action'), {
      method: 'POST',
      data: data,
      block: {
        context: $form
      }
    });

    call.fail(function(xhr, status, error) {
      if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
        var type = xhr.responseJSON.payload.messageType || 'error';
        var message = xhr.responseJSON.message;

        FLASHES.addFlash(type, message);
      }
    });

    call.done(function(data, status, xhr) {
      if ($.type(data) === 'string') {
        // we got form back with errors most probably :)
        $form.replaceWith($(data));
      } else {
        modal.close();

        if (null !== data) {
          var type = data.payload.messageType || 'success';
          var message = data.message;

          FLASHES.addFlash(type, message);
        }
      }
    });
  };

  var manageResources = function() {
    var self = $(this);

    var call = $.ajax(self.attr('href'), {
      method: 'GET',
      block: {
        context: null
      }
    });

    call.fail(function(xhr, status, error) {
      if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
        var type = xhr.responseJSON.payload.messageType || 'error';
        var message = xhr.responseJSON.message;

        FLASHES.addFlash(type, message);
      }
    });

    call.done(function(data, status, xhr) {
      var $form = $(data);

      var modal = langu.modal.instance($form);
      modal.open();
      var dialog = $(modal.dialog);
      dialog.on('click', '.add-objective', addObjective);
      dialog.on('click', '.collection-item-remove', removeObjective);
      dialog.on('submit', 'form', function(e) {
        saveResources.call(this, e, modal);
      });
    });
  };

  var addObjective = function(e) {
    e.preventDefault();

    var self = $(this);
    var collectionId = '#' + self.data('collection-target');
    var collectionContainer = $(collectionId);
    var itemCount = collectionContainer.children('.collection-item').length;

    var proto = collectionContainer.data('prototype');
    var instance = $(proto.replace(/__name__/g, itemCount + 1));
    instance.hide();

    collectionContainer.append(instance);
    instance.fadeIn(300);
  };

  var removeObjective = function(e) {
    e.preventDefault();

    var self = $(this);
    var collectionItem = self.closest('.collection-item');
    var collectionContainer = collectionItem.closest('.collection-container');
    var proto = collectionContainer.data('prototype');

    collectionItem.fadeOut(300).promise().done(function() {
      collectionItem.remove();

      var itemsLeft = collectionContainer.children('.collection-item');
      for (var i = 0; i < itemsLeft.length; i++) {
        var currentItem = $(itemsLeft.get(i));
        var instance = $(proto.replace(/__name__/g, i + 1));
        instance.children('.form-control').val(currentItem.children(
          '.form-control').val());
        currentItem.replaceWith(instance);
      }
    });
  };

  $('.lesson-action').on('click', function(e) {
    e.preventDefault();
    var self = $(this);

    switch (self.data('action')) {
    case 'cancel':
      var lType = self.data('type');
      langu.bbConfirmation.instance('lesson.'
        + lType
        + '.cancel.confirmation.title', 'lesson.'
        + lType
        + '.cancel.confirmation.content', function(result) {
          if (result === true) {
            cancelLesson.call(self);
          }
        },
        {
          confirm: 'lesson.' + lType + '.cancel.confirmation.confirm',
          cancel: 'lesson.' + lType + '.cancel.confirmation.cancel'
        }
      );
      break;
    case 'resources.manage':
      manageResources.call(self);
      break;
    }
  });

  var info = {
    load: function(url) {
      $.get(url)
        .done(info.show)
        .error(function() {

        });
    },
    show: function(data) {
      var info = $('#studentInfo');
      info.html('');
      info.append(data);
      $('#studentInfoBox').addClass('langu-modal--active');
    }
  };

  $('.student-info').on('click', function(e) {
    e.preventDefault();
    info.load($(this).data('info'));

  });

  $(document).mouseup(function(e) {
    var container = $('#studentInfo');
    if (!container.length) {
      return;
    }
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      $('.modal-close').click();
    }
  });

  var container = $('.info');
  var openDialog = function (e) {
      e.preventDefault();
      $('#w-dialog').find('#lesson_id').val($(this).attr('lesson-id'));
      //$(this).siblings(':submit').prop('disabled', true);
      var dialog = $('#w-dialog').clone();
      var modal = langu.modal.instance(dialog);

      modal.open();

      $('body').find('.langu-modal-dialog').width('25%');
  };
    container.on('click', '.gcalendar-btn', openDialog);

})(jQuery);
