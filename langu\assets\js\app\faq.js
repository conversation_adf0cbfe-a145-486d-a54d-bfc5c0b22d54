;(function($){

    // When FAQ item is clicked, collapse others.
    // Store ID as location hash.
    $('.question').click(function(e){
        var isActive = $(e.target).hasClass('active');
        $('.question').removeClass('active');
        if (!isActive) {
            $(e.target).addClass('active');
        }
        location.hash = $(e.target).attr('aria-controls');
        $('.collapse').collapse('hide');        
    });

    // Collapse FAQ item contained in location hash upon page load.
    $(document).ready(function () {
        if(location.hash != null && location.hash != ""){
            $('.collapse').removeClass('in');
            var answer = location.hash + '.collapse';
            var question = $(answer).prev('.question');
            $(answer).collapse('show');
            setTimeout(function(){
                $(question).addClass('active');
            },100);
        }
    });
})(jQuery);