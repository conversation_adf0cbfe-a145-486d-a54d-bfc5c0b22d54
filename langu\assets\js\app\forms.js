;(function($){
    var errorAdd = function(e, data) {
        var self = $(this);
        var errorList = self.find('.error-list');
        var errorCount = self.data('error-count') || 0;
        self.data('error-count', errorCount + 1);
        
        var msg = Translator.trans(data.message);
        
        var msgItem = $('<li>' + msg + '</li>');
        errorList.children('.error-list-inner').append(msgItem);
        
        if(errorCount === 0) {
            errorList.removeClass('hidden');
        }
    };
    
    var errorReset = function(e) {
        var self = $(this);
        self.data('error-count', 0);
        var errorList = self.find('.error-list');

        errorList.addClass('hidden');
        errorList.children('.error-list-inner').html('');
    };
    
    $(document).on('langu_form.errorAdd', 'form', errorAdd);
    $(document).on('langu_form.errorReset', 'form', errorReset);
})(jQuery);