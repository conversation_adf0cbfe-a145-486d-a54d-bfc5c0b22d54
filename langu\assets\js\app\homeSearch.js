;
(function ($, window, document, undefined) {
    var search = $('.lang-homepage-search');
    
    if(!search.length) {
        return;
    }
    
    $('.start-now').on('langu.focusSelect', function () {
        var selectizer = search[0].selectize;
        selectizer.focus();
    });

    

    search.selectize({
        maxOptions: 5,
        maxItems: 1,
        onInitialize: function () {
            var self = this;
            this.setValue([]);
            this.focus();
            this.on('item_add', function(value, item){
                if(self.isFull()) {
                    $('#search-icon-button').trigger('click');                    
                }
            });
        },
        onBlur: function () {
            if (this.items.length === 0 && this.currentResults.total > 0 && this.currentResults.query.length > 0) {
                this.setValue([this.currentResults.items[0].id]);
            }
        },
        openOnFocus: false,
    });

    var submitted = false;
    var toggleOnSearch = $('.toggle-on-search');
    var pickedLangTemplate = null;

    var continueHandlers = {
        language: function () {
            var self = $(this);
            var select = self.find('#appbundle_homepage_search_form_language');
            toggleOnSearch.hide();

            var selectizer = select[0].selectize;
            var selectedId = parseInt(selectizer.items[0]);
            
            if (selectedId && selectizer.options.hasOwnProperty(selectedId)) {
                var selectedOption = selectizer.options[selectedId];
                
                var next = $('.form-step[data-step="learning"]');
                var pickedLanguage = next.find('.picked-language');
                if(null === pickedLangTemplate) {
                    pickedLangTemplate = pickedLanguage.html();
                }
                
                var text = pickedLangTemplate.replace('%entered_language%', '<span class="emp">' + selectedOption.text + '</span>');
                pickedLanguage.html(text);
                self.removeClass('current');
                next.addClass('current');
            } else {
                var selectizer = select[0].selectize;
                var tooltip = selectizer.$wrapper.tooltip({'placement': 'auto', 'title': 'Pick the language first!', 'trigger': 'manual'});
                selectizer.$wrapper.tooltip('show');
            }
        },
        learning: function (checked) {
            var self = $(this);
            var $checked = $(checked);
            self.removeClass('current');
            var item = $checked.closest('.home-search-purpose').clone();
            item.find('label, input').remove();
            var next = $('.form-step[data-step="proficiency"]');
            next.find('.selected-purpose').replaceWith(item);
            next.addClass('current');
        },
        proficiency: function () {
            var self = $(this);
            if (!submitted) {
                submitted = true;
                self.closest('form').submit();
            }
        }
    };

    var backHandlers = {
        learning: function () {
            var self = $(this);
            var prev = $('.form-step[data-step="language"]');
            self.closest('second-form-front').removeClass('animate');
            self.removeClass('current');
            prev.addClass('current');
            toggleOnSearch.show();
            var selectizer = search[0].selectize;
            selectizer.focus();
        },
        proficiency: function () {

        }
    };

    $('[data-action="continue"]').on('click change', function () {
        var self = $(this);
        var $stepContainer = self.closest('.form-step');
        var step = $stepContainer.data('step');

        continueHandlers[step].call($stepContainer, self);
    });

    $('[data-action="back"]').on('click change', function () {
        var self = $(this);
        var $stepContainer = self.closest('.form-step');
        var step = $stepContainer.data('step');

        backHandlers[step].call($stepContainer, self);
    });
})(jQuery, window, document);
