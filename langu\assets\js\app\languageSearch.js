;
(function ($) {
    var items = $('select.selectize.lang-search');
    
    $.each(items, function(index, item){
        var item = $(item);
        var options = {
//            maxOptions: 5,
            maxItems: 1,
            onBlur: function () {
                if (this.items.length === 0 && this.currentResults.total > 0 && this.currentResults.query.length > 0) {
                    this.setValue([this.currentResults.items[0].id]);
                }
            },
            openOnFocus: true
        };
        
        if(item.hasClass('multiple')) {
            var maxItems = item.data('max-items');
            options.maxItems = maxItems;
        }
        
        if(item.hasClass('no-dropdown')) {
            options.openOnFocus = false;
        }

        if(item.hasClass('disable-on-init')) {
            options.onInitialize = function (){
                this.disable();
                this.$wrapper.removeClass('disable-on-init');
                item.removeClass('disable-on-init');
            };
        }    

        item.selectize(options);
    });    
})(jQuery);