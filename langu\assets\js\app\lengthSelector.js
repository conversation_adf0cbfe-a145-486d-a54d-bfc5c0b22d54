;
(function ($) {
    var item = $('select.selectize.selectize-length-selector');
    
    if(!item.length) {
        return;
    }
    
    var panelContent = item.parent().closest('.prices-panel-body');
    var tableRows = panelContent.find('.table tbody tr');
    
    var onEmptyValue = function (value) {
        value = parseInt(value) || null;
        
        if(value === null || this.items.length === 0) {
            if(this.currentResults.total > 0 && this.currentResults.query.length > 0) {
                value = this.currentResults.items[0].id;
            } else {
                value = 60;
            }        
            
            this.setValue([value]);
        }
        
        var current = tableRows.filter('.visible');
        current.removeClass('visible');
        var newCurrent = tableRows.filter('[data-length="' + value + '"]');
        newCurrent.addClass('visible');
    };
    
    item.selectize({
        maxItems: 1,
        hideSelected: true,
        openOnFocus: true,
        onInitialize: function () {
        },
        onChange: function (value) {
            onEmptyValue.call(this, value);
        }
    });    
})(jQuery);
