;
(function ($, undefined) {
    var categoriesList = $('.categories-list');
    var filesList = $('.files-list');
//
    var removeFile = function (e) {
        e.preventDefault();

        var self = $(this);
        var item = self.closest('.files-list-item');
        var resourceId = self.data('resource-id');

        item.fadeTo(200, 0.8);

        self.closest('.uploader-container').block();

        var call = $.ajax(self.attr('href'), {
            dataType: 'json',
            data: {resourceId: resourceId},
            method: 'DELETE',
        });

        call.fail(function (xhr, status, error) {
            self.closest('.uploader-container').unblock();
            
            item.fadeTo(200, 1);

            if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.payload.messageType || 'error';
                var message = xhr.responseJSON.message;

                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
//            item.fadeOut(300, function () {
//                item.remove();
//            });
//            
            location.reload();
        });
    };

    filesList.on('click', '.files-list-item-remove', removeFile);
    var uploadNodes = $('.category-panel [data-uploader]');

    $.each(uploadNodes, function (index, element) {
        var $element = $(element);

        $element.on('click', function (e) {
            e.preventDefault();
        });

        var uploader = $element.data('uploader');
        var url = $element.data('uploader-url');
        var uploaderContainer = $element.closest('.uploader-container');

        switch (uploader) {
            case 'dropzone':
                var dz = $element.dropzone({
                    url: url,
                    maxFilesize: $element.data('uploader-filesize'),
                    previewTemplate: '<span class="hidden"></span>',
                    init: function () {
                        this.on('sending', function (file, xhr, formData) {
                            uploaderContainer.block();
                        });

                        this.on('success', function (file, response) {
                            location.reload();
                        });

                        this.on('error', function (file, response, xhr) {
                            if(undefined !== xhr) {
                                if (response.message) {
                                    FLASHES.addFlash('error', Translator.trans(response.message));
                                } else if(response.error) {
                                    FLASHES.addFlash('error', Translator.trans(response.error));
                                } else {
                                    FLASHES.addFlash('error', Translator.trans('upload.error.generic'));
                                }
                            } else {
                                if($.type(response) === 'string') {
                                    FLASHES.addFlash('error', Translator.trans(response));
                                }
                            }
                            
                            uploaderContainer.unblock();
                        });

                        this.on('complete', function (file) {
                            this.removeFile(file);
                            uploaderContainer.unblock();
                        });
                    }
                });
                break;
            default:
                throw new Error('wrong uploader provider');
        }
    });
})(jQuery);
