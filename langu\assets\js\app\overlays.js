;(function($){
    var $languOverlay = $('.langu-overlay');

    // general close button
    var overlay_close_button = function(e){
        var self = $(this);
        self.closest('.langu-overlay').remove();
    };
    
    $languOverlay.on('click', '.close_on_click', overlay_close_button);
    
    // mobile overlay cookie button
    var mobile_overlay_close = function(e){
        var cookie = 'mobile_overlay_l2=1;domain=' + window.mainDomain + ';path=\'/\';expires=Fri, 31 Dec 9999 23:59:59 GMT';
        document.cookie = cookie;    
    };
    
    $languOverlay.on('click', '.mobile-go-button', mobile_overlay_close);
    
    // language overlay cookie button
    var language_overlay_close = function(e){
        var cookie = 'no_language_detection=1;domain=' + window.mainDomain + ';path=\'/\';expires=Fri, 31 Dec 9999 23:59:59 GMT';
        document.cookie = cookie;
    };
    
    $languOverlay.on('click', '#language-no-detect', language_overlay_close);
})(jQuery);