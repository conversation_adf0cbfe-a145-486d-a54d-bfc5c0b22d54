;
(function($){
    var langu = window.langu;

    var packageBookingFormSubmit = function(e) {
        var self = $(this);
        
        var formId = self.attr('id');
        var calendarPicker = self.data('calendarPicker');
        var slots = calendarPicker.getCurrentSelection();
        
        var data = self.serializeArray();
        var minSlots = parseInt(self.data('length')) / 30;
        var maxSlots = parseInt(self.data('lessons')) * minSlots;
        
        if(slots.length < minSlots || slots.length > maxSlots || slots.length % minSlots) {
            // handle slots error
            window.FLASHES.addFlash('error', 'slots.incorrect_amount');
            return false;
        }
        
        for(var i = 0; i < slots.length; i++) {
            data.push({
                name: formId + '[slots][]',
                value: slots[i]
            });
        }
        
        var promise = $.ajax({
            url: self.attr('action'),
            method: self.attr('method'),
            data: data,
            dataType: 'json',
            cache: false
        });
        
        promise.fail(function(xhr){
            // handle something here, but we should never reach this point of code
            console.log(xhr.responseText);
        });
        
        promise.done(function(data, status, xhr){
            var json = xhr.responseJSON;

            if(json.error) {
                switch(json.error.type) {
                    default:
                        window.FLASHES.addFlash('error', json.error.message);
                        break;
                }
            } else if(json.partial) {
                self.parent().html(json.partial);
            }
        });
        
        e.preventDefault();
    };

    var packageBookingForm = function() {
        var self = $(this);
        
        var promise = $.ajax({
            url: self.attr('href')
        });
        
        promise.done(function(data){
            if(data.error) {
                
            } else if (data.form) {
                var $partial = $(data.form);
                console.log($partial);
                console.log($partial.html());
                
                var modal = langu.modal.instance($partial, 'langu-modal-dialog-transparent');
                modal.open();
                var dialog = $(modal.dialog);
                dialog.on('submit', 'form', packageBookingFormSubmit);
                
                var $form = dialog.find('form');
                var calendarContainer = $form.find('.slots-picker').first();
                
                langu.CalendarScroller(calendarContainer);
                var pageSwitcher = new langu.CalendarPageSwitcher(calendarContainer);

                var pickerState = {
                    selection: {
                        length: $form.data('length'),
                        lessons: $form.data('lessons')
                    }
                };
                
                var calendarPicker = new langu.CalendarPicker(calendarContainer, pickerState);  
                $form.data('calendarPicker', calendarPicker);    
                pageSwitcher.addPageChangeListener(calendarPicker.changePageHandler);
            }
        });
        
        return false;
    };
    
    $('.package-booking').on('click', packageBookingForm);
})(jQuery);
