;
(function ($) {
    var sidebarContainer = $('.sidebar-inner');
    
    if(!sidebarContainer.length) {
        return;
    }
    
    var withdrawSubmit = function (modal) {
        var self = $(this);
        var data = self.serializeArray();

        var call = $.ajax(self.attr('action'), {
            method: 'POST',
            data: data,
            block: {
                context: self
            }
        });

        call.fail(function (xhr, status, error) {
            if (undefined !== xhr.responseJSON && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.hasOwnProperty('payload') ? (xhr.responseJSON.payload.messageType || 'error') : 'error';
                var message = xhr.responseJSON.message;
                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            if ($.type(data) === 'string') {
                // we got form back with errors most probably :)
                self.replaceWith($(data));
                self.find('.selectize-init').selectize({
                    maxItems: 1
                });
            } else {
                modal.close();

                if (undefined !== xhr.responseJSON && null !== xhr.responseJSON) {
                    var sidebar = null;
                    var type = 'success';
                    
                    if(xhr.responseJSON.hasOwnProperty('payload')) {
                        var payload = xhr.responseJSON.payload;
                        
                        if(payload.hasOwnProperty('messageType') && null !== payload.messageType) {
                            type = payload.messageType;
                        }
                        
                        if(payload.hasOwnProperty('partial') && null !== payload.partial) {
                            sidebar = $(payload.partial);
                            
                            var currentSidebar = sidebarContainer.children('.payments-sidebar');
                            
                            if(currentSidebar.length) {
                                currentSidebar.replaceWith(sidebar);
                            }
                        }
                    }
                    
                    var message = xhr.responseJSON.message;
                    FLASHES.addFlash(type, message);
                }
            }
        });
    };
    
    var accountType = function (modal) {
        var self = $(this);
        var container = $(modal.dialog);

        var call = $.ajax(self.attr('href'), {
            method: 'GET',
            block: {
                context: self.closest('.withdraw-panel-body')
            }
        });

        call.fail(function (xhr, status, error) {
            if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.payload.messageType || 'error';
                var message = xhr.responseJSON.message;

                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            var form = $(data);
            container.html(form);

            container.find('.selectize-init').selectize({
                maxItems: 1
            });

            $(modal.dialog).on('submit', 'form', function (e) {
                e.preventDefault();
                withdrawSubmit.call(this, modal);
            });
        });
    };
    
    var accountTypeForm = function (e) {
        e.preventDefault();
        var dialog = $('#withdraw-dialog').clone();
        var modal = langu.modal.instance(dialog);
        modal.open();

        $(modal.dialog).on('click', '.withdraw-account-type', function (e) {
            e.preventDefault();
            accountType.call(this, modal);
        });
    };

    sidebarContainer.on('click', '.withdraw-btn', accountTypeForm);
})(jQuery);
