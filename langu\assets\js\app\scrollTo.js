;(function($, document, window, undefined){
    $('.scroll-to').on('click', function(e){
        e.preventDefault();
        var self = $(this);
        var destElement = $(self.attr('href'));
        
        if(destElement.length === 0) {
            return;
        }
        
        var destPos = destElement.offset().top;
                
        $('html, body').animate({
            scrollTop: destPos
    }, 450, function(){
        var cb = self.data('callback');
        
        if(null !== cb && undefined !== cb) {
            self.trigger(cb);
        }
    });
    });
})(jQuery, document, window);