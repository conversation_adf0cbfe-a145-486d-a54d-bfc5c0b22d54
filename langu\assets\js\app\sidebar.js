;(function($, document, window, undefined) {
  var $forgotPasswordBox = $('#forgotPasswordBox'),
    $loginBox = $('#loginBox'),
    loginActiveClass = 'active',
    offAnywhere = function(e) {
      var target = $(e.target);
      if (!target.parents('div#loginBox').length
        && !target.parents('div#forgotPasswordBox').length
      ) {
        $loginBox.removeClass(loginActiveClass);
        $(this).off(e);
      }
    };

  $(document).on('click', '.sidebar-toggle', function(e) {
    e.preventDefault();
    e.stopImmediatePropagation();
    if (!$loginBox.hasClass('active')) {
      $loginBox.addClass(loginActiveClass);
      $(document).on('click', offAnywhere);
      $(document.body).addClass('no-overflow');
    } else {
      $loginBox.removeClass(loginActiveClass);
      $(document).off('click', offAnywhere);
      $(document.body).removeClass('no-overflow');
    }
  });

  $('.in-button').on('click', function(e) {
    e.preventDefault();
    $(this).closest('.sidebar').toggleClass('in');
  });

  $('.login-sidebar__forgot').click(function(e) {
    $forgotPasswordBox.addClass('langu-modal--active');
  });

  $('#formForgotPassword').submit(function(e) {
    var $error = $('#errorForgotPassword'),
      $success = $('#successForgotPassword');
    $error.addClass('hidden');
    $success.addClass('hidden');
    e.preventDefault();
    e.stopPropagation();
    $.ajax({
      async: false,
      cache: false,
      dataType: 'text',
      data: {
        email: $('#inputForgotPassword').val(),
      },
      type: 'POST',
      url: '/user/forgot',
      success: function(response) {
        $success.removeClass('hidden');
      },
      error: function(response) {
        if (response.status === 422) {
          $error.removeClass('hidden');
        }
      }
    });
  });

  var $inputLoginUsername = $('#login_form_username'),
    $inputLoginPassword = $('#login_form_password');

  $inputLoginUsername.focus(function() {
    $(this).parent().removeClass('login-sidebar__form-input--email');
  });
  $inputLoginPassword.focus(function() {
    $(this).parent().removeClass('login-sidebar__form-input--password');
  });

  $inputLoginUsername.focusout(function() {
    if (!$(this).val()) {
      $(this).parent().addClass('login-sidebar__form-input--email');
    }
  });
  $inputLoginPassword.focusout(function() {
    if (!$(this).val()) {
      $(this).parent().addClass('login-sidebar__form-input--password');
    }
  });


})(jQuery, document, window);