;(function($){
    $("#skip_rating").on('click', function (e) {
        e.preventDefault();
        $('input, textarea').val('');
        $('#skip_rating_form').val(0);

        $('#languRating').val(5);
        $('#lessonRating').val(5);
        $('#teacherRating').val(5);

        $('#submit_rating').trigger('click');
    });

    $('#submit_rating').mousemove(function () {
        var langu_rating = $('#languRating').val();
        var lesson_rating = $('#lessonRating').val();
        var teacher_rating = $('#teacherRating').val();

        if(langu_rating == ''){
            $('#languRating').val(5);
        }

        if(lesson_rating == ''){
            $('#lessonRating').val(5);
        }

        if(teacher_rating == ''){
            $('#teacherRating').val(5);
        }
    });

})(jQuery);
