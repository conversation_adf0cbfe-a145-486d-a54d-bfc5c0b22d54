;
(function ($, window, document, undefined) {
    var fullSearchForm = $('.full-search-form');
    
    if(fullSearchForm.length === 0) {
        return;
    }
    
    fullSearchForm.on('click', '.form-container-toggle', function (e) {
        e.preventDefault();
        var self = $(this);
        var container = self.closest('.form-container');
        if (container.hasClass('active')) {
            container.removeClass('active');
            container.children('a').attr('aria-expanded', 'false');
        } else {
            var currentActive = container.siblings('.active');
            currentActive.removeClass('active');
            currentActive.children('a').attr('aria-expanded', 'false');
            container.addClass('active');
            container.children('a').attr('aria-expanded', 'true');
        }
    });

    var preferenceContainer = $('#appbundle_homepage_search_form_teacherPreference_panel');
    var selectsContainer = preferenceContainer.find('.match-languages-container');
    var selects = selectsContainer.find('select.selectize');

    preferenceContainer.on('change', 'input[type="radio"]', function (e) {
        var self = $(this);
        var value = self.val();

        switch (parseInt(value)) {
            case 0: // any teacher
            case 1: //is a native speaker
                for(var i = 0; i < selects.length; i++) {
                    var selectize = selects[i].selectize;
                    if (null === selectize) {
                        continue;
                    }

                    selectize.disable();
                }
                
                selectsContainer.addClass('hidden');
                break;
            case 2: // speaks my native language
                for(var i = 0; i < selects.length; i++) {
                    var selectize = selects[i].selectize;
                    if (null === selectize) {
                        continue;
                    }

                    selectize.enable();
                }

                selectsContainer.removeClass('hidden');
                break;
        }
    });

    var motivationContainer = $('#appbundle_homepage_search_form_motivation_panel');
    var specialityContainer = motivationContainer.find('.speciality-fields');
    var specialityRadioButtons = specialityContainer.find('.speciality-radio-buttons').find('.radio-button-container');
    
    var specialityFormName = specialityRadioButtons.first().find('input[type="radio"]').attr('name');
    
    var motivationSelectionChanged = function (value) {
        if (value > 0) {
            for(var i = 0; i < specialityRadioButtons.length; i++) {
                var radioButtonContainer = $(specialityRadioButtons[i]);
                var radioButton = radioButtonContainer.find('input[type="radio"]');
                var motivation = parseInt(radioButton.data('motivation'));
                
                if(motivation !== value) {
                    if(radioButton.is(':checked')) {
                        specialityContainer.find('#speciality-default')[0].checked = true;
                    }
                    
                    radioButtonContainer.addClass('hidden');
//                    radioButtonContainer.detach();
                } else {
//                    radioButtonContainer.appendTo(specialityContainer.find('.speciality-radio-buttons'));
                    radioButton.parent().removeClass('hidden');
                }
            }
            
            specialityContainer.removeClass('hidden');
        } else {
            specialityContainer.addClass('hidden');
            specialityContainer.find('#speciality-default')[0].checked = true;
        }
    };

    var motivationRadioButtons = motivationContainer.find('.motivation-fields, .motivation-radio-buttons');
    motivationRadioButtons.on('change', 'input[type="radio"]', function(){
        var self = $(this);
        var value = parseInt(self.val());
        motivationSelectionChanged(value);
    });
    
    var languageContainer = $('#appbundle_homepage_search_form_language_panel');
    var langSelect = languageContainer.find('select.selectize');
    var select = langSelect[0].selectize;
    var lastValue = select.items[0] || 0;
    var cache = {
        
    };
        
    select.on('blur', function(){
        var newValue = this.items[0] || 0;
        if(newValue === lastValue) {
            return;
        }
        
        lastValue = newValue;
                
        var url = specialityContainer.find('.speciality-radio-buttons').data('url').replace('/languageId', lastValue ? '/' + lastValue : '');
        
        var call = $.ajax({
            url: url,
            dateType: 'json'
        });
        
        call.fail(function(){
        });
    });
    
    $(function(){
        var firstContainer = fullSearchForm.find('.form-containers').children('.form-container').first();
        firstContainer.addClass('active');
        firstContainer.children('a').attr('aria-expanded', 'true');
    });
})(jQuery, window, document);
