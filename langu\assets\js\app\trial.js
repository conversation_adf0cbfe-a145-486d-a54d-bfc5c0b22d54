;
(function ($) {
    var bookLessonSubmit = function (e, modal) {
        e.preventDefault();

        var $form = $(this);
        var data = $form.serializeArray();

        var call = $.ajax($form.attr('action'), {
            method: 'POST',
            data: data,
            block: {
                context: $form
            }
        });

        call.fail(function (xhr, status, error) {
            if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.payload.messageType || 'error';
                var message = xhr.responseJSON.message;

                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            if ($.type(data) === 'string') {
                // we got form back with errors most probably :)
                $form.closest('.langu-modal-dialog-content').html($(data));
            } else {
                modal.close();

                if (null !== data) {
                    var type = data.payload.messageType || 'success';
                    var message = data.message;

                    FLASHES.addFlash(type, message);
                }
            }
        });
    };

    var bookLessonFormChange = function (e) {
        e.preventDefault();
        var self = $(this);
        var $content = self.closest('.langu-modal-dialog-content');

        var call = $.ajax(self.attr('href'), {
            method: 'GET',
            block: {
                context: $content
            }
        });

        call.fail(function (xhr, status, error) {
            if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.payload.messageType || 'error';
                var message = xhr.responseJSON.message;

                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            var $form = $(data);
            $content.html($form);
        });
    };

    var bookLessonForm = function (e) {
        e.preventDefault();
        var self = $(this);

        if(self.hasClass('sidebar-toggle')) {
            return;
        }

        var call = $.ajax(self.attr('href'), {
            method: 'GET',
            block: {
                context: null
            }
        });

        call.fail(function (xhr, status, error) {
            if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.payload.messageType || 'error';
                var message = xhr.responseJSON.message;

                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            var $form = $(data);

            var modal = langu.modal.instance($form);
            modal.open();
            var dialog = $(modal.dialog);
            dialog.on('click', '.arrow-row-slide', window.slidePicker);
            dialog.on('click', '.pagination a', bookLessonFormChange);
            dialog.on('submit', 'form', function (e) {
                bookLessonSubmit.call(this, e, modal);
            });
        });
    };

    $('.free-trial-booking').on('click', bookLessonForm);
})(jQuery);