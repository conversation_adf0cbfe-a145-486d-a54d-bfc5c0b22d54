;
(function ($) {
    var uploadNodes = $('.settings-panel [data-uploader]');

    $.each(uploadNodes, function (index, element) {
        var uploadBtn = $(element);
        var uploadContainer = uploadBtn.closest('.uploader-container');
        var uploader = uploadBtn.data('uploader');
        var url = uploadBtn.data('uploader-url');
        var type = uploadBtn.data('uploader-type');

        switch (uploader)
        {
            case 'dropzone':
                var dz = uploadBtn.dropzone({
                    url: url,
                    previewTemplate: '<span class="hidden"></span>',
                    filesizeBase: 1024,
                    maxFilesize: (type === 'image' ? 8 : 2),
                    init: function () {

                        this.on('sending', function (file, xhr, formData) {
                            var filter = uploadBtn.data('image-filter') || false;

                            if (false !== filter) {
                                formData.append('filter_name', filter);
                            }

                            uploadContainer.block();
                        });

                        this.on('success', function (file, response) {
                            if (type === 'file') {
                                var downloadBtn = uploadContainer.find('.uploader-download');
                                var emptyBtn = uploadContainer.find('.uploader-empty');
                                var input = uploadContainer.find('.uploader-input');

                                if (downloadBtn.length) {
                                    downloadBtn.attr('href', response.url);
                                    downloadBtn.removeClass('hidden');
                                }

                                if (input.length) {
                                    input.val(response.url);
                                }

                                if (emptyBtn.length) {
                                    emptyBtn.remove();
                                }
                            } else if (type === 'image') {
                                uploadBtn.attr('src', response.displayUrl ? response.displayUrl : response.url);
                            }
                        });

                        this.on('error', function (file, response, xhr) {
                            if (undefined !== xhr) {
                                if (response.message) {
                                    FLASHES.addFlash('error', Translator.trans(response.message));
                                } else if (response.error) {
                                    //FLASHES.addFlash('error', Translator.trans(response.error));
                                    FLASHES.addFlash('error', 'Please reload page');
                                } else {
                                    FLASHES.addFlash('error', Translator.trans('upload.error.generic'));
                                }
                            } else {
                                if ($.type(response) === 'string') {
                                    FLASHES.addFlash('error', Translator.trans(response));
                                }
                            }

                            uploadContainer.unblock();
                        });

                        this.on('complete', function (file) {
                            this.removeFile(file);
                            uploadContainer.unblock();
                        });
                    }
                });
                break;
            default:
                throw new Error('Wrong uploader type');
        }
    });

//    var orderRows = function (table) {
//        var $table = $(table);
//        var $tbody = $table.children('tbody');
//        var rows = $tbody.find('tr').get();
//        rows.sort(function (r1, r2) {
//            var $r1 = $(r1);
//            var $r2 = $(r2);
//
//            var length1 = $r1.data('length');
//            var length2 = $r2.data('length');
//
//            if (length1 > length2) {
//                return 1;
//            } else if (length1 < length2) {
//                return -1;
//            } else {
//                var lessons1 = $r1.data('lessons');
//                var lessons2 = $r2.data('lessons');
//
//                if (lessons1 > lessons2) {
//                    return 1;
//                } else if (lessons1 < lessons2) {
//                    return -1;
//                }
//            }
//
//            return 0;
//        });
//
//        $.each(rows, function (index, row) {
//            $tbody.append(row);
//        });
//    };
//
//    var serviceSubmitted = function (e, modal) {
//        e.preventDefault();
//
//        var self = $(this);
//        var data = self.serializeArray();
//
//        var call = $.ajax(self.attr('action'), {
//            method: 'POST',
//            data: data,
//            block: {
//                context: self
//            }
//        });
//
//        call.fail(function (xhr, status, error) {
//            if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
//                var type = xhr.responseJSON.payload.messageType || 'error';
//                var message = xhr.responseJSON.message;
//
//                FLASHES.addFlash(type, message);
//            }
//        });
//
//        call.done(function (data, status, xhr) {
//            if ($.type(data) === 'string') {
//                // we got form back with errors most probably :)
//                self.replaceWith($(data));
//            } else {
//                var type = data.payload.newStudents ? 'new' : 'regular';
//                var row = modal.row;
//                var newRow = $(data.payload.partial);
//                var selector = '.pricing-panel[data-pricing="' + type + '"]';
//                var container = row.closest(selector);
//
//                if (container.length === 0) {
//                    row.remove();
//                    var table = $(selector).find('.pricing-panel-table');
//                    table.children('tbody').append(newRow);
//                    orderRows(table);
//                } else {
//                    row.replaceWith(newRow);
//                    var table = newRow.closest('.pricing-panel-table');
//                    orderRows(table);
//                }
//
//                modal.close();
//
//                if (null !== data) {
//                    var type = data.payload.messageType || 'success';
//                    var message = data.message;
//
//                    FLASHES.addFlash(type, message);
//                }
//            }
//        });
//    };

    var qualificationSubmitted = function (e, modal) {
        e.preventDefault();

        var self = $(this);
        var data = new FormData(this);

        var call = $.ajax(self.attr('action'), {
            method: 'POST',
            data: data,
            dataType: 'json',
            contentType: false,
            processData: false,
            block: {
                context: self
            }
        });

        call.fail(function (xhr, status, error) {
            if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.payload.messageType || 'error';
                var message = xhr.responseJSON.message;

                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            if ($.type(data) === 'string') {
                // we got form back with errors most probably :)
                self.replaceWith($(data));
            } else {
                var row = modal.row;
                var newRow = $(data.payload.partial);

                if (row !== null) {
                    row.replaceWith(newRow);
                } else {
                    $('.teaching-qualifications-container').find('tbody').append(newRow);
                }

                modal.close();

                if (null !== data) {
                    var type = data.payload.messageType || 'success';
                    var message = data.message;

                    FLASHES.addFlash(type, message);
                }
            }
        });
    };

    $('.teaching-qualifications-container').on('click', '.qualification-panel-action', function (e) {
        e.preventDefault();
        var self = $(this);
        var action = self.data('action') || 'edit';

        switch (action)
        {
            case 'edit':
                var call = $.ajax(self.attr('href'), {
                    method: 'GET',
                    block: {
                        context: null
                    }
                });

                call.fail(function (xhr, status, error) {
                    if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                        var type = xhr.responseJSON.payload.messageType || 'error';
                        var message = xhr.responseJSON.message;

                        FLASHES.addFlash(type, message);
                    }
                });

                call.done(function (data, status, xhr) {
                    var form = $(data);
                    var modal = langu.modal.instance(form, 'langu-modal-dialog langu-modal-dialog-short');

                    modal.row = self.closest('tr');
                    modal.open();

                    $(modal.dialog).on('submit', 'form', function (e) {
                        qualificationSubmitted.call(this, e, modal);
                    });
                });
                break;
            case 'delete':
                var qualificationId = self.data('qualification-id');

                var call = $.ajax(self.attr('href'), {
                    dataType: 'json',
                    data: {qualificationId: qualificationId},
                    method: 'DELETE',
                    block: {
                        context: self.closest('.teaching-qualifications-container')
                    }
                });

                call.fail(function (xhr, status, error) {
                    if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                        var type = xhr.responseJSON.payload.messageType || 'error';
                        var message = xhr.responseJSON.message;

                        FLASHES.addFlash(type, message);
                    }
                });

                call.done(function (data, status, xhr) {
                    self.closest('tr').fadeOut(300);

                    if (null !== data) {
                        var type = data.payload.messageType || 'success';
                        var message = data.message;

                        FLASHES.addFlash(type, message);
                    }
                });
                break;
            case 'create':
                var call = $.ajax(self.attr('href'), {
                    method: 'GET',
                    block: {
                        context: null
                    }
                });

                call.fail(function (xhr, status, error) {
                    if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                        var type = xhr.responseJSON.payload.messageType || 'error';
                        var message = xhr.responseJSON.message;

                        FLASHES.addFlash(type, message);
                    }
                });

                call.done(function (data, status, xhr) {
                    var form = $(data);
                    var modal = langu.modal.instance(form);

                    modal.row = null;
                    modal.open();

                    $(modal.dialog).on('submit', 'form', function (e) {
                        qualificationSubmitted.call(this, e, modal);
                    });
                });

                break;
            default:
                throw new Error('wrong action type');
        }
    });

//    $('.userinfo-details').on('click', '.pricing-panel-action', function (e) {
//        e.preventDefault();
//
//        var self = $(this);
//        var action = self.data('action') || 'edit';
//
//        switch (action)
//        {
//            case 'edit':
//                var call = $.ajax(self.attr('href'), {
//                    method: 'GET',
//                    block: {
//                        context: null
//                    }
//                });
//
//                call.fail(function (xhr, status, error) {
//                    if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
//                        var type = xhr.responseJSON.payload.messageType || 'error';
//                        var message = xhr.responseJSON.message;
//
//                        FLASHES.addFlash(type, message);
//                    }
//                });
//
//                call.done(function (data, status, xhr) {
//                    var form = $(data);
//                    var modal = langu.modal.instance(form, 'langu-modal-dialog-short');
//
//                    modal.row = self.closest('tr');
//                    modal.open();
//
//                    $(modal.dialog).on('submit', 'form', function (e) {
//                        serviceSubmitted.call(this, e, modal);
//                    });
//                });
//                break;
//            case 'delete':
//                var serviceId = self.data('service-id');
//
//                var call = $.ajax(self.attr('href'), {
//                    dataType: 'json',
//                    data: {serviceId: serviceId},
//                    method: 'DELETE',
//                    block: {
//                        context: self.closest('.userinfo')
//                    }
//                });
//
//                call.fail(function (xhr, status, error) {
//                    if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
//                        var type = xhr.responseJSON.payload.messageType || 'error';
//                        var message = xhr.responseJSON.message;
//
//                        FLASHES.addFlash(type, message);
//                    }
//                });
//
//                call.done(function (data, status, xhr) {
//                    self.closest('tr').fadeOut(300);
//
//                    if (null !== data) {
//                        var type = data.payload.messageType || 'success';
//                        var message = data.message;
//
//                        FLASHES.addFlash(type, message);
//                    }
//                });
//                break;
//            case 'create':
//                var call = $.ajax(self.attr('href'), {
//                    method: 'GET',
//                    block: {
//                        context: null
//                    }
//                });
//
//                call.fail(function (xhr, status, error) {
//                    if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
//                        var type = xhr.responseJSON.payload.messageType || 'error';
//                        var message = xhr.responseJSON.message;
//
//                        FLASHES.addFlash(type, message);
//                    }
//                });
//
//                call.done(function (data, status, xhr) {
//                    var form = $(data);
//                    var modal = langu.modal.instance(form, 'langu-modal-dialog-short');
//
//                    modal.row = self.closest('tr');
//                    modal.open();
//
//                    $(modal.dialog).on('submit', 'form', function (e) {
//                        serviceSubmitted.call(this, e, modal);
//                    });
//                });
//
//                break;
//            default:
//                throw new Error('wrong action type');
//        }
//    });

    $('.options-section-header').on('click', function () {
        $('.options-section-header.active').removeClass('active');

        var self = $(this);
        self.addClass('active');
        var autosized = self.next().find('textarea[data-autosize], textarea.autosize');
        autosize.update(autosized);
    });
//
//    $('.free-trial-checkbox').on('change', function () {
//        var self = $(this);
//
//        var container = self.closest('.userinfo-details-content');
//
//        if (self.is(':checked')) {
//            container.next().addClass('hidden');
//            container.next().next().addClass('hidden');
//        } else {
//            container.next().removeClass('hidden');
//            container.next().next().removeClass('hidden');
//        }
//    });
    
    var paidTrialPrice = $('.paid-trial-price');
    
    $('.free-trial-radio').on('change', function () {
        var self = $(this);

        if(parseInt(self.val()) === 2) {
            paidTrialPrice[0].disabled = false;
            paidTrialPrice[0].focus();
        } else {
            paidTrialPrice[0].disabled = true;
        }
    });
    
    var initDraggableLists = function (container) {    
                
        var selectableList = document.getElementById('specialities-selectable');
        var selectedList = document.getElementById('specialities-selected');
        var drake = dragula([selectableList, selectedList], {
            revertOnSpill: true,
            moves: function (item, source, handle, sibling) {
                if(source !== selectableList) {
                    return true;
                }

                var hiddenSpecialities = $('.hiddenSpeciality ').length;

                var allowedNumberElements = 8 + hiddenSpecialities;

                if(selectedList.childElementCount < allowedNumberElements) {
                    return true;
                }
                
                return false;
            }
        });
    };

    var submitSpecialitiesManager = function (e, modal) {
        e.preventDefault();
        var self = $(this);

        var data = self.serializeArray();

        var call = $.ajax(self.attr('action'), {
            method: 'POST',
            data: data,
            block: {
                context: self
            }
        });
        
        call.fail(function (xhr, status, error) {
            if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.payload.messageType || 'error';
                var message = xhr.responseJSON.message;

                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            if ($.type(data) === 'string') {
                var $data = $(data);
                // we got form back with errors most probably :)
                self.replaceWith($data);
                initDraggableLists(modal.opts.content);
            } else {
                modal.close();

                if (null !== data) {
                    var type = data.payload.messageType || 'success';
                    var message = data.message;

                    FLASHES.addFlash(type, message);
                }
            }
        });
    };

    var openSpecialitiesManager = function (e) {
        e.preventDefault();

        var self = $(this);

        var call = $.ajax(self.attr('href'), {
            method: 'GET',
            block: {
                context: null
            }
        });

        call.fail(function (xhr, status, error) {
            if (xhr.hasOwnProperty('responseJSON') && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.payload.messageType || 'error';
                var message = xhr.responseJSON.message;

                FLASHES.addFlash(type, message);
            }
        });

        call.done(function (data, status, xhr) {
            var form = $(data);
            var modal = langu.modal.instance(form, 'langu-modal-dialog langu-modal-dialog-short', {
                afterOpen: function () {
                    initDraggableLists(modal.opts.content);
                }
            });

            modal.open();            

            $(modal.dialog).on('submit', 'form', function (e) {
                submitSpecialitiesManager.call(this, e, modal);
            });
        });
    };

    $('.specialities-manager').on('click', openSpecialitiesManager);
})(jQuery);
