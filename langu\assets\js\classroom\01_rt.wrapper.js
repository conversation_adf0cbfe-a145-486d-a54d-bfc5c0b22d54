;
(function (window, undefined) {

// isArray method
    if (typeof Array.isArray === 'undefined') {
        Array.isArray = function (obj) {
            return Object.prototype.toString.call(obj) === '[object Array]';
        };
    }

// isObject method
    if (typeof Object.isObject === 'undefined') {
        Object.isObject = function (obj) {
            return (!Array.isArray(obj) && obj === Object(obj));
        };
    }

// isString method

    if (typeof String.isString === 'undefined') {
        String.isString = function (obj) {
            return Object.prototype.toString.call(obj) === '[object String]';
        };
    }

    window.rt = {};

    rt.RealtimeWrapper = function (clientId, options) {
        this.init(clientId, options);
    }

    rt.RealtimeWrapper.prototype = {
        clientId: null,
        mimeType: 'application/vnd.google-apps.drive-sdk',
        accessToken: null,
        init: function (clientId, options) {
            if (clientId === undefined) {
                throw new Error('No Client ID specified');
            }

            if (!String.isString(clientId)) {
                throw new Error('Given Client ID is not a string');
            }

            this.clientId = clientId;
            this.initOptions(options);
            this.bind();
        },
        start: function(startCallback) {
            var that = this;
            window.gapi.load('auth2,auth:client,drive-realtime,drive-share', {
                callback: function () {
                    window.gapi.auth.setToken(that.accessToken);
                    window.gapi.auth.token = that.accessToken;

                    that.tokenRefresh(startCallback);
                }
            });            
        },
        initOptions: function (options) {
            if (options) {
                if (options.hasOwnProperty('accessToken')) {
                    if (!Object.isObject(options.accessToken)) {
                        throw new Error('Access token expects to be an Associative Array (Object)');
                    }

                    this.accessToken = options.accessToken;
                }

                if (options.hasOwnProperty('mimeType')) {
                    if (!String.isString(options.mimeType)) {
                        throw new Error('Mime type expects to be a string');
                    }

                    this.mimeType = options.mimeType;
                }

                if (options.hasOwnProperty('userId')) {
                    if (!String.isString(options.userId)) {
                        throw new Error('Option userId expects to be a string');
                    }

                    this.userId = options.userId;
                } else {
                    throw new Error('Required option userId has not been set');
                }

                if (options.hasOwnProperty('scope')) {
                    if (String.isString(options.scope)) {
                        var scopeArray = options.scope.split(' ');
                        this.scope = scopeArray;
                    } else if (Array.isArray(options.scope)) {
                        this.scope = options.scope;
                    } else {
                        throw new Error('Option scope must be an array or a string');
                    }
                } else {
                    throw new Error('Required option scope has not been set');
                }
            }
        },
        bind: function () {
            this.onError = this.onError.bind(this);
        },
        getParam: function (urlParam) {
            var regExp = new RegExp(urlParam + '=(.*?)($|&)', 'g');
            var match = window.location.search.match(regExp);
            if (match && match.length) {
                match = match[0];
                return match.replace(urlParam + '=', '').replace('&', '');
            }

            return null;
        },
        onError: function (error) {
            if (error.type == window.gapi.drive.realtime.ErrorType
                    .TOKEN_REFRESH_REQUIRED) {
                var that = this;
                this.tokenRefresh(function () {
                    console.log('Error, auth refreshed');
                    
                    var lastCall = that.lastCall;
                    
                    if(null !== lastCall) {
                        this[lastCall.method].apply(this, lastCall.arguments);
                        this.lastCall = null;
                    }
                });
            } else if (error.type == window.gapi.drive.realtime.ErrorType
                    .CLIENT_ERROR) {
                alert('An Error happened: ' + error.message);
            } else if (error.type == window.gapi.drive.realtime.ErrorType.NOT_FOUND) {
                alert('The file was not found. It does not exist or you do not have ' +
                        'read access to the file.');
            } else if (error.type == window.gapi.drive.realtime.ErrorType.FORBIDDEN) {
                alert('You do not have access to this file. Try having the owner share' +
                        'it with you from Google Drive.');
            }
        },
        create: function (title, mail, callback) {
            var that = this;
            window.gapi.client.load('drive', 'v2', function() {

                var insertHash = {
                    'resource': {
                        mimeType: that.mimeType,
                        title: title
                    }
                };
                
                var request = window.gapi.client.drive.files.insert(insertHash);  
                request.execute(function(response){
                    if(response.error) {
                        throw new Error(response.error.message);
                    }
                    
                    var body = {
                        'value': mail,
                        'type': "user",
                        'role': "writer"
                    };

                    var request = window.gapi.client.drive.permissions.insert({
                        'fileId': response.id,
                        'sendNotificationEmails': false,
                        'resource': body
                    });

                    request.execute(function (_response) {
                        if(_response.error) {
                            throw new Error(_response.error.message);
                        }
                        
                        callback(response);
                    });
                });
            });
        },
        attachHandler: function(object, events, callback) {
            if(Array.isArray(events)) {
                for(var event in events) {
                    object.addEventListener(events[event], callback);
                }
            } else {
                object.addEventListener(events, callback);
            }
        },
        load: function (documentId, onFileLoaded, initializeModel) {
            this.lastCall = {
                method: 'load',
                arguments: [documentId, onFileLoaded, initializeModel]
            };

            window.gapi.drive.realtime.load(documentId, function (doc) {
                onFileLoaded(doc);
            }, initializeModel, this.onError);
        },
        tokenRefresh: function (callback) {
            var auth_options = {
                authuser: -1,
                immediate: true,
                client_id: this.clientId,
                user_id: this.userId,
                scope: this.scope
            };
            
            window.gapi.auth.authorize(auth_options, function(result){
                callback(result);
            });
        }
    };
})(window);