;
(function ($) {
    var buzzButton = $('#buzz-student');
    if(!buzzButton) {
        return;
    }
    
    var disabledDescription = buzzButton.data('disabled-title');
    var enabledDescription = buzzButton.data('enabled-title');
    
    if(buzzButton.hasClass('disabled')) {
        buzzButton.attr('title', disabledDescription);
    }
    
    var userId = buzzButton.data('user');
    var socket = window.ws.socket;
    socket.emit('user:status:sub', {users: [userId]});
    
    buzzButton.on('click', function(e){
        e.preventDefault();
        
        if(buzzButton.hasClass('disabled')) {
            return;
        }
        
        buzzButton.addClass('disabled cooldown');
        buzzButton.attr('title', disabledDescription);
        setTimeout(function(){
            var classes = 'cooldown';
            
            if(!buzzButton.hasClass('offline')) {
                classes += ' disabled';
                buzzButton.attr('title', enabledDescription);
            }
            
            buzzButton.removeClass(classes);
        }, 30 * 1000);
        
        $.ajax(buzzButton.attr('href'), {
            cache: false
        });
    });
    
    socket.on('user:status:change', function(data){
        var user = data.user;
        var online = data.online;
        if(undefined === user || undefined === online) {
            return;
        }
        
        if(online === 'offline') {
            buzzButton.addClass('offline disabled');
                buzzButton.attr('title', disabledDescription);
        } else {
            var classes = 'offline';
            
            if(!buzzButton.hasClass('cooldown')) {
                classes += ' disabled';
                buzzButton.attr('title', enabledDescription);
            }
            
            buzzButton.removeClass(classes);
        }
    });
})(jQuery);
