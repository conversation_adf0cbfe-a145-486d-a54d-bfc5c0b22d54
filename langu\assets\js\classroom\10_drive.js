;(function(){
    return;
    var options = {
        scope: classroom.drive.scope,
        userId: classroom.user.googleEmail,
        accessToken: classroom.user.accessToken
    };
    
    var collaborationStringIdentifier = 'demo_string';
    var textArea = document.getElementById('summernote');
    var $textArea = $(textArea);
    var collString = null;
    var foreColor = null;
    
    if(classroom.user.type === 'student') {
        foreColor = '#FF9C00';
    } else {
        foreColor = '#000000';
    }
    
    var realtimeClient = new window.rt.RealtimeWrapper(classroom.drive.clientId, options);
    realtimeClient.start(start);
    
    function start() {
        realtimeClient.load(classroom.drive.fileId, onFileLoaded, onFileInitialize);
    }

    function onFileInitialize(model) {
        var string = model.createString();
        string.setText(classroom.lesson.initText);
        model.getRoot().set(collaborationStringIdentifier, string);
    }

    function onFileLoaded(doc) {
        var docEvents = [
            gapi.drive.realtime.EventType.COLLABORATOR_JOINED,
            gapi.drive.realtime.EventType.DOCUMENT_SAVE_STATE_CHANGED
        ];
        
        collString = doc.getModel().getRoot().get(collaborationStringIdentifier);        
        wireTextBoxes(collString);
        
        if(!classroom.lesson.editable) {
            //doc.close();
        }
        
        $textArea.summernote({
            focus: true,
            airMode: false,
            toolbar: [
                ['style', ['bold', 'underline']],
                ['font', ['strikethrough']],
                ['color', ['color']],
                ['insert', ['link', 'video', 'hr', 'picture', 'table']],
                ['para', ['ul', 'ol', 'paragraph']]
            ],
            popover: {
                air: [
                    ['style', ['bold', 'underline']],
                    ['font', ['strikethrough']],
                    ['color', ['color']],
                    ['insert', ['link', 'video', 'hr', 'picture']],
                    ['table', ['table']],
                    ['para', ['ul', 'ol', 'paragraph']]
                ],
                image: [
                    ['float', ['floatLeft', 'floatRight', 'floatNone']],
                    ['remove', ['removeMedia']]
                ],
                link: [
                    ['link', ['linkDialogShow', 'unlink']]
                ]
            },
//            height: 'calc(100vh - 250px)',
//            minHeight: 'calc(100vh - 250px)',
//            maxHeight: 'calc(100vh - 250px)',
            dialogsFade: true,
            callbacks: {
                onInit: function() {
                    var $progressBar = $('#summernote-pb');
                    $progressBar.remove();
                    
                    $textArea.summernote('code', $textArea.val());
                    $textArea.summernote('foreColor', foreColor);
                    
//                    if(!classroom.lesson.editable) {
//                        $textArea.summernote('disable');
//                    }
                },
                onChange: function(contents) {
                    if(collString.getText() !== contents) {
                        collString.setText(contents);
                    }
                }
            }
        });
    }

    function wireTextBoxes(collaborativeString) {
//        if(!classroom.lesson.editable) {
//            var text = collaborativeString.getText();
//            $textArea.val(text);
//            
//            return;
//        }
        
        window.gapi.drive.realtime.databinding.bindString(collaborativeString, textArea);
        collaborativeString.addEventListener(gapi.drive.realtime.EventType.OBJECT_CHANGED, contentChanged.bind(collaborativeString));
    }
    
    function contentChanged(evt) {
        if(!evt.isLocal) {
            var text = this.getText();
            $textArea.summernote('saveRange');
            $textArea.summernote('code', text);
            $textArea.summernote('restoreRange');
        }

        evt.stopPropagation();
    }
})();
