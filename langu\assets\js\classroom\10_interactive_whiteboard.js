;
(function() {



})(jQuery);

var FPS = 30; // target frames per second
var SECONDSBETWEENFRAMES = 1 / FPS;

var canvas = null;
var ctx = null;
var cursor = null;
var toolbar = null;
var role = undefined;

$(document).ready(function() {
    canvas = document.getElementById('whiteboard_canvas');
    canvas.setAttribute('height', $(window).height());
    canvas.setAttribute('width', $(window).width());

    ctx = canvas.getContext('2d');
    role = window.langu_role;

    if (role == "teacher") {
        cursor = new Cursor('#7FB802', true);
    }
    else {
        cursor = new Cursor('#3C87F8', false);
    }

    toolbar = new Toolbar();

    $(document).keyup(onKeyUp);

    console.info('Setting up listeners');
    $("#whiteboard_canvas").mousemove(function(event) {
        handle_mousemove(event);
    });

    $("#whiteboard_canvas").mouseleave(function(event) {
        cursor.disabled = true;
        if (role == "student")
            $('body').addClass('cursor-pointer-student');
        else
            $('body').addClass('cursor-pointer-teacher');
    });

    $("#whiteboard_canvas").mouseenter(function(event) {
        cursor.disabled = false;
        if (role == "student")
            $('body').removeClass('cursor-pointer-student');
        else
            $('body').removeClass('cursor-pointer-teacher');
    });

    $('#video-load-btn').on('click', function(event){
        cursor.selected = 'VIDEO';
            console.log('whiteboard clcik', cursor.selected);
        })

    $("#whiteboard_canvas").on('mousedown', function(event) {
        handle_mousedown(event);
    });

    $("#whiteboard_canvas").on('mouseup', function(event) {
        handle_mouseup(event);
    });

    $(document).keydown(function(event) {
        if (cursor.keydown(event.key)) {
            // event.preventDefault();
        }
    })

    setInterval(gameloop, SECONDSBETWEENFRAMES * 1000);
});

function update() {
    if (cursor != null) {
        cursor.update();
    }

    if (window.othercursor != undefined && window.othercursor != null) {
        window.othercursor.update();
    }

    if (toolbar != null) {
        toolbar.update();
    }
}

function draw(ctx) {
    ctx.fillStyle = 'white';

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    cursor.pre_draw(ctx, cursor.offset);

    if (window.othercursor != undefined && window.othercursor != null) {
        // If role of current user is teacher - set student's cursor for othercursor
        window.othercursor.draw(ctx, toolbar, role == "teacher" ? false : true, cursor.offset);
    }

    if (cursor != null) {
        cursor.draw(ctx, toolbar, role == "teacher" ? true : false, cursor.offset);
    }
}

function gameloop() {
    update();
    draw(ctx);
}

function handle_mousedown(event) {
    var offset = $("#whiteboard_canvas").offset();

    var cur_x = event.pageX - offset.left;
    var cur_y = event.pageY - offset.top;
    var hit = false;

    if (toolbar != null) {
        if (toolbar.mousedown(cur_x, cur_y)) {
            hit = true;
        }
        cursor.selected = toolbar.cur_selected;
    }

    if (hit == false) {
        cursor.mousedown(cur_x, cur_y);
    }
}

function handle_mouseup(event) {
    var offset = $("#whiteboard_canvas").offset();

    var cur_x = event.pageX - offset.left;
    var cur_y = event.pageY - offset.top;
    // console.log(cur_x + ", " + cur_y);
    cursor.mouseup(cur_x, cur_y);
    if (toolbar != null) {
        toolbar.mouseup(cur_x, cur_y);
    }
}

function handle_mousemove(event) {
    var offset = $("#whiteboard_canvas").offset();
    var _x = event.pageX - offset.left;

    var _y = event.pageY - offset.top;

    cursor.mousemove(_x, _y);

    if (toolbar != null) {
        toolbar.mousemove(_x, _y);
    }
}

/**
 * @deprecated
 */
function setup_summernote() {
    return;
    var text = "JVgwkSjescc";
    var foreColor = '#FF9C00';
    console.log('summernote');

    $('#text_summernote').summernote({
        focus: true,
        airMode: false,
        disableDragAndDrop: true,
        toolbar: [
            ['style', ['bold', 'underline', 'strikethrough', 'color']],
            ['insert', ['link', 'hr', 'picture', 'table']],
            ['para', ['ul', 'ol', 'paragraph']]
        ],
        popover: {
            air: [
                ['style', ['bold', 'underline', 'strikethrough', 'color']],
                ['insert', ['link', 'hr', 'picture', 'table']],
                 ['para', ['ul', 'ol', 'paragraph']]
            ],
            image: [
                ['float', ['floatLeft', 'floatRight', 'floatNone']],
                ['remove', ['removeMedia']]
            ],
            link: [
                ['link', ['linkDialogShow', 'unlink']]
            ]
        },
        dialogsFade: true,
        dialogsInBody: true,
        disableResizeEditor: true,
        callbacks: {
            onInit: function() {
                var $progressBar = $('#summernote-pb');
                $progressBar.remove();
                $('#text_summernote').summernote('code', text);
                $('#text_summernote').summernote('foreColor', foreColor);

                // REMOVED 27/11/19 - PhilJ - Helping Kamil show summernote permenantly
                // $('#summernote_wrapper').hide();
            },
            onChange: function(newtext) {
            },
            onImageUpload: function(files) {
            },
        }
    });

    if(window.role === 'teacher') {
            $('#summernote_wrapper').addClass('teacher');
    } else {
            $('#summernote_wrapper').addClass('student');
    }
}
