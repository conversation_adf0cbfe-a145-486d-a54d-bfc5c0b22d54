function VerticalButton(_type, _x, _y, _index){
    this.type = _type;
    this.loc = new Location(_x, _y);
    this.size = new Location(35, 40);
    this.index = _index;
    this.draw = vertical_button_draw;
    this.update = button_update;

    this.mousemove = button_mousemove;
    this.mousedown = button_mousedown;
    this.hittest = button_hittest;
    this.mouseup = button_mouseup;

    this.is_highlighted = false;
}

function Button(_type, _x, _y, _index, verticalButtons){
    this.type = _type;
    this.loc = new Location(_x + 5, _y);
    this.size = new Location(35, 40);
    this.index = _index;
    this.draw = button_draw;
    this.update = button_update;
    this.imageloaded = false;
    this.image_additional = null;
    this.verticalButtons = verticalButtons ? verticalButtons : [];

    // images/classroom/<filename.svg>
    this.image = new Image();
    if(_type == "VIDEO"){
        this.image.src = "/images/classroom/play.svg";
    }
    this.image.onload = function(){
        toolbar.buttons[_index].imageloaded = true;
    }

    this.mousemove = button_mousemove;
    this.mousedown = button_mousedown;
    this.hittest = button_hittest;
    this.mouseup = button_mouseup;
    this.drawVerticalButtons = drawVerticalButtons;

    this.is_highlighted = false;
}

function button_draw(ctx){
    var old_strokeStyle = ctx.strokeStyle;
    var old_fillStyle = ctx.fillStyle;

    if(this.is_highlighted){
        ctx.fillStyle = "#eeeeee";
    }else{
        ctx.fillStyle = "#ffffff";
    }
 
    ctx.fillRect(this.loc.x, this.loc.y, this.size.x , this.size.y);

    ctx.lineWidth = 2;

    if(this.imageloaded){
        ctx.drawImage(this.image, this.loc.x+5, this.loc.y+9);
    }

    if(this.image_additional != null && this.additional_imageloaded){
        ctx.drawImage(this.image_additional, this.loc.x + 15, this.loc.y + 15);
    }

    if(this.type == "LINE"){
        ctx.font = '25px BBB';
        ctx.textBaseline = 'top';
        ctx.fillStyle = 'black';
        ctx.fillText('\ue925', this.loc.x+5, this.loc.y****);
    }else if(this.type == "5SLINE"){
        ctx.font = '25px BBB';
        ctx.textBaseline = 'top';
        ctx.fillStyle = 'black';
        ctx.fillText('\ue925', this.loc.x+5, this.loc.y****);
        
        ctx.font = "15px Georgia";
        ctx.fillText('3', this.loc.x+22, this.loc.y+20);
    }else if(this.type == "TEXT"){
        ctx.fillStyle = 'black';
        ctx.font = "40px Georgia";
        ctx.beginPath();
        ctx.fillText("T", this.loc.x + 5, this.loc.y + this.size.y - 5);
        ctx.stroke();
        ctx.closePath();
    }else if(this.type == "CLEAR"){
        ctx.font = '25px BBB';
        ctx.textBaseline = 'top';
        ctx.fillStyle = 'black';
        ctx.fillText('\ue924', this.loc.x+5, this.loc.y****);
    }else if(this.type == "REFRESH"){
        ctx.strokeStyle = "black";
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.moveTo( this.loc.x + (this.size.x/2) -4 , this.loc.y + (this.size.y/2) );
        ctx.lineTo( this.loc.x + this.size.x -15, this.loc.y + 10);
        ctx.lineTo(this.loc.x + (this.size.x/2) - 7 , this.loc.y + (this.size.y/2) - 17);
        ctx.stroke();
        ctx.closePath();

        ctx.beginPath();
        ctx.arc(this.loc.x + (this.size.x/2), this.loc.y + (this.size.y/2), 10, 0, 1.5 * Math.PI);
        ctx.stroke();
        ctx.closePath();

    }else if(this.type == "POINTER"){
        ctx.font = '25px BBB';
        ctx.textBaseline = 'top';
        ctx.fillStyle = 'black';
        ctx.fillText("\uE90F", this.loc.x+5, this.loc.y****);
    }else if(this.type == "VIDEO"){

    }
    
    ctx.strokeStyle = old_strokeStyle;
    ctx.fillStyle = old_fillStyle;
}

function button_update(){
    
}

function button_mousedown(_x, _y){
    if(this.hittest(_x, _y)){
        return true;
    }
    return false;
}

function button_mouseup(_x, _y){

}

function button_click(_x, _y){

}

function button_mousemove(_x, _y){
    if(this.hittest(_x, _y)){
        toolbar.dataToSend = true;
        toolbar.isHovered = true;
        toolbar.hoverColor = window.langu_role == "teacher" ? '#7FB802' : '#3C87F8';
        this.is_highlighted = true;
        
    }else{
        this.is_highlighted = false;
    }
}

function button_hittest(_x, _y){
    if(_x > this.loc.x && _x < this.loc.x + this.size.x){
        if(_y > this.loc.y && _y < this.loc.y + this.size.y ){
            return true;
        }
    }
    return false;
}

  ///////////////////////
 // VERTICAL BUTTONS //
/////////////////////

function drawVerticalButtons() {
    let canvas = document.getElementById('whiteboard_canvas');
    let ctx = canvas.getContext('2d');
    
    for(var i=0; i<this.verticalButtons.length; i++){
        this.verticalButtons[i].draw(ctx);
    }
}

function vertical_button_draw(ctx) {
    var old_strokeStyle = ctx.strokeStyle;
    var old_fillStyle = ctx.fillStyle;

    if(this.is_highlighted){
        ctx.fillStyle = "#eeeeee";
    }else{
        ctx.fillStyle = "#ffffff";
    }
 
    ctx.fillRect(this.loc.x, this.loc.y, this.size.x , this.size.y);
    ctx.lineWidth = 2;

    if(this.type == "VERTICAL_TEXT"){
        ctx.font = '25px BBB';
        ctx.textBaseline = 'top';
        ctx.fillStyle = 'black';
        ctx.fillText('\ue925', this.loc.x+5, this.loc.y****);
    }
    
    ctx.strokeStyle = old_strokeStyle;
    ctx.fillStyle = old_fillStyle;
}