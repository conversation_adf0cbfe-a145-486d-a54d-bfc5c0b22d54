function Cursor(color, isTeacher) {
    this.update = cursor_update;
    this.draw = cursor_draw;
    this.pre_draw = cursor_pre_draw;
    this.mousedown = cursor_mousedown;
    this.mouseup = cursor_mouseup;
    this.mousemove = cursor_mousemove;
    this.keydown = cursor_keydown;
    this.keyup = cursor_keyup;

    this.loc = new Location(0, 0);
    this.screen = { width: 0, height: 0 };
    this.attach_to_asset = null;

    this.ripples = [];
    this.selected = "POINTER";
    this.drawing = false;
    this.moving = false;
    this.resizing = false;
    this.start_loc = null;

    this.cur_selected_textbox_index = null;

    this.cur_asset = null;
    this.assets = [];
    this.cleared = false;

    this.color = color;
    this.offset = 0;

    this.lowest_point = 0;

    this.isScrolling = false;

    this.meta_pressed = false;
    this.scroll = cursor_scroll;

    this.hint = null;

    this.refresh_lowest_point = refresh_cursor_lowest_point;

    this.setup = cursor_setup;

    this.has_heard_from_server = false;
}

function cursor_setup() {

}

function cursor_update() {
    if (this.selected === 'VIDEO' && window.video_url) {
        this.selected = 'POINTER';
        window.toolbar.cur_asset = 'POINTER';

        this.assets.push(new VideoItem(600, 100, window.video_url));
        delete window.video_url;
        this.assets[this.assets.length - 1].is_master = true;
        this.assets[this.assets.length - 1].load_video();
    }


    if (this.selected == "REFRESH" && this.cleared == false) {
        for (var i = 0; i < this.assets.length; i++) {
            delete this.assets[i];
        }
        this.assets = [];
        this.cleared = true;
    }

    if (this.selected != "REFRESH") {
        // Make sure we reset so it will clear everything
        this.cleared = false;
    }

    // Update any ripple effects
    for (var i = 0; i < this.ripples.length; i++) {
        var ripple = this.ripples[i];
        ripple.update();
        if (ripple.status == "DONE") {
            delete this.ripples[i];
            this.ripples.splice(i, 1);
        }
    }

    for (var i = 0; i < this.assets.length; i++) {
        var asset = this.assets[i];
        asset.update();
    }

    for (var i = 0; i < this.assets.length; i++) {
        // Clean up assets that have completed their purpose
        if (this.assets[i].status == "DEAD") {
            // Clean up HTML elements for the element
            if (this.assets[i].type == 'videoitem') {
                $("[id*=" + this.assets[i].div_id + "]").remove();

                for (let j = 0; j < this.assets[i].intervals.length; j++) {
                    clearInterval(this.assets[i].intervals[j]);
                }
            }

            delete this.assets[i];
            this.assets.splice(i, 1);
        }
    }
}

function cursor_pre_draw(ctx, offset) {
    // This is so that we draw the messaging window under everything
    // TOOD: See if anything else can be drawn under stuff
    if (this.assets.length > 0) {
        this.assets[0].draw(ctx, offset)
    }
}

function cursor_draw(ctx, toolbar, is_other, offset) {

    // If we have specs for the other persons screen size then draw the blanked bits
    // Phil 09/05/19 - Commented out because we now draw a square rather than blanking out the screen
    // if(is_other == true){
    //     var old_alpha = ctx.globalAlpha;
    //     var old_color = ctx.fillStyle;

    //     ctx.globalAlpha = 0.3;
    //     ctx.fillStyle = "#555555";

    //     var screen_height = $(window).height();
    //     var screen_width = $(window).width();

    //     if(this.screen.width != 0 && this.screen.width < screen_width){
    //         ctx.fillRect(this.screen.width,0, screen_width-this.screen.width, screen_height);
    //     }

    //     if(this.screen.height != 0 && this.screen.height < screen_height){
    //         ctx.fillRect(0,this.screen.height, screen_width - (screen_width - this.screen.width), screen_height);
    //     }

    //     ctx.globalAlpha = old_alpha;
    //     ctx.fillStyle = old_color;
    // }
    // Now draw the rest of the assets

    if (is_other) {
        // Set the other cursors offset to the value passed in (ie. respect the scroll)
        this.offset = offset;
    }

    // Draw scroll bar if appropriate
    var draw_scroll = false;
    if (this.isScrolling && !is_other) {

        draw_scroll = true;
        this.scroll_fade = 20;
    }
    else if (this.scroll_fade > 0) {
        this.scroll_fade -= 1;
        draw_scroll = true;
    }

    if (draw_scroll) {
        var fade_amount = this.scroll_fade / 20;
        ctx.fillStyle = "rgb(150,150,150, " + fade_amount + ")";
        var leftedge = window.innerWidth - 20;
        // Calculate top edge
        var lowest_point = this.lowest_point;
        if (window.othercursor != undefined && window.othercursor != null && lowest_point < window.othercursor.lowest_point) {
            lowest_point = window.othercursor.lowest_point;
        }
        var total_height = lowest_point + ($(window).height() * 0.8);
        var proportion = this.offset / total_height;
        ctx.fillRect(leftedge, parseInt(($('#whiteboard_canvas').height() - 40) * proportion), 10, 40);
    }
    // End scroll bar

    ctx.lineWidth = 1;

    // Draw the assets on the canvas
    for (var i = 0; i < this.assets.length; i++) {
        var asset = this.assets[i];
        if (asset != null) {
            asset.draw(ctx, this.offset);
        }
    }

    // Draw the asset we are currently drawing - if we have one
    // Note: Must be after the currently set assets
    if (this.cur_asset != null) {
        this.cur_asset.draw(ctx, this.offset);
    }

    for (var i = 0; i < this.ripples.length; i++) {
        var ripple = this.ripples[i];
        ripple.draw(ctx, this.offset);
    }

    if (this.disabled === true) {
        $('#whiteboard_canvas').css({ 'cursor': 'none' });
    }
    else {
        // Update
        if (this.hint == "textboxresize") {
            $('#whiteboard_canvas').css({ 'cursor': 'nw-resize' });
        }
        else if (this.hint == "textboxclear") {
            $('#whiteboard_canvas').css({ 'cursor': 'pointer' });
        }
        else if (this.hint == "text") {
            $('#whiteboard_canvas').css({ 'cursor': 'pointer' });
        }
        else if (this.hint == "dragbar") {
            $('#whiteboard_canvas').css({ 'cursor': 'grab' });
        }
        else if (this.hint == "dragging") {
            $('#whiteboard_canvas').css({ 'cursor': 'grabbing' });
        }
        else {
            $('#whiteboard_canvas').css({ 'cursor': 'pointer' });
        }
    }

    this.isScrolling = false;
}

function cursor_mousedown(_x, _y) {
    this.ripples.push(new Ripple(this.loc.x, this.loc.y + this.offset, this.color));
    (new UpdateBuilder()).type(UpdateBuilder.TEMPORARY).name(UpdateBuilder.RIPPLE)
        .updateData({
            loc: {
                x: this.loc.x,
                y: this.loc.y + this.offset
            },
            color: this.color,
        })
        .send();

    // Handle currently selected
    if (this.selected == "POINTER" || "POINTER_HAND") {
        var bFound = false;
        for (var i = 0; i < this.assets.length; i++) {
            var asset = this.assets[i];
            if (!bFound && asset.type == "textbox") {
                var bHit = asset.clicked(_x, _y, this.offset);
                if (bHit) {
                    this.cur_selected_textbox_index = i;
                    asset.isSelected = true;
                    bFound = true; // Max 1 selected at a time
                }
                else {
                    asset.isSelected = false;
                }
            }
            if (!bFound && asset.type === "pdfitem") {
                var bHit = false;

                if (asset.hittestresize(_x, _y, this.offset)) {
                    bHit = true;
                    bFound = true;

                    this.resizing = true;
                    this.start_loc = new Location(_x, _y);
                    this.cur_asset = this.assets[i];
                    return;
                } else if (asset.clicked(_x, _y, this.offset)) {
                    bHit = true;
                    bFound = true;

                    this.start_loc = new Location(_x, _y);
                    this.moving = true;
                    this.cur_asset = this.assets[i];
                    return;
                }
            }
            if (!bFound && asset.type == "imageitem") {
                var bHit = asset.clicked(_x, _y, this.offset);
                if (bHit) {
                    bFound = true; // Max 1 selected at a time
                }
                else {
                    asset.isSelected = false;
                }
            }
            if (!bFound && asset.type == "videoitem") {

                var bHit = asset.clicked(_x, _y, this.offset);
                bFound = bHit || bFound;
                if (bHit) {
                    bFound = true; // Max 1 selected at a time
                }
                else {
                    asset.isSelected = false;

                }

            }
            if (!bFound && this.cur_selected_textbox_index != null && this.assets[this.cur_selected_textbox_index].type == "textbox") {
                // We have clicked somewhere outside a text box so
                this.assets[this.cur_selected_textbox_index].text = $('#text_summernote').val();
                this.assets[this.cur_selected_textbox_index].changed = true;
                this.assets[this.cur_selected_textbox_index].text_updated = true;

                // Set the text on the textbox to the current summernote value
                this.cur_selected_textbox_index = null;

                // In case we somehow end up dissociated from the clicks and are stuck resizing
                // Simply click nothing and reset it
                this.resizing = false;

            }

            if (asset.hittest(_x, _y + this.offset)) {
                $('#whiteboard_canvas').css({ "position": "relative", "z-index": "99999" });
                this.moving = true;
                this.start_loc = new Location(_x, _y);
                this.cur_asset = this.assets[i];

                return;
            }

            // TODO: Add in all of the asset types to make sure we can resize everything
            if ((asset.type == "textbox" || asset.type == "videoitem" || asset.type == "imageitem") &&
                asset.hittestresize(_x, _y, this.offset)) {
                // If we have done a mousedown on a resize element then start the resize
                this.resizing = true;
                asset.start_resize();
                this.start_loc = new Location(_x, _y);
                this.cur_asset = this.assets[i];
            }
        }
    }

    if (this.selected == "LINE") {
        var bFound = false;
        for (var i = 0; !bFound && i < this.assets.length; i++) {
            var asset = this.assets[i];
            // Check if we've hit the item

            if ((asset.type == "textbox" || asset.type == "imageitem" || asset.type == "pdfitem") &&
                asset.bodyhittest(_x, _y, this.offset)) {
                bFound = true;
                this.attach_to_asset = i;
                this.drawing = true;
                this.cur_asset = new Line(_x, _y + this.offset);
            }
            else if (asset.type == "videoitem" && asset.hittest(_x, _y + this.offset)) {
                this.selected = "POINTER"

                this.moving = true;
                this.start_loc = new Location(_x, _y);
                this.cur_asset = this.assets[i];

                bFound = true;
            }
            else {
                this.drawing = true;
                this.cur_asset = new Line(_x, _y + this.offset);
            }
        }
    }
    else if (this.selected == "ERASER") {
        var bFound = false;
        for (var i = 0; !bFound && i < this.assets.length; i++) {
            var asset = this.assets[i];
            // Check if we've hit the item

            if ((asset.type == "textbox" || asset.type == "imageitem" || asset.type == "pdfitem") &&
                asset.bodyhittest(_x, _y, this.offset)) {
                bFound = true;
                this.attach_to_asset = i;
                this.drawing = true;
                this.cur_asset = new Line(_x, _y + this.offset);
            }
            else if (asset.type == "videoitem" && asset.hittest(_x, _y + this.offset)) {
                this.selected = "POINTER"

                this.moving = true;
                this.start_loc = new Location(_x, _y);
                this.cur_asset = this.assets[i];

                bFound = true;
            }
            else {
                this.drawing = true;
                this.cur_asset = new Line(_x, _y + this.offset);
                this.cur_asset.color = '#ffffff';
                this.cur_asset.linesize = 12;
            }
        }
    }
    else if (this.selected == "5SLINE") {

        for (var i = 0; !bFound && i < this.assets.length; i++) {
            var asset = this.assets[i];
            // Check if we've hit the item

            if ((asset.type == "videoitem") &&
                asset.bodyhittest(_x, _y, this.offset)) {
                bFound = true;
                this.attach_to_asset = i;
            }
        }

        this.drawing = true;
        this.cur_asset = new TempLine(_x, _y + this.offset);
        this.cur_asset.status = "DRAWING";
    }
    else if (this.selected == "TEXT") {
        this.drawing = true;
        this.cur_asset = textbox = new TextBox(_x, _y + this.offset);
        this.cur_asset.status = "DRAWING";
    }
    else if (this.selected == "VIDEO") {

        this.selected = "POINTER";
        toolbar.cur_selected = "POINTER";
        var bAllowed = true;
        for (var i = 0; i < this.assets.length && bAllowed; i++) {
            if (this.assets[i].type == "videoitem" && !this.assets[i].url_set) {
                bAllowed = false;
                // alert("Please set one video at a time");
                return;
            }
        }

    }
    else if (this.selected.indexOf('SHAPE_') !== -1) {
        this.resizing = true;
        this.start_loc = new Location(_x, _y);

        let functionName = this.selected.replace('SHAPE_', '');

        switch(functionName) {
            case 'SQUARE':
                this.cur_asset = new ShapeSquare(_x, _y + this.offset);
                break;
            case 'CIRCLE':
                this.cur_asset = new ShapeCircle(_x, _y + this.offset);
                break;
            case 'TRIANGLE':
                this.cur_asset = new ShapeTriangle(_x, _y + this.offset);
                break;
            case 'LINE':
                this.cur_asset = new ShapeLine(_x, _y + this.offset);
                break;
            default:
                console.error('Undefined function called [' + functionName + '].');
                break;
        }
    }
}

function cursor_mouseup(_x, _y) {
    if (this.resizing && this.cur_asset != null && (this.cur_asset.type == "videoitem" || this.cur_asset.parent == 'shape')) {
        // We have finished resizing the video item - now set the video to be the same size
        this.cur_asset.finish_resize();
    } else if (this.resizing && this.cur_asset != null && this.cur_asset.type == "pdfitem") {
        this.cur_asset.resized = true;
    }

    if (this.moving) {
        this.selected = "POINTER";
        $('#whiteboard_canvas').css({ "position": "static", "z-index": "0" });
        if (this.cur_asset.moved == false) {
            this.cur_asset.moved = true;
            this.cur_asset = null;
        }
    }

    if (this.cur_asset != null) {
        this.cur_asset.status = "COMPLETED";

        if (this.cur_asset.type == "textbox") {
            this.cur_asset.setup();
        }
        if (this.attach_to_asset == null) {
            this.assets.push(this.cur_asset);
        }
        else {
            if (this.assets[this.attach_to_asset].add_asset != undefined) {
                this.assets[this.attach_to_asset].add_asset(this.cur_asset);
                this.attach_to_asset = null;
            }
            else {
                console.log("ERROR: We are trying attach to an asset but the asset doesn't accept assets");
            }
        }

        // Calculate the lowest_point and update if appropriate
        if (this.cur_asset.type === "line") {
            for (var i = 0; i < this.cur_asset.points.length; i++) {
                if (this.cur_asset.points[i].y > this.lowest_point) {
                    this.lowest_point = this.cur_asset.points[i].y;
                }
            }
        }

        this.cur_asset = null;
        this.ripples.push(new Ripple(this.loc.x, this.loc.y + this.offset));

        if (this.selected == "TEXT") {
            this.selected = "POINTER";
            toolbar.cur_selected = "POINTER";
        }
        if (this.selected == "LINE") {
            this.selected = "LINE";
            toolbar.cur_selected = "LINE";
        }
    }

    this.drawing = false;
    this.moving = false;
    this.resizing = false;
}

function cursor_mousemove(_x, _y) {
    
  
    this.loc.x = _x;
    this.loc.y = _y;

    if (this.moving) {
        if (this.cur_asset == null) {
            return;
        }

        var diff_x = _x - this.start_loc.x;
        var diff_y = _y - this.start_loc.y;
        this.cur_asset.loc.x += diff_x;
        this.cur_asset.loc.y += diff_y;

        this.start_loc.x = _x;
        this.start_loc.y = _y;

        this.hint = "dragging";
        return;
    }
    else if (this.drawing) {
        // Update the current line
        if (this.selected == "LINE" || this.selected == "5SLINE" || this.selected == 'ERASER') {
            this.cur_asset.add_point(_x, _y + this.offset);
        }
        else if (this.selected == "TEXT") {
            this.cur_asset.set_size(_x, _y + this.offset);
        }
    }
    else if (this.resizing) {
        // Update the currently selected asset with the new size movements
        if (this.cur_asset == null) {
            this.resizing = false;
            return;
        }

        var diff_x = _x - this.start_loc.x;
        var diff_y = _y - this.start_loc.y;
        this.cur_asset.resize(diff_x, diff_y);
        this.start_loc.x = _x;
        this.start_loc.y = _y;
    }
    else {
        this.hint = null;
        if (toolbar.hitcheck(_x, _y)) {
            this.hint = "dragbar";
        }

        for (var i = 0; this.hint == null && i < this.assets.length; i++) {
            if (this.assets[i].type == "videoitem") {

                if (this.assets[i].hittestresize(_x, _y, this.offset)) {
                    this.hint = "textboxresize"; // Badly named first time around - just reuse for all resizes
                }
                else {
                    if (this.assets[i].hittest(_x, _y + this.offset)) {
                        this.hint = "dragbar";
                    };
                }
            }
            else if (this.assets[i].type == "imageitem") {
                if (this.assets[i].hittestresize(_x, _y, this.offset)) {
                    this.hint = "textboxresize";
                }
                else if (this.assets[i].hittest(_x, _y, this.offset)) {
                    this.hint = "dragbar";
                }
            }
            else if (this.assets[i].type == "textbox") {
                if (this.assets[i].hittestresize(_x, _y, this.offset)) {
                    this.hint = "textboxresize";
                }
                else if (this.assets[i].hittestclear(_x, _y, this.offset)) {
                    this.hint = "textboxclear";
                }
                else if (this.assets[i].bodyhittest(_x, _y, this.offset)) {
                    this.hint = "text";
                }
                else if (this.assets[i].hittest(_x, _y, this.offset)) {
                    this.hint = "dragbar";
                }
            }
        }
    }
}

function cursor_keydown(key) {
    // TODO: Make sure we only use the selected text box
    if (this.cur_selected_textbox_index != null &&
        this.assets[this.cur_selected_textbox_index] != undefined &&
        this.assets[this.cur_selected_textbox_index].type == "textbox") {
        this.assets[this.cur_selected_textbox_index].keydown(key);
    }
    else {
        for (var i = 0; i < this.assets.length; i++) {
            if (this.assets[i].type == "videoitem" && this.assets[i].url_set == false) {
                this.assets[i].keydown(key);
            }
        }
    }

    if (key == "r") {
        // Allow page to be refreshed
        return false;
    }

    // TODO: Check if we actually handled the key before sending true
    return true;
}

function cursor_keyup(key) {
    // Make sure we use the connected box
    if (this.cur_selected_textbox_index != null && this.assets[this.cur_selected_textbox_index].type == "textbox") {
        this.assets[this.cur_selected_textbox_index].keyup(key);
    }

    for (var i = 0; i < this.assets.length; i++) {
        if (this.assets[i].type == "videoitem" && this.assets[i].url_set == false) {
            this.assets[i].keyup(key);
        }
    }

    if (key == "r") {
        // Allow refresh on key up
        return false;
    }
    // TODO: Check if we actually handled the key before sending true
    return true;
}

function refresh_cursor_lowest_point() {
    this.lowest_point = 0;
    for (var i = 0; i < this.assets.length; i++) {
        if (this.assets[i].get_lowest_point == undefined || typeof(this.assets[i].get_lowest_point) != "function") {}
        else {
            var val = this.assets[i].get_lowest_point();
            if (val > this.lowest_point) {
                this.lowest_point = val;
            }
        }
    }

    return this.lowest_point;
}

function cursor_scroll(event) {
    var height = $(window).height();
    if (this.moving == true) {
        // We don't want to change the offset when we are moving

        // TODO: Maybe change cursor to indicate we can't scroll at this time?
        return false;
    }

    for (var i = 0; i < this.assets.length; i++) {
        var ass = this.assets[i];

        if (ass.type == "textbox" && ass.bodyhittest(event.offsetX, event.offsetY, this.offset)) {
            if (ass.scroll(event)) {
                // We have scrolled
                event.preventDefault();
                return true;
            }
        }
    }

    // TODO: Maybe consider flipping the wheel depending on whether we are on
    // mac or windows - perhaps configurable
    // TODO: If this is an option then does it need to be a cookie or saved setting?
    this.offset -= event.originalEvent.wheelDelta;
    //Change position pdf when you scroll
    $('#catch-pdf').css({ left: this.left, top: -this.offset });
    if (this.offset < 0) {
        this.offset = 0;
    }

    // Limit the amount down they can scroll
    var lowest = this.lowest_point;
    if (window.othercursor != undefined && window.othercursor != null && lowest < window.othercursor.lowest_point) {
        lowest = window.othercursor.lowest_point;
    }

    if (this.offset > lowest + (height * 0.8)) {
        this.offset = lowest + (height * 0.8);
        return false;
    }
    else {
        return true;
    }
}
