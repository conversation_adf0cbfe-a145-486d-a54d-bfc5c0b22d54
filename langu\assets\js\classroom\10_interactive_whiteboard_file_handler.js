let selectedFiles = [];

function buildData(formData, data, parentKey) {
    if (Array.isArray(data) && data.length === 0) {
        formData.append(parentKey, [])
    } else if (data && typeof data === 'object' && !(data instanceof Date) && !(data instanceof File)) {
        Object.keys(data).forEach(key => {
            buildData(formData, data[key], parentKey ? `${parentKey}[${key}]` : key)
        })
    } else {
        const value = data == null ? '' : ((typeof data === 'boolean') ? Number(data) : data)
        formData.append(parentKey, value)
    }
}

function jsonToFormData(data) {
    const formData = new FormData()
    buildData(formData, data)
    return formData
}
 $('.popup-load-files-wrap').on('dragover', (e) => {
    e.preventDefault();
    $('.popup-load-files-drop-wrap').css({"display": "block"});
});
 $('.popup-load-files-wrap').on('dragleave', (e) => {
    e.preventDefault();
    $('.popup-load-files-drop-wrap').css({"display": "none"});
});
 $('.popup-load-files-drop-wrap').on('dragover', (e) => {
    e.preventDefault();
    $('.popup-load-files-drop-wrap').addClass('active');
});
$('.popup-load-files-drop-wrap').on('dragleave', (e) => {
    e.preventDefault();
    $('.popup-load-files-drop-wrap').removeClass('active');
});

$(document).on('drop', '.popup-load-files-drop-wrap', (e) => {
    e.preventDefault();
    let files = [];
    if (e.originalEvent.dataTransfer.items) {
        // Use DataTransferItemList interface to access the file(s)
        for (var i = 0; i < e.originalEvent.dataTransfer.items.length; i++) {
            // If dropped items aren't files, reject them
            if (e.originalEvent.dataTransfer.items[i].kind === 'file') {
                let file = e.originalEvent.dataTransfer.items[i].getAsFile();
                files.push(file);
            }
        }
    } else {
        // Use DataTransfer interface to access the file(s)
        for (var i = 0; i < e.originalEvent.dataTransfer.files.length; i++) {
                let file = e.originalEvent.dataTransfer.files[i];
                files.push(file);
        }
    }

    console.log('files', files);

     files.forEach((element, index) => {
            console.log('files element', element);
            loadImage(index,  URL.createObjectURL(element));
      });

    $('.popup-load-files-drop-wrap').removeClass('active');
});



$('.popup-load-files-select').on('click', function(event) {
    event.stopPropagation();
    $('.popup-load-files-select-options').toggleClass('active');
})


$('.popup-load-files-select-option').on('click', function(event) {
    event.stopPropagation();
    let optionContent = $(this).html();
    let optionValue = $(this).attr('data-value');
    let sort_direction = null;
    if(optionValue === 'file_name_desc') {
        optionValue.substr(0, optionValue.length-5);
        sort_direction = 'DESC';
    } else if (optionValue === 'file_name_asc') {
        optionValue.substr(0, optionValue.length-4);
        sort_direction = 'ASC';
    }

    console.log('Option selected!');
    axios.post(`/lesson/classroom/library/${window.classroom.voxeet.userId}`, {
            query: $('input.popup-load-files-search').val(),
            sort_direction: sort_direction,
            page: 1,
            sort_type: optionValue
        })
            .then(function (response) {
                console.log('open response', response)
                $('#popup-load-files-list').html('');
                $('#popup-load-files-navigation').html('');

                if(response.data.length) {
                    $('#popup-load-files-navigation').html('<span class="popup-load-files-nav-number active"> 1 </span>');
                    $('.popup-load-files-btn-nav').attr('disabled', 'true')
                } else {
                    let totalPages = Math.round(response.data.length/18);

                    $('#popup-load-files-navigation').html('<span class="popup-load-files-nav-number active"> 1 </span>');

                    for (let i = 2; i <= totalPages; i++) {
                        $('#popup-load-files-navigation').append('<span class="popup-load-files-nav-number"> ' + i + ' </span>');
                    }
                    $('.popup-load-files-btn-nav').attr('disabled', 'true')
                }

                for (let i = 0; i < response.data.length; i++) {
                    $('#popup-load-files-list').append(`
                        <div class="popup-load-files-item" data-url="url"> 
                        <div class="popup-load-files-item-img">
                            <img src="${response.data[i].path}" class="popup-load-files-img-icon">
                        </div>
                        <div class="popup-load-files-item-name">
                            <p>${response.data[i].name}</p>
                        </div>
                    </div>`)
                }
            })
            .catch(function (error) {
                // handle error
                console.log('open error', error);
            })
            .finally(function () {
                // always executed
        });
})

$('.popup-load-files-item').on('click', function() {
    console.log('ACTIVE TICK');
    let tickFile = $(this).children('.popup-load-files-item-tick');
    tickFile.toggleClass('active');
    let fileUrl = $(this).attr('data-url');
    if(tickFile.hasClass('active')) {
        selectedFiles.map(item => item !== fileUrl)
        selectedFiles.push(fileUrl);
    } else {
        selectedFiles.map(item => item !== fileUrl)
    }
    if(selectedFiles.length) {
        $('.popup-load-files-header.selected-files')
    }
})

// $('#library-file-upload').on('change', function(event) {
//     let files = document.getElementById('upload-library-files').files
//     let requestFiles = jsonToFormData({
//         files: files
//     });
//     console.log('request files', requestFiles)
//      axios.post(`/lesson/classroom/library/{userId}`, {
//             file: requestFiles
//         })
//                 .then(function (response) {
                   
//                 })
//                 .catch(function (error) {
//                     // handle error
//                 })
//                 .finally(function () {
//                     // always executed
//             });

// })

$('.popup-load-files-search').on('change', function(event) {
    let searchText = $(this).val();
     axios.post(`/lesson/${classroom.lesson.id}/classroom/library`, {
            query: searchText
        })
                .then(function (response) {
                   
                })
                .catch(function (error) {
                    // handle error
                })
                .finally(function () {
                    // always executed
            });
})




var filesuploaded = [];
function getUniqueFilename(fn){
    // Make sure we don't upload a file with the same name
    // TODO: Also record incoming file names as well

    var bFound = false;
    for(var i=0;i<filesuploaded.length;i++){
        if(filesuploaded[i] == fn){
            bFound = true;
        }
    }

    if(bFound){
        console.log("file already sent - altering name");
        fn = "1" + fn;
        return getUniqueFilename(fn);
    }
    console.log("final file name is " + fn);
    filesuploaded.push(fn);
    return fn;
}

function onFileDropped(evt){
    evt.preventDefault();
    if(next_image != null){
        console.log("We are already uploading a file");
        return;
    }
    if(evt.target.files){
        for( var i=0;i<evt.target.files.length; i++){
            var file = evt.target.files[i];

            next_image = new PdfItem(file,1769, 350);

            // TODO: remove the following call - ideall the node app would signal the image item
            // and this can only happen when the image is shared from the same location no the langu
            // servers.
            cursor.assets.push(next_image);
            // next_image = null; // Clear next_image so that another file drop won't be blocked

            var roomid = getParameterFromURL("roomid");
            var formdata = new FormData();
            var filename = getUniqueFilename(file.name);
            // Add file to form
            formdata.append(filename, file);

            $.ajax({
                url: "/user/classroom/uploadfile/" + roomid,
                type: "POST",
                data: formdata,
                processData: false,
                contentType: false,
                success: function(res){
                    next_image.complete_upload("/user/classroom/files/" + roomid + "/" + filename );
                },
                error: function(data, status, errorThrown){
                    console.log(data);
                    console.log("ERROR occurred: " + status);
                }
            });

        }
    } else if(evt.dataTransfer.items){
        for( var i=0;i<evt.dataTransfer.items.length; i++){
            var file = evt.dataTransfer.items[i].getAsFile();
            //console.log(file.name);

            next_image = new PdfItem(file, evt.clientX, evt.clientY);

            // TODO: remove the following call - ideall the node app would signal the image item
            // and this can only happen when the image is shared from the same location no the langu
            // servers.
            cursor.assets.push(next_image);
            // next_image = null; // Clear next_image so that another file drop won't be blocked

            var roomid = getParameterFromURL("roomid");
            var formdata = new FormData();
            var filename = getUniqueFilename(file.name);
            // Add file to form
            formdata.append(filename, file);


            // 1. This call is copied from another call made within the langu app
            // I couldn't get this to work - not really sure why
            // var call = $.ajax("/user/classroom/uploadfile/" + roomid, {
            //     dataType: 'json',
            //     data: formdata,
            //     method: 'POST',
            //     success: function(res){
            //         console.log(res);
            //         next_image.complete_upload("/user/classroom/files/" + roomid + "/" + filename );
            //     },
            //     error: function(data, status, errorThrown){
            //         console.log(data);
            //         console.log("ERROR occurred: " + status);
            //     }
            // });
            // call.fail(function (xhr, status, error) {
            //     console.log(xhr);
            //     console.log("ERROR occurred: " + status);
            // });
            //
            // call.done(function (data, status, xhr) {
            //     console.log(data);
            //     next_image.complete_upload("/user/classroom/files/" + roomid + "/" + filename );
            // });

            // 2. Below is what I use to make an upload work on my site - for some reason doesn't work
            // with the langu routes - left here for posterity
            $.ajax({
                url: "/user/classroom/uploadfile/" + roomid,
                type: "POST",
                data: formdata,
                processData: false,
                contentType: false,
                success: function(res){
                    next_image.complete_upload("/user/classroom/files/" + roomid + "/" + filename );
                },
                error: function(data, status, errorThrown){
                    console.log(data);
                    console.log("ERROR occurred: " + status);
                }
            });

        }
    }
}

function onFileDragged(evt){
    // console.log(evt);
    evt.preventDefault();
}