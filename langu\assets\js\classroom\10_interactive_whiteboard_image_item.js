var next_image = null;

var scale = [0.1, 0.25, 0.5, 1, 1.5, 2, 4];

function ImageItem(file, x, y, file_name){
    this.loaded = false;
    this.uploaded = false;
    this.sent = false; // For reconciliation through the websocket

    this.server_file_name = "";
    this.complete_upload = function(name){
        this.server_file_name = name;
        this.uploaded = true;
        if(next_image.loaded){
            // If we have finished loading the image and
            // uploading then set as next asset
            cursor.assets.push(next_image);
            next_image = null;
        }
    }
    this.img = new Image();

    this.size = new Location(0,0);
    this.type = "imageitem";
    this.moved = false;
    this.id = uuidv4().replace(/-/g, ""); // For tracking movements of items - ids will match

    this.scale_index = 3;

    this.get_lowest_point = function(){
        // Location and size of y
        // TODO: What about buttons or extra stuff?
        return this.loc.y + this.size.y;
    }


    if (FileReader && file && file!=null) {
        var fr = new FileReader();

        fr.onload = function () {
            next_image.img.src = fr.result;
            next_image.img.onload = function(){
                next_image.loaded = true;
                next_image.size.x = next_image.img.naturalWidth;
                next_image.size.y = next_image.img.naturalHeight;

                if(next_image.uploaded){
                    // If we have finished loading the image and
                    // uploading then set as next asset
                    cursor.assets.push(next_image);
                    next_image = null;
                }
            }
            // TODO: Upload to the server so it can share with the other account

            // document.getElementById(outImage).src = fr.result;
        }
        fr.readAsDataURL(file);
    } else {
        // fallback -- perhaps submit the input to an iframe and temporarily store
        // them on the server until the user's session ends.
        this.server_file_name = file_name;
        this.img.src = file_name;

        this.img.onload = function(){
            // Not sure how to find the item in the list of assets to confirm
        }
        this.loaded = true;
    }

    this.offset = 0;
    this.loc = new Location(x, y);

    // Make sure we are on the visible canvas
    if(this.loc.x < 0){
        this.loc.x = 50;
    }
    if(this.loc.y < 0){
        this.loc.y = 50;
    }
    this.filename = file.name;
    // console.log(file);
    this.update = ImageItemUpdate;
    this.draw = ImageItemDraw;

    this.hittest = ImageItemHitTest;
    this.bodyhittest = ImageItemBodyHitTest;
    this.highlight_dragbar = false;
    this.scaleImage = ImageItemScale;
    this.hittestresize = ImageItemHitTestResize;

    this.assets_deleted = false;
    this.assets = []; // This is so that we can have lines tied to the item
    this.assets_dirty = false;
    this.add_asset = ImageItemAddAsset;

    this.resize = ImageItemResize;
    this.start_resize = ImageItemStartResize;
}

function ImageItemUpdate(){
    // Maybe update if dragging?
}

function ImageItemDraw(ctx, offset){


    var old_style = ctx.strokeStyle;
    var old_fill = ctx.fillStyle;

    if(this.loaded){

        var page = {
            width:$(window).width(),
            height:$(window).height()
        };

        var sc = scale[this.scale_index];
        var sc_x = Math.round(sc * this.size.x);
        var sc_y = Math.round(sc * this.size.y);


        // if(this.size.x > page.width) {
        //     this.size.x = page.width ;
        // }
        //
        // if(this.size.y > page.height) {
        //     this.size.y = page.height;
        // }
        // if(this.scale_index == 3){
        //     console.log(sc_x + ", " + sc_y );
        //     console.log(this.size);
        // }

        if(this.highlight_dragbar === false){
            ctx.fillStyle = "#222222";
        }else{
            ctx.fillStyle = "#444444";
        }

        // Image
        var img = this.img;
        var oc = document.createElement('canvas'),
            octx = oc.getContext('2d');

        oc.width = this.size.x * 0.8;
        oc.height = this.size.y * 0.8;

        ctx.mozImageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";
        ctx.webkitImageSmoothingEnabled = true;
        ctx.msImageSmoothingEnabled = true;
        ctx.imageSmoothingEnabled = true;

        img.src = this.img.src;

        ctx.drawImage(img, this.loc.x, this.loc.y - offset, oc.width * 0.5, oc.height * 0.5);

        // Dragbar Top
        ctx.beginPath();
        var tb_width = oc.width*0.5;
        ctx.fillRect(this.loc.x, this.loc.y - 20 - offset, tb_width , 20);
        ctx.stroke();
        ctx.closePath();

        ctx.font = "14px verdana";
        ctx.fillStyle = "#eeffee";
        ctx.fillText(this.filename, this.loc.x + 5 , this.loc.y -5 - offset, this.size.x*0.4);

        ctx.linewidth = 5;
        ctx.strokeStyle = "#222222";

        sc_x = oc.width*0.5;
        sc_y = oc.height*0.5;


        // Outline of image
        ctx.beginPath();
        ctx.moveTo(this.loc.x, this.loc.y - offset);
        ctx.lineTo(this.loc.x+sc_x, this.loc.y - offset);
        ctx.lineTo(this.loc.x+sc_x, this.loc.y+sc_y - offset);
        ctx.lineTo(this.loc.x,this.loc.y+sc_y - offset);
        ctx.lineTo(this.loc.x,this.loc.y - offset);
        ctx.stroke();
        ctx.closePath();

        // Buttons on image
        // ctx.fillStyle = "#000000";
        // ctx.fillRect( this.loc.x + (sc_x - 100), this.loc.y+sc_y, 40,50 ) ;
        // ctx.strokeStyle = "#ffffff";
        // ctx.beginPath();
        // ctx.moveTo(this.loc.x + (sc_x - 100) + 10, this.loc.y+sc_y + 25);
        // ctx.lineTo(this.loc.x + (sc_x - 100) + 30, this.loc.y+sc_y + 25);
        // ctx.moveTo(this.loc.x + (sc_x - 100) + 20, this.loc.y+sc_y + 15);
        // ctx.lineTo(this.loc.x + (sc_x - 100) + 20, this.loc.y+sc_y + 35);
        // ctx.stroke();
        // ctx.closePath();

        // ctx.fillRect( this.loc.x + (sc_x - 50), this.loc.y+sc_y, 40,50 ) ;
        // // Draw the minus sign
        // ctx.beginPath();
        // ctx.moveTo(this.loc.x + (sc_x - 50) + 10, this.loc.y+sc_y + 25);
        // ctx.lineTo(this.loc.x + (sc_x - 50) + 30, this.loc.y+sc_y + 25);
        // ctx.stroke();
        // ctx.closePath();

        // Draw any assets that are attached to the image
        for(var i=0;!cursor.moving && i<this.assets.length;i++){
            var ass = this.assets[i];
            ass.draw(ctx, offset, this);
        }

        // Resize pips - bottom right hand corner
        ctx.strokeStyle = '#222222';
        ctx.beginPath();
        ctx.moveTo(this.loc.x+sc_x - 20, this.loc.y+sc_y - offset - 2);
        ctx.lineTo(this.loc.x+sc_x - 2, this.loc.y+sc_y- offset - 20);
        ctx.moveTo(this.loc.x+sc_x - 15, this.loc.y+sc_y - offset - 2);
        ctx.lineTo(this.loc.x+sc_x - 2, this.loc.y+sc_y - offset - 15);
        ctx.moveTo(this.loc.x+sc_x - 10, this.loc.y+sc_y - offset - 2);
        ctx.lineTo(this.loc.x+sc_x - 2, this.loc.y+sc_y - offset - 10);
        ctx.moveTo(this.loc.x+sc_x - 5, this.loc.y+sc_y - offset - 2);
        ctx.lineTo(this.loc.x+sc_x - 2, this.loc.y+sc_y - offset - 5);
        // ctx.moveTo(this.l)
        ctx.stroke();
        ctx.closePath();
    }

    ctx.strokeStyle = old_style;
    ctx.fillStyle = old_fill;


}

function ImageItemHitTest(_x, _y){
    var sc = scale[this.scale_index];
    var sc_x = Math.round(sc * this.size.x);
    var sc_y = Math.round(sc * this.size.y);

    // Check if we have hit the drag bar
    if(_x>this.loc.x && _x < this.loc.x+sc_x)
        if(_y>this.loc.y -20 && _y < this.loc.y)
            return true;

    // if(_x>this.loc.x+sc_x - 100 && _x<this.loc.x+sc_x-60)
    //     if(_y>this.loc.y+sc_y && _y<this.loc.y+sc_y+50)
    //         this.scaleImage(true);

    // if(_x>this.loc.x+sc_x - 40 && _x<this.loc.x+sc_x)
    //     if(_y>this.loc.y+sc_y && _y<this.loc.y+sc_y+50)
    //         this.scaleImage(false);

    return false;
}

function ImageItemScale(grow){
    if(grow){
        this.scale_index += 1;
    }else{
        this.scale_index -= 1;
    }
    if(this.scale_index < 0){
        this.scale_index = 0;
    }
    if(this.scale_index > scale.length -1){
        this.scale_index = scale.length - 1;
    }
}

function ImageItemHitTestResize(x, y, offset){

    var sizeX = Math.round(this.size.x*0.4);
    var sizeY = Math.round(this.size.y*0.4);

    // Check if the point given is over the resize bar
    if(x > this.loc.x + sizeX - 20 && x < this.loc.x + sizeX
        && y > this.loc.y + sizeY - offset - 20 && y < this.loc.y + sizeY - offset ){
        return true;
    }
    return false;
}

function ImageItemResize(diff_x, diff_y) {

    const width = this.size.x;
    var height = this.size.y;

    var newWidth = this.size.x += diff_x;

    var ratio = (width/ height);
    var newHeight = (newWidth/ratio);
    newWidth = (newHeight*ratio);

    this.size.x = newWidth;
    this.size.y= newHeight;

}


function ImageItemStartResize(){

}

function ImageItemBodyHitTest(x, y){
    if(x > this.loc.x && x < this.loc.x + this.size.x && y > this.loc.y && y < this.loc.y + this.size.y){
        return true;
    }
    return false;
}

function ImageItemAddAsset(asset){

    if(asset.type != "line"){
        console.log("ERROR: We only handle lines for textboxes");
        return;
    }

    for(var i=0;i<asset.points.length;i++){
        asset.points[i].x -= this.loc.x;
        asset.points[i].y -= this.loc.y; // The offset is to make sure we respect the scroll amount
    }
    console.log(asset.points);
    this.assets.push(asset);
    this.assets_dirty = true;
}
