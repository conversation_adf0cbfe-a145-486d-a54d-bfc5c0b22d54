function Line(_x, _y) {
    this.points = [];
    if (_x != undefined && _x != null && _y != undefined && _y != null) {
        this.points.push(new Location(_x, _y));
    }

    this.update = line_update;
    this.draw = line_draw;
    this.add_point = line_next_point;
    this.hittest = line_hittest;
    this.type = "line";
    this.sent = false;
    this.linesize = 2;
    this.color = "#ff0000";
    this.deleted = false;
    this.id = uuidv4().replace(/-/g, '');
    this.div_id = null;

    this.get_lowest_point = line_get_lowest_point;

}

function line_update(deleted = null) {
    if (deleted == true) {
        this.deleted = true;
        this.points = [];
    }
}

function line_get_lowest_point() {
    var val = 0;
    for (var i = 0; i < this.points.length; i++) {
        if (this.points[i].y > val) {
            val = this.points[i];
        }
    }
    return val;
}

function line_draw(ctx, offset, parent) {
    var oldcolor = ctx.strokeStyle;

    ctx.lineWidth = this.linesize;
    ctx.strokeStyle = this.color;

    ctx.beginPath();
    if (parent == undefined && this.points.length > 0) {
        ctx.moveTo(this.points[0].x, this.points[0].y - offset);
        for (var i = 1; i < this.points.length; i++) {
            ctx.lineTo(this.points[i].x, this.points[i].y - offset);
        }
    }
    else {
        var isFirst = true;
        for (var i = 0; i < this.points.length; i++) {
            // Check that we are within the textbox space before drawing any asset lines
            if (parent.size == null) {
                // alert("parent size is null");
                console.log("Parent null");
            }
            if (!(this.points[i].y - parent.offset < 0 || this.points[i].y - parent.offset > parent.size.y)) {
                if (isFirst) {
                    ctx.moveTo(this.points[i].x + parent.loc.x, this.points[i].y - offset + parent.loc.y - parent.offset);
                    isFirst = false;
                }
                else {
                    ctx.lineTo(this.points[i].x + parent.loc.x, this.points[i].y - offset + parent.loc.y - parent.offset);
                }

            }
        }
    }
    ctx.stroke();
    ctx.closePath();
    ctx.strokeStyle = oldcolor;
}

function line_next_point(_x, _y) {
    this.points.push(new Location(_x, _y));
}

function TempLine(_x, _y) {
    this.update = templine_update;
    this.draw = templine_draw;
    this.add_point = line_next_point;
    this.type = "templine";
    this.points = [];

    this.hittest = line_hittest; // For now this is not needed
    this.sent = false;
    this.start_ttl = 150;
    this.ttl = this.start_ttl;
    this.start_ttd = 30;
    this.ttd = this.start_ttd;
    this.color = "#ff0000";
    this.status = "DRAWING";
    this.get_lowest_point = line_get_lowest_point;
}

function templine_update() {
    if (this.status == "DRAWING") {
        // We haven't finished drawing yet
        return;
    }

    if (this.status == "DYING") {
        this.ttd -= 1;
        if (this.ttd < 1) {
            this.status = "DEAD";
        }
    }
    else {
        if (this.ttl < 1) {
            this.status = "DYING";
        }
        else {
            this.ttl -= 1;
        }
    }
}

function templine_draw(ctx, offset) {
    if (this.status == "DEAD") {
        return;
    }
    if (this.points.length < 1) {
        return;
    }
    var oldcolor = ctx.strokeStyle;
    ctx.lineWidth = this.linesize;
    if (this.ttd != this.start_ttd) {
        var opacity = this.ttd / this.start_ttd;
        ctx.strokeStyle = "rgba(255, 0, 0, " + opacity + ")";
    }
    else {
        ctx.strokeStyle = this.color;
    }

    ctx.beginPath();

    ctx.moveTo(this.points[0].x, this.points[0].y - offset);
    for (var i = 1; i < this.points.length; i++) {
        ctx.lineTo(this.points[i].x, this.points[i].y - offset);
    }
    ctx.stroke();
    ctx.closePath();
    ctx.strokeStyle = oldcolor;
}

/**
 * @deprecated This method is not being used anymore.
 */
function line_hittest(_x, _y) {
}
