function Ripple(_x, _y, color){
    this.loc = new Location(_x, _y);

    this.radius = 2;
    this.MAX_RADIUS = 24;
    this.status = "ACTIVE";
    // this.color = '#ff0000';
    this.color = color;

    // Draw update state etc
    this.draw = ripple_draw;
    this.update = ripple_update;
    this.sent = false;

    this.get_lowest_point = function(){
        // Ripples don't affect the lowest point
        return 0;
    }
}

function ripple_draw(ctx, offset){
    ctx.strokeStyle = this.color;
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.arc(this.loc.x, this.loc.y-offset, this.radius, 0, 2 * Math.PI);
    ctx.stroke();
    ctx.closePath();
}

function ripple_update(){
    this.radius += 1;
    if(this.radius > this.MAX_RADIUS){
        this.status = "DONE"
    }
}