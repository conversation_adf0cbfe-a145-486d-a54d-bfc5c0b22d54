function ShapeCircle(x, y) {
    this.id = uuidv4().replace(/-/g, '');
    this.div_id = this.id;
    this.guid = this.id;
    this.type = 'shape_circle';
    this.parent = 'shape';

    this.loc = new Location(x, y);
    this.size = new Location(10, 10);

    this.update = ShapeCircleFunctionUpdate;
    this.draw = ShapeCircleFunctionDraw;
    this.resize = ShapeCircleResize;
    this.finish_resize = ShapeCircleFinishResize;
    this.hittest = () => { return false };

    this.linesize = 2;
    this.color = "#ff0000";

    this.status = 'active';

    // Sockets indicators
    // false - do not send
    // true - send
    this.sent = false;
    this.resized = false;
    this.deleted = false;
}

function ShapeCircleFunctionUpdate(isDeleted) {
    if (isDeleted === true || isDeleted === false) {
        this.deleted = isDeleted;
    }
}

function ShapeCircleFunctionDraw(ctx, offset) {
    let oldColor = ctx.strokeStyle;

    ctx.lineWidth = this.linesize;
    ctx.strokeStyle = this.color;

    ctx.beginPath();
    drawEllipse(ctx, this.loc.x - (offset ? offset : 0), this.loc.y - (offset ? offset : 0), this.size.x, this.size.y);
    ctx.stroke();

    ctx.strokeStyle = oldColor;
}

function drawEllipse(ctx, x, y, w, h) {
  let kappa = .5522848,
      ox = (w / 2) * kappa,
      oy = (h / 2) * kappa,
      xe = x + w,
      ye = y + h,
      xm = x + w / 2,
      ym = y + h / 2;

  ctx.beginPath();
  ctx.moveTo(x, ym);
  ctx.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);
  ctx.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);
  ctx.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);
  ctx.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);
  ctx.stroke();
}

function ShapeCircleResize(x, y) {
    this.size.x += x;
    this.size.y += y;
}

function ShapeCircleFinishResize() {
    this.resized = true;
}