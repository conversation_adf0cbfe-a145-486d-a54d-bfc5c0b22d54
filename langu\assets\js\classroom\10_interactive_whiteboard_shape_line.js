function ShapeLine(x, y) {
    this.id = uuidv4().replace(/-/g, '');
    this.div_id = this.id;
    this.guid = this.id;
    this.type = 'shape_line';
    this.parent = 'shape';

    this.loc = new Location(x, y);
    this.size = new Location(1, 1);

    this.update = ShapeLineFunctionUpdate;
    this.draw = ShapeLineFunctionDraw;
    this.resize = ShapeLineResize;
    this.finish_resize = ShapeLineFinishResize;
    this.hittest = ShapeLineHittest;

    this.linesize = 2;
    this.color = "#ff0000";

    this.status = 'active';

    // Sockets indicators
    // false - do not send
    // true - send
    this.sent = false;
    this.resized = false;
    this.deleted = false;
}

function ShapeLineFunctionUpdate(isDeleted) {
    if (isDeleted === true || isDeleted === false) {
        this.deleted = isDeleted;
    }
}

function ShapeLineFunctionDraw(ctx, offset) {
    let oldColor = ctx.strokeStyle;

    ctx.lineWidth = this.linesize;
    ctx.strokeStyle = this.color;

    ctx.beginPath();
    ctx.moveTo(this.loc.x - (offset ? offset : 0), this.loc.y - (offset ? offset : 0));
    ctx.lineTo(this.loc.x + this.size.x - (offset ? offset : 0), this.loc.y + this.size.y - (offset ? offset : 0));
    ctx.closePath();

    ctx.stroke();

    ctx.strokeStyle = oldColor;
}

function ShapeLineResize(x, y) {
    this.size.x += x;
    this.size.y += y;
}

function ShapeLineFinishResize() {
    this.resized = true;
}

function ShapeLineHittest(x, y) {
    if (x >= this.loc.x && x <= this.loc.x + this.size.x && y >= this.loc.y && y <= this.loc.y + this.size.x) {
        return true;
    }

    return false;
}