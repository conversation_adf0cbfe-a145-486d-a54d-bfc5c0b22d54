function ShapeSquare(x, y) {
    this.id = uuidv4().replace(/-/g, '');
    this.div_id = this.id;
    this.guid = this.id;
    this.type = 'shape_square';
    this.parent = 'shape';

    this.loc = new Location(x, y);
    this.size = new Location(10, 10);

    this.update = ShapeSquareFunctionUpdate;
    this.draw = ShapeSquareFunctionDraw;
    this.resize = ShapeSquareResize;
    this.finish_resize = ShapeSquareFinishResize;
    this.hittest = () => { return false };

    this.linesize = 2;
    this.color = "#ff0000";

    this.status = 'active';

    // Sockets indicators
    // false - do not send
    // true - send
    this.sent = false;
    this.resized = false;
    this.deleted = false;
}

function ShapeSquareFunctionUpdate(isDeleted) {
    if (isDeleted === true || isDeleted === false) {
        this.deleted = isDeleted;
    }
}

function ShapeSquareFunctionDraw(ctx, offset) {
    let oldColor = ctx.strokeStyle;

    ctx.lineWidth = this.linesize;
    ctx.strokeStyle = this.color;

    ctx.beginPath();
    ctx.rect(this.loc.x  - (offset ? offset : 0), this.loc.y  - (offset ? offset : 0), this.size.x - 8, this.size.y - 9);
    ctx.stroke();

    ctx.strokeStyle = oldColor;
}

function ShapeSquareResize(x, y) {
    this.size.x += x;
    this.size.y += y;
}

function ShapeSquareFinishResize() {
    this.resized = true;
}