function ShapeTriangle(x, y) {
    this.id = uuidv4().replace(/-/g, '');
    this.div_id = this.id;
    this.guid = this.id;
    this.type = 'shape_triangle';
    this.parent = 'shape';

    this.loc = new Location(x, y);
    this.size = new Location(10, 10);

    this.update = ShapeTriangleFunctionUpdate;
    this.draw = ShapeTriangleFunctionDraw;
    this.resize = ShapeTriangleResize;
    this.finish_resize = ShapeTriangleFinishResize;
    this.hittest = () => { return false };

    this.linesize = 2;
    this.color = "#ff0000";

    this.status = 'active';

    // Sockets indicators
    // false - do not send
    // true - send
    this.sent = false;
    this.resized = false;
    this.deleted = false;
}

function ShapeTriangleFunctionUpdate(isDeleted) {
    if (isDeleted === true || isDeleted === false) {
        this.deleted = isDeleted;
    }
}

function ShapeTriangleFunctionDraw(ctx, offset) {
    let oldColor = ctx.strokeStyle;

    ctx.lineWidth = this.linesize;
    ctx.strokeStyle = this.color;

    ctx.beginPath();
    ctx.moveTo(this.loc.x - 30 - (offset ? offset : 0) + this.size.x * 2, this.loc.y - 30 - (offset ? offset : 0));
    ctx.lineTo(this.loc.x - 30 - (offset ? offset : 0) + this.size.x, this.loc.y + this.size.y * 3 - 30 - (offset ? offset : 0));
    ctx.lineTo(this.loc.x - 30 - (offset ? offset : 0) + this.size.x * 3, this.loc.y + this.size.y * 3 - 30 - (offset ? offset : 0));
    ctx.closePath();

    ctx.stroke();

    ctx.strokeStyle = oldColor;
}

function ShapeTriangleResize(x, y) {
    this.size.x += x / 3;
    this.size.y += y / 3;
}

function ShapeTriangleFinishResize() {
    this.resized = true;
}