//////TEXTBOX::::://///////

function TextBox(_x, _y, name) {
    this.type = "textbox";
    this.loc = new Location(_x, _y);
    this.set_size = textbox_set_size;
    this.size = new Location(0, 0);
    this.state = new State();

    // this.update = textbox_update;
    this.update = () => {};
    // this.draw = textbox_draw;
    this.draw = () => {};
    this.aggregateUpdate = textbox_aggregate_update;
    this.hittest = textbox_hittest;
    this.bodyhittest = textbox_bodyhittest;
    this.keydown = textbox_keydown;
    this.keyup = textbox_keyup;
    this.offset = 0;
    this.assets_deleted = false;
    this.assets = []; // This is so that we can have lines tied to the item
    this.assets_dirty = false;

    this.isSelected = false;

    this.update_text = textbox_update_text;
    this.update_location = textbox_update_location;
    this.text = "Here you can write your message.";
    this.text_updated = false;

    this.linesize = 2;
    this.color = "#ff0000";
    this.last_text = "Here you can write your message.";

    this.display_text_array = [[]];
    this.cursor_loc = new Location(0, 0); // Start off at start location
    this.max_length = 10; // To be updated on every update
    this.max_height = 7; // Default
    this.status = "DRAWING";
    this.changed = false;
    this.guid = uuidv4().replace(/-/g, '_');

    this.div_id = "text_" + uuidv4().replace(/-/g, '_');

    this.sent = false;
    this.never_send = false;

    this.shift_pressed = false;
    this.clicked = textbox_clicked;
    this.id = 1;
    this.clicked(this.loc.x + 10, this.loc.y + 10, 0);

    this.moved = false;
    this.hittestresize = textbox_hittest_resize;
    this.hittestclear = textbox_hittest_clear;

    this.resize = textbox_resize;
    this.start_resize = textbox_start_resize;

    this.get_lowest_point = function () {
        // The size of the box + the offset on the y axis
        return this.loc.y + this.size.y;
    }
    this.setup = textbox_setup;
    // this.setup();

    this.add_asset = textbox_add_asset;

    this.hoverColor = getHoverColor();
    this.hasUpdatedText = false; // indicates that the test has been updated
    this.changesSent = true; // Indicate that all changes were sent | true = all sent | false = send new changes
    this.lockListener = false;

    // $('#summernote_wrapper').on('summernote.focus', () => {
    //     this.state.focused = true;
    // });
    // $('#summernote_wrapper').on('summernote.blur', () => {
    //     this.state.focused = false;
    // });
    // $('#summernote_wrapper').on('summernote.change', (we, contents, $editable) => {
    //     if (this.lockListener) return;
    //
    //     (new UpdateBuilder())
    //         .type(UpdateBuilder.PERMANENT)
    //         .name(UpdateBuilder.TEXTBOX_CHANGED)
    //         .updateData({text: contents})
    //         .send();
    // });
}

/**
 * @param text {string} - Received text
 */
function textbox_aggregate_update(text) {
    this.lockListener = true;
    /**
     * @TODO: Add text aggregation + position linking + algorithm
     */
    $('#text_summernote').summernote('code', text);

    this.lockListener = false;
}

function textbox_setup() {
    // console.log('Calling textbox setup in textbox function');
    // if (this.size.x < 500) {
    //     this.size.x = 500;
    // }
    //
    // $('#text').css({left: 20, top: 20, width: 100, height: 100});
    // $('.note-editing-area').css({width: '100%'});
    // $('#summernote_wrapper').css({top: '130px', left: '60px'});
    // $('.note-editable').css({width: '100%', height: '100%'});
    // $('.note-resizebar').removeClass();
    // $('.note-editor').show();
    // $('.note-editor').css({width: '100%', height: '100%'});
    // $('.note-editor').on('mouseover', () => {
    //     $('.note-editor').css({outline: `2px solid ${this.hoverColor}`});
    //
    //     (new UpdateBuilder()).type(UpdateBuilder.TEMPORARY).name(UpdateBuilder.TEXTBOX_MOUSEOVER).send();
    // });
    // $('.note-editor').on('mouseleave', () => {
    //     $('.note-editor').css({outline: 'none'});
    //
    //     (new UpdateBuilder()).type(UpdateBuilder.TEMPORARY).name(UpdateBuilder.TEXTBOX_MOUSELEAVE).send();
    // });
    //
    // $('.note-editor').resizable({
    //     handles: "e, s, se",
    //     create: function (event, ui) {
    //         $(".note-editor.teacher .ui-resizable-e").css("cursor", "url('/images/classroom/cursor-teacher-right.svg') 30 0, auto");
    //         $(".note-editor.student .ui-resizable-e").css("cursor", "url('/images/classroom/cursor-student-right.svg') 30 0, auto");
    //         $(".note-editor.teacher .ui-resizable-s").css("cursor", "url('/images/classroom/cursor-teacher-down.svg') 0 30, auto");
    //         $(".note-editor.student .ui-resizable-s").css("cursor", "url('/images/classroom/cursor-student-down.svg') 0 30, auto");
    //         $(".note-editor.teacher .ui-resizable-se").css("cursor", " url('/images/classroom/teacher-arrow.svg'), auto");
    //         $(".note-editor.student .ui-resizable-se").css("cursor", " url('/images/classroom/student-arrow.svg'), auto");
    //     },
    //     aspectRatio: false,
    //     minHeight: 100,
    //     minWidth: 100,
    //     stop: () => {
    //         this.size.x = $('.note-editor').css("width");
    //         this.size.y = $('.note-editor').css("height");
    //     }
    // });
}

function textbox_util_get_unique_id() {
    var ret_id = 1;
    // Make sure we have a unique id for this
    while (true) {
        for (var i = 0; i < cursor.assets.length; i++) {
            if (cursor.assets[0].type == "textbox" && cursor.assets[0].id == ret_id) {
                ret_id += 1;
            }
        }
    }
}

/**
 * @deprecated
 */
function textbox_draw(ctx, offset) {
    var oldStyle = ctx.strokeStyle;
    var oldFill = ctx.fillStyle;

    // Return old colours
    ctx.strokeStyle = oldStyle;
    ctx.fillStyle = oldFill;

    return;

    var array_text = this.display_text.split("\n");
    ctx.font = "30px Monospace";
    ctx.fillStyle = this.color;
    var text_offset = 0;
    var char_width = Math.floor(ctx.measureText("M").width);
    var char_height = 24;
    this.max_length = Math.floor(this.size.x / char_width);
    this.max_height = Math.floor(this.size.y / char_height);
    // console.log(this.display_text_array);

    var start_point = 0;
    var end_point = this.display_text_array.length;

    if (this.display_text_array.length > this.max_height) {
        start_point = this.display_text_array.length - this.max_height;
    }

    if (this.cursor_loc.y < start_point) {
        start_point = this.cursor_loc.y;
        end_point = start_point + this.max_height;
    }
    for (var y = start_point; y < end_point; y++) {
        for (var x = 0; x < this.display_text_array[y].length; x++) {
            // console.log(this.display_text_array[y][x], x*char_width, char_height* y)
            if (this.display_text_array[y][x] != undefined) {
                // Magic number 40 is some clearance from the top otherwise text is flush with the box
                ctx.fillText(this.display_text_array[y][x], this.loc.x + (x * char_width), this.loc.y + (char_height * (y - start_point)) + 40 - offset);
            }
        }
    }
    // for(var i=0; i < array_text.length;i++){
    //     var disp = array_text[i];
    //     var twidth = ctx.measureText(disp).width;
    //     // console.log(twidth);
    //     if(twidth > this.size.x){

    //         var num_lines = Math.ceil(twidth/this.size.x);

    //         var char_width = ctx.measureText("M").width;
    //         var num_chars = Math.floor(this.size.x / char_width);

    //         for(var j = 0; j <= num_lines ; j++){
    //             ctx.fillText(disp.substring(j*num_chars,(j+1)*num_chars), this.loc.x + 5, this.loc.y+35 + (i*30) + (j*30) + text_offset - offset);
    //         }
    //         text_offset += (num_lines)*30;
    //     }else{
    //         ctx.fillText(disp, this.loc.x + 5, this.loc.y+35 + (i*30) + text_offset - offset);
    //     }
    // }

    var char_width = Math.floor(ctx.measureText("M").width);
    this.max_length = Math.floor(this.size.x / char_width);
    // for(var i=0;i<this.display_text_array.length;i++){
    //     if(this.display_text_array[i].length<this.max_length){
    //         // Update entire line to be spaces
    //         for(var j=0;j<this.max_length;j++){
    //             if(this.display_text_array[i][j] == undefined){
    //                 this.display_text_array[i][j] = "";
    //             }
    //         }

    //     }
    // }
    // if(this.display_text_array)


    // Draw our cursor location
    if (this.isSelected) {
        // console.log(char_height);
        // console.log(this.loc.x + (this.cursor_loc.x*char_width), (this.cursor_loc.y*char_height) + 10);
        ctx.beginPath();
        ctx.strokeStyle = "#ff0000";
        ctx.moveTo(this.loc.x + (this.cursor_loc.x * char_width) + 2, this.loc.y + ((this.cursor_loc.y - start_point) * char_height) + 40 - offset);
        ctx.lineTo(this.loc.x + (this.cursor_loc.x * char_width) + char_width + 2, this.loc.y + ((this.cursor_loc.y - start_point) * char_height) + 40 - offset)
        ctx.stroke();
        ctx.closePath()
    }


}

/**
 * @deprecated
 */
function textbox_update() {
    // if (cursor.moving) {
    //     // Hide the div
    //     $('#' + this.div_id).hide();
    // } else {
    //     $('#' + this.div_id).show();
    //     $('#' + this.div_id).css({left: this.loc.x, top: this.loc.y + 55 - cursor.offset}); // 55 is the header size
    //     // TODO: Read in any borders to make sure we are always in the correct place
    // }
}

function textbox_update_text() {
    $('#text_summernote').summernote('code', this.text);
}

function textbox_update_location() {
    // if(this.isSelected){
    //     $('#summernote_wrapper').css({left: this.loc.x, top:this.loc.y, height: this.size.y, width: this.size.x});
    // }
}


function textbox_set_size(_x, _y) {
    console.log('textbox set size event');

    this.size.x = _x - this.loc.x;
    this.size.y = _y - this.loc.y;

    // TODO: Trigger the resizing of the text for drawing
    if (this.size.x < 500) {
        this.size.x = 500;
    }
    if (this.size.y < 200) {
        this.size.y = 200;
    }
}


function textbox_resize(diff_x, diff_y) {
    console.log('textbox resize event');
    this.size.x += diff_x;
    this.size.y += diff_y;

    // Bounds - don't let be smaller than 500x200
    if (this.size.x < 500) {
        this.size.x = 500;
    }
    if (this.size.y < 200) {
        this.size.y = 200;
    }

    this.state.resized = true;

}

function textbox_start_resize() {
    // TODO: any setup - hide anything?
}

function textbox_add_asset(asset) {
    // console.log(asset);
    if (asset.type != "line") {
        console.log("ERROR: We only handle lines for textboxes");
        return;
    }
    for (var i = 0; i < asset.points.length; i++) {
        asset.points[i].x -= this.loc.x;
        asset.points[i].y -= this.loc.y - this.offset; // The offset is to make sure we respect the scroll amount
    }
    // asset.loc.x -= this.loc.x;
    // asset.loc.y -= this.loc.y;
    this.assets.push(asset);
    this.assets_dirty = true;
}

function textbox_hittest(x, y) {
    // Hittest the top bar

    // Test if we hit the clear button and respond accordingly
    if (x > this.loc.x + this.size.x - 20 && x < this.loc.x + this.size.x - 5
        && y > this.loc.y - 17 && y < this.loc.y - 3) {
        // Remove all of the assets on this
        this.assets_deleted = true;
        this.assets = [];
        return false;
    }

    //this.loc.x + this.size.x - 20, this.loc.y - 17, 15, 14

    if (x > this.loc.x && x < this.loc.x + this.size.x && y > this.loc.y - 20 && y < this.loc.y) {
        return true;
    }
    return false;
}

function textbox_hittest_resize(x, y, offset) {
    // Check if we are hovering over the resize pips
    if (x > this.loc.x + this.size.x - 20 && x < this.loc.x + this.size.x
        && y > this.loc.y + this.size.y - offset - 20 && y < this.loc.y + this.size.y - offset) {
        return true;
    }
    return false;
}

function textbox_hittest_clear(x, y, offset) {
    // Check if we are hovering over the clear button
    //ctx.fillRect(this.loc.x + this.size.x - 20, this.loc.y - offset - 17, 15, 14);
    if (x > this.loc.x + this.size.x - 20 && x < this.loc.x + this.size.x
        && y < this.loc.y - offset && y > this.loc.y - offset - 17) {
        return true;
    }
    return false;
}

function textbox_bodyhittest(x, y, offset) {
    if (x > this.loc.x && x < this.loc.x + this.size.x && y > this.loc.y - offset && y < this.loc.y + this.size.y - offset) {
        return true;
    }
    return false;
}


function textbox_scroll(event) {
    // return true if we've handled false if at the stops either end
    var amount = $('#' + this.div_id)[0].scrollHeight;
    var view_height = $('#' + this.div_id).height();
    var scrollAmount = event.originalEvent.wheelDelta;
    var handled = true;

    this.offset -= scrollAmount;
    if (this.offset > amount - view_height) {
        this.offset = amount - view_height;
        handled = false;
    }
    if (this.offset < 0) {
        this.offset = 0;
        handled = false;
    }

    // Update the div to apply the scroll
    $('#' + this.div_id).scrollTop(this.offset);

    return handled;

}


function textbox_clicked(x, y, offset) {

    this.is_selected = true;
    this.assets_dirty = true;

    // if(this.hittestresize(x, y, offset)){
    //     // console.log("Hit the resize button");
    //     // Don't want to select the text box if we click on the resiz corner
    //     // return false;
    // }

    if (x > this.loc.x && x < this.loc.x + this.size.x && y > this.loc.y - offset && y < this.loc.y + this.size.y - offset) {
        // We have clicked into the box, now figure out where the cursor should be.
        // console.log("Text box clicked")
        // this.cursor_loc.y = this.display_text_array.length - 1;
        // this.cursor_loc.x = get_last_character(this.display_text_array[this.cursor_loc.y]);
        // this.is_selected = true;
        // console.log("this is selected" + this.guid);
        // console.log(this.text);

        $('#summernote_wrapper').css({left: 60, top: 130, height: 500, width: this.size.x});
        $('#summernote_wrapper').show();
        $('#text_summernote').summernote('code', this.text);
        // $('div.note-editable').height(this.size.y - 80);
        // $('#test_summernote').css({height: this.size.y, width: this.size.x});
        return true;
    }

    if (x > this.loc.x + this.size.x - 20 && x < this.loc.x + this.size.x - 5
        && y > this.loc.y - 17 && y < this.loc.y - 3) {
        console.log('&&&&&&&&&&&&&');
        // Remove all of the assets on this
        // this.assets_deleted = true;
        // this.assets = [];
        // return false;
    }

    // return false;
}

function textbox_keydown(key) {
    console.log(key);
    return;
    if (key == "Enter") {

        var start_x = this.cursor_loc.x;
        this.cursor_loc.y += 1;
        this.cursor_loc.x = 0;
        if (this.display_text_array[this.cursor_loc.y] == undefined) {
            this.display_text_array[this.cursor_loc.y] = []; // New line so empty array

        } else {
            // Insert a new array to push the other arrays down
            this.display_text_array.splice(this.cursor_loc.y, 0, []);
            // TODO: Push remaining characters into this
        }
        this.changed = true;
    } else if (key == "Backspace") {
        // this.cursor_loc.x += 1;
        if (this.cursor_loc.x != 0) {
            // Remove character if not already at 0
            this.display_text_array[this.cursor_loc.y][this.cursor_loc.x - 1] = "";
            this.cursor_loc.x -= 1;
        } else {
            if (this.cursor_loc.y == 0) {
                // we are at the home position - reiterate to prevent leaking
                this.cursor_loc.x = 0;
                this.cursor_loc.y = 0;
            } else {
                // Jump to previous line
                this.cursor_loc.y -= 1;
                this.cursor_loc.x = this.display_text_array[this.cursor_loc.y].length;

                // TODO: Find the first character that is either undefined or ""
                this.cursor_loc.x = get_last_character(this.display_text_array[this.cursor_loc.y]) + 1;

            }
        }
        this.changed = true;

    } else if (key == "Shift") {
        this.shift_pressed = true;
    } else if (key == "Meta" || key == "Alt") {
        // Alt pressed - not sure
    } else if (key == "Control") {
        // Ctrl pressed - not sure
    } else if (key == "ArrowRight" || key == "ArrowLeft" || key == "ArrowUp" || key == "ArrowDown") {
        // console.log(key)
        if (key == "ArrowRigth") {
            // TODO: Make sure we wrap around if necesary
        } else if (key == "ArrowLeft") {
            this.cursor_loc.x -= 1;
            if (this.cursor_loc.x < 0) {
                this.cursor_loc.y -= 1;
                if (this.cursor_loc.y < 0) {
                    this.cursor_loc.y = 0;
                }
                this.cursor_loc.x = get_last_character(this.display_text_array[this.cursor_loc.y]);
            }
        } else if (key == "ArrowUp") {
            if (this.cursor_loc.y > 0) {
                this.cursor_loc.y -= 1;

                var last_char = get_last_character(this.display_text_array[this.cursor_loc.y]);
                if (last_char < this.cursor_loc.x) {
                    this.cursor_loc.x = last_char + 1;
                }
            }
        } else if (key == "ArrowDown") {
            if (this.cursor_loc.y < this.display_text_array.length - 1) {
                this.cursor_loc.y += 1;

                var last_char = get_last_character(this.display_text_array[this.cursor_loc.y]);
                if (last_char < this.cursor_loc.x) {
                    this.cursor_loc.x = last_char;
                }
            }
        } else if (key == "ArrowRight") {
            this.cursor_loc.x += 1;
            if (this.cursor_loc.x >= this.max_length) {
                console.log("Wrap arond");
                if (this.cursor_loc.y == this.display_text_array.length - 1) {
                    // We have hit the end - move back and no further
                    this.cursor_loc.x -= 1;
                } else {
                    this.cursor_loc.x = 0;
                    this.cursor_loc.y += 1;
                }

            }
        }
    } else {

        if (this.shift_pressed) {
            this.display_text_array[this.cursor_loc.y][this.cursor_loc.x] = key.toUpperCase();
        } else {
            this.display_text_array[this.cursor_loc.y][this.cursor_loc.x] = key;
        }

        this.changed = true;

        // Move to next position
        this.cursor_loc.x += 1;
        if (this.cursor_loc.x > this.max_length - 1) {
            this.cursor_loc.y += 1;
            this.cursor_loc.x = 0;

            // this.cursor_loc.y += 1; // Move to the next line
            if (this.display_text_array[this.cursor_loc.y] == undefined) {
                this.display_text_array[this.cursor_loc.y] = [];
            }
        }
    }
}


function get_last_character(line) {
    if (line == undefined) {
        console.log("ERROR: we have been passed an undefined line (get_last_character)");
        return 0;
    }
    for (var i = line.length + 1; i > 0; i--) {
        if (line[i] != undefined && line[i] != "") {
            return i + 1;
        }
    }
    return 0;
}

function textbox_keyup(key) {
    if (key == "Shift") {
        this.shift_pressed = false;
    }
}