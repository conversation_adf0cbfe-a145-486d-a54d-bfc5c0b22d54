function Toolbar(){
    this.loc = new Location(200, 25);
    this.type = 'toolbar';

    this.buttons = [];

    this.draw = toolbar_draw;
    this.update = toolbar_update;
    this.mousedown = toolbar_mousedown;
    this.mouseup = toolbar_mouseup;
    this.click = toolbar_click;
    this.mousemove = toolbar_mousemove;
    this.hitcheck = toolbar_hitcheck;
    
    this.dataToSend = false;

    this.isHovered = false;
    this.hoverColor = '';
    this.highlight_dragbar = false;
    this.is_dragging = false;
    this.cur_selected = "POINTER";
    
    this.get_lowest_point = function(){
        // Toolbar doesn't affect how far we can scroll
        return 0;
    }
    this.last_move = new Location(0,0);
}

/**
 * @deprecated this method is not in use anymore.
 */
function toolbar_draw(ctx){
    if(this.highlight_dragbar === false){
        ctx.fillStyle = "#646464";
    }else{
        ctx.fillStyle = "#000";
    }

    var tb_width = (this.buttons.length * 40) + 10;

    var rectWidth = 200;
    var rectHeight = 50;
    var cornerRadius = 10;

    var y = this.loc.y + 15;

    ctx.beginPath();
    ctx.moveTo(this.loc.x + 10, y);
    ctx.lineTo(this.loc.x + tb_width - cornerRadius, y);
    ctx.arcTo(this.loc.x + tb_width, y, this.loc.x + tb_width, y + cornerRadius, cornerRadius);
    ctx.arcTo(this.loc.x + tb_width, y + rectHeight, this.loc.x + tb_width - 10, y + rectHeight, cornerRadius);
    ctx.lineTo(this.loc.x + tb_width - 10, y + rectHeight);
    ctx.arcTo(this.loc.x , y + rectHeight, this.loc.x, y + rectHeight -10, cornerRadius);
    ctx.arcTo(this.loc.x , y, this.loc.x +10, y, cornerRadius);
    ctx.lineWidth = 2;
    ctx.strokeStyle = "#646464";
    ctx.stroke();
    
     if(this.isHovered) {
        ctx.beginPath();
        ctx.moveTo(this.loc.x + 10, y-2);
        ctx.lineTo(this.loc.x + tb_width - cornerRadius, y-2);
        ctx.arcTo(this.loc.x + 2 + tb_width, y -2, this.loc.x + 2 + tb_width, y - 2 + cornerRadius, cornerRadius + 2);
        ctx.arcTo(this.loc.x + 2 + tb_width, y+2 + rectHeight, this.loc.x+2 + tb_width - 10, y+2 + rectHeight, cornerRadius + 2);
        ctx.lineTo(this.loc.x - 2 + tb_width - 10, y+2 + rectHeight);
        ctx.arcTo(this.loc.x - 2 , y+2 + rectHeight, this.loc.x - 2, y+2 + rectHeight -10, cornerRadius + 2);
        ctx.arcTo(this.loc.x - 2 , y-2, this.loc.x - 2 +10, y-2, cornerRadius + 2);
        ctx.strokeStyle = this.hoverColor;
        ctx.lineWidth = 1;
        ctx.stroke();
    }

    for(var i=0; i<this.buttons.length; i++){
        this.buttons[i].draw(ctx);
    }
}

function toolbar_hitcheck(_x, _y){
    
    let toolbar_width = this.buttons.length * 40;
        if((_y > this.loc.y && (_y > this.loc.y + 60 && _y < this.loc.y + 69 ) || (_y < this.loc.y + 26 && _y > this.loc.y + 18 )) ){
            if(_x > this.loc.x && (_x < this.loc.x + 210) /*|| (_x < this.loc.x + toolbar_width + 10 && _x > this.loc.x + toolbar_width)*/){
            return true;
            }
        } else if(_x > this.loc.x && (_x < this.loc.x + 10) || (_x < this.loc.x + toolbar_width + 10 && _x > this.loc.x + toolbar_width)) {
            if(_y > this.loc.y && (_y > this.loc.y + 25 || _y < this.loc.y + 77)) {
                return true;
            }
        }
    return false
}
function toolbar_update(){

}

function toolbar_click(_x, _y){

}

function toolbar_mousedown(_x, _y){
    if(this.hitcheck(_x, _y)){
        this.is_dragging = true;
        this.last_move = new Location(_x,_y);
        return true;
    }

    var hit = false;
    for(var i=0; i<this.buttons.length; i++){
        var btn = this.buttons[i];
        if(btn.mousedown(_x, _y)){
            hit = true;

            if (btn.verticalButtons.length) {
                btn.drawVerticalButtons();
            }

            this.cur_selected = btn.type;
        };
    }
    return hit;
}

function toolbar_mouseup(_x, _y){
    this.is_dragging = false;
}

function toolbar_mousemove(_x, _y){
    this.highlight_dragbar = false;
    if(this.is_dragging){
        var new_x = _x - this.last_move.x;
        var new_y = _y - this.last_move.y;
        this.last_move.x = _x;
        this.last_move.y = _y;
        this.loc.x += new_x;
        this.loc.y += new_y;

        for(var i=0; i<this.buttons.length; i++){
            this.buttons[i].loc.x += new_x;
            this.buttons[i].loc.y += new_y;
        }
    }else if(this.hitcheck(_x, _y)){
        this.highlight_dragbar = true;
        this.isHovered = true;
        this.dataToSend = true;
        
        this.hoverColor = window.langu_role == "teacher" ? '#7FB802' : '#3C87F8';
    }else{
         this.isHovered = false;
        for(var i=0; i<this.buttons.length; i++){
            this.buttons[i].mousemove(_x, _y);
        }
    }

}
