function VideoItem(_x, _y, urlVideo) {
    this.loc = new Location(_x, _y);
    this.size = new Location(600, 100);

    this.draw = VideoItemDraw;
    this.update = VideoItemUpdate;
    this.sent = false;

    this.url_set = false;
    this.url = urlVideo;

    this.type = "videoitem";
    this.highlight_dragbar = false;

    this.keydown = VideoItemKeyPressed;
    this.keyup = VideoItemKeyUp;
    this.hittest = VideoItemHitTest;
    this.get_lowest_point = VideoItemGetLowestPoint;

    this.div_id = uuidv4().replace(/-/g, '');
    this.player = null;

    this.shift_down = false;
    this.control_down = false;
    this.load_video = VideoItemLoadVideo;
    this.video_loaded = false;
    this.moved = false;
    this.clicked = VideoItemClick;

    this.deleted = false;
    this.reset = VideoItemReset;

    this.stop_video = VideoItemStopVideo;
    this.play_video = VideoItemPlayVideo;
    this.jump_to = VideoItemJumpTo;

    this.event_to_send = null;
    this.video_assets = null;
    this.timeVideo = 0;
    this.handle_event = VideoItemHandleEvent;
    this.status = "stopped";
    this.hittestresize = VideoItemHitTestResize;
    this.bodyhittest = VideoItemBodyHitTest;
    this.is_master = false;

    this.resize = VideoItemResize;
    this.resizing = false;
    this.isResized = false;
    this.finish_resize = VideoItemFinishResize;
    this.start_resize = VideoItemStartResize;

    this.assets = [];
    this.add_asset = VideoItemAddAsset;
    this.assets_dirty = false;

    this.canvas = null;

    this.offset = 0; // Never changes because we can't scroll videos

    this.currentTime = 0;
    this.playbackRate = 1.0;
    this.mouseCanvasVideoClick = false;

    this.points = [];
    this.videoCanvasDraw = videoCanvasDraw;
    this.assets_to_send = null;
    this.assets_to_append = [];

    this.intervals = [];
    this.videoCtx = null;
    this.videoAssets = [];
    this.lockSocketSend = true;
    this.currentPlayerState = {
        currentTime: 0.0,
        status: false,
        playbackRate: 1.0
    };
    this.handleVideoAssets = handleVideoAssets;

    this.start_loc = null;
}

function handleVideoAssets (asset) {
    let videoAsset = {
        type: undefined,
        component: null,
        percent: asset.percent,
    };

    if (asset.type && asset.type.indexOf('shape') !== -1) {
        let functionName = asset.type.replace('shape_', '');

        switch(functionName) {
            case 'square':
                videoAsset.component = (new ShapeSquare(asset.component.loc.x, asset.component.loc.y));
                break;
            case 'circle':
                videoAsset.component = (new ShapeCircle(asset.component.loc.x, asset.component.loc.y));
                break;
            case 'triangle':
                videoAsset.component = (new ShapeTriangle(asset.component.loc.x, asset.component.loc.y));
                break;
            case 'line':
                videoAsset.component = (new ShapeLine(asset.component.loc.x, asset.component.loc.y));
                break;
            default:
                console.error('Undefined function called [' + functionName + '].');
                break;
        }

        videoAsset.component.size = asset.component.size;
        videoAsset.component.linesize = asset.component.linesize;
        videoAsset.component.color = asset.component.color;
        videoAsset.component.id = asset.component.id;
        videoAsset.component.div_id = asset.component.div_id;
        videoAsset.component.guid = asset.component.guid;
        videoAsset.component.sent = true;
    } else if (asset.type) {
        let line = new Line(asset.component.points[0].x, asset.component.points[0].y);
        line.points = asset.component.points;

        videoAsset.component = line;
    }

    this.videoAssets.push(videoAsset);

    appendAnnotationsBlock(asset.percent, this.div_id);
}

function appendAnnotationsBlock(percent, id) {
    if (!$('div#container_' + id + ' div.plyr__progress').length) {
        setTimeout(() => {
            appendAnnotationsBlock(percent, id);
        }, 1000);
    } else {
        let annotationsBlock = '<span class="video_annotations" id="video_annotations_' + id + '" data-annotation-percent="annotation_percent_' + percent + '"style="left: ' + percent + '%;"></span>';
        $('div#container_' + id + ' div.plyr__progress').append(annotationsBlock);
    }
}

function VideoItemReset() {}

function VideoItemUpdate() {
    if (this.status == 'DEAD') return;

    if (this.assets_to_append && this.assets_to_append.length > 0) {
        let line = new Line(this.assets_to_append[0].x, this.assets_to_append[0].y);

        for (var i = 1; i < this.assets_to_append.length; i++) {
            line.add_point(this.assets_to_append[i].x, this.assets_to_append[i].y);
        }

        this.videoAssets.push({
            percent: retrievePercentFromPlayerProgress(this.player),
            component: line
        });
        this.assets_to_append = [];
    }

    if (cursor.moving || this.resizing) {
        $('#container_' + this.div_id).hide();
    }
    else {
        $('#container_' + this.div_id).show();
        $('#container_' + this.div_id).css({ left: this.loc.x, top: this.loc.y });
        $('#canvas_' + this.div_id).css({ left: this.loc.x, top: this.loc.y });
    }
}

function VideoItemDraw(ctx, offset) {
    if (this.status == 'DEAD') return;

    var oldStyle = ctx.strokeStyle;
    var oldFill = ctx.fillStyle;
    var oldFont = ctx.font;

    if (this.highlight_dragbar === false) {
        ctx.fillStyle = "#222222";
    }
    else {
        ctx.fillStyle = "#444444";
    }
    // Dragbar Top
    ctx.beginPath();
    var tb_width = this.size.x;
    ctx.fillRect(this.loc.x, this.loc.y - 20 - offset, tb_width, 20);
    ctx.stroke();
    ctx.closePath();

    ctx.strokeStyle = "#555500";
    ctx.fillStyle = "white";
    ctx.font = "20px Arial";

    ctx.fillText("X", this.loc.x + this.size.x - 20, this.loc.y - offset - 2, 30, 28);
    ctx.stroke();

    ctx.font = "20px Arial";
    if (!this.url_set) {
        console.error('Something went wrong...');
        this.deleted = true;
    }
    else {
        // Drag bar heading
        ctx.fillStyle = "#eeeeee";
        let title = 'Video';
        if (this.player && this.player.config && this.player.config.title) {
            title = this.player.config.title;
        }

        ctx.fillText(title, this.loc.x + 10, this.loc.y - 2 - offset, this.size.x - 5);

        this.videoCanvasDraw(this.videoAssets, this.videoCtx);
    }

    ctx.strokeStyle = oldStyle;
    ctx.fillStyle = oldFill;
    ctx.font = oldFont;
}

function VideoItemLoadVideo() {
    if (this.url == "") {
        alert("Please enter a video id first");
        return;
    }
    if (this.video_loaded) {
        console.log("Attempting to reload the video");
        return;
    }

    if (this.url !== "") {
        this.size = new Location(600, 400);
        var newUrl = youtube_parser(this.url);

        if (!newUrl) {
            alert('The given URL is not a video URL');
            return;
        }

        this.url_set = true;
        // var html = '<div id="container_' + this.div_id + '" class="whiteboard_video_el" style="resize: auto; overflow: auto; position: absolute; height: 400px; width: ' + this.size.x + 'px; background: none; left: ' + (this.loc.x + 41) + 'px; top: ' + (this.loc.y + 31) + 'px;"></div>';
        // this.canvas = "<canvas id='canvas_" + this.div_id + "' class='whiteboard_video_canvas' width='" + this.size.x + "px' height='355px' style='z-index: 90;'></canvas>";
        // $('div.middle-inner').append(html);
        // $('body').append(this.canvas);
        // $('#canvas_' + this.div_id).css({ left: this.loc.x + 10, top: this.loc.y });
        // this.canvas = document.getElementById('canvas_' + this.div_id);
        // this.videoCtx = this.canvas.getContext('2d');
        // $('#canvas_' + this.div_id).hide();

        // let playerHtml = '<div id="player' + this.div_id + '" data-plyr-provider="youtube" data-plyr-embed-id="' + newUhhhhhhhhhrl + '"></div>';
        // $('#container_' + this.div_id).append(playerHtml);

        // this.player = new Plyr('#player' + this.div_id, {
        //     controls: ['play', 'progress', 'current-time', 'mute', 'volume', 'captions', 'settings', 'pip'],
        //     clickToPlay: false,
        //     hideControls: false,
        //     resetOnEnd: true,
        //     speed: { selected: 1, options: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2] }
        // });
        this.player.speed = 1.0; // Plyr sents playback randomply. So we need to forcely set the speed to 1.0

        this.currentPlayerState = {
            currentTime: this.player.currentTime,
            status: getCurrentStatus(this.player),
            playbackRate: this.player.speed
        };

        _this = this;
        this.player.on('play', (event) => {
            if (event.detail.plyr.speed == 0.5 && event.detail.plyr.currentTime < 15) {
                this.lockSocketSend = true;

                this.currentPlayerState.playbackRate = 1.0;
                this.player.speed = 1.0;

                this.lockSocketSend = false;
            }

            $('#canvas_' + this.div_id).hide();
        });
        this.player.on('pause', (event) => {
            $('#canvas_' + this.div_id).show();
        });
        this.player.on('seeking', (event) => {
            this.lockSocketSend = true;

            setTimeout(() => {this.lockSocketSend = false;}, 1000);
        });
        this.player.on('ready', event => {
            this.lockSocketSend = false;
        });

        this.intervals.push(setInterval(() => {
            if (getCurrentStatus(_this.player) == 'paused' && $('div.plyr').hasClass('plyr--menu-open')) {
                $('#canvas_' + this.div_id).hide();
            } else if (getCurrentStatus(_this.player) == 'paused' && !$('div.plyr').hasClass('plyr--menu-open')) {
                $('#canvas_' + this.div_id).show();
            }

            if (_this.status == 'DEAD' || this.lockSocketSend) {
                return;
            }

            let width = parseInt(($('#container_' + this.div_id).css('width')).slice(0, -2));
            let height = parseInt(($('#container_' + this.div_id).css('height')).slice(0, -2));

            if (width != this.size.x || height != this.size.y) {
                document.getElementById('canvas_' + this.div_id).style.height = (height-45) + 'px';
                document.getElementById('canvas_' + this.div_id).style.width = width + 'px';
                this.size.x = width;
                this.size.y = height;

                this.isResized = true;
            }

            let found = false;
            if (!this.lockSocketSend && this.currentPlayerState.status != getCurrentStatus(this.player)) {
                this.currentPlayerState.status = getCurrentStatus(this.player);

                found = true;
            }

            if (!this.lockSocketSend && is_float(this.player.speed) && this.player.currentTime > 5 && this.currentPlayerState.playbackRate != this.player.speed) {
                this.currentPlayerState.playbackRate = this.player.speed;

                found = true;
            }

            if (!this.lockSocketSend && Math.abs(this.currentPlayerState.currentTime - this.player.currentTime) >= 5) {
                found = true;
            }
            this.currentPlayerState.currentTime = this.player.currentTime

            if (!this.lockSocketSend && found === true) {
                this.event_to_send = this.currentPlayerState;
                found = false;
            }
        }, 500));

        function getMousePos(canvas, evt) {
            var rect = canvas.getBoundingClientRect();
            return {
                x: (evt.clientX - rect.left) / (rect.right - rect.left) * canvas.width,
                y: (evt.clientY - rect.top) / (rect.bottom - rect.top) * canvas.height
            };
        }

        var _this = this;

        this.canvas.addEventListener('mousedown', (e) => {
            if (this.annotationsFlashing === true) {
                resolveNewStatus('paused', this.player);

                return;
            }

            this.mouseCanvasVideoClick = true;
            let pos = getMousePos(this.canvas, e);

            let videoAsset = {
                type: undefined,
                component: null,
                percent: retrievePercentFromPlayerProgress(this.player),
            };

            if (window.cursor.selected.indexOf('SHAPE_') !== -1) {
                let functionName = window.cursor.selected.replace('SHAPE_', '');

                switch(functionName) {
                    case 'SQUARE':
                        videoAsset.component = (new ShapeSquare(pos.x, pos.y));
                        break;
                    case 'CIRCLE':
                        videoAsset.component = (new ShapeCircle(pos.x, pos.y));
                        break;
                    case 'TRIANGLE':
                        videoAsset.component = (new ShapeTriangle(pos.x, pos.y));
                        break;
                    case 'LINE':
                        videoAsset.component = (new ShapeLine(pos.x, pos.y));
                        break;
                    default:
                        console.error('Undefined function called [' + functionName + '].');
                        break;
                }

                videoAsset.type = videoAsset.component.type;

                this.start_loc = new Location(pos.x, pos.y);
                this.videoAssets.push(videoAsset);
            } else {
                this.videoAssets.push({
                    type: 'line',
                    component: new Line(pos.x, pos.y),
                    percent: retrievePercentFromPlayerProgress(this.player)
                });
            }

            cursor.disabled = true;
        });

        this.canvas.addEventListener('mouseleave', () => {
            if (this.mouseCanvasVideoClick === true && this.videoAssets.length) {
                _this.video_assets = this.videoAssets[this.videoAssets.length - 1];

                appendAnnotationsBlock(_this.video_assets.percent, _this.div_id);
            }

            this.mouseCanvasVideoClick = false;
            cursor.disabled = false;
        });

        this.canvas.addEventListener('mouseup', () => {
            if (this.mouseCanvasVideoClick === true && this.videoAssets.length) {
                _this.video_assets = this.videoAssets[this.videoAssets.length - 1];

                appendAnnotationsBlock(_this.video_assets.percent, _this.div_id);
            }
            this.start_loc = null;

            this.mouseCanvasVideoClick = false;
            cursor.disabled = false;
        });

        this.canvas.addEventListener('mousemove', (e) => {
            cursor.disabled = true;

            let pos = getMousePos(this.canvas, e);
            if (this.mouseCanvasVideoClick == true) {
                if (window.cursor.selected.indexOf('SHAPE_') !== -1) {
                    let diff_x = pos.x - this.start_loc.x;
                    let diff_y = pos.y - this.start_loc.y;

                    (this.videoAssets[this.videoAssets.length - 1].component).resize(diff_x, diff_y);

                    this.start_loc.x = pos.x;
                    this.start_loc.y = pos.y;
                } else {
                    (this.videoAssets[this.videoAssets.length - 1].component).add_point(pos.x, pos.y);
                }
            }
        });
    }
}

/**
 * Retrieve percent from the progress
 * 
 * @var player Plyr player
 * 
 * @returns int
 */
function retrievePercentFromPlayerProgress(player)
{
    let percentPerTime = player.duration / 100;
    let percent = player.currentTime / percentPerTime;

    return percent ? percent : 0;
}

/**
 * Check that the url has URL
 */
function youtube_parser(url) {
    // The URL is video ID
    if (url.length == 11) {
        return url;
    }

    var regExp = /(?:youtu\.be\/|youtube\.com(?:\/embed\/|\/v\/|\/watch\?v=|\w\/\w\/.*\/))([^\/&]{10,12})/;
    var match = url.match(regExp);

    return match && Array.isArray(match) && match[1].length == 11 ? match[1] : false;
}

function VideoItemJumpTo(timestamp) {

}

function videoCanvasDraw(videoAssets, videoCtx) {
    videoCtx.clearRect(0, 0, this.size.x, 355);
    if (videoAssets.length == 0) {
        return;
    }

    let percent = retrievePercentFromPlayerProgress(this.player);
    for (var i = 0; i < videoAssets.length; i++) {
        if (getCurrentStatus(this.player) === 'paused' && Math.abs(percent - videoAssets[i].percent) <= 2) {
            (videoAssets[i].component).componentsize = 2.0;
            (videoAssets[i].component).draw(videoCtx, -7.5, undefined);
        } else if (Math.abs(percent - videoAssets[i].percent) <= 0.1) {
            (videoAssets[i].component).componentsize = 2.0;
            (videoAssets[i].component).draw(videoCtx, -7.5, undefined);

            this.annotationsFlashing = true;

            if (!$('#canvas_' + this.div_id).is(":visible")) {
                $('#canvas_' + this.div_id).show();

                setTimeout(() => { $('#canvas_' + this.div_id).hide(); this.annotationsFlashing = false; }, 500);
            }
        }
    }
}

function VideoItemHandleEvent(event) {
    if (!this.player) return;

    if ('size' in event && event.size && event.size.x && event.size.y) {
        this.lockSocketSend = true;

        $('#container_' + this.div_id).css('width', event.size.x);
        $('#container_' + this.div_id).css('height', event.size.y);
        document.getElementById('canvas_' + this.div_id).style.height = (event.size.y-45) + 'px';
        document.getElementById('canvas_' + this.div_id).style.width = event.size.x + 'px';
        this.size.x = event.size.x;
        this.size.y = event.size.y;

        this.lockSocketSend = false;
    }
    else if ('assets_to_append' in event && event.assets_to_append.length > 0) {
        let line = new Line(event.assets_to_append[0].x, event.assets_to_append[0].y);

        for (var i = 1; i < event.assets_to_append.length; i++) {
            line.add_point(event.assets_to_append[i].x, event.assets_to_append[i].y);
        }

        this.videoAssets.push({
            percent: retrievePercentFromPlayerProgress(this.player),
            component: line
        });
    }
    else {
        this.lockSocketSend = true;
        if (this.currentPlayerState.status == event.status && Math.abs(this.currentPlayerState.currentTime - event.currentTime) >= 5) {
            this.player.currentTime = event.currentTime;
            this.currentPlayerState.currentTime = event.currentTime;
        }

        this.currentPlayerState.playbackRate = event.playbackRate;
        this.player.speed = event.playbackRate;

        if (this.currentPlayerState.status != event.status) {
            this.currentPlayerState.status = event.status;
            resolveNewStatus(event.status, this.player);

            this.currentPlayerState.currentTime = event.currentTime;
            this.player.currentTime = event.currentTime;
        }

        this.lockSocketSend = false;
    }
}

function VideoItemPlayVideo(seekTo) {}

function VideoItemStopVideo() {}

function VideoItemKeyPressed(key) {
    // If key is Enter then we need to set the url and get the iframe for youtube
    if (this.url_set) {
        // We are done so no more
        return;
    }

    if (key == "Shift") {
        this.shift_down = true;
        return;
    }
    if (key == "Control" || key == 'Meta') {
        this.control_down = true;
        return;
    }
    if (!this.url_set && this.control_down && key == 'v') {
        navigator.clipboard.readText().then(function(clipText) {
            var bFound = false;
            for (var i = 0; i < cursor.assets.length && !bFound; i++) {
                if (cursor.assets[i].type === "videoitem" && cursor.assets[i].url_set === false) {
                    bFound = true;
                    cursor.assets[i].url = clipText.replace(/\s/g, "");
                }
            }
        });

        return;
    }
    if (key == "Alt") {
        this.alt_down = true;
        return;
    }

    if (key == "Backspace" && this.url != "") {
        this.url = this.url.substring(0, this.url.length - 1);
    }
    else if (key == "Meta") {
        // TODO: Figure out how to handle these keys
    }
    else if (key == "Enter") {
        // Set the url and load the video
        this.load_video();
    }
    else if (key != "Backspace") {
        if (this.shift_down) {
            this.url += key.toUpperCase();
        }
        else {
            this.url += key;
        }
    }
}

function VideoItemKeyUp(key) {
    if (key == "Shift") {
        this.shift_down = false;
    }

    if (event.ctrlKey && event.keyCode === 86) {
        navigator.clipboard.readText().then(function(clipText) {
            var bFound = false;
            for (var i = 0; i < cursor.assets.length && !bFound; i++) {
                if (cursor.assets[i].type === "videoitem" && cursor.assets[i].url_set === false) {
                    bFound = true;
                    cursor.assets[i].url += clipText.replace(/\s/g, "");
                }
            }
        });
    }
}

function VideoItemClick(_x, _y, offset) {
    if (_x > this.loc.x + this.size.x - 25 && _x < this.loc.x + this.size.x + 15 && _y < this.loc.y - offset && _y > this.loc.y - 33 - offset) {
        this.deleted = true;
    }

    if (_x > this.loc.x + this.size.x - 80 && _x < this.loc.x + this.size.x &&
        _y > this.loc.y && _y < this.loc.y + 40) {
        this.load_video();
        return true;
    }

    return false;
}

function VideoItemHitTest(_x, _y) {
    // Test if we have hit the dragbar
    if (_x > this.loc.x && _x < this.loc.x + this.size.x &&
        _y > this.loc.y - 20 && _y < this.loc.y) {
        this.highlight_dragbar = true;
        return true;
    }
    else {
        this.highlight_dragbar = false;
        return false;
    }

}

function VideoItemResize(diff_x, diff_y) {
    const width = this.size.x;
    var height = this.size.y;

    var newWidth = this.size.x += diff_x;

    var ratio = (width / height);
    var newHeight = (newWidth / ratio);
    newWidth = (newHeight * ratio);

    this.size.x = newWidth;
    this.size.y = newHeight;

    // Hide the video element until we are done
    $('#' + this.div_id).hide()
}

function VideoItemFinishResize() {}

function VideoItemStartResize() {}

function VideoItemHitTestResize(x, y, offset) {
    // Check if the point given is over the resize bar
    if (x > this.loc.x + this.size.x - 20 && x < this.loc.x + this.size.x &&
        y > this.loc.y + this.size.y - offset - 20 && y < this.loc.y + this.size.y - offset) {
        return true;
    }
    return false;
}

function VideoItemBodyHitTest(x, y, offset) {
    if (x > this.loc.x && x < this.loc.x + this.size.x &&
        y > this.loc.y - offset && y < this.loc.y + this.size.y - offset) {
        return true;
    }
    return false;
}

function VideoItemGetLowestPoint() {
    return this.loc.y + this.size.y;
}

function VideoItemAddAsset(asset) {
    if (asset.type != "templine") {
        console.log("ERROR: We only handle temporary lines for videoitems");
        return;
    }
    for (var i = 0; i < asset.points.length; i++) {
        asset.points[i].x -= this.loc.x;
        asset.points[i].y -= this.loc.y - this.offset; // The offset is to make sure we respect the scroll amount
    }
    // asset.loc.x -= this.loc.x;
    // asset.loc.y -= this.loc.y;
    this.assets.push(asset);
    this.assets_dirty = true;
}

function getCurrentStatus(player) {
    if (player.playing === true || player.buffered === true) {
        return 'playing';
    }
    else if (player.paused === true) {
        return 'paused';
    }
    else {
        return 'paused';
    }
}

function resolveNewStatus(status, player) {
    if (status == 'playing') {
        player.play();
    }
    else if (status == 'paused') {
        player.pause();
    }
}
