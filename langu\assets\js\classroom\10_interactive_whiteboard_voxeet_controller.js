
function VoxeetWindow(_x, _y){
    this.loaded = true;
    this.uploaded = true;
    this.sent = ""; // For reconciliation through the websocket
    this.update = VoxeetUpdate;
    this.draw = VoxeetWindowDraw;
    this.hittest = VoxeetWindowHitTest;
    this.assets_deleted = true;
    this.assets = []; // This is so that we can have lines tied to the item
    this.assets_dirty = false;
    this.add_asset = VoxeetWindowAddAsset;
    this.server_file_name = "";
    this.clicked = voxeet_clicked;
    this.scroll = voxeet_scroll;

    this.loc = new Location(_x, _y);
    this.size = new Location(_x, _y);

    this.type = "voxeetwindow";
    this.moved = false;
    this.id = uuidv4().replace(/-/g, ""); // For tracking movements of items - ids will match
    this.isSelected = false;

    this.offset = 0;
    if (false === this.sent) {
        $('#video-window').css({left: "65%", top: _y});
    }else {
        $('#video-window').css({left: _x, top: _y});
    }
}

function VoxeetControllerChangeSize(x, y){
    this.size.x = x;
    this.size.y = y;
    this.is_dirty = true;
}

function voxeet_scroll(){
}

function VoxeetUpdate(){
    var offsets = $('#video-window').offset();
    var top = offsets.top;
    var left = offsets.left;
    this.is_dirty = true;
    this.loc.x = left;
    this.loc.y = top;

    var page = {
        width:$(window).width(),
        height:$(window).height()
    };

    if (this.loc.x > page.width) {
        this.loc.x = 50;
    }

    if (this.loc.y > page.height) {
        this.loc.y = 50;
    }

}

function VoxeetWindowDraw(ctx, offset) {
}

function voxeet_clicked(asset){
    console.log('Clicked - wox', asset);
    this.is_selected = true;
    this.moved = true;
    this.sent = true;
    if(asset.type != "line"){
        console.log("ERROR: We only handle lines");
        return;
    }

    for(var i=0;i<asset.points.length;i++){
        asset.points[i].x -= this.loc.x;
        asset.points[i].y -= this.loc.y; // The offset is to make sure we respect the scroll amount
    }
    console.log(asset.points);
    this.assets.push(asset);
    this.assets_dirty = true;
    // // if(this.hittestresize(x, y, offset)){
    // //     // console.log("Hit the resize button");
    // //     // Don't want to select the text box if we click on the resiz corner
    // //     // return false;
    // // }
    //
    // if(x > this.loc.x && x < this.loc.x+this.size.x && y>this.loc.y - offset && y<this.loc.y+this.size.y - offset){
    //     // We have clicked into the box, now figure out where the cursor should be.
    //     console.log("Text box clicked")
    //
    //     // $('div.note-editable').height(this.size.y - 80);
    //     // $('#test_summernote').css({height: this.size.y, width: this.size.x});
    //     return true;
    // }
    //
    // return false;
}

function VoxeetWindowHitTest(_x, _y) {
    this.moved = true;

    var offsets = $('#video-window').offset();
    var top = offsets.top;
    var left = offsets.left;
    // console.log(top);
    // console.log(left);
    this.is_dirty = true;
    this.loc.x = left;
    this.loc.y = top;
    var offsets = $('#video-window').offset();
    var top = offsets.top;
    var left = offsets.left;


    this.loc = new Location(left, top);
    this.loc.x = left;
    this.loc.y = top;
    var sc = scale[3];
    var sc_x = Math.round(sc * this.size.x);
    var sc_y = Math.round(sc * this.size.y);

    // Check if we have hit the drag bar
    if(_x>this.loc.x && _x < this.loc.x+sc_x)
        if(_y>this.loc.y -20 && _y < this.loc.y)
            return true;

    // if(_x>this.loc.x+sc_x - 100 && _x<this.loc.x+sc_x-60)
    //     if(_y>this.loc.y+sc_y && _y<this.loc.y+sc_y+50)
    //         this.scaleImage(true);

    // if(_x>this.loc.x+sc_x - 40 && _x<this.loc.x+sc_x)
    //     if(_y>this.loc.y+sc_y && _y<this.loc.y+sc_y+50)
    //         this.scaleImage(false);

    return false;
}

function VoxeetWindowAddAsset(asset){
    if(asset.type != "line"){
        console.log("ERROR: We only handle lines for textboxes");
        return;
    }

    for(var i=0;i<asset.points.length;i++){
        asset.points[i].x -= this.loc.x;
        asset.points[i].y -= this.loc.y; // The offset is to make sure we respect the scroll amount
    }
    console.log(asset.points);
    this.assets.push(asset);
    this.assets_dirty = true;
}
