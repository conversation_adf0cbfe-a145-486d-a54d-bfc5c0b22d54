$(document).ready(function() {
    $('#summernote_host').hide();
    var roomid = getParameterFromURL("roomid");
    var role = window.langu_role;
    if (role == undefined || role == "" || role == null) {
        role = "student"
    }

    var socket = null;
    if (window.ws !== undefined && window.ws.socket !== undefined) {
        socket = window.ws.socket;
    }
    else {
        console.log("We don't have a websocket - are we debugging?");
    }

    var manyPdf = 0;
    var manyPdf2 = 0;

    function get_asset_from_json(ass, limitPdfNumber = null) {
        if (ass.type === "pdfitem") {
            manyPdf++;
        }

        // Read in details of asset and return the relevant asset type
        if (ass.type == "ripple") {
            // We don't care about ripples or imageitems here
            return;
        }
        else if (ass.type === "line") {
            var ln = new Line();
            ln.points = ass.points;
            ln.linesize = ass.linesize ? ass.linesize : ass.size;
            ln.color = ass.color;
            ln.div_id = ass.id;
            ln.id = ass.div_id;
            return ln;
        }
        else if (ass.type === "templine") {
            var ln = new TempLine();
            ln.points = ass.points;
            ln.linesize = ass.linesize;
            ln.color = ass.color;
            return ln;
        }
        else if (ass.type === "imageitem") {
            createVideo(ass.videoId);
        }
        else if (ass.type === "textbox") {
            var tb = textbox = new TextBox(ass.loc.x, ass.loc.y, ass.filename);
            tb.guid = ass.id;
            tb.size.x = ass.loc.w;
            tb.size.y = ass.loc.h;
            tb.setup();
            tb.clicked(506, 65, 0);
            setTimeout(function() {
                tb.clicked(506, 65, 0);
            }, 500)
            return tb;
        }
        else if (ass.type === "pdfitem") {
            let pdfItem = new PdfItem(null, ass.loc.x, ass.loc.y, ass.filename);
            pdfItem.img = {};
            pdfItem.img.src = ass.src;
            pdfItem.size.x = ass.size.x;
            pdfItem.size.y = ass.size.y;
            pdfItem.id = ass.id;
            pdfItem.sent = true;
            pdfItem.filename = ass.filename;
            pdfItem.uploaded = true;
            pdfItem.loaded = true;

            return pdfItem;
        }
        else if (ass.type === "videoitem") {
            var vi = new VideoItem(ass.loc.x, ass.loc.y);
            vi.size.x = ass.size.x;
            vi.size.y = ass.size.y;
            ass.sent = true;
            vi.url = ass.url;
            vi.div_id = ass.id;
            return vi;
        }
        else if (ass.type === "voxeetwindow") {
            var vt = new VoxeetWindow(ass.loc.x, ass.loc.y);
            ass.sent = false;
            vt.div_id = ass.div_id;
            vt.id = ass.id;
            vt.filename = ass.filename;
            return vt;
        }

        else if (ass.parent && ass.parent === 'shape') {
            let shape = null;

            switch(ass.type) {
                case 'shape_circle':
                    shape = new ShapeCircle(ass.loc.x, ass.loc.y);
                    break;
                case 'shape_line':
                    shape = new ShapeLine(ass.loc.x, ass.loc.y);
                    break;
                case 'shape_square':
                    shape = new ShapeSquare(ass.loc.x, ass.loc.y);
                    break;
                case 'shape_triangle':
                    shape = new ShapeTriangle(ass.loc.x, ass.loc.y);
                    break;
                default:
                    console.error('Undefined shape type [' + ass.type + '].');
                    break;
            }

            shape.size = ass.size;
            shape.linesize = ass.linesize;
            shape.color = ass.color;
            shape.id = ass.id;
            shape.div_id = ass.id;
            shape.guid = ass.id;

            return shape;
        }
        else {
            // console.log("Unfulfilled asset type: " + ass.type);
            // console.log(ass);
        }
    }

    var countPdf = 0;
    var countPdf2 = 0;
    var breakPdfCount = 0;
    var breakPdfCount2 = 0;
    var deletedImages = [];

    if (socket != null) {
        socket.on('asset update', function(e) {
            if (typeof e !== 'object') {
                var data = JSON.parse(e);
            }
            else {
                var data = e;
            }

            if (window.provider == null) {
                window.provider = {
                    type: data.provider,
                    changed: false,
                };
            } else if (window.provider.type != data.provider) {
                window.provider = {
                    type: data.provider,
                    changed: false,
                };
                window.location.reload(false);
            }

            if (window.othercursor == null) {
                // Flip the colours as we are the other role
                if (role == "teacher") {
                    window.othercursor = new Cursor('#3C87F8', false);
                }
                else {
                    window.othercursor = new Cursor('#7FB802', true);
                }
            }

            var assets = [];

            window.othercursor.loc.x = data.cursor.x;
            window.othercursor.loc.y = data.cursor.y;

            if (data.cursor.screen_width !== undefined && data.cursor.screen_width !== 0) {
                window.othercursor.screen.width = data.cursor.screen_width;
            }
            if (data.cursor.screen_height !== undefined && data.cursor.screen_height !== 0) {
                window.othercursor.screen.height = data.cursor.screen_height;
            }
            if (data.cursor.offset !== undefined) {
                window.othercursor.offset = data.cursor.offset;
            }

            assets = data.assets;

            // If we are refreshing assets (on initial page reload for instance) then
            // do that now
            for (var i = 0; data.refresh_assets !== undefined && data.refresh_assets != null && i < data.refresh_assets.length; i++) {
                // Add the asset to the cursor - will return undefined for ripples and other
                // bits we want to ignore for now.

                if (data.refresh_assets[i].type == "textboxchange") {
                    for (var j = 0; j < window.cursor.assets.length; j++) {
                        if (window.cursor.assets[j].guid === data.refresh_assets[i].id) {
                            window.cursor.assets[j].text = data.refresh_assets[i].text;
                            window.cursor.assets[j].display_text_array = data.refresh_assets[i].data;
                        }
                    }
                }
                else if (data.refresh_assets[i].type == "imageitemmove") {
                    // console.log(data.refresh_assets[i].type);

                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];

                        if (a.div_id === data.refresh_assets[i].id) {
                            cursor.assets[j].loc.x = data.refresh_assets[i].loc.x;
                            cursor.assets[j].loc.y = data.refresh_assets[i].loc.y;
                            cursor.assets[j].size.x = data.refresh_assets[i].size.x;
                            cursor.assets[j].size.y = data.refresh_assets[i].size.y;

                            bFound = true;
                        }
                    }
                    // TODO: Make sure we move the correct image to an appropriate location
                    console.log("We got a refresh item for text box moving");
                }
                else if (data.refresh_assets[i].type == "imagedeleted") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];

                        if (a.div_id === data.refresh_assets[i].id) {
                            cursor.assets[j].loc.x = -1000;
                            cursor.assets[j].loc.y = -1000;
                            bFound = true;
                        }
                    }
                    // TODO: Make sure we move the correct image to an appropriate location
                    console.log("We got a refresh item for text box moving");
                }
                else if (data.refresh_assets[i].type === "linedeleted") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];

                        if (a.id === data.refresh_assets[i].id) {
                            a.points = [];
                            a.status = 'DEAD';

                            bFound = true;
                        }
                    }
                }
                else if (data.refresh_assets[i].type === "videoitemdelete") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];

                        if (a.div_id === data.refresh_assets[i].div_id) {
                            $("[id*=" + window.cursor.assets[j].div_id + "]").remove();

                            for (let k = 0; k < window.cursor.assets[j].intervals.length; k++) {
                                clearInterval(window.cursor.assets[j].intervals[k]);
                            }

                            delete window.cursor.assets[j];
                            window.cursor.assets.splice(j, 1);

                            bFound = true;
                        }
                    }
                }
                else if (data.refresh_assets[i].type == "pdfitemmove") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];

                        if (a.id === data.refresh_assets[i].id) {
                            a.loc = data.refresh_assets[i].loc;

                            bFound = true;
                        }
                    }
                }
                else if (data.refresh_assets[i].type == "pdfitemdeleted") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];

                        if (a.id === data.refresh_assets[i].id) {
                            a.status = 'DEAD';

                            bFound = true;
                        }
                    }
                }
                else if (data.refresh_assets[i].type == "pdfitemresized") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];

                        if (a.id === data.refresh_assets[i].id) {
                            a.size = data.refresh_assets[i].size;

                            bFound = true;
                        }
                    }
                }
                else if (data.refresh_assets[i].type == "voxeetwindowmove") {
                    // console.log(data.refresh_assets[i].type);

                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];

                        if (a.div_id === data.refresh_assets[i].id) {

                            cursor.assets[j].loc.x = data.refresh_assets[i].loc.x;
                            cursor.assets[j].loc.y = data.refresh_assets[i].loc.y;

                            cursor.assets[j].size.x = data.refresh_assets[i].size.x;
                            cursor.assets[j].size.y = data.refresh_assets[i].size.y;
                            bFound = true;
                        }
                    }
                    // TODO: Make sure we move the correct image to an appropriate location
                }
                else if (data.refresh_assets[i].type == "videoitemmove") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];
                        if (a.div_id == data.refresh_assets[i].id) {
                            cursor.assets[j].loc.x = data.refresh_assets[i].loc.x;
                            cursor.assets[j].loc.y = data.refresh_assets[i].loc.y;
                            bFound = true;
                        }
                    }
                }
                else if (data.refresh_assets[i].type == "videoitemevent") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];
                        if (a.div_id == data.refresh_assets[i].id) {
                            cursor.assets[j].handle_event(data.refresh_assets[i]);
                            bFound = true;
                        }
                    }
                    // Not sure what to do with this - for now ignore.
                }
                else if (data.refresh_assets[i].type == "videoassets") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];
                        if (a.div_id == data.refresh_assets[i].div_id) {
                            cursor.assets[j].handleVideoAssets(data.refresh_assets[i].asset);

                            bFound = true;
                        }
                    }
                    // Not sure what to do with this - for now ignore.
                }
                else if (data.refresh_assets[i].type == "videoitemresize") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];
                        if (a.div_id == data.refresh_assets[i].id) {
                            cursor.assets[j].handle_event(data.refresh_assets[i]);
                            bFound = true;
                        }
                    }
                    // Not sure what to do with this - for now ignore.
                }
                else if (data.refresh_assets[i].type == "textboxmove") {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];
                        if (a.guid == data.refresh_assets[i].id) {
                            cursor.assets[j].loc.x = data.refresh_assets[i].loc.x;
                            cursor.assets[j].loc.y = data.refresh_assets[i].loc.y;
                            cursor.assets[j].update_location();
                            bFound = true;
                        }
                    }
                }
                else if (data.refresh_assets[i].parent && data.refresh_assets[i].parent === "shape" && data.refresh_assets[i].type.indexOf('deleted') !== -1) {
                    var bFound = false;
                    for (var j = 0; j < cursor.assets.length && bFound == false; j++) {
                        var a = cursor.assets[j];
                        if (a.id == data.refresh_assets[i].id) {
                            cursor.assets[j].status = 'DEAD';

                            bFound = true;
                        }
                    }

                    if (!bFound) {
                        console.error("Failed to find shape component [" + data.refresh_assets[i].type + "] by ID [" + data.refresh_assets[i].id + "].");
                    }
                }
                else {
                    data.refresh_assets.forEach(function(entry) {
                        if (entry.type === "pdfitem" && breakPdfCount === 0) {
                            countPdf++;
                        }
                    });
                    breakPdfCount = 1;
                    // console.log(data);
                    var ass = get_asset_from_json(data.refresh_assets[i], countPdf);
                    if (
                        ass !== undefined &&
                        (
                            ass.type == "textbox" ||
                            ass.type == "videoitem" ||
                            ass.type == "imageitem" ||
                            ass.type == "voxeetwindow" ||
                            ass.type == "pdfitem" ||
                            (ass.parent && ass.parent == 'shape')
                        )) {
                        // Check that we don't have one with the same id already
                        var bFound = false;
                        for (var j = 0; j < window.cursor.assets.length; j++) {
                            if (ass.type == "textbox" && window.cursor.assets[j].guid == ass.guid) {
                                console.log("we have this asset already (during refresh)")
                                bFound = true;
                                break;
                            }
                            else if (ass.type == "videoitem" && window.cursor.assets[j].div_id == ass.div_id) {
                                bFound = true;
                                break;
                            }
                            else if (ass.type == "pdfitem" && window.cursor.assets[j].div_id == ass.id) {
                                bFound = true;
                                break;
                            }
                            else if (ass.type == "voxeetwindow" && window.cursor.assets[j].div_id == ass.div_id) {
                                bFound = true;
                                break;
                            }
                            else if (ass.type == "imageitem" && window.cursor.assets[j].id == ass.id) {
                                bFound = true;
                                break;
                            } else if (ass.parent && ass.parent === 'shape' && window.cursor.assets[j].id == ass.id) {
                                bFound = true;
                                break;
                            }
                        }
                        if (!bFound) {
                            if (ass.type == "videoitem") {
                                ass.load_video();
                                ass.is_master = true; // We have been given our own video back - make sure we know we are master
                            }
                            ass.sent = true; // Otherwise we'll just send it again
                            ass.never_send = true; // We must never send this - it was obtained on a refresh
                            window.cursor.assets.push(ass);
                        }
                    }
                    else if (ass !== undefined && ass !== null) {
                        ass.sent = true; // Otherwise we'll just send it again
                        ass.never_send = true; // We must never send this - it was obtained on a refresh
                        window.cursor.assets.push(ass);
                    }
                }
            }
            if (data.refresh_assets !== undefined && data.refresh_assets != null && data.refresh_assets.length > 0) {
                window.cursor.refresh_lowest_point();
            }

            for (var i = 0; i < assets.length; i++) {
                var ass = assets[i];
                if (ass.type == "ripple") {
                    window.othercursor.ripples.push(new Ripple(ass.loc.x, ass.loc.y, window.othercursor.color));
                }
                else if (ass.type === "textboxchange") {
                    // console.log("Updating text in textbox")
                    // console.log(ass)

                    for (var j = 0; j < window.cursor.assets.length; j++) {
                        var a = window.othercursor.assets[j];
                        var a = window.cursor.assets[j];
                        if (a.type == "textbox" && ass.id == a.guid) {
                            // Create a new array for the text
                            window.cursor.assets[j].display_text_array = [];
                            window.cursor.assets[j].text = ass.text;
                            window.cursor.assets[j].update_text();
                            $('#text_summernote').summernote('code', ass.text);
                        }
                    }
                }
                else if (ass.type == "imageitemmove") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        window.cursor.assets[j].loc.x = ass.loc.x;
                        window.cursor.assets[j].loc.y = ass.loc.y;
                        window.cursor.assets[j].size.x = ass.size.x;
                        window.cursor.assets[j].size.y = ass.size.y;
                        window.cursor.assets[j].moved = true;
                        bFound = true;
                    }
                    if (!bFound) {
                        console.log(ass)
                        console.log("failed to find video item with div_id " + ass.id);
                    }
                }
                else if (ass.type === "imagedeleted") {
                    for (var i = 0; assets !== undefined && assets !== null && i < assets.length; i++) {
                        if (assets[i].type === "imageitem") {
                            for (
                                var j = 0; deletedImages !== undefined && deletedImages !== null &&
                                j < deletedImages.length; j++
                            ) {
                                if (assets[i].id === deletedImages[j].id) {
                                    for (
                                        var k = 0; window.othercursor.assets !== undefined &&
                                        window.othercursor.assets !== null && k < window.othercursor.assets.length; k++
                                    ) {
                                        if (window.othercursor.assets[k].id === deletedImages[j].id) {
                                            window.othercursor.assets[k].loaded = false;
                                            window.othercursor.assets[k].uploaded = false;
                                            window.othercursor.assets[k].sent = false;
                                            window.othercursor.assets[k].src = "";
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                // else if (ass.type == "pdfitem") {
                //     manyPdf2++;
                //     var bFound = false;
                //     for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                //         var a = window.cursor.assets[j];
                //         // window.cursor.assets[j].loc.x = ass.loc.x;
                //         // window.cursor.assets[j].loc.y = ass.loc.y;
                //         // window.cursor.assets[j].size.x = ass.size.x;
                //         // window.cursor.assets[j].size.y = ass.size.y;
                //         if (manyPdf2 === countPdf2) {
                //             showPDF(ass.src);
                //             //console.log(ass.loc.x);
                //             //console.log(ass.loc.y);
                //             $('#catch-pdf').css({ left: 100, top: 200 });
                //         }
                //         window.cursor.assets[j].moved = false;
                //         bFound = true;
                //     }
                //     if (!bFound) {
                //         //console.log(ass);
                //         console.log("failed to find video item with div_id " + ass.id);
                //     }
                // }
                else if (ass.type == "pdfitemmove") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        var a = window.cursor.assets[j];
                        if (a.type === "pdfitem" && a.id === ass.id) {
                            window.cursor.assets[j].loc = ass.loc;
                            window.cursor.assets[j].size = ass.size;
                            window.cursor.assets[j].moved = false;
                            bFound = true;
                        }
                    }
                    if (!bFound) {
                        console.log(ass)
                        console.log("failed to find pdf item with id " + ass.id);
                    }
                }
                else if (ass.type == "pdfitemdeleted") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        var a = window.cursor.assets[j];
                        if (a.type === "pdfitem" && a.id === ass.id) {
                            window.cursor.assets[j].status = 'DEAD';
                            bFound = true;
                        }
                    }
                    if (!bFound) {
                        console.log(ass)
                        console.log("failed to find pdf item with id " + ass.id);
                    }
                }
                else if (ass.type == "pdfitemresized") {
                    var bFound = false;
                    for (var j = 0; j < window.othercursor.assets.length && !bFound; j++) {
                        var a = window.cursor.assets[j];
                        if (a.type === "pdfitem" && a.id === ass.id) {
                            window.cursor.assets[j].size = ass.size;
                            window.cursor.assets[j].resized = false;
                            bFound = true;
                        }
                    }
                    if (!bFound) {
                        console.log(ass)
                        console.log("failed to find pdf item with id " + ass.id);
                    }
                }
                else if (ass.type == "voxeetwindowmove") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        var a = window.cursor.assets[j];
                        if (a.type === "voxeetwindow" && a.div_id === ass.id) {
                            window.cursor.assets[j].loc.x = ass.loc.x;
                            window.cursor.assets[j].loc.y = ass.loc.y;
                            window.cursor.assets[j].size.x = ass.size.x;
                            window.cursor.assets[j].size.y = ass.size.y;
                            window.cursor.assets[j].moved = false;
                            bFound = true;
                        }
                    }
                    if (!bFound) {
                        console.log(ass)
                        console.log("failed to find video item with div_id " + ass.id);
                    }
                }
                else if (ass.type == "textboxmove") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length; j++) {
                        var a = window.cursor.assets[j];
                        if (a.type == "textbox" && ass.id == a.guid) {
                            window.cursor.assets[j].loc.x = ass.loc.x;
                            window.cursor.assets[j].loc.y = ass.loc.y;
                            window.cursor.assets[j].moved = false; // Just in case we were about to send a new position
                            bFound = true;
                        }
                    }
                    if (!bFound) {
                        console.log("Failed to find the textbox with id: " + ass.id);
                    }
                }
                else if (ass.type == "videoitemmove") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        var a = window.cursor.assets[j];
                        if (a.type == "videoitem" && a.div_id == ass.id) {
                            window.cursor.assets[j].loc.x = ass.loc.x;
                            window.cursor.assets[j].loc.y = ass.loc.y;
                            window.cursor.assets[j].moved = false;
                            bFound = true;
                        }
                    }
                    if (!bFound) {
                    }
                }
                 else if (ass.type == "vueimageitemcreate") {
                    var bFound = false;
                    let image = document.getElementById(`desc-${ass.imageId}`);
                     window.cursor.assets.forEach(element => {
                        if(element.type === 'vue_imageitem' && element.imageId == ass.imageId) {
                            itemObject = element;
                        }
                    })
                    if(!image && ass.isCreated) {
                        createImage(ass.imageId, ass.imageUrl, ass.imageName, true);
                    }
                    if(ass.isClose) {
                        itemObject.isClosed = true
                    }
                    bFound = true;
                }
                 else if (ass.type == "vueimageitemclose") {
                    var bFound = false;
                     window.cursor.assets.forEach(element => {
                        if(element.type === 'vue_imageitem' && element.imageId == ass.imageId) {
                            itemObject = element;
                        }
                    })
                    if(ass.isClose) {
                        itemObject.isClosed = true
                    }
                    bFound = true;
                }
                else if (ass.type == "vue_imageitemresize") {
                    let itemObject;

                    window.cursor.assets.forEach(element => {
                        if(element.type === 'vue_imageitem' && element.imageId === ass.imageId) {
                            itemObject = element;
                        }
                    })
                    if(ass.isDrag) {
                        itemObject.position.top = ass.position.top
                        itemObject.position.left = ass.position.left
                    } else if(ass.isResize) {
                        itemObject.size.width = ass.size.width
                        itemObject.size.height = ass.size.height
                    }
                }
                else if (ass.type == "videoitemcreate") {
                    var bFound = false;
                    let video = document.getElementById(`desc-${ass.videoId}`);
                    if(!video) {
                        createVideo(ass.videoId);
                        window.cursor.assets.forEach(element => {
                        if(element.type === 'vue_videoitem' && element.videoId === ass.videoId) {
                            element
                        }
                    })
                    }
                    bFound = true;
                }
                else if (ass.type == "vue_videoitemevent") {
                    let videoObject = null;
                    window.cursor.assets.forEach(element => {
                        if(element.type === 'vue_videoitem' && element.videoId === ass.videoId) {
                            videoObject = element;
                        }
                    })
                    if(videoObject) {
                        videoObject.isMute = ass.isMute;
                        videoObject.volumeValue = ass.volumeValue
                        videoObject.speedValue = ass.speedValue
                        if(ass.isPlay) {
                            videoObject.isPlay = true
                        } else if(!ass.isPlay) {
                            videoObject.isPlay = false
                        }
                        if(ass.isSeeking) {
                            videoObject.currentTime = ass.currentTime
                            ass.isSeeking = false
                        }
                        if(ass.isClose) {
                            videoObject.isClose = ass.isClose
                        }
                    }

                }
                 else if (ass.type == "vue_videoitemdraw") {
                    let videoObject = null;
                    window.cursor.assets.forEach(element => {
                        if(element.type === 'vue_videoitem' && element.videoId === ass.videoId) {
                            videoObject = element;
                        }
                    })
                    videoObject.canvasGetAssets = true
                    videoObject.videoAssets = ass.videoAssets
                }
                else if (ass.type == "vue_videoitemresize") {
                    window.cursor.assets.forEach(element => {
                        if(element.type === 'vue_videoitem' && element.videoId === ass.videoId) {
                            videoObject = element;
                        }
                    })
                    if(ass.isDrag) {
                        videoObject.position.top = ass.position.top
                        videoObject.position.left = ass.position.left
                    } else if(ass.isResize) {
                        videoObject.size.width = ass.size.width
                        videoObject.size.height = ass.size.height
                    }
                }

                 else if (ass.type == "cursor_move") {
                    let cursor = null;
                    window.cursor.assets.forEach(element => {
                        if(element.type === 'cursor_move' && element.id === ass.id) {
                            cursor = element;
                        }
                    })
                    if(cursor) {
                        cursor.update(ass.top, ass.left, ass.cursor_name);
                    }
                }
                else if (ass.type == "videoitemevent") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        var a = window.cursor.assets[j];
                        if (a.type == "videoitem" && a.div_id == ass.id) {
                            bFound = true;
                            if (ass.currentTime || ass.status || ass.playbackRate) {
                                window.cursor.assets[j].currentTime = ass.currentTime;
                                window.cursor.assets[j].status = ass.status;
                                window.cursor.assets[j].playbackRate = ass.playbackRate;

                                window.cursor.assets[j].handle_event(ass);
                            }
                            else {
                                window.cursor.assets[j].assets_to_append = ass.assets_to_append;
                            }
                        }
                    }
                }
                else if (ass.type == "videoassets") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        var a = window.cursor.assets[j];

                        if (a.type == "videoitem" && a.div_id == ass.id) {
                            bFound = true;

                            cursor.assets[j].handleVideoAssets(ass.asset);
                        }
                    }
                }
                else if (ass.type == "videoitemresize") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        var a = window.cursor.assets[j];
                        if (a.type == "videoitem" && a.div_id == ass.id) {
                            bFound = true;
                            if (ass.size && ass.size.x && ass.size.y) {
                                cursor.assets[j].handle_event(ass);
                            }
                        }
                    }
                }
                else if (ass.type == "videoitemcanvas") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        var a = window.cursor.assets[j];
                        if (a.type == "videoitem" && a.div_id == ass.id) {
                            bFound = true;
                            window.cursor.assets[j].assets_to_append = ass.assets_to_append;
                        }
                    }
                }
                else if (ass.type == 'toolbar_hover' && toolbar != null) {
                    toolbar.isHovered = ass.isHovered;

                    if (window.langu_role === "teacher") {
                        toolbar.hoverColor = '#3C87F8'
                    }
                    else {
                        toolbar.hoverColor = '#7FB802'
                    }
                }
                else if (ass.type == 'textbox_hover') {
                    textbox.isHovered = ass.isHovered;
                    if (textbox.isHovered) {
                        $('.note-editor').css({ border: `2px solid ${window.langu_role == "teacher" ? '#3C87F8'  : '#7FB802'}` });
                    }
                    else {
                        $('.note-editor').css({ border: 'none' });
                    }
                }
                else if (ass.type === "textboxresize") {
                    // console.log("Updating text in textbox")
                    console.log('textbox size');
                    // Create a new array for the text
                    /* $('.note-editor').css({width: `${ass.size.x}px`});
                     $('.note-editor').css({height: `${ass.size.y}px`});*/
                    textbox.size.x = ass.loc.w;
                    textbox.size.y = ass.loc.h;

                }
                else if (ass.type === "linedeleted") {
                    var bFound = false;
                    for (var j = 0; j < window.othercursor.assets.length && !bFound; j++) {
                        if (window.othercursor.assets[j].id === ass.id) {
                            bFound = true;

                            window.othercursor.assets[j].status = 'DEAD';
                        }
                    }

                    if (!bFound) {
                        console.error('Line component not found.', ass, window.cursor.assets);
                    }
                }
                else if (ass.type === "videoitemdelete") {
                    var bFound = false;
                    for (var j = 0; j < window.cursor.assets.length && !bFound; j++) {
                        if (window.cursor.assets[j].div_id === ass.div_id) {
                            $("[id*=" + window.cursor.assets[j].div_id + "]").remove();

                            for (let k = 0; k < window.cursor.assets[j].intervals.length; k++) {
                                clearInterval(window.cursor.assets[j].intervals[k]);
                            }

                            delete window.cursor.assets[j];
                            window.cursor.assets.splice(j, 1);

                            bFound = true;
                        }
                    }

                    if (!bFound) {
                        console.error('Video component not found.', ass, window.cursor.assets);
                    }
                }
                else if (ass.parent && ass.parent === "shape" && ass.type.indexOf('deleted') !== -1) {
                    var bFound = false;
                    for (var j = 0; j < window.othercursor.assets.length && !bFound; j++) {
                        if (window.othercursor.assets[j].id === ass.id) {
                            window.othercursor.assets[j].status = 'DEAD';

                            bFound = true;
                        }
                    }

                    if (!bFound) {
                        console.error("Failed to find shape component [" + ass.type + "] by ID [" + ass.id + "].");
                    }
                }
                else {
                    var a = get_asset_from_json(ass);

                    if (a !== undefined) {
                        if (a.type == "textbox") {
                            a.sent = true;
                            a.never_send = true;
                            var bFound = false;
                            for (var j = 0; j < window.cursor.assets.length; j++) {
                                if (window.cursor.assets[j].guid == a.guid) {
                                    bFound = true;
                                    console.log("we already have this " + a.type);
                                    break;
                                }
                            }
                            // Don't push if we already have one with the same guid
                            if (!bFound) {
                                if (a.name == "Messaging") {
                                    // Push messaging so it's the first one
                                    window.cursor.assets.unshift(a);
                                }
                                else {
                                    window.cursor.assets.push(a);
                                }
                            }
                        }
                        else if (a.type == "videoitem") {
                            a.sent = true;
                            a.never_send = true;
                            var bFound = false;
                            for (var j = 0; j < window.cursor.assets.length; j++) {
                                if (window.cursor.assets[j].div_id == a.div_id) {

                                    bFound = true;
                                    console.log("We already have this " + a.type + " , div_id: " + a.id);
                                    break;
                                }
                            }
                            // Don't push if we already have one with the same guid
                            if (!bFound) {
                                // Must remember to load video and create div
                                a.load_video();

                                window.cursor.assets.push(a);
                            }
                        }
                        else if (a.type == 'pdfitem') {
                            a.size.x = ass.size.x;
                            a.size.y = ass.size.y;

                            window.cursor.assets.push(a);
                        }
                        else {
                            window.othercursor.assets.push(a);
                        }
                    }
                }
            }

            if (assets.length > 0) {
                window.othercursor.refresh_lowest_point();
            }
            return false;
        });
    }

    if (socket != null) {
        socket.on('status', function(e) {
            var data = JSON.parse(e);
            if (data.status !== undefined) {
                if (data.status == "fail") {
                    // console.log(data.message);
                }
            }
        });
    }

    var screen_width = $(window).width();
    var screen_height = $(window).height();

    function send() {
        var assets = [];

        for (var i = 0; i < cursor.ripples.length; i++) {
            var asset = cursor.ripples[i];

            if (asset.sent == false) {
                var next_obj = { type: 'ripple', loc: { 'x': asset.loc.x, 'y': asset.loc.y } };
                assets.push(next_obj);
                cursor.ripples[i].sent = true;

            }
        }

        // TOOLBAR SECTION
        if (toolbar != null && toolbar.dataToSend) {
            var next_obj = {
                type: "toolbar_hover",
                isHovered: toolbar.isHovered,
                hoverColor: toolbar.hoverColor
            }

            assets.push(next_obj);
            toolbar.dataToSend = false;
        }
        // TOOLBAR SECTION END

        var commands = [];
        for (var i = 0; i < cursor.assets.length; i++) {
            var asset = cursor.assets[i];
            // DEBUG:
            // @TODO: REMOVE THIS
            /*if (asset.type == "toolbar")
            console.log('asset.type == "toolbar"', asset);*/
            // @TODO: END REMOVE THIS

            if (asset.type == "line" && asset.sent == false) {
                // console.log(cursor.assets);
                var next_obj = {
                    id: asset.id,
                    div_id: asset.id,
                    type: "line",
                    points: asset.points,
                    size: asset.linesize,
                    color: asset.color,
                    deleted: false
                }

                assets.push(next_obj);
                cursor.assets[i].sent = true;
            }
            else if (asset.type === "line" && asset.deleted === true) {
                var next_obj = {
                    id: asset.id,
                    div_id: asset.id,
                    type: "linedeleted",
                    deleted: true
                }

                assets.push(next_obj);
                asset.deleted = false;
                asset.status = 'DEAD';
            }
            else if (asset.type === "templine" && asset.sent == false) {
                var next_obj = {
                    type: "templine",
                    points: asset.points,
                    size: asset.linesize,
                    color: asset.color
                }
                assets.push(next_obj);

                cursor.assets[i].sent = true;
            }
            else if (asset.type === 'imageitem' && asset.sent === false && asset.uploaded === true) { // Wait until uploaded before sending
                var next_obj = {
                    type: "imageitem",
                    id: asset.id,
                    div_id: asset.id,
                    filename: asset.filename,
                    src: asset.img.src,
                    loc: {
                        x: asset.loc.x,
                        y: asset.loc.y
                    },
                    size: {
                        x: asset.size.x,
                        y: asset.size.y
                    }
                }

                // TEMP DEBUG
                // console.log("Sending image");
                // console.log(next_obj);

                // TODO: push file_data
                assets.push(next_obj);

                // console.log(assets);

                cursor.assets[i].sent = true;
            }
            else if (asset.type === 'imageitem' && (asset.isDrag === true || asset.isResize === true)) { // Wait until uploaded before sending
                var next_obj = {
                    type: asset.type,
                    id: asset.id,
                    imageUrl: asset.imageUrl,
                    imageId: asset.imageId,
                    position: {
                        top: asset.position.top,
                        left: asset.position.top
                    },
                    size: {
                        width: asset.size.width,
                        height: asset.size.height
                    }
                }

                // TEMP DEBUG
                 //console.log("Sending image", next_obj);
                // console.log(next_obj);

                // TODO: push file_data
                assets.push(next_obj);

                // console.log(assets);

                cursor.assets[i].sent = true;
            }
            else if (asset.type === 'videoitem' && (asset.isDrag === true || asset.isResize === true)) { // Wait until uploaded before sending
                var next_obj = {
                    type: asset.type,
                    id: asset.id,
                    videoId: asset.videoId,
                    position: {
                        top: asset.position.top,
                        left: asset.position.top
                    },
                    size: {
                        width: asset.size.width,
                        height: asset.size.height
                    }
                }
                 //console.log("Sending image", next_obj);
                assets.push(next_obj);
                cursor.assets[i].sent = true;
            }
            else if (asset.type === 'pdfitem' && asset.sent === false && asset.uploaded === true) { // Wait until uploaded before sending
                cursor.assets[i].sent = true;

                var next_obj = {
                    type: "pdfitem",
                    id: asset.id,
                    div_id: asset.id,
                    file: asset.file,
                    filename: asset.filename,
                    src: asset.img.src,
                    loc: {
                        x: asset.loc.x,
                        y: asset.loc.y
                    },
                    size: {
                        x: asset.size.x,
                        y: asset.size.y
                    },
                }

                asset.assets_dirty = false;

                assets.push(next_obj);
            }
            else if (asset.type === "pdfitem" && asset.sent === true && asset.moved === true) {
                var next_obj = {
                    type: "pdfitemmove",
                    id: asset.id,
                    div_id: asset.id,
                    src: asset.img.src,
                    loc: {
                        x: asset.loc.x,
                        y: asset.loc.y
                    },
                    size: {
                        x: asset.size.x,
                        y: asset.size.y
                    }
                }

                cursor.assets[i].moved = false;
                assets.push(next_obj);
            }
            else if (asset.type === "pdfitem" && asset.resized === true) {
                var next_obj = {
                    id: asset.id,
                    type: "pdfitemresized",
                    div_id: asset.id,
                    size: asset.size,
                }

                asset.resized = false;

                assets.push(next_obj);
            }
            else if (asset.type === "pdfitem" && asset.deleted === true) {
                // console.log('to jest to id'+ asset.id);
                var next_obj = {
                    id: asset.id,
                    type: "pdfitemdeleted",
                    div_id: asset.id,
                    src: "",
                    loc: {
                        x: asset.loc.x,
                        y: asset.loc.y
                    },
                    status: 'DEAD',
                }

                cursor.assets[i].moved = false;
                asset.deleted = false;
                asset.loaded = false;
                asset.uploaded = false;
                asset.sent = false;
                assets.push(next_obj);
            }
            else if (asset.type === 'voxeetwindow' && asset.moved === true) { // Wait until uploaded before sending
                var next_obj = {
                    type: "voxeetwindow",
                    id: asset.id,
                    loc: {
                        x: asset.loc.x,
                        y: asset.loc.y
                    },
                    size: {
                        x: asset.size.x,
                        y: asset.size.y
                    }
                }
                asset.moved = false;
                assets.sent = true;

                // TODO: push file_data
                assets.push(next_obj);

                cursor.assets[i].sent = true;
            }
            else if (asset.type === 'voxeetwindow' && asset.sent === true && asset.assets_dirty === true) { // Wait until uploaded before sending
                var next_obj = {
                    type: "voxeetwindow",
                    id: asset.id,
                    loc: {
                        x: asset.size.x,
                        y: asset.size.y
                    },
                    size: {
                        x: asset.size.x,
                        y: asset.size.y
                    }
                }
                // TODO: push file_data
                assets.push(next_obj);

                cursor.assets[i].sent = false;
            }
            else if (asset.type === "imageitem" && asset.sent === true && asset.moved === true) {
                // console.log('to jest to id'+ asset.id);
                var next_obj = {
                    type: "imageitemmove",
                    id: asset.id,
                    div_id: asset.id,
                    src: asset.img.src,
                    loc: {
                        x: asset.loc.x,
                        y: asset.loc.y
                    },
                    size: {
                        x: asset.size.x,
                        y: asset.size.y
                    }
                }
                cursor.assets[i].moved = false;
                assets.push(next_obj);
            }
            else if (asset.type === "imageitem" && asset.deleted === true) {
                var next_obj = {
                    id: asset.id,
                    type: "imagedeleted",
                    div_id: asset.id,
                    src: "",
                    loc: {
                        x: -1000,
                        y: -1000
                    },
                    size: {
                        x: asset.size.x,
                        y: asset.size.y
                    }
                }
                cursor.assets[i].moved = false;
                asset.deleted = false;
                asset.loaded = false;
                asset.uploaded = false;
                asset.sent = false;
                assets.push(next_obj);
            }
            else if (asset.type === "textbox" && asset.assets_dirty == true) {
                var next_obj = {
                    type: "textboxchange",
                    loc: {
                        x: asset.loc.x,
                        y: asset.loc.y,
                        w: asset.size.x,
                        h: asset.size.y
                    },
                    text: asset.text,
                    id: asset.guid,
                }

                asset.assets_dirty = false;
                cursor.assets[i].sent = true;
                assets.push(next_obj);
            }
            else if (asset.type === "textbox" && asset.isResized === true) {
                var next_obj = {
                    type: "textboxresize",
                    loc: {
                        x: asset.loc.x,
                        y: asset.loc.y,
                        w: asset.size.x,
                        h: asset.size.y
                    },
                    id: asset.guid,
                }

                asset.isResized = false;
                cursor.assets[i].sent = true;
                assets.push(next_obj);
            }
            else if (asset.type === "textbox" && asset.dataToSend == true) {

                var next_obj = {
                    type: "textbox_hover",
                    isHovered: asset.isHovered,
                    hoverColor: asset.hoverColor,
                    id: asset.guid,
                }
                cursor.assets[i].sent = true;
                assets.push(next_obj);
                asset.dataToSend = false;
            }
            else if (asset.type == "textbox" && asset.changed == true) {
                var next_obj = {
                    type: "textboxchange",
                    text: asset.last_text,
                    id: asset.guid
                }

                cursor.assets[i].changed = false;
                assets.push(next_obj);
            }
            else if (asset.type == "textbox" && asset.sent == true && asset.moved == true) {
                // var next_obj = {
                //     type: "textboxmove",
                //     loc: {
                //         x: asset.loc.x,
                //         y: asset.loc.y
                //     },
                //     id: asset.guid
                // }
                // cursor.assets[i].moved = false;
                // assets.push(next_obj);
            }
            else if (asset.type == "textbox") {
                for (var j = 0; j < asset.assets.length; j++) {
                    var next_asset = asset.assets[j];
                    if (next_asset.sent == false) {
                        var next_obj = {
                            type: "textboxasset",
                            id: asset.guid,
                            asset: next_asset
                        }
                    }
                    // console.log('dasdasdsa')
                }
            }
            else if (asset.type == "textbox" && asset.assets_deleted == true) {
                var next_obj = {
                    type: "textboxassetclear",
                    id: asset.guid
                }
                cursor.assets[i].assets_deleted = false;
                assets.push(next_obj);
            }
            else if (asset.type == "videoitem" && asset.sent == false && asset.url_set) {
                var next_obj = {
                    type: "videoitem",
                    url: asset.url,
                    loc: { x: asset.loc.x, y: asset.loc.y },
                    size: { x: asset.size.x, y: asset.size.y },
                    id: asset.div_id
                }
                cursor.assets[i].sent = true;

                assets.push(next_obj);
            }
            else if (asset.type == "videoitem" && asset.sent == true && asset.moved == true) {
                var next_obj = {
                    type: "videoitemmove",
                    loc: {
                        x: asset.loc.x,
                        y: asset.loc.y
                    },
                    id: asset.div_id
                }
                cursor.assets[i].moved = false;
                assets.push(next_obj);
            }
            else if (asset.type === "videoitem" && asset.deleted === true) {
                var next_obj = {
                    type: "videoitemdelete",
                    div_id: asset.div_id,
                    id: asset.div_id,
                    deleted: true
                }

                assets.push(next_obj);

                cursor.assets[i].deleted = false;
                cursor.assets[i].status = 'DEAD';
            }

            else if (asset.type === "vue_videoitem" && asset.isCreated === true) {
                var next_obj = {
                    type: "videoitemcreate",
                    videoId: asset.videoId,
                    playerObject: asset.playerObject,
                    isPlay: asset.isPlay,
                }

                assets.push(next_obj);

                cursor.assets[i].isCreated = false;
            }
            else if (asset.type === "vue_videoitem" && asset.videoEvent === true) {
                var next_obj = {
                    type: "vue_videoitemevent",
                    videoId: asset.videoId,
                    isPlay: asset.isPlay,
                    currentTime: asset.currentTime,
                    isSeeking: asset.isSeeking,
                    isMute: asset.isMute,
                    volumeValue: asset.volumeValue,
                    speedValue: asset.speedValue,
                    isClose: asset.isClose,
                    videoAssets: asset.videoAssets
                }

                assets.push(next_obj);

                cursor.assets[i].videoEvent = false;
                cursor.assets[i].isSeeking = false;
            }
            else if (asset.type === "vue_videoitem" && asset.addCanvasDraw === true) {
                var next_obj = {
                    type: "vue_videoitemdraw",
                    videoId: asset.videoId,
                    videoAssets: asset.videoAssets
                }

                assets.push(next_obj);

                cursor.assets[i].addCanvasDraw = false;
            }

            else if (asset.type === "vue_imageitem" && asset.isCreated === true) {
                var next_obj = {
                    type: "vueimageitemcreate",
                    imageId: asset.imageId,
                    imageUrl: asset.imageUrl,
                    imageName: asset.imageName,
                    isCreated: asset.isCreated,
                }

                assets.push(next_obj);
                cursor.assets[i].isCreated = false;
            }
             else if (asset.type === "vue_imageitem" && asset.isClose === true) {
                var next_obj = {
                    type: "vueimageitemclose",
                    imageId: asset.imageId,
                    imageUrl: asset.imageUrl,
                    imageName: asset.imageName,
                    isClose: asset.isClose,
                    isClosed: asset.isClosed,
                }

                assets.push(next_obj);
                cursor.assets[i].isClose = false;
            }
               else if (asset.type === "vue_imageitem" && (asset.isResize === true || asset.isDrag === true)) {
                var next_obj = {
                    type: "vue_imageitemresize",
                    imageId: asset.imageId,
                    isDrag: asset.isDrag,
                    isResize: asset.isResize,
                    position: asset.position,
                    size: asset.size,
                }

                assets.push(next_obj);

                cursor.assets[i].isDrag = false;
                cursor.assets[i].isResize = false;
            }
            else if (asset.type === "cursor_move" && asset.isMove === true) {
                var next_obj = {
                    id: asset.id,
                    type: "cursor_move",
                    top: asset.top,
                    left: asset.left,
                    cursor_name: asset.cursor_name
                }

                assets.push(next_obj);

                cursor.assets[i].isMove = false;
            }
            else if (asset.type === "vue_videoitem" && (asset.isResize === true || asset.isDrag === true)) {
                var next_obj = {
                    type: "vue_videoitemresize",
                    videoId: asset.videoId,
                    isDrag: asset.isDrag,
                    isResize: asset.isResize,
                    position: asset.position,
                    size: asset.size,
                }

                assets.push(next_obj);

                cursor.assets[i].isDrag = false;
                cursor.assets[i].isResize = false;
            }
            else if (asset.type === "videoitem" && asset.event_to_send != null) {
                var next_obj = {
                    type: "videoitemevent",
                    id: asset.div_id,
                    data: asset.event_to_send
                }

                assets.push(next_obj);
                cursor.assets[i].event_to_send = null;
            }
            else if (asset.type === "videoitem" && asset.video_assets != null) {
                var next_obj = {
                    type: "videoassets",
                    id: asset.div_id,
                    div_id: asset.div_id,
                    asset: asset.video_assets,
                    status: 'active'
                }

                assets.push(next_obj);
                cursor.assets[i].video_assets = null;
            }
            else if (asset.type === "videoitem" && asset.isResized == true) {
                var next_obj = {
                    type: "videoitemresize",
                    id: asset.div_id,
                    data: {
                        isResized: asset.isResized,
                        size: asset.size,
                    }
                }

                assets.push(next_obj);
                cursor.assets[i].isResized = false;
            }
            else if (asset.parent && asset.parent == 'shape' && asset.sent === false) {
                var next_obj = {
                    type: asset.type,
                    parent: asset.parent,
                    id: asset.id,
                    loc: asset.loc,
                    size: asset.size,
                    linesize: asset.linesize,
                    color: asset.color
                }

                assets.push(next_obj);
                cursor.assets[i].sent = true;
            }
            else if (asset.parent && asset.parent == 'shape' && asset.deleted === true) {
                var next_obj = {
                    type: asset.type + '_deleted',
                    parent: asset.parent,
                    id: asset.id,
                    div_id: asset.div_id,
                    status: 'DEAD',
                }

                assets.push(next_obj);
                cursor.assets[i].deleted = false;
                cursor.assets[i].status = 'DEAD';
            }
            else if (window.provider && window.provider.changed === true) {
                window.provider.changed = false;
                commands.push({
                    type: 'video_provider_changed',
                    provider: window.provider.type,
                });
            }
        }

        // Prep the data object to send to the websocket
        var data_str = { cursor: { x: cursor.loc.x, y: cursor.loc.y, offset: cursor.offset }, screen: { width: screen_width, height: screen_height } };

        var data = {
            id: roomid,
            data: data_str,
            role: role,
            assets: assets,
            commands: commands,
        }

        if (socket !== null) {
            socket.emit('classroom update', JSON.stringify(data));
        }

        if (commands.length > 0) {
            window.location.reload(false);
        }

        setTimeout(send, 100);
    }

    // This will give us all of the assets - ours and theirs for initial setup
    // On return from that we will then start sending updates
    setTimeout(function() {
        if (socket === undefined || socket === null) {
            // console.debug("The socket does not exist - are we debugging?");
            return;
        }
        socket.emit("refresh", { id: roomid, role: role });
    }, 1000);

    // Delay before we start the process of sending updates
    setTimeout(send, 3000);
});
