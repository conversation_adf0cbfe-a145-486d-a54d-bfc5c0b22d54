;
(function ($) {
    var startDate = moment(classroom.lesson.startDate);
    var endDate = startDate.clone().add(classroom.lesson.length, 'm');
    var currentDate = moment();

    var timer = $('#lesson-timer');

    var padLeft = function (value, padding, char) {
        char = char || '0';

        var padString = '';
        for (var i = 0; i < padding; i++) {
            padString += char;
        }

        return (padString + value).slice(-padding);
    };

    var getTimeDiffReadable = function (fDate, sDate) {
        var hours = Math.abs(sDate.diff(fDate, 'hours') % 24);
        var minutes = Math.abs(sDate.diff(fDate, 'minutes') % 60);
        var seconds = Math.abs(sDate.diff(fDate, 'seconds') % 60);

        return ': ' + padLeft(hours, 2) + ':' + padLeft(minutes, 2) + ':' + padLeft(seconds, 2);
    };

    var getDateDiffReadable = function (fDate, sDate) {
        if (sDate.diff(fDate, 'days') === 0) {
            return getTimeDiffReadable(fDate, sDate);
        }

        var weeks = Math.abs(sDate.diff(fDate, 'weeks'));
        var days = Math.abs(sDate.diff(fDate, 'days'));

        var txt = '';
        var readable = ' ';
        if (weeks > 1) {
            readable += weeks;
            txt = Translator.trans('lesson.classroom.timer.weeks');
        } else if (weeks > 0) {
            txt = Translator.trans('lesson.classroom.timer.week');
        } else if (days > 1) {
            readable += days;
            txt = Translator.trans('lesson.classroom.timer.days');
        } else {
            txt = Translator.trans('lesson.classroom.timer.day');
        }
        
        return readable + txt;
    };

    var endsInText = Translator.trans('lesson.classroom.timer.ends_in');
    var startsInText = Translator.trans('lesson.classroom.timer.starts_in');
    var classTimer = function (startDate, endDate) {
        var currentDate = moment();
        
        if (startDate <= currentDate && currentDate < endDate) {
            var msg = endsInText;
            msg += getDateDiffReadable(currentDate, endDate);
            
            if(timer.text() !== msg) {
                timer.text(msg);
            }
            
            setTimeout(classTimer.bind(this, startDate, endDate), 500);
        } else if (currentDate < startDate) {
            var msg = startsInText;
            msg += getDateDiffReadable(currentDate, startDate);
            
            if(timer.text() !== msg) {
                timer.text(msg);
            }
            
            setTimeout(classTimer.bind(this, startDate, endDate), 500);
        } else {
            var msg = Translator.trans('lesson.classroom.timer.ended');
            timer.text(msg);
        }
    };

    classTimer(startDate, endDate);
})(jQuery);
