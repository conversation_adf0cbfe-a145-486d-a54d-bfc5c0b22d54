;
(function () {
    window.initializeTokbox = () => {
        $('button#full-screen').hide();

        var sessionId = classroom.ot.sessionId;
        var token = classroom.ot.token;
        var apiKey = classroom.ot.apiKey;

        var session = null;
        var publisher = null;
        var screenSharingPublisher = null;
        var subscriber = null;

        var $remoteControls = $('.stream-controls.remote');
        var $localControls = $('.stream-controls.local');
        var $screenshareControls = $('.stream-controls.screenshare');
        var $screenshareContainer = $('#tokbox-screenshare-stream-placeholder');
        var $screenshareStream = $('#video-window-shared');
        var $sidebarTabsContainer = $('.sidebar-tabs-container');
        
        var screensharePlaceholderHtml = '<div id="tokbox-screenshare-stream-placeholder"></div>';
        var remoteStreamPlaceholderHtml = $('<div>').append($('#tokbox-other-stream-placeholder').clone()).html();

        if (OT.checkSystemRequirements() === 1) {
            var pubOptions = {
                usePreviousDeviceSelection: true,
                name: classroom.user.name,
                style: {
                    nameDisplayMode: "off"
                },
                fitMode: "contain",
                mirror: true,
                showControls: false,
                frameRate: 15
            };

            publisher = OT.initPublisher('tokbox-my-stream-placeholder', pubOptions, function (error) {
                if (error) {
                    if (error.name !== 'OT_USER_MEDIA_ACCESS_DENIED') {
                        alert('The publishing stream could not be initialized. Please report to Langu Team. Error code: ' + error.code + ', details: ' + error.message);
                    }
                }
            });

            publisher.on({
                streamCreated: function (event) {
                    var $el = $(event.target.element);

                    $el.css({
                        width: '100%',
                        height: '100%',
                        position: 'absolute'
                    });

                    if (event.stream.videoType === 'screen'){
                        $screenshareControls.removeClass('hidden');
                        $screenshareStream.removeClass('hidden');
                    } else {
                        $localControls.removeClass('hidden');
                    }
                },
                accessDenied: function (event) {
                    alert('You have denied access to audio/video devices. Without the permission to access these devices we are unable to publish your stream');
                },
                streamDestroyed: function (event) {
                    event.preventDefault();

                    if (event.stream.videoType === 'screen'){
                        $screenshareControls.addClass('hidden');
                        $screenshareStream.addClass('hidden');                    
                    } else {
                        $localControls.addClass('hidden');
                    }
                }
            });

            session = sessionInit(apiKey, sessionId);
            window.tokbox_session = session;

            session.on({
                sessionReconnecting: function (event) {
                    console.log('OT reconnecting');
                },
                sessionReconnected: function (event) {
                    console.log('OT reconnected');
                },
                sessionDisconnected: function (event) {
                    console.log('OT disconnected: ' + event.reason);
                },
                connectionCreated: function (event) {
                    if (event.connection.connectionId !== session.connection.connectionId) {
    //                    console.log('OT participant connected');
                    }
                },
                connectionDestroyed: function (event) {
    //                console.log('OT participant disconnected');
                },
                streamCreated: function (event) {                

                    // If it's a screensharing stream.
                    if (event.stream.videoType === 'screen'){
                        subscriber = session.subscribe(event.stream, 'tokbox-screenshare-stream-placeholder',{
                            fitMode: "contain",
                            style: {
                                audioLevelDisplayMode: "off",
                                buttonDisplayMode: "off",
                                nameDisplayMode: "off"
                            },
                            mirror: false
                        }, function(err){
                            if (err) return console.log(err);

                            $('#tokbox-screenshare-stream-placeholder').css({
                                width: '100%'
                            });
                            $screenshareControls.removeClass('hidden');
                            $screenshareStream.removeClass('hidden');
                            $sidebarTabsContainer.addClass('screenshare-active');
                        });
                        return;
                    }

                    // Otherwise (audio / video).
                    subscriber = session.subscribe(event.stream, 'tokbox-other-stream-placeholder', {
                        fitMode: "contain",
                        style: {
                            audioLevelDisplayMode: "off",
                            buttonDisplayMode: "off",
                            nameDisplayMode: "auto"
                        },
                        mirror: false
                    }, function (error) {
                        if (error) {
                            alert('Sorry, we encountered an error trying to connect you to the remote stream. Please report to Langu Team. Error code: ' + error.code + ', details: ' + error.message);
                        } else {
                            $('#tokbox-other-stream-placeholder').css({
                                width: '100%',
                                height: '100%',
                                position: 'absolute'
                            });

                            $remoteControls.removeClass('hidden');
                        }
                    });
                },
                streamDestroyed: function (event) {
                    if (event.stream.videoType === 'screen'){
                        var $streamContainer = $screenshareControls.closest('.stream-container');
                        $streamContainer.prepend(screensharePlaceholderHtml);  
                        $screenshareControls.addClass('hidden');     
                        $screenshareStream.addClass('hidden');                                                     
                        $sidebarTabsContainer.removeClass('screenshare-active');                                                      
                        return;
                    } else {
                        $remoteControls.addClass('hidden');
                        var $streamContainer = $remoteControls.closest('.stream-container');
                        $streamContainer.prepend(remoteStreamPlaceholderHtml); 
                        console.log('OT partner stream destroyed: ' + event.reason);
                    }
                }
            });

            session.connect(token, onConnect);
        } else {
            // The client does not support WebRTC.
            // You can display your own message.
            alert('Sorry. Your browser does not support our streaming protocols. Please try latest version of Google Chrome or Mozilla Firefox.');
        }

        function sessionInit(apiKey, sessionId) {
            return OT.initSession(apiKey, sessionId);
        }

        function onConnect(error) {
            if (error) {
                alert('Sorry, we encountered an error trying to connect to streaming session. Please report to Langu Team. Error code: ' + error.code + ', details: ' + error.message);
                //console.log('OT connecting error: ' + error.code + ', ' + error.message);
                return;
            }

            session.publish(publisher);
        }

        var controlActionVideoToggle = function () {
            var self = $(this);
            let icon = self.find("i");
            
            if(publisher.stream.hasVideo) {
                publisher.publishVideo(false);
                icon.text('videocam_off');
            } else {
                publisher.publishVideo(true);
                icon.text('videocam');          
            }
        };
        
        var controlActionAudioToggle = function () {
            var self = $(this);
            let icon = self.find("i");
            
            if(publisher.stream.hasAudio) {
                publisher.publishAudio(false);
                icon.text('mic_off');
            } else {
                publisher.publishAudio(true);
                icon.text('mic');
            }
        };

        var controlScreenshareToggle = function(){
            var self = $(this);
            let icon = self.find("i");

            if (screenSharingPublisher!=null){
                screenSharingPublisher.destroy();
                var $streamContainer = $screenshareControls.closest('.stream-container');
                $streamContainer.prepend(screensharePlaceholderHtml);
                $sidebarTabsContainer.removeClass('screenshare-active');            
                $screenshareStream.addClass('hidden');            
                screenSharingPublisher = null;
                icon.text('screen_share');
            } else {
                screenSharingPublisher = OT.initPublisher('tokbox-screenshare-stream-placeholder', {videoSource: 'screen', showControls: false, resolution: '640x480', frameRate: 7 });
                $screenshareStream.removeClass('hidden');  
                $sidebarTabsContainer.addClass('screenshare-active');                      
                session.publish(screenSharingPublisher);
                icon.text('stop_screen_share');
            }
        };

        var controlClick = function (e) {
            var self = $(this);
            var action = self.data('action');

            switch (action) {
                case 'video-toggle':
                    controlActionVideoToggle.call(this);
                    break;
                case 'audio-toggle':
                    controlActionAudioToggle.call(this);
                    break;
                case 'screenshare-toggle':
                    controlScreenshareToggle.call(this);
                    break;
            }

            return;
        };

        $('#video-window-buttons').on('click', 'button', controlClick);
    }
})();
