;
(function ($) {
    const PROVIDER_VOXEET = 'voxeet';
    const PROVIDER_TOKBOX = 'tokbox';

    const videoSwitcherText = 'switch to ' + ((window.classroom.provider && window.classroom.provider.toLowerCase() == 'tokbox') ? 'primary' : 'backup') +' video player';
    const videoSwitcherSpan = $('div.video-provider-switcher > span');

    videoSwitcherSpan.text(videoSwitcherText);

    window.commitVideoProvider = () => {
        if (!window.classroom.provider) window.classroom.provider = PROVIDER_VOXEET;

        $.ajax(videoSwitcherSpan.attr('href'), {
            cache: false,
            data: {
                provider: (window.classroom.provider == 'tokbox') ? 'voxeet' : 'tokbox'
            }
        });

        document.location.reload(false);
    }
})(jQuery);
