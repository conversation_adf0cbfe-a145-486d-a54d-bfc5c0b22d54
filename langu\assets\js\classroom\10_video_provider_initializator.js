;
(function () {
    function checkForProvider() {
        if (!window.provider) {
            setTimeout(checkForProvider, 100);
            
            return;
        }

        if (window.provider && window.provider.type == 'tokbox') {
            $('#provider-switch-button').text('Switch to primary video player');

            console.log('Calling Tokbox');
            window.initializeTokbox();
        } else {
            $('#provider-switch-button').text('Switch to backup video player');

            console.log('Calling Voxeet');
            window.initializeVoxeet();
        }
    }

    checkForProvider();
})();
