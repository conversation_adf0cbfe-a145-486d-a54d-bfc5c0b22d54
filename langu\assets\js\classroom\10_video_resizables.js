;
(function() {
        console.log('resize video');

    $("#video-window").resizable(
        { aspectRatio: true, autoHide: true}
        );
    $("#video-window-shared").resizable({ aspectRatio: true, autoHide: true});

    $('#video-window-shared').on('hover', () => {
        if (!$('#video-window-shared > video').prop('controls')) {
            $('#video-window-shared > video').prop('controls', true);
        }
    });

    $("#video-window").hover(function () {
        $("#video-window-buttons").animate({
            opacity: "1"
        }, {
            queue: false
        });
    }, function () {
        $("#video-window-buttons").animate({
            opacity: "0"
        }, {
            queue: false
        });
    });
})();
