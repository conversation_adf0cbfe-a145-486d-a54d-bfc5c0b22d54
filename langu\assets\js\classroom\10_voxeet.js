;
(function() {
    window.initializeVoxeet = () => {
        const settings = {
            isMuted: false,
            isVideoEnabled: true,
            isScreenShareEnabled: false,
            isFullscreenEnabled: false,
        };

        VoxeetSDK.conference.on('streamAdded', (participant, stream) => {
            if (stream.type === 'ScreenShare') return addScreenShareNode(stream);

            let videoContainer = $((participant.info && participant.info.name == classroom.voxeet.userId) ?
            '.local-stream > .stream-container' :
            '.remote-stream > .stream-container');

            let videoNode = $('video-' + participant.id);

            if(!videoNode.length) {
                videoNode = document.createElement('video');

                videoNode.setAttribute('id', 'video-' + participant.id);
                videoNode.setAttribute('height', 240);
                videoNode.setAttribute('width', 320);

                videoContainer.append(videoNode);

                videoNode.autoplay = 'autoplay';
                videoNode.muted = true;
            }

            navigator.attachMediaStream(videoNode, stream);
            $('div.remote-stream').removeClass('hidden');
        });

        VoxeetSDK.conference.on('streamRemoved', (participant, stream) => {
            if (stream.type === 'ScreenShare') return removeScreenShareNode(participant);

            $('div.remote-stream video').remove();
            $('div.remote-stream').addClass('hidden');
        });

        const initializeVoxeet = async (videoEnabled) => {
            console.log('Voxeet initialization started');

            if(navigator.webkitGetUserMedia === null) { 
                $('#video-span-no-camera').css('display', 'inline-block');
            } else {
                 $('#video-span-no-camera').css('display', 'none');
            }
            
            try {
                await VoxeetSDK.initialize(classroom.voxeet.consumer_key, classroom.voxeet.consumer_secret_key);
                await VoxeetSDK.session.open({ name: classroom.voxeet.userId });

                VoxeetSDK.conference.create({ alias: ("classroom_" + getParameterFromURL("roomid")) })
                    .then((conference) => {
                        VoxeetSDK.conference.join(conference, { video: videoEnabled, constraints: {audio: true, video: videoEnabled} })
                            .then((info) => {
                                $('.stream-controls.local').removeClass('hidden');
                            })
                            .catch((e) => console.error(e));
                    })
                    .catch((e) => console.log('Something wrong happened : ' + e));
            } catch (e) {
                alert('Something went wrong : ' + e);
            }
        }

        initializeVoxeet(true);

        const controlPanel = function (e) {
            var self = $(this);
            var action = self.data('action');

            switch (action) {
                case 'audio-toggle':
                    controlActionAudioToggle.call(this);
                    break;
                case 'video-toggle':
                    controlActionVideoToggle.call(this);
                    break;
                case 'screenshare-toggle':
                    controlScreenshareToggle.call(this);
                    break;
                case 'fullscreen-toggle':
                    controlFullscreenToggle.call(this);
                    break;
            }

            return;
        };

        $('#video-window-buttons').on('click', 'button', controlPanel);

        const controlActionVideoToggle = function () {
            let self = $(this);
             $("#play-pause").toggleClass("active");
            settings.isVideoEnabled = !settings.isVideoEnabled;
            if(!settings.isVideoEnabled) {
                $('#video-span-no-camera').css('display', 'inline-block');
            } else {
                 $('#video-span-no-camera').css('display', 'none');
            }

            let icon = self.find("i");
            if (settings.isVideoEnabled) {
                VoxeetSDK.conference.startVideo(VoxeetSDK.session.participant, {});

                $('div.local-stream').removeClass('hidden');
                icon.text('videocam');
            } else {
                VoxeetSDK.conference.leave();
                initializeVoxeet(false);

                $('div.local-stream').addClass('hidden');
                icon.text('videocam_off');
            }
        };

        const controlActionAudioToggle = function () {
            let self = $(this);
             $("#mute").toggleClass("active");
            
            settings.isMuted = !settings.isMuted;
            VoxeetSDK.conference.mute(VoxeetSDK.session.participant, settings.isMuted);

            let icon = self.find("i");
            icon.text(settings.isMuted ? 'mic_off' : 'mic');
        };

        const controlScreenshareToggle = function(){
            var self = $(this);

            if (settings.isScreenShareEnabled) {
                VoxeetSDK.conference.stopScreenShare();
            } else {
                VoxeetSDK.conference.startScreenShare();
            }

            settings.isScreenShareEnabled = !settings.isScreenShareEnabled;

            let icon = self.find("i");
            icon.text(settings.isScreenShareEnabled ? 'stop_screen_sharem' : 'screen_share');

            $('[data-action="fullscreen"]').removeClass('hidden');
            $('video#screenshare').remove();
        };

        const controlFullscreenToggle = () => {
            settings.isFullscreenEnabled = !settings.isFullscreenEnabled;

            if (settings.isFullscreenEnabled) {
                let container = document.getElementById('video-window');
                requestFullScreen(container);
            } else {
                screenfull.exit();
            }
        };

        const addScreenShareNode = (stream) => {
            let screenShareIcon = $('button#screen-share > i');
            if (screenShareIcon.text() === 'screen_share') {
                $('button#screen-share').prop('disabled', true);
            }

            const screenShareContainer = $('#video-window-shared');
            let screenShareNode = document.getElementById('screenshare');

            if (screenShareNode) return;

            screenShareNode = document.createElement('video');
            screenShareNode.setAttribute('id', 'screenshare');
            screenShareNode.autoplay = 'autoplay';
            screenShareNode.controls = true;
            navigator.attachMediaStream(screenShareNode, stream);
            screenShareContainer.append(screenShareNode);

            let videoStreamingBlock = document.getElementById('video-window');
            $('#video-share-catch').css('left', videoStreamingBlock.style.left.slice(0, -2) + 'px');
            $('#video-share-catch').css('top', parseFloat(videoStreamingBlock.style.top.slice(0, -2)) + 400 + 'px');

            $('#video-share-catch').removeClass('hidden');
            $('#video-window-shared').removeClass('hidden');
        }

        const removeScreenShareNode = (participant) => {
            let screenShareIcon = $('button#screen-share > i');
            if (screenShareIcon.text() === 'screen_share') {
                $('button#screen-share').prop('disabled', false);
            }

            let videoNode = $('#video-window-shared > video');

            if (videoNode.length) {
                videoNode.remove();
            }

            $('#video-share-catch').addClass('hidden');
            $('#video-window-shared').addClass('hidden');
        }

        window.onbeforeunload = function(){
            VoxeetSDK.conference.stopScreenShare();
            VoxeetSDK.conference.leave();
        }
    }
})();
