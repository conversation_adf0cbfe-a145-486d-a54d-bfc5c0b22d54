;(function($) {

  $.fn.extend({
    getFocusPosition: function() {
      this[0].focus();
      var selection = window.getSelection(),
        focusNode = selection.focusNode,
        focusOffset = selection.focusOffset;
      while (focusNode.offsetTop === undefined) {
        focusNode = focusNode.parentNode;
      }
      return {
        offsetTop: focusNode.offsetTop,
        offsetLeft: focusNode.offsetLeft,
        focusOffset: focusOffset,
      }
    },
    setFocusPosition: function(position) {
      this[0].focus();
      var el = this[0],
        addRange = function(node, pos) {
          var range = document.createRange(),
            selection = window.getSelection();
          range.setStart(node, pos);
          range.collapse(true);
          selection.removeAllRanges();
          selection.addRange(range);
        };

      el.childNodes.forEach(function(node) {
        if (node.offsetTop !== position.offsetTop) {
          return;
        }
        if (node.offsetLeft === position.offsetLeft) {
          try {
            addRange(
              position.focusOffset ? node.firstChild : node,
              position.focusOffset
            );
          } catch (e) {
            addRange(node, 0);
          }
          return;
        }
      });

    },
  });


  var $textArea = $('#summernote'),
    lessonId = classroom.lesson.id,
    foreColor = classroom.user.type === 'student' ? '#FF9C00' : '#000000',
    saveUrl = classroom.lesson.saveUrl,
    uploadUrl = classroom.lesson.uploadUrl,
    text = classroom.lesson.text,
    pusherKey = classroom.pusherKey,
    event = 'client-whiteboard-' + lessonId + ':updated',
    pusher = new Pusher(pusherKey, {
      cluster: 'eu',
      encrypted: true,
      authEndpoint: classroom.lesson.authUrl,
      authTransport: 'ajax'
    }),
    channel = pusher.subscribe('private-whiteboard-' + lessonId);


  channel.bind(event, function(data) {
    var $editable = $('.note-editable');
    if (text === data.text) {
      return;
    }
    text = data.text;
    var pos = $editable.getFocusPosition();
    $textArea.summernote('code', text);
    $editable.setFocusPosition(pos);
  });

  $textArea.summernote({
    focus: true,
    airMode: false,
    toolbar: [
      ['style', ['bold', 'underline']],
      ['font', ['strikethrough']],
      ['color', ['color']],
      ['insert', ['link', 'video', 'hr', 'picture', 'table']],
      ['para', ['ul', 'ol', 'paragraph']]
    ],
    popover: {
      air: [
        ['style', ['bold', 'underline']],
        ['font', ['strikethrough']],
        ['color', ['color']],
        ['insert', ['link', 'video', 'hr', 'picture']],
        ['table', ['table']],
        ['para', ['ul', 'ol', 'paragraph']]
      ],
      image: [
        ['float', ['floatLeft', 'floatRight', 'floatNone']],
        ['remove', ['removeMedia']]
      ],
      link: [
        ['link', ['linkDialogShow', 'unlink']]
      ]
    },
    dialogsFade: true,
    dialogsInBody: true,
    callbacks: {
      onInit: function() {
        var $progressBar = $('#summernote-pb');
        $progressBar.remove();
        $textArea.summernote('code', text);
        $textArea.summernote('foreColor', foreColor);
      },
      onChange: function(newtext) {
        if (newtext === text) {
          return;
        }
        text = newtext;
        $.ajax({
          async: true,
          cache: false,
          data: {
            text: newtext
          },
          type: 'POST',
          url: saveUrl,
          success: function() {
            channel.trigger(event, {text: newtext})
          },
          dataType: 'text'
        });
      },
      onImageUpload: function(files) {
        var data = new FormData();
        data.append('file', files[0]);
        $.ajax({
          data: data,
          type: 'POST',
          url: uploadUrl,
          cache: false,
          contentType: false,
          processData: false,
          success: function(answer) {
            $textArea.summernote('insertImage', answer.payload.url);
          }
        });
      },
    }
  });


})(jQuery);