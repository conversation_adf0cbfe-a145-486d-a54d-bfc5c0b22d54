export default class UpdateBuilder {
    // TYPES
    static TEMPORARY = 'temporary';
    static PERMANENT = 'permanent';

    // EVENT NAMES
    static TEXTBOX_MOUSEOVER = 'textbox_mouseover';
    static TEXTBOX_MOUSELEAVE = 'textbox_mouseleave';
    static TEXTBOX_CHANGED = 'textbox_changed';

    static CURSOR_HIDE = 'cursor_hide';
    static CURSOR_SHOW = 'cursor_show';
    static RIPPLE = 'ripple';

    static TOOLBAR_MOUSEOVER = 'toolbar_mouseover';
    static TOOLBAR_MOUSELEAVE = 'toolbar_mouseleave';

    static MOUSE_MOVE = 'mouse_move';

    // VUE EVENTS
    static VUE_COMPONENT_UPDATE = 'vue_component_update';

    _type;
    _name;
    _id;
    _updateData;

    type(type) {
        if (!type.toLowerCase() === UpdateBuilder.TEMPORARY || !type.toLowerCase() === UpdateBuilder.PERMANENT) {
            throw new Error('Unacceptable update type given');
        }

        this._type = type;
        return this;
    }

    name(name) {
        this._name = name;
        return this;
    }

    componentId(id) {
        this._id = id;
        return this;
    }

    updateData(data) {
        this._updateData = data;
        return this;
    }

    validate() {
        if (!window.ws.socket) {
            throw new Error('Socket instance is not initialized');
        }

        if (!window.transmitter) {
            throw new Error('Transmitter instance is not initialized');
        }

        return this;
    }

    send() {
        window.transmitter.dispatch(this.validate()).then(() => {
        });

        return this;
    }

    /**
     * @typedef {Object} Update
     * @property {Object} data
     * @property {number} id - component ID
     * @property {number} classroom_id - current classroom ID
     * @property {string} name - event name
     * @property {string} role - current user role
     * @property {number} user_id - current user ID
     */
    wrap() {
        return Object.assign(
            {
                data: this._updateData
            },
            {
                id: this._id,
                classroom_id: getParameterFromURL("roomid"),
                name: this._name,
                role: window.langu_role,
                user_id: window.classroom.voxeet.userId
            });
    }
}

// @TODO: Add singleton pattern here
class Transmitter {
    /**
     * @param update {UpdateBuilder}
     * @returns {Promise<void>}
     */
    async dispatch(update) {
        window.ws.socket.emit(`server_classroom_${update._type}_update`, JSON.stringify(update.wrap()));
    }
}

window.transmitter = new Transmitter();
Object.freeze(window.transmitter);

// @TODO: Add singleton pattern here
class Receiver {
    /**
     * @param update {Update}
     * @returns {Promise<void>}
     */
    addUpdate(update) {
        return this.dispatch(update);
    }

    /**
     * @param update {Update}
     * @returns {Promise<void>}
     *
     * @TODO: Strategy pattern here?
     * @TODO: Generate class from update JSON to know what attributes it has
     */
    async dispatch(update) {
        switch (update.name) {
            case UpdateBuilder.VUE_COMPONENT_UPDATE:
                // @TODO: Add const import and support on gulp side or replace compile with webpack
                dispatch('UPDATE_COMPONENT_DATA', null, update.id, update.data.path, update.data.target)
                    .then(() => {});
                break;
            case UpdateBuilder.TEXTBOX_MOUSEOVER:
                $('.note-editor').css({outline: `2px solid ${getHoverColor(true)}`});
                break;

            case UpdateBuilder.TEXTBOX_MOUSELEAVE:
                $('.note-editor').css({outline: 'none'});
                break;

            case UpdateBuilder.TOOLBAR_MOUSEOVER:
                $('ul.toolbar-buttons').css({outline: `2px solid ${getHoverColor(true)}`});
                break;

            case UpdateBuilder.TOOLBAR_MOUSELEAVE:
                $('ul.toolbar-buttons').css({outline: 'none'});
                break;

            case UpdateBuilder.CURSOR_HIDE:
                $('div#other_cursor').hide();
                break;

            case UpdateBuilder.CURSOR_SHOW:
                $('div#other_cursor').show();
                break;

            case UpdateBuilder.RIPPLE:
                window.othercursor.ripples.push(new Ripple(update.data.loc.x, update.data.loc.y, update.data.color));
                break;

            /**
             * @TODO: GET RID OF LOOPS (UPD. EVEN ASYNC!)
             * @TODO: Handle type of returned object.
             */
            case UpdateBuilder.TEXTBOX_CHANGED:
                (await findAssetByType(window.cursor.assets, 'textbox')).aggregateUpdate(update.data.text);

                break;

            default:
                throw new Error('Unhandled event name received');
        }
    }
}

window.receiver = new Receiver();
Object.freeze(window.receiver);