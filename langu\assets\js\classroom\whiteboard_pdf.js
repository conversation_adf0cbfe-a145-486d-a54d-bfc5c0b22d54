var next_image = null;

function PdfItem(file, x, y, file_name){
    this.loaded = false;
    this.uploaded = false;
    this.sent = false; // For reconciliation through the websocket
    this.scroll = pdf_scroll;
    this.hittest = PdfItemHitTest;
    this.bodyhittest = ImageItemBodyHitTest;
    this.highlight_dragbar = false;
    this.scaleImage = ImageItemScale;
    // this.hittestresize = ImageItemHitTestResize;
    this.clicked = pdfitem_clicked;
    this.file = file;
    this.imageConverted = false;
    this.resized = false;

    this.server_file_name = "";
    this.complete_upload = function(name){
        this.server_file_name = name;
        this.uploaded = true;
        if(next_image.loaded){
            // If we have finished loading the image and
            // uploading then set as next asset
            cursor.assets.push(next_image);
            next_image = null;
        }
    }
    this.img = new Image();

    this.size = new Location(x,y);

    this.type = "pdfitem";
    this.deleted = false;
    this.moved = false;
    this.id = uuidv4().replace(/-/g, ""); // For tracking movements of items - ids will match

    this.scale_index = 3;

    this.assets_deleted = false;
    this.assets = []; // This is so that we can have lines tied to the item
    this.assets_dirty = false;
    this.add_asset = PdfAddAsset;


    this.get_lowest_point = function(){
        // Location and size of y
        // TODO: What about buttons or extra stuff?
        return this.loc.y + this.size.y;
    }

    if (FileReader && file) {
        var fr = new FileReader();

        fr.onload = function () {
            if (next_image !== null) {
                next_image.img.src = fr.result;
                next_image.loaded = true;

                if(next_image.uploaded){
                    // If we have finished loading the image and
                    // uploading then set as next asset
                    cursor.assets.push(next_image);
                    next_image = null;
                }
                console.log('weszłotu');
            }
            // TODO: Upload to the server so it can share with the other account

            // document.getElementById(outImage).src = fr.result;
        }

        fr.readAsDataURL(file);
    } else {
        // fallback -- perhaps submit the input to an iframe and temporarily store
        // them on the server until the user's session ends.

    }

    this.server_file_name = file_name;
    this.img.src = file_name;

    this.img.onload = function(){
        // Not sure how to find the item in the list of assets to confirm
    }
    this.loaded = true;

    this.offset = 0;
    this.loc = new Location(x, y);

    // Make sure we are on the visible canvas
    this.filename = (file) ? file.name : file_name;
    this.update = PdfItemUpdate;
    this.draw = PdfItemDraw;

    this.hittest = PdfitemHitTest;
    this.hittestresize = PdfItemHitTestResize;

    this.resize = PdfItemResize;
    this.correctionsEnabled = true;
    // this.start_resize = ImageItemStartResize;
    if(this.moved ) {
        $('#catch-pdf').css({left: x, top: y});
    }else {
        $('#catch-pdf').css({left: '10%', top: 100});
    }

}

function PdfItemResize(diff_x, diff_y) {
    var width = this.size.x;
    var height = this.size.y;

    var newWidth = this.size.x += diff_x;

    var ratio = (width / height);
    var newHeight = (newWidth / ratio);
    newWidth = (newHeight * ratio);

    this.size.x = newWidth;
    this.size.y = newHeight;
    this.correctionsEnabled = false;
}

function pdfitem_clicked(_x, _y, offset) {
    if (_y >= this.loc.y - 20 && _y < this.loc.y && _x >= this.loc.x + (this.size.x * 0.8 - 17.5) && _x < this.loc.x + (this.size.x * 0.8)) {
        this.deleted = true;        
    }

    if (_y >= this.loc.y - 20 && _y < this.loc.y + (this.size.y * 0.8) && _x >= this.loc.x && _x < this.loc.x + (this.size.x * 0.8)) {
        return true;
    }

    return false;
}

function pdf_scroll() {
}

function PdfitemHitTest(_x, _y){

}

function PdfItemDraw(ctx, offset) { 
    var old_style = ctx.strokeStyle;
    var old_fill = ctx.fillStyle;

    if(this.loaded){
        var page = {
            width:$(window).width(),
            height:$(window).height()
        };

        if (!this.imageConverted) {
            var img = new Image();
            img.src = this.img.src;

            this.img = img;
            this.imageConverted = true;

            this.size.x = this.img.width * 0.8;
            this.size.y = this.img.height * 0.8;
        }

        if (this.size.x == 0 || this.size.y == 0) {
            this.size.x = this.img.width;
            this.size.y = this.img.height;
        }

        if (this.correctionsEnabled && this.size.x > page.width/2) {
            let newWidth = this.size.x /= 2;

            let ratio = (this.size.x / this.size.y);
            let newHeight = (newWidth * ratio);

            this.size.x = newWidth;
            this.size.y = newHeight;
        }

        var sc = scale[this.scale_index];
        var sc_x = Math.round(sc * this.size.x);
        var sc_y = Math.round(sc * this.size.y);

        if(this.highlight_dragbar === false){
            ctx.fillStyle = "#222222";
        }else{
            ctx.fillStyle = "#444444";
        }

        var oc = document.createElement('canvas'),
            octx = oc.getContext('2d');

        oc.width = this.size.x * 0.8;
        oc.height = this.size.y * 0.8;

        ctx.mozImageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";
        ctx.webkitImageSmoothingEnabled = true;
        ctx.msImageSmoothingEnabled = true;
        ctx.imageSmoothingEnabled = true;

        ctx.drawImage(this.img, this.loc.x, this.loc.y - offset, oc.width, oc.height);

        // Dragbar Top
        ctx.beginPath();
        var tb_width = oc.width;
        ctx.fillRect(this.loc.x, this.loc.y - 20 - offset, tb_width, 20);
        ctx.stroke();
        ctx.closePath();

        ctx.font = "14px verdana";
        ctx.fillStyle = "white";
        // ctx.fillText(this.filename, this.loc.x + 5 , this.loc.y -5 - offset, this.size.x*0.4);
        ctx.fillText('X', this.loc.x + (tb_width - 17.5), this.loc.y - 5 - offset, this.size.x * 0.4);

        ctx.linewidth = 5;
        ctx.strokeStyle = "#222222";

        sc_x = oc.width;
        sc_y = oc.height;

        // Outline of image
        ctx.beginPath();
        ctx.moveTo(this.loc.x, this.loc.y - offset);
        ctx.lineTo(this.loc.x+sc_x, this.loc.y - offset);
        ctx.lineTo(this.loc.x+sc_x, this.loc.y+sc_y - offset);
        ctx.lineTo(this.loc.x,this.loc.y+sc_y - offset);
        ctx.lineTo(this.loc.x,this.loc.y - offset);
        ctx.stroke();
        ctx.closePath();

        // Draw any assets that are attached to the image
        for(var i=0;!cursor.moving && i<this.assets.length;i++){
            var ass = this.assets[i];
            ass.draw(ctx, offset, this);
        }

        // Resize pips - bottom right hand corner
        ctx.strokeStyle = '#222222';
        ctx.beginPath();
        ctx.moveTo(this.loc.x+sc_x - 20, this.loc.y+sc_y - offset - 2);
        ctx.lineTo(this.loc.x+sc_x - 2, this.loc.y+sc_y- offset - 20);
        ctx.moveTo(this.loc.x+sc_x - 15, this.loc.y+sc_y - offset - 2);
        ctx.lineTo(this.loc.x+sc_x - 2, this.loc.y+sc_y - offset - 15);
        ctx.moveTo(this.loc.x+sc_x - 10, this.loc.y+sc_y - offset - 2);
        ctx.lineTo(this.loc.x+sc_x - 2, this.loc.y+sc_y - offset - 10);
        ctx.moveTo(this.loc.x+sc_x - 5, this.loc.y+sc_y - offset - 2);
        ctx.lineTo(this.loc.x+sc_x - 2, this.loc.y+sc_y - offset - 5);
        // ctx.moveTo(this.l)
        ctx.stroke();
        ctx.closePath();
    }

    ctx.strokeStyle = old_style;
    ctx.fillStyle = old_fill;


}

function PdfItemHitTestResize(_x, _y, offset) {
    if (_x >= this.loc.x + (this.size.x * 0.8) - 20 && _x < this.loc.x + (this.size.x * 0.8) && _y >= this.loc.y + (this.size.y * 0.8) - 20 && _y < this.loc.y + (this.size.y * 0.8)) {
        return true;
    }

    return false;
}

function PdfItemUpdate() {}

function PdfItemHitTest(_x, _y){
    /*    this.moved = true;

        var offsets = $('#catch-pdf').offset();
        var top = offsets.top;
        var left = offsets.left;
        this.is_dirty = true;
        this.loc.x = left;
        this.loc.y = top;
        var offsets = $('#catch-pdf').offset();
        var top = offsets.top;
        var left = offsets.left;


        this.loc = new Location(left, top);
        this.loc.x = left;
        this.loc.y = top;
        var sc = scale[3];
        var sc_x = Math.round(sc * this.size.x);
        var sc_y = Math.round(sc * this.size.y);

        // Check if we have hit the drag bar
        if(_x>this.loc.x && _x < this.loc.x+sc_x)
            if(_y>this.loc.y -20 && _y < this.loc.y)
                return true;*/
}

function PdfAddAsset(asset){
    if(asset.type != "line"){
        console.log("ERROR: We only handle lines for textboxes");
        return;
    }
    for(var i=0;i<asset.points.length;i++){
        asset.points[i].x -= this.loc.x;
        asset.points[i].y -= this.loc.y - this.offset; // The offset is to make sure we respect the scroll amount
    }
    /*  asset.loc.x -= this.loc.x;
      asset.loc.y -= this.loc.y;*/
    this.assets.push(asset);
    this.assets_dirty = true;
}
