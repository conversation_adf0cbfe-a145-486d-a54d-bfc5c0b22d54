function nl2br(str, is_xhtml) {
    var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';
    return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
}

function br2nl(str) {
    return str.replace(/<br>|<br \/><br\/>/g, "\r");
}

;
(function ($, window, document, autosize, Translator, FLASHES, undefined) {
    var Messages = {
        init: function () {
            var self = this;

            this.classNames = {
                outerContainer: '.columns-wrapper',
                newMessageForm: '.new-message-form',
                newFileMessageForm: '.new-file-message-form',
                fileUploadButton: '.new-file-message-upload-button',
                messagesMoreButton: '.messages-list-more-button',
                newMessageTextarea: '.new-message-box-input',
                threadsMoreButton: '.threads-list-more-button',
                threadsList: '.threads-list',
                threadSwitch: '.message-info a',
                messagesList: '.messages-list',
                messagePrototype: '.prototype',
                thread: '.threads-list-item',
                currentThread: '.threads-list-item.current',
                stickyThread: '.threads-list-item.sticky',
                sidebar: '.sidebar',
                conversation: '.conversation',
                timer: '.timer-time',
                onlineStatus: '.online-status'
            };
            
            linkify.options.defaults.defaultProtocol = 'https';
            linkify.options.defaults.className = 'message-link';

            var options = {};
            options.markup = {
                spinner: '<span class="glyphicon glyphicon-repeat spin"></span>',
                conversationSpinner: '<div class="activity-indicator"><div></div></div>',
                timeFormat: 'H:i',
            };

            this.state = {
                threadsCache: {},
                onlineStatusSubscriptions: {},
                threadLoadingRequest: null,
                hasThreads: false
            };

            this.options = options;
            var $document = $(document);

            var $outerContainer = $document.find(this.classNames.outerContainer);
            var $sidebar = $outerContainer.find(this.classNames.sidebar);

            var references = {};
            references.global = {
                outerContainer: $outerContainer,
                sidebar: $sidebar
            };

            this.references = references;

            this.updateConversationReferences();
            this.updateThreadsReferences();
            this.linkifyConversation();

            var fileInput = this.references.conversation.newFileMessageForm.find(this.classNames.fileUploadButton);
            var maxSize = parseInt(fileInput.data('max-size'));
            var mimeTypes = fileInput.data('allowed-mimetypes').split(',');
            this.options.fileUpload = {
                maxSize: maxSize,
                mimeTypes: mimeTypes
            };

            var prototypeMarkup = this.references.conversation.messagesList.children(this.classNames.messagePrototype).prop('outerHTML');
            this.options.markup.messagePrototype = prototypeMarkup;
            
            this.socket = window.ws.socket;
            this.bind();
        },
        linkifyConversation: function () {
            this.references.conversation.messagesList.linkify();
        },
        updateConversationReferences: function () {
            var self = this;

            var $outerContainer = this.references.global.outerContainer;
            var $conversation = $outerContainer.find(this.classNames.conversation);
            var $newMessageForm = $conversation.find(this.classNames.newMessageForm);
            var $messagesList = $conversation.find(this.classNames.messagesList);
            var $newMessageInput = $newMessageForm.find(this.classNames.newMessageTextarea);
            var $timerOutput = $conversation.find(this.classNames.timer);
            var $onlineStatus = $conversation.find(this.classNames.onlineStatus);
            var $newFileMessageForm = $conversation.find(this.classNames.newFileMessageForm);
            
            var conversation = {
                conversation: $conversation,
                newMessageForm: $newMessageForm,
                newFileMessageForm: $newFileMessageForm,
                messagesList: $messagesList,
                newMessageInput: $newMessageInput,
                timerOutput: $timerOutput,
                onlineStatus: $onlineStatus
            };
            
            this.references.conversation = conversation;
        },
        updateThreadsReferences: function () {
            var self = this;
            var $sidebar = this.references.global.sidebar;

            var $threadsList = $sidebar.find(this.classNames.threadsList);
            var $stickyThread = $threadsList.children(this.classNames.stickyThread);
            var $currentThread = $threadsList.children(this.classNames.currentThread);

            this.state.hasThreads = $threadsList.length ? true : false;
            this.references.threads = {
                threadsList: $threadsList,
                stickyThread: $stickyThread,
                currentThread: $currentThread
            };
        },
        tickTimer: function() {
            if(typeof this.references.conversation === 'undefined' || !this.references.conversation) {
                return;
            }
            
            var timerOutput = this.references.conversation.timerOutput;
            
            if(!timerOutput.length) {
                return;
            }
            
            var localDate = new Date();
            var localTimestamp = localDate.getTime();
            var localOffset = localDate.getTimezoneOffset() * 60 * 1000;
            var recipientOffset = parseInt(timerOutput.data('timezoneOffset')) * 1000;
                        
            var recipientTimestamp = localTimestamp + localOffset + recipientOffset;
            var recipientDate = new Date(recipientTimestamp);
            var hours = recipientDate.getHours();
            var minutes = recipientDate.getMinutes();
            
            var output = hours > 9 ? hours : '0' + hours;
            output = output + ':' + (minutes > 9 ? minutes : '0' + minutes);
             
            if(timerOutput.text() === output) {
                return;
            }
            
            timerOutput.text(output);
        },
        appendNewThread: function(list, thread, sticky) {
            sticky = sticky || null;
            
            if(null !== sticky && sticky.length > 0) {
                sticky.after(thread);
                return;
            }
            
            list.prepend(thread);
        },
        appendNewMessage: function(list, message) {
            message.linkify();
            list.prepend(message);
        },
        onStatusUpdate: function (data) {
            var userId = data.user;
            var newStatus = data.online;
            
            if(!newStatus || !userId) {
                return;
            }
            
            var threadsList = this.references.threads.threadsList;
            var targetThread = threadsList.find('[data-user="' + userId + '"]').first();
            if(targetThread.length === 0) {
                return;
            }
            
            var threadId = targetThread.data('thread');
            var isCurrent = targetThread.hasClass('current');

            if(!threadId) {
                return;
            }

            if(false === isCurrent) {
                if(this.state.threadsCache.hasOwnProperty(threadId)) {
                    var virtualConversation = $(this.state.threadsCache[threadId].data);
                    var conversationStatus = virtualConversation.find(this.classNames.onlineStatus);
                    this.updateConversationStatus(conversationStatus, newStatus);
                    this.state.threadsCache[threadId].data = $('<div/>').append(virtualConversation).html();
                }
            } else {               
                var conversationStatus = this.references.conversation.onlineStatus;
                this.updateConversationStatus(conversationStatus, newStatus);
            }
        },
        updateConversationStatus: function(statusNode, status) {
            switch(status) {
                case 'online':
                    statusNode.removeClass('offline idle').addClass('online');
                    break;
                case 'idle':
                    statusNode.removeClass('offline online').addClass('idle');
                    break;
                case 'offline':
                    statusNode.removeClass('online idle').addClass('offline');
                    break;
            }
        },
        onNewMessage: function (data) {
            var threadId = data.thread_info.id;
            var isNewThread = data.thread_info.is_new;
            var threadsList = this.references.threads.threadsList;
            var stickyThread = this.references.threads.stickyThread;            
            var newThread = $(data.fragments.thread);
            var targetThread = threadsList.find('[data-thread="' + threadId + '"]').first();
            
            if(isNewThread || targetThread.length === 0) {
                this.appendNewThread(threadsList, newThread, stickyThread);
                return;
            } 

            var isCurrent = targetThread.hasClass('current');
            var message = $(data.fragments.message);

            if(false === isCurrent) {
                if(this.state.threadsCache.hasOwnProperty(threadId)) {
                    var virtualConversation = $(this.state.threadsCache[threadId].data);
                    var messagesList = virtualConversation.find(this.classNames.messagesList);
                    this.appendNewMessage(messagesList, message);                    
                    this.state.threadsCache[threadId].data = $('<div/>').append(virtualConversation).html();
                    this.onlineStatusSubscribe(targetThread);
                }
                
                targetThread.replaceWith(newThread);
            } else {               
                var messagesList = this.references.conversation.messagesList;
                newThread.addClass('current');
                targetThread.replaceWith(newThread);

                this.appendNewMessage(messagesList, message);
                this.onlineStatusSubscribe(newThread);
            }
        },
        bind: function () {
            var threadsList = this.references.threads.threadsList;
            var outerContainer = this.references.global.outerContainer;

            outerContainer.on('submit', this.classNames.newMessageForm, this.sendMessageHandler.bind(this));
            outerContainer.on('submit', this.classNames.newFileMessageForm, this.sendFileMessageHandler.bind(this));
            outerContainer.on('change', this.classNames.fileUploadButton, this.fileMessageStateChanged.bind(this));
            outerContainer.on('keydown', this.classNames.newMessageTextarea, this.keyboardSubmit.bind(this));
            outerContainer.on('click', this.classNames.messagesMoreButton, this.moreMessages.bind(this));

            threadsList.on('click', this.classNames.threadSwitch, this.switchThread.bind(this));
            threadsList.on('click', this.classNames.threadsMoreButton, this.moreThreads.bind(this));
        
            window.setInterval(this.tickTimer.bind(this), 999);
            
            this.socket.on('message:received', this.onNewMessage.bind(this));            
            this.socket.on('user:status:change', this.onStatusUpdate.bind(this));
            
            var currentThread = this.references.threads.currentThread;
            this.onlineStatusSubscribe(currentThread);
        },
        fileMessageStateChanged: function(e) {
            var input = e.target;
            
            if(!e.target.files || !e.target.files.length) {
                return;
            }
            
            this.references.conversation.newFileMessageForm.submit();
        },
        onlineStatusSubscribe: function(thread) {
            if(!thread) {
                return;
            }
            
            var userId = thread.data('user');
            if(!userId) {
                return;
            }
            
            if(this.state.onlineStatusSubscriptions.hasOwnProperty(userId)) {
                return;
            }
            
            this.socket.emit('user:status:sub', {users: [userId]});
            this.state.onlineStatusSubscriptions[userId] = true;
        },
        moreThreads: function(e) {
            e.preventDefault();
            var self = $(e.currentTarget);
            var item = self.parent();
            self.html(this.options.markup.spinner);
            
            var callDefinition = {
                url: self.attr('href'),
                method: 'GET',
                global: false    
            };
            
            var that = this;
            var successCallback = function(data, status, xhr) {
                that.appendThreads(item, data);
            };

            this.ajaxCall(callDefinition, successCallback, this.ajaxFailureCallback);
        },
        appendThreads: function(item, data) {
            var stickyThread = this.references.threads.stickyThread;
            
            var $threads = $(data);
            if (stickyThread.length > 0) {
                var stickyId = praseInt(stickyThread.data('thread'));
                var duplicates = $threads.filter('[data-thread="' + stickyId + '"]');
                
                if (duplicates.length) {
                    duplicates.remove();
                }
            }

            item.replaceWith($threads);            
        },
        moreMessages: function (e) {
            e.preventDefault();
            var self = $(e.currentTarget);
            var item = self.parent();
            self.html(this.options.markup.spinner);
            
            var callDefinition = {
                url: self.attr('href'),
                method: 'GET',
                global: false
            };
    
            var messagesList = this.references.conversation.messagesList;
            var that = this;
            var successCallback = function(data, status, xhr) {
                that.appendMessages(item, data);
                var pos = messagesList.position().top + messagesList.height();
                $('html, body').scrollTop(pos, 300);
            };

            this.ajaxCall(callDefinition, successCallback, this.ajaxFailureCallback);
        },
        appendMessages: function (item, data) {
            var $data = $(data);
            item.replaceWith($data);
            item.linkify();
        },
        stripTags: function (str) {
            return str.toString().replace(/<\/?[^>]+>/gi, '');
        },
        keyboardSubmit: function(e) {
            if (e.which === 13 && e.shiftKey)
            {
                e.preventDefault();

                var newMessageForm = this.references.conversation.newMessageForm;
                newMessageForm.submit();
            }   
        },
        sendFileMessageHandler: function (e) {
            e.preventDefault();
            var form = this.references.conversation.newFileMessageForm;
           
            var formData = new FormData(form[0]);
            var fileInput = form.find(this.classNames.fileUploadButton).get(0);
            var file = fileInput.files[0];
            
            if(file.size > this.options.fileUpload.maxSize) {
                form[0].reset();
                FLASHES.addFlash('error', 'messaging.new_file_message.file_too_big');
                return;
            }
            
            if(file.type !== '' && this.options.fileUpload.mimeTypes.indexOf(file.type) === -1) {
                form[0].reset();
                FLASHES.addFlash('error', 'messaging.new_file_message.file_type_not_allowed');
                return;
            }
            
            formData.append('file', file);
            
            var messageFragment = $(this.options.markup.messagePrototype);
            messageFragment.find('[data-property="message"]').addClass('content-file').html(nl2br(file.name));
            messageFragment.removeClass('hidden');
            
            var messagesList = this.references.conversation.messagesList;
            messagesList.prepend(messageFragment);

            var messageIntro = file.name;

            if (messageIntro.length > 50) {
                messageIntro = messageIntro.substr(0, 47) + '...';
            }

            var dateProperty = messageFragment.find('[data-property="date"]');
            var currentThread = this.references.threads.currentThread;
            var threadMessageProperty = currentThread.find('[data-property="message"]');
            
            var callDefinition = {
                url: form.attr('action'),
                method: 'POST',
                data: formData,
                dataType: 'json',
                contentType: false,
                processData: false,
                cache: false,
                global: false
            };
            var successCallback = function (data, status, xhr) {
                var partial = $(data.payload.partial);
                messageFragment.find('[data-property="message"]').html(partial);
                messageFragment.removeClass('sending');
                var date = data.payload.date;
                dateProperty.text(date);
                threadMessageProperty.text(messageIntro);
                form[0].reset();
            };
            var that = this;
            var failureCallback = function (xhr, status, error) {
                that.ajaxFailureCallback(xhr, status, error);
                messageFragment.remove();
                form[0].reset();
            };

            this.ajaxCall(callDefinition, successCallback, failureCallback);
        },
        sendMessageHandler: function (e) {
            e.preventDefault();
            var input = this.references.conversation.newMessageInput;
            var message = this.stripTags(input.val());

            if (message.length === 0) {
                return;
            }

            var form = this.references.conversation.newMessageForm;
            var data = form.serialize();
            input.val(null);

            var messageFragment = $(this.options.markup.messagePrototype);
            messageFragment.find('[data-property="message"]').html(nl2br(message));
            messageFragment.removeClass('hidden');

            var messagesList = this.references.conversation.messagesList;
            messagesList.prepend(messageFragment);

            var messageIntro = message;

            if (messageIntro.length > 50) {
                messageIntro = messageIntro.substr(0, 47) + '...';
            }

            messageFragment.linkify();
            var dateProperty = messageFragment.find('[data-property="date"]');
            var currentThread = this.references.threads.currentThread;
            var threadMessageProperty = currentThread.find('[data-property="message"]');

            var messageForm = this.references.conversation.newMessageForm;

            var callDefinition = {
                url: messageForm.attr('action'),
                method: 'POST',
                data: data,
                dataType: 'json',
                global: false
            };
            var successCallback = function (data, status, xhr) {
                messageFragment.removeClass('sending');
                var date = data.payload.date;
                dateProperty.text(date);
                threadMessageProperty.text(messageIntro);
            };
            var that = this;
            var failureCallback = function (xhr, status, error) {
                that.ajaxFailureCallback(xhr, status, error);
                messageFragment.remove();
            };

            this.ajaxCall(callDefinition, successCallback, failureCallback);
        },
        switchThread: function (e) {
            e.preventDefault();
            
            var sidebar = this.references.global.sidebar;
            sidebar.removeClass('in');
            var self = $(e.currentTarget);
            var currentThread = this.references.threads.currentThread;
            var newThread = self.closest(this.classNames.thread);
            
            var currentThreadId = currentThread.data('thread');
            var newThreadId = newThread.data('thread');

            var threadsCacheItem = null;
            
            if (currentThreadId === newThreadId) {
                return;
            }

            var conversation = this.references.conversation.conversation;
            var currentThreadContent = conversation.html();
            
            if (this.state.threadsCache.hasOwnProperty(newThreadId)) {
                threadsCacheItem = this.state.threadsCache[newThreadId];
            }

            newThread.addClass('current');
            currentThread.removeClass('current');
            
            if (null !== this.state.threadLoadingRequest) {
                this.state.threadLoadingRequest.abort();
                this.state.threadLoadingRequest = null;
            } else {
                this.state.threadsCache[currentThreadId] = {
                    id: currentThreadId,
                    data: currentThreadContent
                };
            }

            if (null !== threadsCacheItem) {
                conversation.html(threadsCacheItem.data);
                this.updateConversationReferences();
                this.updateThreadsReferences();
                this.tickTimer();
                var input = this.references.conversation.newMessageInput;
                autosize(input);
                autosize.update(input);                
                return;
            }

            var callDefinition = {
                url: self.attr('href'),
                method: 'GET',
                global: false
            };
            
            var that = this;
            var successCallback = function(data, status, xhr) {
                that.state.threadLoadingRequest = null;
                var $_conversation = $(data);
                conversation.replaceWith($_conversation);
                that.updateConversationReferences();
                that.linkifyConversation();
                that.tickTimer();
                
                var input = that.references.conversation.newMessageInput;
                autosize(input);
                autosize.update(input);                
                
                var messagesList = that.references.conversation.messagesList;
                var message = br2nl(messagesList.children('.messages-list-item').last().children('.content').html()).trim();
                
                var messageProperty = newThread.find('[data-property="message"]');
                
                if(message.length > 50) {
                    message = message.substr(0, 47) + '...';
                }
                
                messageProperty.text(message);
                that.updateConversationReferences();
                that.updateThreadsReferences();
                that.tickTimer();
                
                that.onlineStatusSubscribe(newThread);
            };

            conversation.html(this.options.markup.conversationSpinner);
            that.state.threadLoadingRequest = this.ajaxCall(callDefinition, successCallback, this.ajaxFailureCallback);
        },
        ajaxCall: function (options, successCallback, failureCallback) {
            var call = $.ajax(options);
            call.fail(failureCallback);
            call.done(successCallback);
            
            return call;
        },
        ajaxFailureCallback: function (xhr, status, error) {
            if (undefined !== xhr.responseJSON && null !== xhr.responseJSON) {
                var type = xhr.responseJSON.hasOwnProperty('payload') ? (xhr.responseJSON.payload.messageType || 'error') : 'error';
                var message = xhr.responseJSON.message;
                FLASHES.addFlash(type, message);
            }
        }
    };

    Messages.init();
})(jQuery, window, document, autosize, Translator, FLASHES);
