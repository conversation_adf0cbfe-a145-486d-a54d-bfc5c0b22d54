;
(function(window, undefined) {

  // isArray method
  if (typeof Array.isArray === 'undefined') {
    Array.isArray = function(obj) {
      return Object.prototype.toString.call(obj) === '[object Array]';
    };
  }

  // isObject method
  if (typeof Object.isObject === 'undefined') {
    Object.isObject = function(obj) {
      return (!Array.isArray(obj) && obj === Object(obj));
    };
  }

  // isString method

  if (typeof String.isString === 'undefined') {
    String.isString = function(obj) {
      return Object.prototype.toString.call(obj) === '[object String]';
    };
  }

  window.rt = {};

  rt.RealtimeWrapper = function(clientId, options) {
    this.init(clientId, options);
  };

  rt.RealtimeWrapper.prototype = {
    clientId: null,
    mimeType: 'application/vnd.google-apps.drive-sdk',
    accessToken: null,
    init: function(clientId, options) {
      if (clientId === undefined) {
        throw new Error('No Client ID specified');
      }

      if (!String.isString(clientId)) {
        throw new Error('Given Client ID is not a string');
      }

      this.clientId = clientId;
      this.initOptions(options);
      this.bind();
    },
    start: function(startCallback) {
      var that = this;
      window.gapi.load('auth2,auth:client,drive-realtime,drive-share', {
        callback: function() {
          window.gapi.auth.setToken(that.accessToken);
          window.gapi.auth.token = that.accessToken;

          that.tokenRefresh(startCallback);
        }
      });
    },
    initOptions: function(options) {
      if (options) {
        if (options.hasOwnProperty('accessToken')) {
          if (!Object.isObject(options.accessToken)) {
            throw new Error(
              'Access token expects to be an Associative Array (Object)');
          }

          this.accessToken = options.accessToken;
        }

        if (options.hasOwnProperty('mimeType')) {
          if (!String.isString(options.mimeType)) {
            throw new Error('Mime type expects to be a string');
          }

          this.mimeType = options.mimeType;
        }

        if (options.hasOwnProperty('userId')) {
          if (!String.isString(options.userId)) {
            throw new Error('Option userId expects to be a string');
          }

          this.userId = options.userId;
        } else {
          throw new Error('Required option userId has not been set');
        }

        if (options.hasOwnProperty('scope')) {
          if (String.isString(options.scope)) {
            var scopeArray = options.scope.split(' ');
            this.scope = scopeArray;
          } else if (Array.isArray(options.scope)) {
            this.scope = options.scope;
          } else {
            throw new Error('Option scope must be an array or a string');
          }
        } else {
          throw new Error('Required option scope has not been set');
        }
      }
    },
    bind: function() {
      this.onError = this.onError.bind(this);
    },
    getParam: function(urlParam) {
      var regExp = new RegExp(urlParam + '=(.*?)($|&)', 'g');
      var match = window.location.search.match(regExp);
      if (match && match.length) {
        match = match[0];
        return match.replace(urlParam + '=', '').replace('&', '');
      }

      return null;
    },
    onError: function(error) {
      var that = this;
      console.log('RealtimeAPI error:');
      switch (true) {
      case error.type == window.gapi.drive.realtime.ErrorType
        .TOKEN_REFRESH_REQUIRED:
        this.tokenRefresh(function() {
          console.log('Error, auth refreshed');
          var lastCall = that.lastCall;
          if (null !== lastCall) {
            this[lastCall.method].apply(this, lastCall.arguments);
            this.lastCall = null;
          }
        });
        break;
      case error.type == window.gapi.drive.realtime.ErrorType.CLIENT_ERROR:
        console.log('An Error happened: ' + error.message);
        break;
      case error.type == window.gapi.drive.realtime.ErrorType.NOT_FOUND:
        console.log(
          'The file was not found. It does not exist or you do not have '
          + 'read access to the file.');
        break;
      case error.type == window.gapi.drive.realtime.ErrorType.FORBIDDEN:
        console.log(
          'You do not have access to this file. Try having the owner share'
          + 'it with you from Google Drive.'
        );
      default:
        console.log(error);
        break;
      }
    },
    load: function(lessonId, documentId, onFileLoaded, initializeModel, onError) {
      this.lastCall = {
        method: 'load',
        arguments: [documentId, onFileLoaded, initializeModel]
      };

      window.gapi.drive.realtime.load(
        documentId,
        function(doc) {
          onFileLoaded(lessonId, doc);
        },
        function(doc) {
          initializeModel(lessonId, doc)
        },
        function () {
          onError(lessonId);
        }
      );
    },
    tokenRefresh: function(callback) {
      var auth_options = {
        authuser: -1,
        immediate: true,
        client_id: this.clientId,
        user_id: this.userId,
        scope: this.scope
      };

      window.gapi.auth.authorize(auth_options, function(result) {
        callback(result);
      });
    }
  };
})(window);