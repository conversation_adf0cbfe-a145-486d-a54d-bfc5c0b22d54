;(function($) {
  var options = {
    scope: migrateData.scope,
    userId: migrateData.googleEmail,
    accessToken: migrateData.accessToken
  };
  var collaborationStringIdentifier = 'demo_string';
  var realtimeClient = new window.rt.RealtimeWrapper(
    migrateData.clientId,
    options
  );

  var doneLessons = [];

  setInterval(function() {
    if (doneLessons.length === migrateData.lessons.length) {
      window.location.replace('/');
    }
  }, 1000);

  realtimeClient.start(start);

  function start() {
    migrateData.lessons.forEach(function(lesson) {
      realtimeClient.load(
        lesson.id,
        lesson.fileId,
        onFileLoaded,
        onFileInitialize,
        onError
      );
    });
  }

  function onFileInitialize(lessonId, model) {
    save(lessonId, '');
    return false;
  }

  function onFileLoaded(lessonId, doc) {
    try {
      var colStr = doc
          .getModel()
          .getRoot()
          .get(collaborationStringIdentifier),
        text = colStr ? colStr.getText() : '';
      return save(lessonId, text);
    } catch (error) {
      return save(lessonId, '');
    }
  }

  function onError(lessonId) {
    return save(lessonId, '');
  }

  function save(lessonId, text) {
    $.ajax({
      async: false,
      cache: false,
      data: {
        text: text
      },
      type: 'POST',
      url: '/lesson/' + lessonId + '/classroom/save',
      dataType: 'text',
      success: function(answer) {
        doneLessons.push(lessonId);
      },
      error: function(xhr, textStatus, errorThrown) {
        doneLessons.push(lessonId);
      }
    });
    return false;
  }
})(jQuery);