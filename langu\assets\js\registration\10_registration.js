;(function($) {

  var updateFormFields = function() {
    var self = $(this);
    var val = self.val();

    var studentFields = $('.registration-form [data-field="student"]');
    var teacherFields = $('.registration-form [data-field="teacher"]');

    if (val === '2') {
      teacherFields.removeClass('hidden');
      studentFields.addClass('hidden');
    } else {
      teacherFields.addClass('hidden');
      studentFields.removeClass('hidden');
    }
  };

  var selectTimezone = function(timezone) {
    $('#' + selectizeId)[0]
      .selectize
      .setValue(timezone);
  };

  $('.registration-form [data-field="user_type"]').on(
    'change',
    'input[type="radio"], input[type="checkbox"]',
    updateFormFields
  );

  $(document).on('click', '.timezone_country', function() {
    selectTimezone($(this).data('timezone'));
    $timezones.html('');
    $timezones.append(makeBtn($(this).data('timezone'), $(this).data('value')));
  });



  var getTimeZone = function() {
    return (new Date()).getTimezoneOffset() / 60 * -1;
  };

  var offset = getTimeZone();
  var countries = timezones[offset];
  var $timezones = $('#timezones');

  var makeCountries = function($timezones, countries) {
    $timezones.html('');
    $.each(countries, function(index, value) {
      $timezones.append(makeBtn(index, value));
    });
  };

  var makeBtn = function(index, value) {
    return '<div class="col-xs-12 col-ms-6 col-xl-4">'
      + '<button type="button" class="timezone__btn timezone_country"'
      + ' data-timezone="' + index + '" data-value="' + value + '">'
      + '<span>(GMT'
      + (+offset > 0 ? '+' : '')
      + offset
      + ':00) </span>'
      + value
      + '</button>'
      + '</div>';
  };

  makeCountries($timezones, countries);

  $('#resetTimezone').click(function() {
    makeCountries($timezones, countries);
  });

})(jQuery);