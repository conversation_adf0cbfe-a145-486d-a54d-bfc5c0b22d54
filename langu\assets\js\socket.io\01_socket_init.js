;(function () {
    window.ws = window.ws || {};
    window.ws.host = location.host + '/';
    window.ws.endpoint = '/websocket';
    window.ws.secure = true;

    console.log('halo')

    var ioSocket = io.connect(window.ws.host, {
        path: window.ws.endpoint,
        secure: window.ws.secure,
        reconnectionAttempts: 5,
    });

    ioSocket.on('reconnect_failed', function () {
        console.log('The socket.io can not connect.')
//        FLASHES.addFlash('warning', 'realtime connection could not be established. Some features may not be working properly.');
    });

    window.ws.socket = ioSocket;

    var teacherCallHandler = function (data) {
        var message = data.message;
        var modalContent = $('<div>' + message + '</div>');

        var audio = modalContent.find('audio');
        var modal = langu.modal.instance(modalContent, 'langu-modal-dialog langu-modal-dialog-short', {
            afterOpen: function () {
                audio[0].play();
            },
        });
        modal.open();
    };

    ioSocket.on('lesson:call', teacher<PERSON>all<PERSON>andler);

    ioSocket.on('user_joined_classroom', (message) => {
        console.log(message);
    })
})();
