;(function($, window, undefined){
    var lessonsList = $('.lessons-list');
    
    if(!lessonsList.length) {
        return;
    }
    
    var onLessonInitialized = function(data){
        var lesson = data.lesson;
        var url = data.url;
        $('.lessons-list-item[data-lesson="' + lesson + '"]').find('.lesson-enter').removeClass('disabled').attr('href', url).removeAttr('title');
    };

    
    var socket = window.ws.socket;
    socket.on('lesson:init', onLessonInitialized);
    
    var userLessonsMap = {};
    var lessons = lessonsList.find('.lessons-list-inner').children('li[data-user]');
    
    for(var i = 0; i < lessons.length; i++) {
        var $lesson = $(lessons[i]);
        var user = parseInt($lesson.data('user'));
        
        if(!user) {
            return;
        }
        
        if(!userLessonsMap.hasOwnProperty(user)) {
            userLessonsMap[user] = $();
        }
        
        userLessonsMap[user] = userLessonsMap[user].add($lesson);
    }
    
    if(!lessons.length) {
        return;
    }
    
    var users = Object.keys(userLessonsMap);
    
    socket.emit('user:status:sub', {users: users});
    
    socket.on('user:status:change', function(data){
        console.log('Status change', data);
        var user = data.user;
        var online = data.online;
        if(undefined === user || undefined === online) {
            return;
        }
        
        var lessons = userLessonsMap[user];
        if(!lessons.length) {
            return;
        }
        
        var statusIndicators = lessons.find('.online-status');
        
        switch(online) {
            case 'online':
                statusIndicators.removeClass('offline idle').addClass('online');
                break;
            case 'idle':
                statusIndicators.removeClass('offline online').addClass('idle');
                break;
            case 'offline':
                statusIndicators.removeClass('idle online').addClass('offline');
                break;
        }
//        if(online) {
//            statusIndicators.removeClass('offline');
//        } else {
//            statusIndicators.addClass('offline');
//        }
    });
})(jQuery, window);
