import Vue from "vue";
export function createImage(imageId, imageUrl, name, isCreated) {
    console.log(imageUrl, name);
    let imageWrap = document.createElement('div');
    imageWrap.setAttribute("id", `image-classroom${imageId}`);
    let imageArea = document.getElementsByClassName('middle-inner-classroom')[0];
    imageArea.appendChild(imageWrap); 
    function updateImg() {
    }

    function drawImg() {
    }
    function hittestResize() {
    }
    function hittest() {
    }
    
    new Vue({ 
        el: '#image-classroom'+imageId,
        template: `  
            <div v-if="activeImage" v-bind:id="this.getId" class="image-wrap-classroom active">
                <div :id="\`desc-\${this.assets.imageId}\`" class="image-classroom-description">
                    <span class="image-classroom-name">{{assets.imageName}}</span>
                    <span @click="closeImage" class="image-classroom-cross">&times;</span>
                </div>
                <img class="image-classroom-item-icon"  :id="\`image-classroom-icon-\${this.assets.imageId}\`" :src="assets.imageUrl" />
            </div>
        `,
        computed: {
            getId() {
                return 'image-classroom-item-'+this.imageId
            }
        },
        data: {
            activeImage: true,
            assets: {
                id: imageId,
                type: 'vue_imageitem',
                imageId: imageId,
                imageUrl: imageUrl,
                imageName: name,
                update: updateImg,
                draw: drawImg,
                hittestresize: hittestResize,
                hittest: hittest,
                position: {
                    top: '20%',
                    left: '40%'
                },
                size: {
                    width: '500',
                    height: '350'
                },
                isResize: false,
                isDrag: false,
                isCreated: false,
                isClose: false,
                isClosed: false,
            }
        },
        mounted() {
            this.assets.imageId = imageId
            this.assets.imageUrl = imageUrl
            this.assets.isCreated = isCreated
            // window.cursor.assets.push(this.assets);
            this.$nextTick(() => {
                if(window.role === 'teacher') {
                    $('.image-wrap-classroom').addClass('teacher');
                } else {
                    $('.image-wrap-classroom').addClass('student');
                }
                $('#'+this.getId).draggable({
                    drag: ( event, ui ) => {
                        this.assets.position.top = `${ui.position.top}px`
                        this.assets.position.left = `${ui.position.left}px`
                        this.assets.isDrag = true;
                        // window.cursor.assets.push(this.assets)
                    },
                    stop: ( event, ui ) => {
                        // ui.position.top, ui.position.left
                        this.assets.position.top = `${ui.position.top}px`
                        this.assets.position.left = `${ui.position.left}px`
                        this.assets.isDrag = true;
                        window.cursor.assets.push(this.assets)
                    }
                });
                $('#'+this.getId).resizable({
                    handles: "e, s, se",
                    aspectRatio: true,
                    create: function( event, ui ) {
                        $(".image-wrap-classroom.teacher .ui-resizable-e").css("cursor","url('/images/classroom/cursor-teacher-right.svg') 30 0, auto");
                        $(".image-wrap-classroom.student .ui-resizable-e").css("cursor","url('/images/classroom/cursor-student-right.svg') 30 0, auto");
                        $(".image-wrap-classroom.teacher .ui-resizable-s").css("cursor","url('/images/classroom/cursor-teacher-down.svg') 0 30, auto");
                        $(".image-wrap-classroom.student .ui-resizable-s").css("cursor","url('/images/classroom/cursor-student-down.svg') 0 30, auto");
                        $(".image-wrap-classroom.teacher .ui-resizable-se").css("cursor"," url('/images/classroom/teacher-arrow.svg'), auto");
                        $(".image-wrap-classroom.student .ui-resizable-se").css("cursor"," url('/images/classroom/student-arrow.svg'), auto");
                    },
                    resize: ( event, ui ) => {
                        this.assets.size.width = ui.size.width
                        this.assets.size.height = ui.size.height
                        this.assets.isResize = true;
                        // window.cursor.assets.push(this.assets)
                    },
                    stop: ( event, ui ) => {
                        // ui.size.width, ui.size.height
                        this.assets.size.width = ui.size.width
                        this.assets.size.height = ui.size.height
                        this.assets.isResize = true;
                        // window.cursor.assets.push(this.assets)
                    }
                });
            })
        },
        methods: {
            closeImage() {
                this.assets.isClose = true
                this.activeImage = false
            },
             closeImageAssets() {
                this.activeImage = false
            },
        },
        watch: {
            'assets.position.top': function(val) {
                document.getElementById(this.getId).style.top = val
             },
             'assets.position.left': function(val) {
                document.getElementById(this.getId).style.left = val
             },
              'assets.size.width': function(val) {
                document.getElementById(this.getId).style.width = `${val}px`
             },
              'assets.size.height': function(val) {
                document.getElementById(this.getId).style.height = `${val}px`
             },

             'assets.isClosed': function(val) {
                 if(val) {
                    this.closeImageAssets()
                 }
             },
        }
    });
}
