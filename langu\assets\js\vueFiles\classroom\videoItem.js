
function createVideo(videoId) {
    let videoWrap = document.createElement('div');
    videoWrap.setAttribute("id", `video${videoId}`);
    let videoArea = document.getElementsByClassName('middle-inner-classroom')[0];
    videoArea.appendChild(videoWrap);

    function updateVideo() {

    }
    function drawVideo() {

    }
    function hittestVideo() {

    }

    new Vue({
        el: '#video'+videoId ,
        template: `
            <div :id="this.getId" class="video-item" data-id="" v-bind:class="{active: activeVideo}">
                <div :id="\`desc-\${this.videoId}\`" class="video-item-description">
                    <span class="video-item-name">{{player.config.title}}</span>
                    <span @click="closeVideo" class="video-item-cross">&times;</span>
                </div>
                <div  :id="\`player-\${this.videoId}\`" data-plyr-provider="youtube" :data-plyr-embed-id="videoId"></div>
            </div>
        `,
        computed: {
            getId() {
                return 'video-item-'+this._uid
            }
        },
        data: {
            activeVideo: true,
            videoId: null,
            player: {
                config: {},
            },
            canvas: null,
            mouseCanvasVideoClick: false,
            start_loc: null,
            videoAssets: [],
            video_assets: null,
            assets: {
                type: 'vue_videoitem',
                videoId: videoId,
                update: updateVideo,
                videoAssets: [],
                draw: drawVideo,
                hittest: hittestVideo,
                position: {
                    top: '20%',
                    left: '40%'
                },
                size: {
                    width: '500',
                    height: '350'
                },
                currentTime: null,
                isResize: false,
                isDrag: false,
                isCreated: false,
                isPlay: false,
                isSeeking: false,
                isMute: false,
                volumeValue: null,
                speedValue: null,
                isClose: false,
                addCanvasDraw: false,
                canvasGetAssets: false,
                videoEvent: false
            }
        },
        mounted() {
            this.videoId = videoId
            this.assets.isCreated = true;
            window.cursor.assets.push(this.assets);

            this.$nextTick(() => {
                if(window.role === 'teacher') {
                    $('.video-item').addClass('teacher');
                } else {
                    $('.video-item').addClass('student');
                }
                this.player = new Plyr('#player-'+this.videoId, {
                    controls: ['play', 'progress', 'current-time', 'mute', 'volume', 'captions', 'settings', 'pip'],
                    clickToPlay: false,
                    hideControls: false,
                    resetOnEnd: true,
                    speed: { selected: 1.0, options: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2] }
                });
                setTimeout(() => {
                    this.player.speed = 1.0
                }, 1000)
                this.player.on('play', this.playVideo)
                this.player.on('pause', this.pauseVideo)
                this.player.on('seeking', this.seekingVideo)
                this.player.on('ratechange', this.changeSpeed)


                $('#'+this.getId).draggable({
                    drag: ( event, ui ) => {
                        this.assets.position.top = `${ui.position.top}px`
                        this.assets.position.left = `${ui.position.left}px`
                        this.assets.isDrag = true;
                        window.cursor.assets.push(this.assets)
                    },
                    stop: ( event, ui ) => {
                        // ui.position.top, ui.position.left
                        this.assets.position.top = `${ui.position.top}px`
                        this.assets.position.left = `${ui.position.left}px`
                        this.assets.isDrag = true;
                        window.cursor.assets.push(this.assets)
                        setTimeout(() => {
                            this.assets.isDrag = false;
                        }, 60)
                    }
                });

                $('#'+this.getId).resizable({
                    handles: "e, s, se",
                    create: function( event, ui ) {


                        $(".video-item.teacher .ui-resizable-e").css("cursor","url('/images/classroom/cursor-teacher-right.svg') 30 0, auto");
                        $(".video-item.student .ui-resizable-e").css("cursor","url('/images/classroom/cursor-student-right.svg') 30 0, auto");
                        $(".video-item.teacher .ui-resizable-s").css("cursor","url('/images/classroom/cursor-teacher-down.svg') 0 30, auto");
                        $(".video-item.student .ui-resizable-s").css("cursor","url('/images/classroom/cursor-student-down.svg') 0 30, auto");
                        $(".video-item.teacher .ui-resizable-se").css("cursor"," url('/images/classroom/teacher-arrow.svg'), auto");
                        $(".video-item.student .ui-resizable-se").css("cursor"," url('/images/classroom/student-arrow.svg'), auto");
                    },
                    start: ( event, ui ) => {
                        $(`#canvas-video${videoId}`).hide()
                        $('a, button, input, label, span').css('cursor', 'none !important')
                        $('.cursor-pointer-teacher, .cursor-pointer-student').css('cursor', 'none !important')
                    },
                    resize: ( event, ui ) => {
                        this.assets.videoEvent = true
                        this.assets.size.width = ui.size.width
                        this.assets.size.height = ui.size.height
                        this.assets.isResize = true;

                        let cursorName = null;
                        let resizableSe = $(event.toElement).hasClass('ui-resizable-se');

                        let resizableE = $(event.toElement).hasClass('ui-resizable-e');
                        let resizableS = $(event.toElement).hasClass('ui-resizable-s');
                        if(resizableSe) {
                            cursorName = 'resizable-se'
                        } else if(resizableE) {
                            cursorName = 'resizable-e'
                        } else if(resizableS) {
                            cursorName = 'resizable-s'
                        }
                        document.addEventListener('mousemove', (e) => {
                            cursorMouseMoveEvent(cursorName, e)
                        });

                    },
                    stop: ( event, ui ) => {
                        // ui.size.width, ui.size.height
                        this.assets.videoEvent = true
                        this.assets.size.width = ui.size.width
                        this.assets.size.height = ui.size.height
                        this.assets.isResize = true;
                        setTimeout(() => {
                            this.assets.isDrag = false;
                        }, 60)
                        $(`#canvas-video${videoId}`).show()
                        if (role == 'student')
                            var cursorStyles = "<style>#dropbox{cursor: url('/images/classroom/cursor_pointer_student_css.svg') 11 0, auto !important;} a, button, .cursor-pointer-student input, label, span {cursor: url('/images/classroom/cursor_pointer_student_css.svg') 11 0, auto !important;}</style>";
                        else
                            var cursorStyles = "<style>#dropbox{cursor: url('/images/classroom/cursor_pointer_teacher_css.svg') 11 0, auto !important;} a, button, input, label, span, .cursor-pointer-teacher {cursor: url('/images/classroom/cursor_pointer_teacher_css.svg') 11 0, auto !important;}</style>";
                    }
                });

                this.createVideoCanvas()
                this.assets.canvasGetAsssets = false
                this.drawAssetsCanvas()
            })

        },
        methods: {
            closeVideo() {
                this.assets.videoEvent = true
                this.activeVideo = false
                this.assets.isClose = true
            },
            playVideo(e) {
                this.clearCanvas()
                if (this.assets.isPlay !== true) {
                    this.assets.videoEvent = true
                    this.assets.isPlay = true
                    $(`#canvas-video${videoId}`).hide()
                }
            },
            pauseVideo(e) {
                if (this.assets.isPlay !== false) {
                    this.assets.videoEvent = true
                    this.assets.isPlay = false
                    $(`#canvas-video${videoId}`).show()
                    this.drawAssetsCanvas()
                }
            },
            seekingVideo(e) {
                setTimeout(() => {
                    currentTime = e.detail.plyr.media.currentTime
                    if((Math.abs(currentTime - this.assets.currentTime)) > 5) {
                        this.assets.videoEvent = true
                        this.assets.isSeeking = true
                        this.assets.currentTime = currentTime
                        this.clearCanvas()
                        this.drawAssetsCanvas()
                        let percent = this.retrievePercentFromPlayerProgress(this.player)
                    }
                }, 1000)


            },
            seekingVideoAssets(value) {
                if(this.player.currentTime !== value) {
                    this.player.currentTime = value
                    this.clearCanvas()
                    this.drawAssetsCanvas()
                    let percent = this.retrievePercentFromPlayerProgress(this.player)
                }
                    this.drawAssetsCanvas()

                console.log('currentTime seeking', this.player.currentTime)

            },
            changeMute(value) {
                if (this.assets.isMute !== value) {
                    this.assets.videoEvent = true
                    this.assets.isMute = value
                }
            },
            changeMuteAssets(value) {
                if (this.player.muted !== value) {
                    this.assets.videoEvent = true
                    this.player.muted = value
                }
            },
            changeSpeed(e) {
                if (this.assets.speedValue !== e.detail.plyr.config.speed.selected) {
                    this.assets.videoEvent = true
                    this.assets.speedValue = e.detail.plyr.config.speed.selected
                }
            },
            changeSpeedAssets(value) {
                this.player.speed = value
            },
            changeVolume(value) {
                if (this.assets.volumeValue !== value) {
                    this.assets.videoEvent = true
                    this.assets.volumeValue = value
                }
            },
            changeVolumeAssets(value) {
                if(this.player.volume !== value) {
                    this.assets.videoEvent = true
                    this.player.volume = value
                }
            },
            changeStatus(value) {
                if(value) {
                     this.player.play()
                 } else {
                     this.player.pause()

                    this.drawAssetsCanvas()
                    this.assets.addCanvasDraw = true;

                 }
            },
            createVideoCanvas() {
                let videoCanvas = document.createElement("canvas");
                videoCanvas.setAttribute("id", `canvas-video${videoId}`);
                videoCanvas.setAttribute("class", `canvas-video`);
                let videoWrap = document.querySelector('#video-item-'+this._uid + ' .plyr');
                if(videoWrap) {
                  videoWrap.appendChild(videoCanvas);
                }
                canvasV =  document.getElementById(`canvas-video${videoId}`)
                canvasV.style.position = 'absolute'
                canvasV.style.zIndex = '100'
                canvasV.addEventListener('click', (e) => {
                    e.stopPropagation();
                })
                canvasV.addEventListener('mousemove', (e) => {
                    e.stopPropagation();
                })
                canvasV.addEventListener('mousedown', (e) => {
                    e.stopPropagation();
                })
                canvasV.addEventListener('mouseup', (e) => {
                    e.stopPropagation();
                })
                this.canvas = canvasV

                this.canvas.addEventListener('mousedown', (e) => {
                    this.mouseCanvasVideoClick = true;

                    let pos = this.getMousePos(this.canvas, e);

                    let videoAsset = {
                        type: undefined,
                        component: null,
                        percent: this.retrievePercentFromPlayerProgress(this.player),
                    };

                    if (window.cursor.selected.indexOf('SHAPE_') !== -1) {
                        let functionName = window.cursor.selected.replace('SHAPE_', '');

                        switch(functionName) {
                            case 'SQUARE':
                                videoAsset.component = (new ShapeSquare(pos.x, pos.y));
                                break;
                            case 'CIRCLE':
                                videoAsset.component = (new ShapeCircle(pos.x, pos.y));
                                break;
                            case 'TRIANGLE':
                                videoAsset.component = (new ShapeTriangle(pos.x, pos.y));
                                break;
                            case 'LINE':
                                videoAsset.component = (new ShapeLine(pos.x, pos.y));
                                break;
                            default:
                                console.error('Undefined function called [' + functionName + '].');
                                break;
                        }

                        videoAsset.type = videoAsset.component.type;
                        this.start_loc = new Location(pos.x, pos.y);
                        this.assets.videoAssets.push(videoAsset);
                    } else {
                        this.assets.videoAssets.push({
                            type: 'line',
                            component: new Line(pos.x, pos.y),
                            percent: this.retrievePercentFromPlayerProgress(this.player)
                        });
                    }
                })

                 this.canvas.addEventListener('mouseleave', () => {
                    if (this.mouseCanvasVideoClick === true && this.assets.videoAssets.length) {
                        this.video_assets = this.assets.videoAssets[this.assets.videoAssets.length - 1];

                        appendAnnotationsBlock(this.video_assets.percent, this._uid);
                    }

                    this.mouseCanvasVideoClick = false;
                    cursor.disabled = false;
                });

                 this.canvas.addEventListener('mouseup', () => {
                    if (this.mouseCanvasVideoClick === true && this.assets.videoAssets.length) {
                        this.video_assets = this.assets.videoAssets[this.assets.videoAssets.length - 1];

                        appendAnnotationsBlock(this.video_assets.percent, this._uid);
                    }
                    this.mouseCanvasVideoClick = false;
                    this.start_loc = null;
                    this.drawOnCanvas()
                    this.assets.addCanvasDraw = true;
                });

                 this.canvas.addEventListener('mousemove', (e) => {
                    let pos = this.getMousePos(this.canvas, e);
                    if (this.mouseCanvasVideoClick == true) {
                         if (window.cursor.selected.indexOf('SHAPE_') !== -1) {
                            let diff_x = pos.x - this.start_loc.x;
                            let diff_y = pos.y - this.start_loc.y;

                            (this.assets.videoAssets[this.assets.videoAssets.length - 1].component).resize(diff_x, diff_y);

                            this.start_loc.x = pos.x;
                            this.start_loc.y = pos.y;
                        } else {
                            (this.assets.videoAssets[this.assets.videoAssets.length - 1].component).add_point(pos.x, pos.y);
                        }
                    }
                });
            },
            getMousePos(canvas, evt) {
                var rect = canvas.getBoundingClientRect();
                return {
                    x: (evt.clientX - rect.left) / (rect.right - rect.left) * canvas.width,
                    y: (evt.clientY - rect.top) / (rect.bottom - rect.top) * canvas.height
                };
            },
            retrievePercentFromPlayerProgress(player){
                let percentPerTime = player.duration / 100;
                let percent = this.assets.currentTime / percentPerTime;

                return percent ? percent : 0;
            },
            appendAnnotationsBlock(percent, id) {
                if (!$(`div#video-item-${id} div.plyr__progress`).length) {
                    setTimeout(() => {
                        appendAnnotationsBlock(percent, id);
                    }, 1000);
                } else {
                    let annotationsBlock = `<span class="video_annotations" id="video_annotations_${id}" data-annotation-percent="annotation_percent_${percent}"style="left: ${percent}%;"></span>`;
                    $(`div#video-item-${id} div.plyr__progress`).append(annotationsBlock);
                }
            },
            drawOnCanvas() {
                 let videoCtx = this.canvas.getContext('2d');
                (this.assets.videoAssets[this.assets.videoAssets.length - 1].component).draw(videoCtx, -7.5, undefined);
            },
            clearCanvas() {
                 let videoCtx = this.canvas.getContext('2d');
                 videoCtx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            },
            drawAssetsCanvas() {
                if(this.canvas) {
                    let videoCtx = this.canvas.getContext('2d');
                    let percent = this.retrievePercentFromPlayerProgress(this.player)


                    for (var i = 0; i < this.assets.videoAssets.length; i++) {
                        if(this.assets.videoAssets[i].component.type === 'shape_circle') {
                            this.assets.videoAssets[i].component.draw = ShapeCircleFunctionDraw
                        } else if(this.assets.videoAssets[i].component.type === 'shape_triangle') {
                            this.assets.videoAssets[i].component.draw = ShapeTriangleFunctionDraw
                        } else if(this.assets.videoAssets[i].component.type === 'shape_square') {
                            this.assets.videoAssets[i].component.draw = ShapeSquareFunctionDraw
                        } else if(this.assets.videoAssets[i].component.type === 'shape_line') {
                            this.assets.videoAssets[i].component.draw = ShapeLineFunctionDraw
                        } else if(this.assets.videoAssets[i].component.type === 'line') {
                            this.assets.videoAssets[i].component.draw = line_draw
                        }

                        if(Math.abs(percent - this.assets.videoAssets[i].percent) <= 1) {
                            console.log('percent diff',Math.abs(percent - this.assets.videoAssets[i].percent))

                            this.assets.videoAssets[i].component.draw(videoCtx, -7.5, undefined)
                       }
                    }
                }

                this.assets.canvasGetAssets = false
            }
        },
        watch: {
             'player.config.mutedpercent': function(val) {
                 this.changeMute(val)
             },
             'assets.isMute': function(val) {
                 this.changeMuteAssets(val)
             },
             'assets.volumeValue': function(val) {
                 this.changeVolumeAssets(val)
             },
             'player.config.volume': function(val) {
                 this.changeVolume(val)
             },
             'assets.speedValue': function(val) {
                 this.changeSpeedAssets(val)
             },
             'assets.isPlay': function(val) {
                 this.changeStatus(val)
             },
             'assets.currentTime': function(val) {
                 this.seekingVideoAssets(val)
             },
             'assets.isClose': function(val) {
                 if(val) {
                     this.closeVideo()
                 }
             },
             'assets.position.top': function(val) {
                document.getElementById(this.getId).style.top = val

             },
             'assets.position.left': function(val) {
                document.getElementById(this.getId).style.left = val
             },
              'assets.size.width': function(val) {
                document.getElementById(this.getId).style.width = `${val}px`
             },
              'assets.size.height': function(val) {
                document.getElementById(this.getId).style.height = `${val}px`
             },
            'assets.canvasGetAssets': function(val) {
                  console.log('watcher ', val)

                  if(val) {
                      console.log('watcher true')
                      this.drawAssetsCanvas()
                  }
             },
         }
    });
}
