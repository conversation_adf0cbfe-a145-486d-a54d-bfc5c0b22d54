/*
 By <PERSON>svaldas Valutis, www.osvaldas.info
 Available for use under the MIT License
 */
!function(t,n,e,i){"use strict";var o=function(){var t=e.body||e.documentElement,t=t.style;return""==t.WebkitTransition?"-webkit-":""==t.MozTransition?"-moz-":""==t.OTransition?"-o-":""==t.transition&&""},r=!1!==o(),a=function(t,n,e){var i={},r=o();i[r+"transform"]="translateX("+n+")",i[r+"transition"]=r+"transform "+e+"s linear",t.css(i)},u="ontouchstart"in n,c=n.navigator.pointerEnabled||n.navigator.msPointerEnabled,l=function(t){if(u)return!0;if(!c||void 0===t||void 0===t.pointerType)return!1;if(void 0!==t.MSPOINTER_TYPE_MOUSE){if(t.MSPOINTER_TYPE_MOUSE!=t.pointerType)return!0}else if("mouse"!=t.pointerType)return!0;return!1};t.fn.imageLightbox=function(i){var i=t.extend({selector:'id="imagelightbox"',allowedTypes:"png|jpg|jpeg|gif",animationSpeed:250,preloadNext:!0,enableKeyboard:!0,quitOnEnd:!1,quitOnImgClick:!1,quitOnDocClick:!0,onStart:!1,onEnd:!1,onLoadStart:!1,onLoadEnd:!1},i),o=t([]),f=t(),d=t(),p=0,s=0,g=0,h=!1,m=function(n){return"a"==t(n).prop("tagName").toLowerCase()&&new RegExp(".("+i.allowedTypes+")$","i").test(t(n).attr("href"))},v=function(){if(!d.length)return!0;var e=.8*t(n).width(),i=.9*t(n).height(),o=new Image;o.src=d.attr("src"),o.onload=function(){if(p=o.width,s=o.height,p>e||s>i){var r=p/s>e/i?p/e:s/i;p/=r,s/=r}d.css({width:p+"px",height:s+"px",top:(t(n).height()-s)/2+"px",left:(t(n).width()-p)/2+"px"})}},x=function n(e){if(h)return!1;if(e=void 0!==e&&("left"==e?1:-1),d.length){if(!1!==e&&(o.length<2||!0===i.quitOnEnd&&(-1===e&&0==o.index(f)||1===e&&o.index(f)==o.length-1)))return S(),!1;var u={opacity:0};r?a(d,100*e-g+"px",i.animationSpeed/1e3):u.left=parseInt(d.css("left"))+100*e+"px",d.animate(u,i.animationSpeed,function(){E()}),g=0}h=!0,!1!==i.onLoadStart&&i.onLoadStart(),setTimeout(function(){d=t("<img "+i.selector+" />").attr("src",f.attr("href")).load(function(){d.appendTo("body"),v();var n={opacity:1};if(d.css("opacity",0),r)a(d,-100*e+"px",0),setTimeout(function(){a(d,"0px",i.animationSpeed/1e3)},50);else{var u=parseInt(d.css("left"));n.left=u+"px",d.css("left",u-100*e+"px")}if(d.animate(n,i.animationSpeed,function(){h=!1,!1!==i.onLoadEnd&&i.onLoadEnd()}),i.preloadNext){var c=o.eq(o.index(f)+1);c.length||(c=o.eq(0)),t("<img />").attr("src",c.attr("href")).load()}}).error(function(){!1!==i.onLoadEnd&&i.onLoadEnd()});var u=0,s=0,m=0;d.on(c?"pointerup MSPointerUp":"click",function(t){if(t.preventDefault(),i.quitOnImgClick)return S(),!1;if(l(t.originalEvent))return!0;var e=(t.pageX||t.originalEvent.pageX)-t.target.offsetLeft;f=o.eq(o.index(f)-(p/2>e?1:-1)),f.length||(f=o.eq(p/2>e?o.length:0)),n(p/2>e?"left":"right")}).on("touchstart pointerdown MSPointerDown",function(t){if(!l(t.originalEvent)||i.quitOnImgClick)return!0;r&&(m=parseInt(d.css("left"))),u=t.originalEvent.pageX||t.originalEvent.touches[0].pageX}).on("touchmove pointermove MSPointerMove",function(t){if(!l(t.originalEvent)||i.quitOnImgClick)return!0;t.preventDefault(),s=t.originalEvent.pageX||t.originalEvent.touches[0].pageX,g=u-s,r?a(d,-g+"px",0):d.css("left",m-g+"px")}).on("touchend touchcancel pointerup pointercancel MSPointerUp MSPointerCancel",function(t){if(!l(t.originalEvent)||i.quitOnImgClick)return!0;Math.abs(g)>50?(f=o.eq(o.index(f)-(g<0?1:-1)),f.length||(f=o.eq(g<0?o.length:0)),n(g>0?"right":"left")):r?a(d,"0px",i.animationSpeed/1e3):d.animate({left:m+"px"},i.animationSpeed/2)})},i.animationSpeed+100)},E=function(){if(!d.length)return!1;d.remove(),d=t()},S=function(){if(!d.length)return!1;d.animate({opacity:0},i.animationSpeed,function(){E(),h=!1,!1!==i.onEnd&&i.onEnd()})};return t(n).on("resize",v),i.quitOnDocClick&&t(e).on(u?"touchend":"click",function(n){d.length&&!t(n.target).is(d)&&S()}),i.enableKeyboard&&t(e).on("keyup",function(t){if(!d.length)return!0;t.preventDefault(),27==t.keyCode&&S(),37!=t.keyCode&&39!=t.keyCode||(f=o.eq(o.index(f)-(37==t.keyCode?1:-1)),f.length||(f=o.eq(37==t.keyCode?o.length:0)),x(37==t.keyCode?"left":"right"))}),t(e).on("click",this.selector,function(n){return!m(this)||(n.preventDefault(),!h&&(h=!1,!1!==i.onStart&&i.onStart(),f=t(this),void x()))}),this.each(function(){if(!m(this))return!0;o=o.add(t(this))}),this.switchImageLightbox=function(t){var n=o.eq(t);if(n.length){var e=o.index(f);f=n,x(t<e?"left":"right")}return this},this.quitImageLightbox=function(){return S(),this},this}}(jQuery,window,document);