function _typeof(e){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e){if("object"===("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,n.dragula=e()}}(function(){return function e(n,t,o){function r(u,c){if(!t[u]){if(!n[u]){var a="function"==typeof require&&require;if(!c&&a)return a(u,!0);if(i)return i(u,!0);var f=new Error("Cannot find module '"+u+"'");throw f.code="MODULE_NOT_FOUND",f}var l=t[u]={exports:{}};n[u][0].call(l.exports,function(e){var t=n[u][1][e];return r(t||e)},l,l.exports,e,n,t,o)}return t[u].exports}for(var i="function"==typeof require&&require,u=0;u<o.length;u++)r(o[u]);return r}({1:[function(e,n,t){"use strict";function o(e){var n=u[e];return n?n.lastIndex=0:u[e]=n=new RegExp(c+e+a,"g"),n}function r(e,n){var t=e.className;t.length?o(n).test(t)||(e.className+=" "+n):e.className=n}function i(e,n){e.className=e.className.replace(o(n)," ").trim()}var u={},c="(?:^|\\s)",a="(?:\\s|$)";n.exports={add:r,rm:i}},{}],2:[function(e,n,t){(function(t){"use strict";function o(e,n){function t(e){return-1!==fe.containers.indexOf(e)||ae.isContainer(e)}function o(e){var n=e?"remove":"add";r(x,n,"mousedown",O),r(x,n,"mouseup",_)}function c(e){r(x,e?"remove":"add","mousemove",N)}function m(e){var n=e?"remove":"add";w[n](x,"selectstart",C),w[n](x,"click",C)}function h(){o(!0),_({})}function C(e){ue&&e.preventDefault()}function O(e){if(ee=e.clientX,ne=e.clientY,!(1!==i(e)||e.metaKey||e.ctrlKey)){var n=e.target,t=T(n);t&&(ue=t,c(),"mousedown"===e.type&&(p(n)?n.focus():e.preventDefault()))}}function N(e){if(ue){if(0===i(e))return void _({});if(void 0===e.clientX||e.clientX!==ee||void 0===e.clientY||e.clientY!==ne){if(ae.ignoreInputTextSelection){var n=y("clientX",e),t=y("clientY",e);if(p(S.elementFromPoint(n,t)))return}var o=ue;c(!0),m(),D(),B(o);var r=u(Q);W=y("pageX",e)-r.left,Z=y("pageY",e)-r.top,E.add(re||Q,"gu-transit"),K(),q(e)}}}function T(e){if(!(fe.dragging&&G||t(e))){for(var n=e;v(e)&&!1===t(v(e));){if(ae.invalid(e,n))return;if(!(e=v(e)))return}var o=v(e);if(o&&!ae.invalid(e,n)){if(ae.moves(e,o,n,g(e)))return{item:e,source:o}}}}function X(e){return!!T(e)}function Y(e){var n=T(e);n&&B(n)}function B(e){$(e.item,e.source)&&(re=e.item.cloneNode(!0),fe.emit("cloned",re,e.item,"copy")),J=e.source,Q=e.item,te=oe=g(e.item),fe.dragging=!0,fe.emit("drag",Q,J)}function P(){return!1}function D(){if(fe.dragging){var e=re||Q;L(e,v(e))}}function I(){ue=!1,c(!0),m(!0)}function _(e){if(I(),fe.dragging){var n=re||Q,t=y("clientX",e),o=y("clientY",e),r=a(G,t,o),i=k(r,t,o);i&&(re&&ae.copySortSource||!re||i!==J)?L(n,i):ae.removeOnSpill?M():R()}}function L(e,n){var t=v(e);re&&ae.copySortSource&&n===J&&t.removeChild(Q),j(n)?fe.emit("cancel",e,J,J):fe.emit("drop",e,n,J,oe),A()}function M(){if(fe.dragging){var e=re||Q,n=v(e);n&&n.removeChild(e),fe.emit(re?"cancel":"remove",e,n,J),A()}}function R(e){if(fe.dragging){var n=arguments.length>0?e:ae.revertOnSpill,t=re||Q,o=v(t),r=j(o);!1===r&&n&&(re?o&&o.removeChild(re):J.insertBefore(t,te)),r||n?fe.emit("cancel",t,J,J):fe.emit("drop",t,o,J,oe),A()}}function A(){var e=re||Q;I(),z(),e&&E.rm(e,"gu-transit"),ie&&clearTimeout(ie),fe.dragging=!1,ce&&fe.emit("out",e,ce,J),fe.emit("dragend",e),J=Q=re=te=oe=ie=ce=null}function j(e,n){var t;return t=void 0!==n?n:G?oe:g(re||Q),e===J&&t===te}function k(e,n,o){for(var r=e;r&&!function(){if(!1===t(r))return!1;var i=H(r,e),u=V(r,i,n,o);return!!j(r,u)||ae.accepts(Q,r,J,u)}();)r=v(r);return r}function q(e){function n(e){fe.emit(e,u,ce,J)}if(G){e.preventDefault();var t=y("clientX",e),o=y("clientY",e),r=t-W,i=o-Z;G.style.left=r+"px",G.style.top=i+"px";var u=re||Q,c=a(G,t,o),f=k(c,t,o),l=null!==f&&f!==ce;(l||null===f)&&(function(){ce&&n("out")}(),ce=f,function(){l&&n("over")}());var d=v(u);if(f===J&&re&&!ae.copySortSource)return void(d&&d.removeChild(u));var s,p=H(f,c);if(null!==p)s=V(f,p,t,o);else{if(!0!==ae.revertOnSpill||re)return void(re&&d&&d.removeChild(u));s=te,f=J}(null===s&&l||s!==u&&s!==g(u))&&(oe=s,f.insertBefore(u,s),fe.emit("shadow",u,f,J))}}function U(e){E.rm(e,"gu-hide")}function F(e){fe.dragging&&E.add(e,"gu-hide")}function K(){if(!G){var e=Q.getBoundingClientRect();G=Q.cloneNode(!0),G.style.width=d(e)+"px",G.style.height=s(e)+"px",E.rm(G,"gu-transit"),E.add(G,"gu-mirror"),ae.mirrorContainer.appendChild(G),r(x,"add","mousemove",q),E.add(ae.mirrorContainer,"gu-unselectable"),fe.emit("cloned",G,Q,"mirror")}}function z(){G&&(E.rm(ae.mirrorContainer,"gu-unselectable"),r(x,"remove","mousemove",q),v(G).removeChild(G),G=null)}function H(e,n){for(var t=n;t!==e&&v(t)!==e;)t=v(t);return t===x?null:t}function V(e,n,t,o){function r(e){return e?g(n):n}var i="horizontal"===ae.direction;return n!==e?function(){var e=n.getBoundingClientRect();return r(i?t>e.left+d(e)/2:o>e.top+s(e)/2)}():function(){var n,r,u,c=e.children.length;for(n=0;n<c;n++){if(r=e.children[n],u=r.getBoundingClientRect(),i&&u.left+u.width/2>t)return r;if(!i&&u.top+u.height/2>o)return r}return null}()}function $(e,n){return"boolean"==typeof ae.copy?ae.copy:ae.copy(e,n)}1===arguments.length&&!1===Array.isArray(e)&&(n=e,e=[]);var G,J,Q,W,Z,ee,ne,te,oe,re,ie,ue,ce=null,ae=n||{};void 0===ae.moves&&(ae.moves=l),void 0===ae.accepts&&(ae.accepts=l),void 0===ae.invalid&&(ae.invalid=P),void 0===ae.containers&&(ae.containers=e||[]),void 0===ae.isContainer&&(ae.isContainer=f),void 0===ae.copy&&(ae.copy=!1),void 0===ae.copySortSource&&(ae.copySortSource=!1),void 0===ae.revertOnSpill&&(ae.revertOnSpill=!1),void 0===ae.removeOnSpill&&(ae.removeOnSpill=!1),void 0===ae.direction&&(ae.direction="vertical"),void 0===ae.ignoreInputTextSelection&&(ae.ignoreInputTextSelection=!0),void 0===ae.mirrorContainer&&(ae.mirrorContainer=S.body);var fe=b({containers:ae.containers,start:Y,end:D,cancel:R,remove:M,destroy:h,canMove:X,dragging:!1});return!0===ae.removeOnSpill&&fe.on("over",U).on("out",F),o(),fe}function r(e,n,o,r){var i={mouseup:"touchend",mousedown:"touchstart",mousemove:"touchmove"},u={mouseup:"pointerup",mousedown:"pointerdown",mousemove:"pointermove"},c={mouseup:"MSPointerUp",mousedown:"MSPointerDown",mousemove:"MSPointerMove"};t.navigator.pointerEnabled?w[n](e,u[o],r):t.navigator.msPointerEnabled?w[n](e,c[o],r):(w[n](e,i[o],r),w[n](e,o,r))}function i(e){if(void 0!==e.touches)return e.touches.length;if(void 0!==e.which&&0!==e.which)return e.which;if(void 0!==e.buttons)return e.buttons;var n=e.button;return void 0!==n?1&n?1:2&n?3:4&n?2:0:void 0}function u(e){var n=e.getBoundingClientRect();return{left:n.left+c("scrollLeft","pageXOffset"),top:n.top+c("scrollTop","pageYOffset")}}function c(e,n){return void 0!==t[n]?t[n]:x.clientHeight?x[e]:S.body[e]}function a(e,n,t){var o,r=e||{},i=r.className;return r.className+=" gu-hide",o=S.elementFromPoint(n,t),r.className=i,o}function f(){return!1}function l(){return!0}function d(e){return e.width||e.right-e.left}function s(e){return e.height||e.bottom-e.top}function v(e){return e.parentNode===S?null:e.parentNode}function p(e){return"INPUT"===e.tagName||"TEXTAREA"===e.tagName||"SELECT"===e.tagName||m(e)}function m(e){return!!e&&("false"!==e.contentEditable&&("true"===e.contentEditable||m(v(e))))}function g(e){return e.nextElementSibling||function(){var n=e;do{n=n.nextSibling}while(n&&1!==n.nodeType);return n}()}function h(e){return e.targetTouches&&e.targetTouches.length?e.targetTouches[0]:e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e}function y(e,n){var t=h(n),o={pageX:"clientX",pageY:"clientY"};return e in o&&!(e in t)&&o[e]in t&&(e=o[e]),t[e]}var b=e("contra/emitter"),w=e("crossvent"),E=e("./classes"),S=document,x=S.documentElement;n.exports=o}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./classes":1,"contra/emitter":5,crossvent:6}],3:[function(e,n,t){n.exports=function(e,n){return Array.prototype.slice.call(e,n)}},{}],4:[function(e,n,t){"use strict";var o=e("ticky");n.exports=function(e,n,t){e&&o(function(){e.apply(t||null,n||[])})}},{ticky:9}],5:[function(e,n,t){"use strict";var o=e("atoa"),r=e("./debounce");n.exports=function(e,n){var t=n||{},i={};return void 0===e&&(e={}),e.on=function(n,t){return i[n]?i[n].push(t):i[n]=[t],e},e.once=function(n,t){return t._once=!0,e.on(n,t),e},e.off=function(n,t){var o=arguments.length;if(1===o)delete i[n];else if(0===o)i={};else{var r=i[n];if(!r)return e;r.splice(r.indexOf(t),1)}return e},e.emit=function(){var n=o(arguments);return e.emitterSnapshot(n.shift()).apply(this,n)},e.emitterSnapshot=function(n){var u=(i[n]||[]).slice(0);return function(){var i=o(arguments),c=this||e;if("error"===n&&!1!==t.throws&&!u.length)throw 1===i.length?i[0]:i;return u.forEach(function(o){t.async?r(o,i,c):o.apply(c,i),o._once&&e.off(n,o)}),e}},e}},{"./debounce":4,atoa:3}],6:[function(e,n,t){(function(t){"use strict";function o(e,n,t,o){return e.addEventListener(n,t,o)}function r(e,n,t){return e.attachEvent("on"+n,f(e,n,t))}function i(e,n,t,o){return e.removeEventListener(n,t,o)}function u(e,n,t){var o=l(e,n,t);if(o)return e.detachEvent("on"+n,o)}function c(e,n,t){var o=-1===v.indexOf(n)?function(){return new s(n,{detail:t})}():function(){var e;return p.createEvent?(e=p.createEvent("Event"),e.initEvent(n,!0,!0)):p.createEventObject&&(e=p.createEventObject()),e}();e.dispatchEvent?e.dispatchEvent(o):e.fireEvent("on"+n,o)}function a(e,n,o){return function(n){var r=n||t.event;r.target=r.target||r.srcElement,r.preventDefault=r.preventDefault||function(){r.returnValue=!1},r.stopPropagation=r.stopPropagation||function(){r.cancelBubble=!0},r.which=r.which||r.keyCode,o.call(e,r)}}function f(e,n,t){var o=l(e,n,t)||a(e,n,t);return h.push({wrapper:o,element:e,type:n,fn:t}),o}function l(e,n,t){var o=d(e,n,t);if(o){var r=h[o].wrapper;return h.splice(o,1),r}}function d(e,n,t){var o,r;for(o=0;o<h.length;o++)if(r=h[o],r.element===e&&r.type===n&&r.fn===t)return o}var s=e("custom-event"),v=e("./eventmap"),p=t.document,m=o,g=i,h=[];t.addEventListener||(m=r,g=u),n.exports={add:m,remove:g,fabricate:c}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./eventmap":7,"custom-event":8}],7:[function(e,n,t){(function(e){"use strict";var t=[],o="",r=/^on/;for(o in e)r.test(o)&&t.push(o.slice(2));n.exports=t}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],8:[function(e,n,t){(function(e){var t=e.CustomEvent;n.exports=function(){try{var e=new t("cat",{detail:{foo:"bar"}});return"cat"===e.type&&"bar"===e.detail.foo}catch(e){}return!1}()?t:"function"==typeof document.createEvent?function(e,n){var t=document.createEvent("CustomEvent");return n?t.initCustomEvent(e,n.bubbles,n.cancelable,n.detail):t.initCustomEvent(e,!1,!1,void 0),t}:function(e,n){var t=document.createEventObject();return t.type=e,n?(t.bubbles=Boolean(n.bubbles),t.cancelable=Boolean(n.cancelable),t.detail=n.detail):(t.bubbles=!1,t.cancelable=!1,t.detail=void 0),t}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],9:[function(e,n,t){var o,r="function"==typeof setImmediate;o=r?function(e){setImmediate(e)}:function(e){setTimeout(e,0)},n.exports=o},{}]},{},[2])(2)});