!function(o,e){var n,s,l=null;o.blockUI.defaults.css={},o.blockUI.defaults.blockMsgClass="block-msg",o.blockUI.defaults.message='<span class="glyphicon glyphicon-repeat spin"></span>',o(document).ajaxSend(function(e,t,c){if((c.ignoreIfPrevious||!1)&&null!==l)return t.abort(),!1;(c.abortPrevious||!0)&&null!==l&&(l.abort(),clearTimeout(n),s&&s.modal("hide"),s=null),l=t;var r=c.bootbox||!1,a=c.bootboxMessage||'<p class="text-center">Loading...</p>';!1!==r&&(n=setTimeout(function(){s=bootbox.dialog({message:a,closeButton:!1}),s.css("z-index",99999)},300));var u=c.block||!1;if(!1!==u){var i=u.context||null,p=u.options||{};n=setTimeout(function(){null===i?o.blockUI(p):o(i).block(p)},300)}}),o(document).ajaxComplete(function(e,t,c){if(0!==t.status||0!==t.readystate){l=null,clearTimeout(n),s&&s.modal("hide"),s=null;var r=c.block||!1;if(!1!==r){var a=r.context||null;null===a?o.unblockUI(r.options):o(a).unblock(r.options)}}}),o(document).ajaxSuccess(function(o,e,n,s){"json"===n.dataType&&e.hasOwnProperty("responseJSON")&&e.responseJSON.hasOwnProperty("redirect")&&null!=e.responseJSON.redirect&&(e.responseJSON.hasOwnProperty("mock")&&!1!==e.responseJSON.mock?(console.log("redirect mocked to: "),console.log(e.responseJSON.redirect)):window.location.replace(e.responseJSON.redirect))})}(jQuery);