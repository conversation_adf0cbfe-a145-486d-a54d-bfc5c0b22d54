!function(n){var e=function(){function e(n,e){return(n+"").replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g,"$1"+(e||void 0===e?"<br />":"<br>")+"$2")}function i(i,r){var c=t.children(".alert-"+i),d=s[i];0===c.length&&(c=n(l.replace("%type%",d.name)),t.append(c));var u=e(Translator.trans(r)),p=n(o.replace("%icon%",d.icon).replace("%message%",u));c.children("ul").first().append(p),c.addClass("in"),setTimeout(a,3e3,p)}function a(n){var e=n.closest(".alert");n.siblings().length>1?n.remove():(e.removeClass("in"),e.on("transitionend webkitTransitionEnd oTransitionEnd MSTransitionEnd",function(){e.remove()}))}function r(){var e=0,i=t.find(".alert.fade-out");n.each(i,function(i,r){var s=n(r),t=s.children("ul").first().children("li");n.each(t,function(i,r){var s=n(r);setTimeout(a,3e3+1e3*e,s),e++})})}var s={notice:{name:"info",icon:"glyphicon-info-sign"},warning:{name:"warning",icon:"glyphicon-alert"},error:{name:"danger",icon:"glyphicon-exclamation-sign"},success:{name:"success",icon:"glyphicon-ok-sign"}},t=n(".flash-container"),l='<div class="alert fade alert-%type% alert-dismissable" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button><ul class="list-unstyled"></ul></div>',o='<li><span class="glyphicon %icon%"></span> %message%</li>',c={};return c.addFlash=i,n(function(){r()}),c}();window.FLASHES=e}(jQuery);