!function(n){var t=n(document.body),o=null,i=null,c=null,e=function(){null===o&&(o=n('<div class="image-lightbox-overlay"></div>')),o.appendTo(t)},u=function(){o.detach()},a=function(){null===i&&(i=n('<div class="image-lightbox-activity-indicator"><div></div></div>')),i.appendTo(t)},l=function(){i.detach()},d=function(n,t){return n.preventDefault(),c.detach(),t.quitImageLightbox(),!1},f=function(o){null===c&&(c=n('<button type="button" class="image-lightbox-close-button"></button>')),c.appendTo(t),c.on("click touchend",{instance:o},d)},b=function(){c.detach(),c.off("click touchend",d)},g=n(".image-lightbox").imageLightbox({onLoadStart:function(){a()},onLoadEnd:function(){l()},onStart:function(){e(),f(g)},onEnd:function(){u(),l(),b()},selector:'class="image-lightbox-full"',quitOnEnd:!0,enableKeyboard:!1})}(jQuery);