!function(a){langu=window.langu||{};var l={instance:function(l,n,o){n=n||"langu-modal-dialog";var e=a('<div class="langu-modal langu-modal--active"><div class="'+n+'"><a href="#" class="langu-modal-dialog-close">&times;</a><div class="langu-modal-dialog-content"></div></div></div>'),d={content:a(l).html(),dialogClass:"langu-modal-dialog-content",escapeClose:!0,afterClose:function(){e.remove()}};o=a.extend({},o,d);var i=new RModal(e[0],o);return a("body").append(e),e.on("click",".langu-modal-dialog-close, .modal-close",function(a){a.preventDefault(),i.close()}),i}};langu.modal=l}(jQuery);