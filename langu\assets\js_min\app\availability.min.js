!function(s){s(".slots-container").on("click",".close",function(e){e.preventDefault();var o=s(this);if(o.hasClass("removing"))return!1;var a=o.closest(".slots-slot"),r=a.data("slot-id");o.addClass("removing"),a.addClass("removing");var n=s.ajax(o.attr("href"),{dataType:"json",data:{slot_id:r},method:"DELETE"});return n.fail(function(s,e,r){if(a.removeClass("removing"),o.removeClass("removing"),s.hasOwnProperty("responseJSON")&&null!==s.responseJSON){var n="error";s.responseJSON.hasOwnProperty("payload")&&s.responseJSON.payload.hasOwnProperty("messageType")&&(n=s.responseJSON.payload.messageType);var t=s.responseJSON.message;FLASHES.addFlash(n,t)}}),n.done(function(s,e,o){a.remove()}),!1}),s(".copy-slots-form").on("submit",function(e){e.preventDefault();var o=s(this);if(o.find(".copy-slots-container-button").hasClass("disabled"))return e.stopPropagation(),!1;var a=o.serializeArray(),r=s.ajax(o.attr("action"),{dataType:"json",data:a,method:"POST",block:{context:".copy-slots-form"}});return r.fail(function(s,e,o){if(s.hasOwnProperty("responseJSON")&&null!==s.responseJSON){var a=s.responseJSON.payload.messageType||"error",r=s.responseJSON.message;FLASHES.addFlash(a,r)}}),r.done(function(s,e,o){if(null!==s){var a=s.payload.messages;for(var r in a)for(var n=a[r],t=0;t<n.length;t++)FLASHES.addFlash(r,n[t])}}),e.stopPropagation(),!1}),s(".copy-slots-form").on("change",".copy-slots-item-checkbox",function(){var e=s(this),o=e.closest(".copy-slots-form"),a=o.find(".copy-slots-container-button");o.find(".copy-slots-item-checkbox:checked").length?a.removeClass("disabled"):a.addClass("disabled")})}(jQuery);