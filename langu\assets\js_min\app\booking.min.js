!function(e){window.langu=window.langu||{};var n=function(e){var n=e.find(".slots-picker-page.active .arrow-row .arrow-row-slide"),t=n.filter('[data-picker-slide="up"]'),r=n.filter('[data-picker-slide="down"]'),a=e.find(".slots-picker-page.active .calendar-row .calendar-row-inner"),i=parseInt(a.css("marginTop")),o=7,s=function(){if(0===o)i=0;else if(7===o)i="";else{var e=3*o;i="calc(-"+e+"em - "+e+"px)"}a.css("marginTop",i)};t.on("click",function(){0!==o&&(o--,s())}),r.on("click",function(){9!==o&&(o++,s())})};window.langu.CalendarScroller=n;var t=function(n){var t=n.attr("id"),r=[],a=n.find('input[name="'+t+'[length]"]'),i=n.find(".booking-selection-services").first(),o=i.find('input[name="'+t+'[service]"]'),s=function(e,n){for(var t=0;t<r.length;t++)r[t](e,n)};a.on("change",function(e){o.parent().addClass("hidden");var n=parseInt(this.value),t=null;if(0===n)i.addClass("hidden"),o.prop("checked",!1);else{var r=o.filter('[data-length="'+n+'"]'),a=r.first();a.prop("checked",!0),t=a.data("lessons"),r.parent().removeClass("hidden"),i.removeClass("hidden")}s(n||30,t||1)}),o.on("change",function(n){var t=e(this),r=t.data("length"),a=t.data("lessons");s(r||30,a||1)}),this.getCurrentState=function(){var e=parseInt(a.filter(":checked").first().val()),n=1,t=o.filter(":checked");return t.length&&(n=parseInt(t.first().data("lessons"))),{lessons:n||1,length:e||30}},this.addStateChangeListener=function(n){-1===e.inArray(n,r)&&r.push(n)},this.removeStateChangeListener=function(n){var t=e.inArray(n,r);-1!==t&&r.splice(t,1)}};window.langu.SelectionUpdater=t;var r=function(n,t){var r,a,i=n,o=t.selection.length,s=t.selection.lessons,l={state:{}},c=function(){r&&(r.off("change","input.slot-picker-checkbox",u),r.off("mouseenter","label.slot.slot-label.free",d),r.off("mouseleave","label.slot.slot-label.free",f)),r=i.find(".slots-picker-page.active .calendar-row .calendar-row-inner .calendar-row-inner-slots-column"),r.on("change","input.slot-picker-checkbox",u),r.on("mouseenter","label.slot.slot-label.free",d),r.on("mouseleave","label.slot.slot-label.free",f)},d=function(n){var t=e(this);if(!(s<=a.selection.selectedCount||t.siblings().first().prop("checked"))){var r=o/30,i=p(this,r);if(null===i)return!1;t.addClass("active"),e.each(i,function(){this.siblings().first().addClass("active")})}},f=function(n){var t=e(this);if(!(s<=a.selection.selectedCount||t.siblings().first().prop("checked"))){var r=o/30,i=p(this,r);if(null===i)return!1;t.removeClass("active"),e.each(i,function(){this.siblings().first().removeClass("active")})}},u=function(e){return this.checked?g(this,e):v(this,e)},h=function(e){r.off("change","input.slot-picker-checkbox",u),e(),r.on("change","input.slot-picker-checkbox",u)},p=function(n,t){if(1===t)return[];for(var r=e(n),a=[],i=r.parent();;){i=i.next();var o=i.children("input");if(!o.length||o.prop("checked"))break;if(a.push(o.first()),t<=a.length+1)return a}for(i=r.parent();;){i=i.prev();var o=i.children("input");if(!o.length||o.prop("checked"))break;if(a.push(o.first()),t<=a.length+1)return a}return null},g=function(n,t){if(a.selection.selectedCount+1>s)return t.preventDefault(),h(function(){n.checked=!n.checked}),!1;var r=o/30,i=p(n,r);return null!==i&&(h(function(){var t=a.selection.selectedCount,r=[{id:parseInt(n.value),element:e(n)}];r[0].element.siblings().first().removeClass("active"),a.selection.groupMappings[r[0].id]=t,e.each(i,function(){this.prop("checked",!0),this.siblings().first().removeClass("active");var e=parseInt(this.val());r.push({id:e,element:this}),a.selection.groupMappings[e]=t}),a.selection.groups[t]=r}),a.selection.selectedCount++,!0)},v=function(n,t){var r=e(n),i=parseInt(r.val()),o=a.selection.groupMappings[i];if(!a.selection.groups.hasOwnProperty(o))throw Error("No selection group found");var s=a.selection.groups[o];return h(function(){for(var e=0;e<s.length;e++)s[e].id!==i&&s[e].element.prop("checked",!1)}),delete a.selection.groupMappings[i],delete a.selection.groups[o],a.selection.selectedCount--,d.call(r.siblings().get(0)),!0},m=function(e,n){return e+"_"+n},k=function(){h(function(){for(var e in a.selection.groups){var n=a.selection.groups[e];for(var t in n){n[t].element.prop("checked",!1)}}})},w=function(){h(function(){for(var e in a.selection.groups){var n=a.selection.groups[e];for(var t in n){n[t].element.prop("checked",!0)}}})},C=function(){c(),b(),S()},b=function(){l={state:{}}},S=function(){a={length:o,lessons:s,selection:{selectedCount:0,groups:{},groupMappings:{}}};var e=m(o,s);l.state.hasOwnProperty(e)&&(a=l.state[e],w())},y=function(){if(a.length!==o||a.lessons!==s){var e=m(a.length,a.lessons);l.state[e]=a,k(),S()}};this.changeStateHandler=function(e,n){o=e,s=n,y()},this.changePageHandler=function(){c()},this.getCurrentSelection=function(){var e=[];for(var n in a.selection.groups)for(var t=a.selection.groups[n],r=0;r<t.length;r++)e.push(t[r].id);return e},this.init=C.bind(this),c(),b(),C()};window.langu.CalendarPicker=r;var a=function(t){var r=t,a=[],i=!1;this.addPageChangeListener=function(n){-1===e.inArray(n,a)&&a.push(n)},this.removePageChangeListener=function(n){var t=e.inArray(n,a);-1!==t&&a.splice(t,1)};var o=function(){for(var e=0;e<a.length;e++)a[e]()},s=function(a){if(a.preventDefault(),!i){i=!0;var s,l=e(this),c=r.find(".slots-picker-page.active"),d=l.data("dir");switch(d){case"prev":s=c.prev(".slots-picker-page");break;case"next":s=c.next(".slots-picker-page")}if(c.hasClass("first")&&"prev"===d)return void(i=!1);if(s.length)return s.addClass("active"),c.removeClass("active"),o(),void(i=!1);s=null;var f={url:l.attr("href"),method:"GET",cache:!1};i=!0;var u=e.ajax(f);return u.always(function(){i=!1}),u.fail(function(e,n,t){c.addClass("first")}),u.done(function(r,a,i){switch(s=e(r),d){case"prev":s.insertBefore(c);break;case"next":s.insertAfter(c)}s.addClass("active"),c.removeClass("active"),new n(t),o()}),!1}};r.on("click",".calendar-page-switcher",s)};window.langu.CalendarPageSwitcher=a;var i=function(n,t,r){var a=n,i=n.attr("id"),s=t,l=r,c=function(n){for(var t=a.serializeArray(),r=1,c=1,d=[],f=0;f<t.length;f++){var u=t[f];u.name===i+"[length]"&&(r=parseInt(u.value)/30,r=r>0?r:1),d.push(u)}var h=a.find('input[name="'+i+'[service]"]:checked');h.length&&(c=parseInt(h.data("lessons"))*r);var p=s();if(p.length<r||p.length>c||p.length%r)return window.FLASHES.addFlash("error","slots.incorrect_amount"),!1;for(var f=0;f<p.length;f++)d.push({name:i+"[slots][]",value:p[f]});var g=e.ajax({url:a.attr("action"),method:a.attr("method"),data:d,dataType:"json",cache:!1});return g.fail(function(e){console.log(e.responseText)}),g.done(function(n,t,r){var i=r.responseJSON;if(i.error)switch(i.error.type){case"reservation":var s=e(i.calendar);calendarContainer.find(".slots-picker-page").remove(),calendarContainer.append(s),window.FLASHES.addFlash("error",i.error.message),l();break;case"slots":calendarContainer.find(".slots-picker-page").find('input[type="checkbox"]:checked').prop("checked",!1),window.FLASHES.addFlash("error",i.error.message),l();break;case"form":break;default:window.FLASHES.addFlash("error",i.error.message)}else if(i.form){var c=e(i.form);a.after(c),a.hide(),e("html, body").animate({scrollTop:a.closest(".sidebar").offset().top},450);var d=c.find("form.trial-confirm-form");d.length&&autosize(d.find("textarea")),o(c)}}),!1};a.on("submit",c)},o=function n(t){var r=t,a=function(n){var t=e(this),a=e.ajax({url:t.attr("href"),method:"GET",dataType:"json",cache:!1});return a.fail(function(e){console.log(e.responseText)}),a.done(function(n,t,a){var i=a.responseJSON;if(i.error)switch(i.error.type){case"generic":default:window.FLASHES.addFlash("error",i.error.message)}else if(i.form){var o=r.parent();o.html(i.form),e("html, body").animate({scrollTop:o.closest(".sidebar").offset().top},450),s()}}),!1},i=function(n){var t=e(this);if(t.hasClass("payment-method-form"))return!0;if(t.hasClass("trial-confirm-form")){var r=t.attr("id");if(0===t.find('input[name="'+r+'[terms]"]:checked').length)return n.stopImmediatePropagation(),window.FLASHES.addFlash("error","trial.booking.terms.not_checked"),!1;var a=t.find('textarea[name="'+r+'[message]"]'),i=a.attr("minlength"),o=a.attr("maxlength"),s=a.val().length;if(s<i)return n.stopImmediatePropagation(),window.FLASHES.addFlash("error","trial.booking.message.too_short"),!1;if(s>o)return n.stopImmediatePropagation(),window.FLASHES.addFlash("error","trial.booking.message.too_long"),!1}},o=function(t){var r=e(this),a=e.ajax({url:r.attr("action"),method:r.attr("method"),data:r.serializeArray(),dataType:"json",cache:!1});a.fail(function(e){console.log(e.responseText)}),a.done(function(t,a,i){var o=i.responseJSON;if(o.error)switch(o.error.type){case"generic":default:window.FLASHES.addFlash("error",o.error.message)}else if(o.form){var s=e(o.form);r.replaceWith(s),e("html, body").animate({scrollTop:s.closest(".sidebar").offset().top},450),n(s)}})};!function(){var e=t.find("form");e.on("submit",i),e.on("submit",o),e.on("click","a.back-button",a)}()},s=function(){var o=e(document.querySelector(".selection-form"));if(o.length){var s=o.find(".slots-picker").first();new n(s);var l=new t(o),c={selection:l.getCurrentState()},d=new r(s,c),f=new a(s);new i(o,d.getCurrentSelection,d.init);l.addStateChangeListener(d.changeStateHandler),l.addStateChangeListener(function(n,t){var r=e("#multi-lesson-info"),a=e("#slots-label-single"),i=e("#slots-label-multi");1===t?(r.addClass("hidden"),a.removeClass("hidden"),i.addClass("hidden")):(r.removeClass("hidden"),i.removeClass("hidden"),a.addClass("hidden"))}),f.addPageChangeListener(d.changePageHandler)}};s()}(jQuery);