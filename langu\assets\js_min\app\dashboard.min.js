!function(e){var n=function(){var n=e(this),a=n.data("id");e.blockUI();var o=e.ajax(n.attr("href"),{method:"POST",dataType:"json",data:{lessonId:a}});o.fail(function(n,a,o){if(e.unblockUI(),n.hasOwnProperty("responseJSON")&&null!==n.responseJSON){var t=n.responseJSON.payload.messageType||"error",l=n.responseJSON.message;FLASHES.addFlash(t,l)}}),o.done(function(e,n,a){document.location.reload()})},a=function(n,a){n.preventDefault();var o=e(this),t=o.serializeArray(),l=e.ajax(o.attr("action"),{method:"POST",data:t,block:{context:o}});l.fail(function(e,n,a){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var o=e.responseJSON.payload.messageType||"error",t=e.responseJSON.message;FLASHES.addFlash(o,t)}}),l.done(function(n,t,l){if("string"===e.type(n))o.replaceWith(e(n));else if(a.close(),null!==n){var c=n.payload.messageType||"success",s=n.message;FLASHES.addFlash(c,s)}})},o=function(){var n=e(this),o=e.ajax(n.attr("href"),{method:"GET",block:{context:null}});o.fail(function(e,n,a){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var o=e.responseJSON.payload.messageType||"error",t=e.responseJSON.message;FLASHES.addFlash(o,t)}}),o.done(function(n,o,c){var s=e(n),i=langu.modal.instance(s);i.open();var r=e(i.dialog);r.on("click",".add-objective",t),r.on("click",".collection-item-remove",l),r.on("submit","form",function(e){a.call(this,e,i)})})},t=function(n){n.preventDefault();var a=e(this),o="#"+a.data("collection-target"),t=e(o),l=t.children(".collection-item").length,c=t.data("prototype"),s=e(c.replace(/__name__/g,l+1));s.hide(),t.append(s),s.fadeIn(300)},l=function(n){n.preventDefault();var a=e(this),o=a.closest(".collection-item"),t=o.closest(".collection-container"),l=t.data("prototype");o.fadeOut(300).promise().done(function(){o.remove();for(var n=t.children(".collection-item"),a=0;a<n.length;a++){var c=e(n.get(a)),s=e(l.replace(/__name__/g,a+1));s.children(".form-control").val(c.children(".form-control").val()),c.replaceWith(s)}})};e(".lesson-action").on("click",function(a){a.preventDefault();var t=e(this);switch(t.data("action")){case"cancel":var l=t.data("type");langu.bbConfirmation.instance("lesson."+l+".cancel.confirmation.title","lesson."+l+".cancel.confirmation.content",function(e){!0===e&&n.call(t)},{confirm:"lesson."+l+".cancel.confirmation.confirm",cancel:"lesson."+l+".cancel.confirmation.cancel"});break;case"resources.manage":o.call(t)}});var c={load:function(n){e.get(n).done(c.show).error(function(){})},show:function(n){var a=e("#studentInfo");a.html(""),a.append(n),e("#studentInfoBox").addClass("langu-modal--active")}};e(".student-info").on("click",function(n){n.preventDefault(),c.load(e(this).data("info"))}),e(document).mouseup(function(n){var a=e("#studentInfo");a.length&&(a.is(n.target)||0!==a.has(n.target).length||e(".modal-close").click())});var s=e(".info"),i=function(n){n.preventDefault(),e("#w-dialog").find("#lesson_id").val(e(this).attr("lesson-id"));var a=e("#w-dialog").clone();langu.modal.instance(a).open(),e("body").find(".langu-modal-dialog").width("25%")};s.on("click",".gcalendar-btn",i)}(jQuery);