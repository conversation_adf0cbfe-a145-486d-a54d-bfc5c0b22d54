!function(e,t,n,s){var a=e(".lang-homepage-search");if(a.length){e(".start-now").on("langu.focusSelect",function(){a[0].selectize.focus()}),a.selectize({maxOptions:5,maxItems:1,onInitialize:function(){var t=this;this.setValue([]),this.focus(),this.on("item_add",function(n,s){t.isFull()&&e("#search-icon-button").trigger("click")})},onBlur:function(){0===this.items.length&&this.currentResults.total>0&&this.currentResults.query.length>0&&this.setValue([this.currentResults.items[0].id])},openOnFocus:!1});var i=!1,r=e(".toggle-on-search"),o=null,c={language:function(){var t=e(this),n=t.find("#appbundle_homepage_search_form_language");r.hide();var s=n[0].selectize,a=parseInt(s.items[0]);if(a&&s.options.hasOwnProperty(a)){var i=s.options[a],c=e('.form-step[data-step="learning"]'),l=c.find(".picked-language");null===o&&(o=l.html());var u=o.replace("%entered_language%",'<span class="emp">'+i.text+"</span>");l.html(u),t.removeClass("current"),c.addClass("current")}else{var s=n[0].selectize;s.$wrapper.tooltip({placement:"auto",title:"Pick the language first!",trigger:"manual"});s.$wrapper.tooltip("show")}},learning:function(t){var n=e(this),s=e(t);n.removeClass("current");var a=s.closest(".home-search-purpose").clone();a.find("label, input").remove();var i=e('.form-step[data-step="proficiency"]');i.find(".selected-purpose").replaceWith(a),i.addClass("current")},proficiency:function(){var t=e(this);i||(i=!0,t.closest("form").submit())}},l={learning:function(){var t=e(this),n=e('.form-step[data-step="language"]');t.closest("second-form-front").removeClass("animate"),t.removeClass("current"),n.addClass("current"),r.show(),a[0].selectize.focus()},proficiency:function(){}};e('[data-action="continue"]').on("click change",function(){var t=e(this),n=t.closest(".form-step"),s=n.data("step");c[s].call(n,t)}),e('[data-action="back"]').on("click change",function(){var t=e(this),n=t.closest(".form-step"),s=n.data("step");l[s].call(n,t)})}}(jQuery,window,document);