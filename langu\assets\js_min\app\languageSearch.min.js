!function(s){var e=s("select.selectize.lang-search");s.each(e,function(e,t){var t=s(t),i={maxItems:1,onBlur:function(){0===this.items.length&&this.currentResults.total>0&&this.currentResults.query.length>0&&this.setValue([this.currentResults.items[0].id])},openOnFocus:!0};if(t.hasClass("multiple")){var n=t.data("max-items");i.maxItems=n}t.hasClass("no-dropdown")&&(i.openOnFocus=!1),t.hasClass("disable-on-init")&&(i.onInitialize=function(){this.disable(),this.$wrapper.removeClass("disable-on-init"),t.removeClass("disable-on-init")}),t.selectize(i)})}(jQuery);