!function(e){var t=e("select.selectize.selectize-length-selector");if(t.length){var l=t.parent().closest(".prices-panel-body"),s=l.find(".table tbody tr"),i=function(e){null!==(e=parseInt(e)||null)&&0!==this.items.length||(e=this.currentResults.total>0&&this.currentResults.query.length>0?this.currentResults.items[0].id:60,this.setValue([e])),s.filter(".visible").removeClass("visible"),s.filter('[data-length="'+e+'"]').addClass("visible")};t.selectize({maxItems:1,hideSelected:!0,openOnFocus:!0,onInitialize:function(){},onChange:function(e){i.call(this,e)}})}}(jQuery);