!function(e,r){var a=(e(".categories-list"),e(".files-list")),o=function(r){r.preventDefault();var a=e(this),o=a.closest(".files-list-item"),n=a.data("resource-id");o.fadeTo(200,.8),a.closest(".uploader-container").block();var s=e.ajax(a.attr("href"),{dataType:"json",data:{resourceId:n},method:"DELETE"});s.fail(function(e,r,n){if(a.closest(".uploader-container").unblock(),o.fadeTo(200,1),e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var s=e.responseJSON.payload.messageType||"error",t=e.responseJSON.message;FLASHES.addFlash(s,t)}}),s.done(function(e,r,a){location.reload()})};a.on("click",".files-list-item-remove",o);var n=e(".category-panel [data-uploader]");e.each(n,function(r,a){var o=e(a);o.on("click",function(e){e.preventDefault()});var n=o.data("uploader"),s=o.data("uploader-url"),t=o.closest(".uploader-container");switch(n){case"dropzone":o.dropzone({url:s,maxFilesize:o.data("uploader-filesize"),previewTemplate:'<span class="hidden"></span>',init:function(){this.on("sending",function(e,r,a){t.block()}),this.on("success",function(e,r){location.reload()}),this.on("error",function(r,a,o){void 0!==o?a.message?FLASHES.addFlash("error",Translator.trans(a.message)):a.error?FLASHES.addFlash("error",Translator.trans(a.error)):FLASHES.addFlash("error",Translator.trans("upload.error.generic")):"string"===e.type(a)&&FLASHES.addFlash("error",Translator.trans(a)),t.unblock()}),this.on("complete",function(e){this.removeFile(e),t.unblock()})}});break;default:throw new Error("wrong uploader provider")}})}(jQuery);