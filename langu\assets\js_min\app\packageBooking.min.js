!function(a){var e=window.langu,r=function(e){var r=a(this),n=r.attr("id"),t=r.data("calendarPicker"),o=t.getCurrentSelection(),l=r.serializeArray(),s=parseInt(r.data("length"))/30,i=parseInt(r.data("lessons"))*s;if(o.length<s||o.length>i||o.length%s)return window.FLASHES.addFlash("error","slots.incorrect_amount"),!1;for(var d=0;d<o.length;d++)l.push({name:n+"[slots][]",value:o[d]});var c=a.ajax({url:r.attr("action"),method:r.attr("method"),data:l,dataType:"json",cache:!1});c.fail(function(a){console.log(a.responseText)}),c.done(function(a,e,n){var t=n.responseJSON;t.error?(t.error.type,window.FLASHES.addFlash("error",t.error.message)):t.partial&&r.parent().html(t.partial)}),e.preventDefault()},n=function(){var n=a(this);return a.ajax({url:n.attr("href")}).done(function(n){if(n.error);else if(n.form){var t=a(n.form);console.log(t),console.log(t.html());var o=e.modal.instance(t,"langu-modal-dialog-transparent");o.open();var l=a(o.dialog);l.on("submit","form",r);var s=l.find("form"),i=s.find(".slots-picker").first();e.CalendarScroller(i);var d=new e.CalendarPageSwitcher(i),c={selection:{length:s.data("length"),lessons:s.data("lessons")}},g=new e.CalendarPicker(i,c);s.data("calendarPicker",g),d.addPageChangeListener(g.changePageHandler)}}),!1};a(".package-booking").on("click",n)}(jQuery);