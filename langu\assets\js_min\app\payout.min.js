!function(e){var a=e(".sidebar-inner");if(a.length){var s=function(s){var n=e(this),r=n.serializeArray(),t=e.ajax(n.attr("action"),{method:"POST",data:r,block:{context:n}});t.fail(function(e,a,s){if(void 0!==e.responseJSON&&null!==e.responseJSON){var n=e.responseJSON.hasOwnProperty("payload")?e.responseJSON.payload.messageType||"error":"error",r=e.responseJSON.message;FLASHES.addFlash(n,r)}}),t.done(function(r,t,o){if("string"===e.type(r))n.replaceWith(e(r)),n.find(".selectize-init").selectize({maxItems:1});else if(s.close(),void 0!==o.responseJSON&&null!==o.responseJSON){var i=null,l="success";if(o.responseJSON.hasOwnProperty("payload")){var p=o.responseJSON.payload;if(p.hasOwnProperty("messageType")&&null!==p.messageType&&(l=p.messageType),p.hasOwnProperty("partial")&&null!==p.partial){i=e(p.partial);var c=a.children(".payments-sidebar");c.length&&c.replaceWith(i)}}var d=o.responseJSON.message;FLASHES.addFlash(l,d)}})},n=function(a){var n=e(this),r=e(a.dialog),t=e.ajax(n.attr("href"),{method:"GET",block:{context:n.closest(".withdraw-panel-body")}});t.fail(function(e,a,s){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var n=e.responseJSON.payload.messageType||"error",r=e.responseJSON.message;FLASHES.addFlash(n,r)}}),t.done(function(n,t,o){var i=e(n);r.html(i),r.find(".selectize-init").selectize({maxItems:1}),e(a.dialog).on("submit","form",function(e){e.preventDefault(),s.call(this,a)})})},r=function(a){a.preventDefault();var s=e("#withdraw-dialog").clone(),r=langu.modal.instance(s);r.open(),e(r.dialog).on("click",".withdraw-account-type",function(e){e.preventDefault(),n.call(this,r)})};a.on("click",".withdraw-btn",r)}}(jQuery);