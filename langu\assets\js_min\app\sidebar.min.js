!function(o,s,a,t){var e=o("#forgotPasswordBox"),n=o("#loginBox"),i=function(s){var a=o(s.target);a.parents("div#loginBox").length||a.parents("div#forgotPasswordBox").length||(n.removeClass("active"),o(this).off(s))};o(s).on("click",".sidebar-toggle",function(a){a.preventDefault(),a.stopImmediatePropagation(),n.hasClass("active")?(n.removeClass("active"),o(s).off("click",i),o(s.body).removeClass("no-overflow")):(n.addClass("active"),o(s).on("click",i),o(s.body).addClass("no-overflow"))}),o(".in-button").on("click",function(s){s.preventDefault(),o(this).closest(".sidebar").toggleClass("in")}),o(".login-sidebar__forgot").click(function(o){e.addClass("langu-modal--active")}),o("#formForgotPassword").submit(function(s){var a=o("#errorForgotPassword"),t=o("#successForgotPassword");a.addClass("hidden"),t.addClass("hidden"),s.preventDefault(),s.stopPropagation(),o.ajax({async:!1,cache:!1,dataType:"text",data:{email:o("#inputForgotPassword").val()},type:"POST",url:"/user/forgot",success:function(o){t.removeClass("hidden")},error:function(o){422===o.status&&a.removeClass("hidden")}})});var r=o("#login_form_username"),l=o("#login_form_password");r.focus(function(){o(this).parent().removeClass("login-sidebar__form-input--email")}),l.focus(function(){o(this).parent().removeClass("login-sidebar__form-input--password")}),r.focusout(function(){o(this).val()||o(this).parent().addClass("login-sidebar__form-input--email")}),l.focusout(function(){o(this).val()||o(this).parent().addClass("login-sidebar__form-input--password")})}(jQuery,document,window);