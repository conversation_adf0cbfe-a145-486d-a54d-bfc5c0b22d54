!function(e,a,i,n){var t=e(".full-search-form");if(0!==t.length){t.on("click",".form-container-toggle",function(a){a.preventDefault();var i=e(this),n=i.closest(".form-container");if(n.hasClass("active"))n.removeClass("active"),n.children("a").attr("aria-expanded","false");else{var t=n.siblings(".active");t.removeClass("active"),t.children("a").attr("aria-expanded","false"),n.addClass("active"),n.children("a").attr("aria-expanded","true")}});var r=e("#appbundle_homepage_search_form_teacherPreference_panel"),d=r.find(".match-languages-container"),s=d.find("select.selectize");r.on("change",'input[type="radio"]',function(a){var i=e(this),n=i.val();switch(parseInt(n)){case 0:case 1:for(var t=0;t<s.length;t++){var r=s[t].selectize;null!==r&&r.disable()}d.addClass("hidden");break;case 2:for(var t=0;t<s.length;t++){var r=s[t].selectize;null!==r&&r.enable()}d.removeClass("hidden")}});var o=e("#appbundle_homepage_search_form_motivation_panel"),l=o.find(".speciality-fields"),c=l.find(".speciality-radio-buttons").find(".radio-button-container"),f=c.first().find('input[type="radio"]').attr("name"),u=function(a){if(a>0){for(var i=0;i<c.length;i++){var n=e(c[i]),t=n.find('input[type="radio"]');parseInt(t.data("motivation"))!==a?(t.is(":checked")&&(l.find("#speciality-default")[0].checked=!0),n.addClass("hidden")):t.parent().removeClass("hidden")}l.removeClass("hidden")}else l.addClass("hidden"),l.find("#speciality-default")[0].checked=!0},p=o.find(".motivation-fields, .motivation-radio-buttons");p.on("change",'input[type="radio"]',function(){var a=e(this),i=parseInt(a.val());u(i)});var h=e("#appbundle_homepage_search_form_language_panel"),v=h.find("select.selectize"),m=v[0].selectize,g=m.items[0]||0,b=function(a){var i=a.specialities;l.find(".speciality-radio-buttons").html(i),c=l.find(".speciality-radio-buttons").find(".radio-button-container"),e.each(c,function(){e(this).find("input").attr("name",f)});var n=parseInt(p.find("input:checked").val());u(n)},_={};m.on("blur",function(){var a=this.items[0]||0;if(a!==g){if(g=a,_.hasOwnProperty(g))return void b(_[g]);var i=l.find(".speciality-radio-buttons").data("url").replace("/languageId",g?"/"+g:""),n=e.ajax({url:i,dateType:"json"});n.done(function(e){_[g]=e,b(e)}),n.fail(function(){})}}),e(function(){var e=t.find(".form-containers").children(".form-container").first();e.addClass("active"),e.children("a").attr("aria-expanded","true")})}}(jQuery,window,document);