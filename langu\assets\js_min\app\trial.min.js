!function(e){var a=function(a,n){a.preventDefault();var o=e(this),s=o.serializeArray(),r=e.ajax(o.attr("action"),{method:"POST",data:s,block:{context:o}});r.fail(function(e,a,n){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var o=e.responseJSON.payload.messageType||"error",s=e.responseJSON.message;FLASHES.addFlash(o,s)}}),r.done(function(a,s,r){if("string"===e.type(a))o.closest(".langu-modal-dialog-content").html(e(a));else if(n.close(),null!==a){var t=a.payload.messageType||"success",l=a.message;FLASHES.addFlash(t,l)}})},n=function(a){a.preventDefault();var n=e(this),o=n.closest(".langu-modal-dialog-content"),s=e.ajax(n.attr("href"),{method:"GET",block:{context:o}});s.fail(function(e,a,n){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var o=e.responseJSON.payload.messageType||"error",s=e.responseJSON.message;FLASHES.addFlash(o,s)}}),s.done(function(a,n,s){var r=e(a);o.html(r)})},o=function(o){o.preventDefault();var s=e(this);if(!s.hasClass("sidebar-toggle")){var r=e.ajax(s.attr("href"),{method:"GET",block:{context:null}});r.fail(function(e,a,n){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var o=e.responseJSON.payload.messageType||"error",s=e.responseJSON.message;FLASHES.addFlash(o,s)}}),r.done(function(o,s,r){var t=e(o),l=langu.modal.instance(t);l.open();var i=e(l.dialog);i.on("click",".arrow-row-slide",window.slidePicker),i.on("click",".pagination a",n),i.on("submit","form",function(e){a.call(this,e,l)})})}};e(".free-trial-booking").on("click",o)}(jQuery);