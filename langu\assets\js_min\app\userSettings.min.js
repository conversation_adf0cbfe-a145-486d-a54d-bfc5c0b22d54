!function(e){var a=e(".settings-panel [data-uploader]");e.each(a,function(a,n){var t=e(n),o=t.closest(".uploader-container"),r=t.data("uploader"),s=t.data("uploader-url"),l=t.data("uploader-type");switch(r){case"dropzone":t.dropzone({url:s,previewTemplate:'<span class="hidden"></span>',filesizeBase:1024,maxFilesize:"image"===l?8:2,init:function(){this.on("sending",function(e,a,n){var r=t.data("image-filter")||!1;!1!==r&&n.append("filter_name",r),o.block()}),this.on("success",function(e,a){if("file"===l){var n=o.find(".uploader-download"),r=o.find(".uploader-empty"),s=o.find(".uploader-input");n.length&&(n.attr("href",a.url),n.removeClass("hidden")),s.length&&s.val(a.url),r.length&&r.remove()}else"image"===l&&t.attr("src",a.displayUrl?a.displayUrl:a.url)}),this.on("error",function(a,n,t){void 0!==t?n.message?FLASHES.addFlash("error",Translator.trans(n.message)):n.error?FLASHES.addFlash("error","Please reload page"):FLASHES.addFlash("error",Translator.trans("upload.error.generic")):"string"===e.type(n)&&FLASHES.addFlash("error",Translator.trans(n)),o.unblock()}),this.on("complete",function(e){this.removeFile(e),o.unblock()})}});break;default:throw new Error("Wrong uploader type")}});var n=function(a,n){a.preventDefault();var t=e(this),o=new FormData(this),r=e.ajax(t.attr("action"),{method:"POST",data:o,dataType:"json",contentType:!1,processData:!1,block:{context:t}});r.fail(function(e,a,n){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var t=e.responseJSON.payload.messageType||"error",o=e.responseJSON.message;FLASHES.addFlash(t,o)}}),r.done(function(a,o,r){if("string"===e.type(a))t.replaceWith(e(a));else{var s=n.row,l=e(a.payload.partial);if(null!==s?s.replaceWith(l):e(".teaching-qualifications-container").find("tbody").append(l),n.close(),null!==a){var i=a.payload.messageType||"success",d=a.message;FLASHES.addFlash(i,d)}}})};e(".teaching-qualifications-container").on("click",".qualification-panel-action",function(a){a.preventDefault();var t=e(this);switch(t.data("action")||"edit"){case"edit":var o=e.ajax(t.attr("href"),{method:"GET",block:{context:null}});o.fail(function(e,a,n){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var t=e.responseJSON.payload.messageType||"error",o=e.responseJSON.message;FLASHES.addFlash(t,o)}}),o.done(function(a,o,r){var s=e(a),l=langu.modal.instance(s,"langu-modal-dialog langu-modal-dialog-short");l.row=t.closest("tr"),l.open(),e(l.dialog).on("submit","form",function(e){n.call(this,e,l)})});break;case"delete":var r=t.data("qualification-id"),o=e.ajax(t.attr("href"),{dataType:"json",data:{qualificationId:r},method:"DELETE",block:{context:t.closest(".teaching-qualifications-container")}});o.fail(function(e,a,n){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var t=e.responseJSON.payload.messageType||"error",o=e.responseJSON.message;FLASHES.addFlash(t,o)}}),o.done(function(e,a,n){if(t.closest("tr").fadeOut(300),null!==e){var o=e.payload.messageType||"success",r=e.message;FLASHES.addFlash(o,r)}});break;case"create":var o=e.ajax(t.attr("href"),{method:"GET",block:{context:null}});o.fail(function(e,a,n){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var t=e.responseJSON.payload.messageType||"error",o=e.responseJSON.message;FLASHES.addFlash(t,o)}}),o.done(function(a,t,o){var r=e(a),s=langu.modal.instance(r);s.row=null,s.open(),e(s.dialog).on("submit","form",function(e){n.call(this,e,s)})});break;default:throw new Error("wrong action type")}}),e(".options-section-header").on("click",function(){e(".options-section-header.active").removeClass("active");var a=e(this);a.addClass("active");var n=a.next().find("textarea[data-autosize], textarea.autosize");autosize.update(n)});var t=e(".paid-trial-price");e(".free-trial-radio").on("change",function(){var a=e(this);2===parseInt(a.val())?(t[0].disabled=!1,t[0].focus()):t[0].disabled=!0});var o=function(e){var a=document.getElementById("specialities-selectable"),n=document.getElementById("specialities-selected");dragula([a,n],{revertOnSpill:!0,moves:function(e,t,o,r){return t!==a||n.childElementCount<8}})},r=function(a,n){a.preventDefault();var t=e(this),r=t.serializeArray(),s=e.ajax(t.attr("action"),{method:"POST",data:r,block:{context:t}});s.fail(function(e,a,n){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var t=e.responseJSON.payload.messageType||"error",o=e.responseJSON.message;FLASHES.addFlash(t,o)}}),s.done(function(a,r,s){if("string"===e.type(a)){var l=e(a);t.replaceWith(l),o(n.opts.content)}else if(n.close(),null!==a){var i=a.payload.messageType||"success",d=a.message;FLASHES.addFlash(i,d)}})},s=function(a){a.preventDefault();var n=e(this),t=e.ajax(n.attr("href"),{method:"GET",block:{context:null}});t.fail(function(e,a,n){if(e.hasOwnProperty("responseJSON")&&null!==e.responseJSON){var t=e.responseJSON.payload.messageType||"error",o=e.responseJSON.message;FLASHES.addFlash(t,o)}}),t.done(function(a,n,t){var s=e(a),l=langu.modal.instance(s,"langu-modal-dialog langu-modal-dialog-short",{afterOpen:function(){o(l.opts.content)}});l.open(),e(l.dialog).on("submit","form",function(e){r.call(this,e,l)})})};e(".specialities-manager").on("click",s)}(jQuery);