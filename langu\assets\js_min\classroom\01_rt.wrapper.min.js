!function(e,r){void 0===Array.isArray&&(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),void 0===Object.isObject&&(Object.isObject=function(e){return!Array.isArray(e)&&e===Object(e)}),void 0===String.isString&&(String.isString=function(e){return"[object String]"===Object.prototype.toString.call(e)}),e.rt={},rt.RealtimeWrapper=function(e,r){this.init(e,r)},rt.RealtimeWrapper.prototype={clientId:null,mimeType:"application/vnd.google-apps.drive-sdk",accessToken:null,init:function(e,r){if(void 0===e)throw new Error("No Client ID specified");if(!String.isString(e))throw new Error("Given Client ID is not a string");this.clientId=e,this.initOptions(r),this.bind()},start:function(r){var t=this;e.gapi.load("auth2,auth:client,drive-realtime,drive-share",{callback:function(){e.gapi.auth.setToken(t.accessToken),e.gapi.auth.token=t.accessToken,t.tokenRefresh(r)}})},initOptions:function(e){if(e){if(e.hasOwnProperty("accessToken")){if(!Object.isObject(e.accessToken))throw new Error("Access token expects to be an Associative Array (Object)");this.accessToken=e.accessToken}if(e.hasOwnProperty("mimeType")){if(!String.isString(e.mimeType))throw new Error("Mime type expects to be a string");this.mimeType=e.mimeType}if(!e.hasOwnProperty("userId"))throw new Error("Required option userId has not been set");if(!String.isString(e.userId))throw new Error("Option userId expects to be a string");if(this.userId=e.userId,!e.hasOwnProperty("scope"))throw new Error("Required option scope has not been set");if(String.isString(e.scope)){var r=e.scope.split(" ");this.scope=r}else{if(!Array.isArray(e.scope))throw new Error("Option scope must be an array or a string");this.scope=e.scope}}},bind:function(){this.onError=this.onError.bind(this)},getParam:function(r){var t=new RegExp(r+"=(.*?)($|&)","g"),i=e.location.search.match(t);return i&&i.length?(i=i[0],i.replace(r+"=","").replace("&","")):null},onError:function(r){if(r.type==e.gapi.drive.realtime.ErrorType.TOKEN_REFRESH_REQUIRED){var t=this;this.tokenRefresh(function(){console.log("Error, auth refreshed");var e=t.lastCall;null!==e&&(this[e.method].apply(this,e.arguments),this.lastCall=null)})}else r.type==e.gapi.drive.realtime.ErrorType.CLIENT_ERROR?alert("An Error happened: "+r.message):r.type==e.gapi.drive.realtime.ErrorType.NOT_FOUND?alert("The file was not found. It does not exist or you do not have read access to the file."):r.type==e.gapi.drive.realtime.ErrorType.FORBIDDEN&&alert("You do not have access to this file. Try having the owner shareit with you from Google Drive.")},create:function(r,t,i){var n=this;e.gapi.client.load("drive","v2",function(){var o={resource:{mimeType:n.mimeType,title:r}};e.gapi.client.drive.files.insert(o).execute(function(r){if(r.error)throw new Error(r.error.message);var n={value:t,type:"user",role:"writer"};e.gapi.client.drive.permissions.insert({fileId:r.id,sendNotificationEmails:!1,resource:n}).execute(function(e){if(e.error)throw new Error(e.error.message);i(r)})})})},attachHandler:function(e,r,t){if(Array.isArray(r))for(var i in r)e.addEventListener(r[i],t);else e.addEventListener(r,t)},load:function(r,t,i){this.lastCall={method:"load",arguments:[r,t,i]},e.gapi.drive.realtime.load(r,function(e){t(e)},i,this.onError)},tokenRefresh:function(r){var t={authuser:-1,immediate:!0,client_id:this.clientId,user_id:this.userId,scope:this.scope};e.gapi.auth.authorize(t,function(e){r(e)})}}}(window);