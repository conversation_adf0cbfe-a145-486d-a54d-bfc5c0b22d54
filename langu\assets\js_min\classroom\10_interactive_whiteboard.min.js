function update(){null!=cursor&&cursor.update(),void 0!=window.othercursor&&null!=window.othercursor&&window.othercursor.update(),null!=toolbar&&toolbar.update()}function draw(o){o.fillStyle="white",o.clearRect(0,0,canvas.width,canvas.height),cursor.pre_draw(o,cursor.offset),void 0!=window.othercursor&&null!=window.othercursor&&window.othercursor.draw(o,toolbar,"teacher"!=role,cursor.offset),null!=cursor&&cursor.draw(o,toolbar,"teacher"==role,cursor.offset)}function gameloop(){update(),draw(ctx)}function handle_mousedown(o){var e=$("#whiteboard_canvas").offset(),r=o.pageX-e.left,t=o.pageY-e.top,n=!1;null!=toolbar&&(toolbar.mousedown(r,t)&&(n=!0),cursor.selected=toolbar.cur_selected),0==n&&cursor.mousedown(r,t)}function handle_mouseup(o){var e=$("#whiteboard_canvas").offset(),r=o.pageX-e.left,t=o.pageY-e.top;cursor.mouseup(r,t),null!=toolbar&&toolbar.mouseup(r,t)}function handle_mousemove(o){var e=$("#whiteboard_canvas").offset(),r=o.pageX-e.left,t=o.pageY-e.top;cursor.mousemove(r,t),null!=toolbar&&toolbar.mousemove(r,t)}function setup_summernote(){return}jQuery;var FPS=30,SECONDSBETWEENFRAMES=1/FPS,canvas=null,ctx=null,cursor=null,toolbar=null,role=void 0;$(document).ready(function(){canvas=document.getElementById("whiteboard_canvas"),canvas.setAttribute("height",$(window).height()),canvas.setAttribute("width",$(window).width()),ctx=canvas.getContext("2d"),role=window.langu_role,cursor="teacher"==role?new Cursor("#7FB802",!0):new Cursor("#3C87F8",!1),toolbar=new Toolbar,$(document).keyup(onKeyUp),console.info("Setting up listeners"),$("#whiteboard_canvas").mousemove(function(o){handle_mousemove(o)}),$("#whiteboard_canvas").mouseleave(function(o){cursor.disabled=!0,"student"==role?$("body").addClass("cursor-pointer-student"):$("body").addClass("cursor-pointer-teacher")}),$("#whiteboard_canvas").mouseenter(function(o){cursor.disabled=!1,"student"==role?$("body").removeClass("cursor-pointer-student"):$("body").removeClass("cursor-pointer-teacher")}),$("#video-load-btn").on("click",function(o){cursor.selected="VIDEO",console.log("whiteboard clcik",cursor.selected)}),$("#whiteboard_canvas").on("mousedown",function(o){handle_mousedown(o)}),$("#whiteboard_canvas").on("mouseup",function(o){handle_mouseup(o)}),$(document).keydown(function(o){cursor.keydown(o.key)}),setInterval(gameloop,1e3*SECONDSBETWEENFRAMES)});