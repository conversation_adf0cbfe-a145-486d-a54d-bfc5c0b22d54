function VerticalButton(t,i,e,s){this.type=t,this.loc=new Location(i,e),this.size=new Location(35,40),this.index=s,this.draw=vertical_button_draw,this.update=button_update,this.mousemove=button_mousemove,this.mousedown=button_mousedown,this.hittest=button_hittest,this.mouseup=button_mouseup,this.is_highlighted=!1}function Button(t,i,e,s,o){this.type=t,this.loc=new Location(i+5,e),this.size=new Location(35,40),this.index=s,this.draw=button_draw,this.update=button_update,this.imageloaded=!1,this.image_additional=null,this.verticalButtons=o||[],this.image=new Image,"VIDEO"==t&&(this.image.src="/images/classroom/play.svg"),this.image.onload=function(){toolbar.buttons[s].imageloaded=!0},this.mousemove=button_mousemove,this.mousedown=button_mousedown,this.hittest=button_hittest,this.mouseup=button_mouseup,this.drawVerticalButtons=drawVerticalButtons,this.is_highlighted=!1}function button_draw(t){var i=t.strokeStyle,e=t.fillStyle;this.is_highlighted?t.fillStyle="#eeeeee":t.fillStyle="#ffffff",t.fillRect(this.loc.x,this.loc.y,this.size.x,this.size.y),t.lineWidth=2,this.imageloaded&&t.drawImage(this.image,this.loc.x+5,this.loc.y+9),null!=this.image_additional&&this.additional_imageloaded&&t.drawImage(this.image_additional,this.loc.x+15,this.loc.y+15),"LINE"==this.type?(t.font="25px BBB",t.textBaseline="top",t.fillStyle="black",t.fillText("",this.loc.x+5,this.loc.y****)):"5SLINE"==this.type?(t.font="25px BBB",t.textBaseline="top",t.fillStyle="black",t.fillText("",this.loc.x+5,this.loc.y****),t.font="15px Georgia",t.fillText("3",this.loc.x+22,this.loc.y+20)):"TEXT"==this.type?(t.fillStyle="black",t.font="40px Georgia",t.beginPath(),t.fillText("T",this.loc.x+5,this.loc.y+this.size.y-5),t.stroke(),t.closePath()):"CLEAR"==this.type?(t.font="25px BBB",t.textBaseline="top",t.fillStyle="black",t.fillText("",this.loc.x+5,this.loc.y****)):"REFRESH"==this.type?(t.strokeStyle="black",t.lineWidth=5,t.beginPath(),t.moveTo(this.loc.x+this.size.x/2-4,this.loc.y+this.size.y/2),t.lineTo(this.loc.x+this.size.x-15,this.loc.y+10),t.lineTo(this.loc.x+this.size.x/2-7,this.loc.y+this.size.y/2-17),t.stroke(),t.closePath(),t.beginPath(),t.arc(this.loc.x+this.size.x/2,this.loc.y+this.size.y/2,10,0,1.5*Math.PI),t.stroke(),t.closePath()):"POINTER"==this.type?(t.font="25px BBB",t.textBaseline="top",t.fillStyle="black",t.fillText("",this.loc.x+5,this.loc.y****)):this.type,t.strokeStyle=i,t.fillStyle=e}function button_update(){}function button_mousedown(t,i){return!!this.hittest(t,i)}function button_mouseup(t,i){}function button_click(t,i){}function button_mousemove(t,i){this.hittest(t,i)?(toolbar.dataToSend=!0,toolbar.isHovered=!0,toolbar.hoverColor="teacher"==window.langu_role?"#7FB802":"#3C87F8",this.is_highlighted=!0):this.is_highlighted=!1}function button_hittest(t,i){return t>this.loc.x&&t<this.loc.x+this.size.x&&i>this.loc.y&&i<this.loc.y+this.size.y}function drawVerticalButtons(){for(var t=document.getElementById("whiteboard_canvas"),i=t.getContext("2d"),e=0;e<this.verticalButtons.length;e++)this.verticalButtons[e].draw(i)}function vertical_button_draw(t){var i=t.strokeStyle,e=t.fillStyle;this.is_highlighted?t.fillStyle="#eeeeee":t.fillStyle="#ffffff",t.fillRect(this.loc.x,this.loc.y,this.size.x,this.size.y),t.lineWidth=2,"VERTICAL_TEXT"==this.type&&(t.font="25px BBB",t.textBaseline="top",t.fillStyle="black",t.fillText("",this.loc.x+5,this.loc.y****)),t.strokeStyle=i,t.fillStyle=e}