function Cursor(s,t){this.update=cursor_update,this.draw=cursor_draw,this.pre_draw=cursor_pre_draw,this.mousedown=cursor_mousedown,this.mouseup=cursor_mouseup,this.mousemove=cursor_mousemove,this.keydown=cursor_keydown,this.keyup=cursor_keyup,this.loc=new Location(0,0),this.screen={width:0,height:0},this.attach_to_asset=null,this.ripples=[],this.selected="POINTER",this.drawing=!1,this.moving=!1,this.resizing=!1,this.start_loc=null,this.cur_selected_textbox_index=null,this.cur_asset=null,this.assets=[],this.cleared=!1,this.color=s,this.offset=0,this.lowest_point=0,this.isScrolling=!1,this.meta_pressed=!1,this.scroll=cursor_scroll,this.hint=null,this.refresh_lowest_point=refresh_cursor_lowest_point,this.setup=cursor_setup,this.has_heard_from_server=!1}function cursor_setup(){}function cursor_update(){if("VIDEO"===this.selected&&window.video_url&&(this.selected="POINTER",window.toolbar.cur_asset="POINTER",this.assets.push(new VideoItem(600,100,window.video_url)),delete window.video_url,this.assets[this.assets.length-1].is_master=!0,this.assets[this.assets.length-1].load_video()),"REFRESH"==this.selected&&0==this.cleared){for(var s=0;s<this.assets.length;s++)delete this.assets[s];this.assets=[],this.cleared=!0}"REFRESH"!=this.selected&&(this.cleared=!1);for(var s=0;s<this.ripples.length;s++){var t=this.ripples[s];t.update(),"DONE"==t.status&&(delete this.ripples[s],this.ripples.splice(s,1))}for(var s=0;s<this.assets.length;s++){this.assets[s].update()}for(var s=0;s<this.assets.length;s++)if("DEAD"==this.assets[s].status){if("videoitem"==this.assets[s].type){$("[id*="+this.assets[s].div_id+"]").remove();for(var e=0;e<this.assets[s].intervals.length;e++)clearInterval(this.assets[s].intervals[e])}delete this.assets[s],this.assets.splice(s,1)}}function cursor_pre_draw(s,t){this.assets.length>0&&this.assets[0].draw(s,t)}function cursor_draw(s,t,e,i){e&&(this.offset=i);var h=!1;if(this.isScrolling&&!e?(h=!0,this.scroll_fade=20):this.scroll_fade>0&&(this.scroll_fade-=1,h=!0),h){var r=this.scroll_fade/20;s.fillStyle="rgb(150,150,150, "+r+")";var o=window.innerWidth-20,a=this.lowest_point;void 0!=window.othercursor&&null!=window.othercursor&&a<window.othercursor.lowest_point&&(a=window.othercursor.lowest_point);var c=a+.8*$(window).height(),n=this.offset/c;s.fillRect(o,parseInt(($("#whiteboard_canvas").height()-40)*n),10,40)}s.lineWidth=1;for(var l=0;l<this.assets.length;l++){var _=this.assets[l];null!=_&&_.draw(s,this.offset)}null!=this.cur_asset&&this.cur_asset.draw(s,this.offset);for(var l=0;l<this.ripples.length;l++){this.ripples[l].draw(s,this.offset)}!0===this.disabled?$("#whiteboard_canvas").css({cursor:"none"}):"textboxresize"==this.hint?$("#whiteboard_canvas").css({cursor:"nw-resize"}):"textboxclear"==this.hint?$("#whiteboard_canvas").css({cursor:"pointer"}):"text"==this.hint?$("#whiteboard_canvas").css({cursor:"pointer"}):"dragbar"==this.hint?$("#whiteboard_canvas").css({cursor:"grab"}):"dragging"==this.hint?$("#whiteboard_canvas").css({cursor:"grabbing"}):$("#whiteboard_canvas").css({cursor:"pointer"}),this.isScrolling=!1}function cursor_mousedown(s,t){if(this.ripples.push(new Ripple(this.loc.x,this.loc.y+this.offset,this.color)),(new UpdateBuilder).type(UpdateBuilder.TEMPORARY).name(UpdateBuilder.RIPPLE).updateData({loc:{x:this.loc.x,y:this.loc.y+this.offset},color:this.color}).send(),this.selected,!0)for(var e=!1,i=0;i<this.assets.length;i++){var h=this.assets[i];if(!e&&"textbox"==h.type){var r=h.clicked(s,t,this.offset);r?(this.cur_selected_textbox_index=i,h.isSelected=!0,e=!0):h.isSelected=!1}if(!e&&"pdfitem"===h.type){var r=!1;if(h.hittestresize(s,t,this.offset))return r=!0,e=!0,this.resizing=!0,this.start_loc=new Location(s,t),void(this.cur_asset=this.assets[i]);if(h.clicked(s,t,this.offset))return r=!0,e=!0,this.start_loc=new Location(s,t),this.moving=!0,void(this.cur_asset=this.assets[i])}if(!e&&"imageitem"==h.type){var r=h.clicked(s,t,this.offset);r?e=!0:h.isSelected=!1}if(!e&&"videoitem"==h.type){var r=h.clicked(s,t,this.offset);e=r||e,r?e=!0:h.isSelected=!1}if(e||null==this.cur_selected_textbox_index||"textbox"!=this.assets[this.cur_selected_textbox_index].type||(this.assets[this.cur_selected_textbox_index].text=$("#text_summernote").val(),this.assets[this.cur_selected_textbox_index].changed=!0,this.assets[this.cur_selected_textbox_index].text_updated=!0,this.cur_selected_textbox_index=null,this.resizing=!1),h.hittest(s,t+this.offset))return $("#whiteboard_canvas").css({position:"relative","z-index":"99999"}),this.moving=!0,this.start_loc=new Location(s,t),void(this.cur_asset=this.assets[i]);"textbox"!=h.type&&"videoitem"!=h.type&&"imageitem"!=h.type||!h.hittestresize(s,t,this.offset)||(this.resizing=!0,h.start_resize(),this.start_loc=new Location(s,t),this.cur_asset=this.assets[i])}if("LINE"==this.selected)for(var e=!1,i=0;!e&&i<this.assets.length;i++){var h=this.assets[i];"textbox"!=h.type&&"imageitem"!=h.type&&"pdfitem"!=h.type||!h.bodyhittest(s,t,this.offset)?"videoitem"==h.type&&h.hittest(s,t+this.offset)?(this.selected="POINTER",this.moving=!0,this.start_loc=new Location(s,t),this.cur_asset=this.assets[i],e=!0):(this.drawing=!0,this.cur_asset=new Line(s,t+this.offset)):(e=!0,this.attach_to_asset=i,this.drawing=!0,this.cur_asset=new Line(s,t+this.offset))}else if("ERASER"==this.selected)for(var e=!1,i=0;!e&&i<this.assets.length;i++){var h=this.assets[i];"textbox"!=h.type&&"imageitem"!=h.type&&"pdfitem"!=h.type||!h.bodyhittest(s,t,this.offset)?"videoitem"==h.type&&h.hittest(s,t+this.offset)?(this.selected="POINTER",this.moving=!0,this.start_loc=new Location(s,t),this.cur_asset=this.assets[i],e=!0):(this.drawing=!0,this.cur_asset=new Line(s,t+this.offset),this.cur_asset.color="#ffffff",this.cur_asset.linesize=12):(e=!0,this.attach_to_asset=i,this.drawing=!0,this.cur_asset=new Line(s,t+this.offset))}else if("5SLINE"==this.selected){for(var i=0;!e&&i<this.assets.length;i++){var h=this.assets[i];"videoitem"==h.type&&h.bodyhittest(s,t,this.offset)&&(e=!0,this.attach_to_asset=i)}this.drawing=!0,this.cur_asset=new TempLine(s,t+this.offset),this.cur_asset.status="DRAWING"}else if("TEXT"==this.selected)this.drawing=!0,this.cur_asset=textbox=new TextBox(s,t+this.offset),this.cur_asset.status="DRAWING";else if("VIDEO"==this.selected){this.selected="POINTER",toolbar.cur_selected="POINTER";for(var o=!0,i=0;i<this.assets.length&&o;i++)if("videoitem"==this.assets[i].type&&!this.assets[i].url_set)return void(o=!1)}else if(-1!==this.selected.indexOf("SHAPE_")){this.resizing=!0,this.start_loc=new Location(s,t);var a=this.selected.replace("SHAPE_","");switch(a){case"SQUARE":this.cur_asset=new ShapeSquare(s,t+this.offset);break;case"CIRCLE":this.cur_asset=new ShapeCircle(s,t+this.offset);break;case"TRIANGLE":this.cur_asset=new ShapeTriangle(s,t+this.offset);break;case"LINE":this.cur_asset=new ShapeLine(s,t+this.offset);break;default:console.error("Undefined function called ["+a+"].")}}}function cursor_mouseup(s,t){if(!this.resizing||null==this.cur_asset||"videoitem"!=this.cur_asset.type&&"shape"!=this.cur_asset.parent?this.resizing&&null!=this.cur_asset&&"pdfitem"==this.cur_asset.type&&(this.cur_asset.resized=!0):this.cur_asset.finish_resize(),this.moving&&(this.selected="POINTER",$("#whiteboard_canvas").css({position:"static","z-index":"0"}),0==this.cur_asset.moved&&(this.cur_asset.moved=!0,this.cur_asset=null)),null!=this.cur_asset){if(this.cur_asset.status="COMPLETED","textbox"==this.cur_asset.type&&this.cur_asset.setup(),null==this.attach_to_asset?this.assets.push(this.cur_asset):void 0!=this.assets[this.attach_to_asset].add_asset?(this.assets[this.attach_to_asset].add_asset(this.cur_asset),this.attach_to_asset=null):console.log("ERROR: We are trying attach to an asset but the asset doesn't accept assets"),"line"===this.cur_asset.type)for(var e=0;e<this.cur_asset.points.length;e++)this.cur_asset.points[e].y>this.lowest_point&&(this.lowest_point=this.cur_asset.points[e].y);this.cur_asset=null,this.ripples.push(new Ripple(this.loc.x,this.loc.y+this.offset)),"TEXT"==this.selected&&(this.selected="POINTER",toolbar.cur_selected="POINTER"),"LINE"==this.selected&&(this.selected="LINE",toolbar.cur_selected="LINE")}this.drawing=!1,this.moving=!1,this.resizing=!1}function cursor_mousemove(s,t){if(this.loc.x=s,this.loc.y=t,this.moving){if(null==this.cur_asset)return;var e=s-this.start_loc.x,i=t-this.start_loc.y;return this.cur_asset.loc.x+=e,this.cur_asset.loc.y+=i,this.start_loc.x=s,this.start_loc.y=t,void(this.hint="dragging")}if(this.drawing)"LINE"==this.selected||"5SLINE"==this.selected||"ERASER"==this.selected?this.cur_asset.add_point(s,t+this.offset):"TEXT"==this.selected&&this.cur_asset.set_size(s,t+this.offset);else if(this.resizing){if(null==this.cur_asset)return void(this.resizing=!1);var e=s-this.start_loc.x,i=t-this.start_loc.y;this.cur_asset.resize(e,i),this.start_loc.x=s,this.start_loc.y=t}else{this.hint=null,toolbar.hitcheck(s,t)&&(this.hint="dragbar");for(var h=0;null==this.hint&&h<this.assets.length;h++)"videoitem"==this.assets[h].type?this.assets[h].hittestresize(s,t,this.offset)?this.hint="textboxresize":this.assets[h].hittest(s,t+this.offset)&&(this.hint="dragbar"):"imageitem"==this.assets[h].type?this.assets[h].hittestresize(s,t,this.offset)?this.hint="textboxresize":this.assets[h].hittest(s,t,this.offset)&&(this.hint="dragbar"):"textbox"==this.assets[h].type&&(this.assets[h].hittestresize(s,t,this.offset)?this.hint="textboxresize":this.assets[h].hittestclear(s,t,this.offset)?this.hint="textboxclear":this.assets[h].bodyhittest(s,t,this.offset)?this.hint="text":this.assets[h].hittest(s,t,this.offset)&&(this.hint="dragbar"))}}function cursor_keydown(s){if(null!=this.cur_selected_textbox_index&&void 0!=this.assets[this.cur_selected_textbox_index]&&"textbox"==this.assets[this.cur_selected_textbox_index].type)this.assets[this.cur_selected_textbox_index].keydown(s);else for(var t=0;t<this.assets.length;t++)"videoitem"==this.assets[t].type&&0==this.assets[t].url_set&&this.assets[t].keydown(s);return"r"!=s}function cursor_keyup(s){null!=this.cur_selected_textbox_index&&"textbox"==this.assets[this.cur_selected_textbox_index].type&&this.assets[this.cur_selected_textbox_index].keyup(s);for(var t=0;t<this.assets.length;t++)"videoitem"==this.assets[t].type&&0==this.assets[t].url_set&&this.assets[t].keyup(s);return"r"!=s}function refresh_cursor_lowest_point(){this.lowest_point=0;for(var s=0;s<this.assets.length;s++)if(void 0==this.assets[s].get_lowest_point||"function"!=typeof this.assets[s].get_lowest_point);else{var t=this.assets[s].get_lowest_point();t>this.lowest_point&&(this.lowest_point=t)}return this.lowest_point}function cursor_scroll(s){var t=$(window).height();if(1==this.moving)return!1;for(var e=0;e<this.assets.length;e++){var i=this.assets[e];if("textbox"==i.type&&i.bodyhittest(s.offsetX,s.offsetY,this.offset)&&i.scroll(s))return s.preventDefault(),!0}this.offset-=s.originalEvent.wheelDelta,$("#catch-pdf").css({left:this.left,top:-this.offset}),this.offset<0&&(this.offset=0);var h=this.lowest_point;return void 0!=window.othercursor&&null!=window.othercursor&&h<window.othercursor.lowest_point&&(h=window.othercursor.lowest_point),!(this.offset>h+.8*t)||(this.offset=h+.8*t,!1)}