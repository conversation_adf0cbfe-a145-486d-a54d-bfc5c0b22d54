function _typeof(e){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function buildData(e,a,o){if(Array.isArray(a)&&0===a.length)e.append(o,[]);else if(!a||"object"!==_typeof(a)||a instanceof Date||a instanceof File){var l=null==a?"":"boolean"==typeof a?Number(a):a;e.append(o,l)}else Object.keys(a).forEach(function(l){buildData(e,a[l],o?"".concat(o,"[").concat(l,"]"):l)})}function jsonToFormData(e){var a=new FormData;return buildData(a,e),a}function getUniqueFilename(e){for(var a=!1,o=0;o<filesuploaded.length;o++)filesuploaded[o]==e&&(a=!0);return a?(console.log("file already sent - altering name"),e="1"+e,getUniqueFilename(e)):(console.log("final file name is "+e),filesuploaded.push(e),e)}function onFileDropped(e){if(e.preventDefault(),null!=next_image)return void console.log("We are already uploading a file");if(e.target.files)for(var a=0;a<e.target.files.length;a++){var o=e.target.files[a];next_image=new PdfItem(o,1769,350),cursor.assets.push(next_image);var l=getParameterFromURL("roomid"),n=new FormData,t=getUniqueFilename(o.name);n.append(t,o),$.ajax({url:"/user/classroom/uploadfile/"+l,type:"POST",data:n,processData:!1,contentType:!1,success:function(e){next_image.complete_upload("/user/classroom/files/"+l+"/"+t)},error:function(e,a,o){console.log(e),console.log("ERROR occurred: "+a)}})}else if(e.dataTransfer.items)for(var a=0;a<e.dataTransfer.items.length;a++){var o=e.dataTransfer.items[a].getAsFile();next_image=new PdfItem(o,e.clientX,e.clientY),cursor.assets.push(next_image);var l=getParameterFromURL("roomid"),n=new FormData,t=getUniqueFilename(o.name);n.append(t,o),$.ajax({url:"/user/classroom/uploadfile/"+l,type:"POST",data:n,processData:!1,contentType:!1,success:function(e){next_image.complete_upload("/user/classroom/files/"+l+"/"+t)},error:function(e,a,o){console.log(e),console.log("ERROR occurred: "+a)}})}}function onFileDragged(e){e.preventDefault()}var selectedFiles=[];$(".popup-load-files-wrap").on("dragover",function(e){e.preventDefault(),$(".popup-load-files-drop-wrap").css({display:"block"})}),$(".popup-load-files-wrap").on("dragleave",function(e){e.preventDefault(),$(".popup-load-files-drop-wrap").css({display:"none"})}),$(".popup-load-files-drop-wrap").on("dragover",function(e){e.preventDefault(),$(".popup-load-files-drop-wrap").addClass("active")}),$(".popup-load-files-drop-wrap").on("dragleave",function(e){e.preventDefault(),$(".popup-load-files-drop-wrap").removeClass("active")}),$(document).on("drop",".popup-load-files-drop-wrap",function(e){e.preventDefault();var a=[];if(e.originalEvent.dataTransfer.items){for(var o=0;o<e.originalEvent.dataTransfer.items.length;o++)if("file"===e.originalEvent.dataTransfer.items[o].kind){var l=e.originalEvent.dataTransfer.items[o].getAsFile();a.push(l)}}else for(var o=0;o<e.originalEvent.dataTransfer.files.length;o++){var n=e.originalEvent.dataTransfer.files[o];a.push(n)}console.log("files",a),a.forEach(function(e,a){console.log("files element",e),loadImage(a,URL.createObjectURL(e))}),$(".popup-load-files-drop-wrap").removeClass("active")}),$(".popup-load-files-select").on("click",function(e){e.stopPropagation(),$(".popup-load-files-select-options").toggleClass("active")}),$(".popup-load-files-select-option").on("click",function(e){e.stopPropagation();var a=($(this).html(),$(this).attr("data-value")),o=null;"file_name_desc"===a?(a.substr(0,a.length-5),o="DESC"):"file_name_asc"===a&&(a.substr(0,a.length-4),o="ASC"),console.log("Option selected!"),axios.post("/lesson/classroom/library/".concat(window.classroom.voxeet.userId),{query:$("input.popup-load-files-search").val(),sort_direction:o,page:1,sort_type:a}).then(function(e){if(console.log("open response",e),$("#popup-load-files-list").html(""),$("#popup-load-files-navigation").html(""),e.data.length)$("#popup-load-files-navigation").html('<span class="popup-load-files-nav-number active"> 1 </span>'),$(".popup-load-files-btn-nav").attr("disabled","true");else{var a=Math.round(e.data.length/18);$("#popup-load-files-navigation").html('<span class="popup-load-files-nav-number active"> 1 </span>');for(var o=2;o<=a;o++)$("#popup-load-files-navigation").append('<span class="popup-load-files-nav-number"> '+o+" </span>");$(".popup-load-files-btn-nav").attr("disabled","true")}for(var l=0;l<e.data.length;l++)$("#popup-load-files-list").append('\n                        <div class="popup-load-files-item" data-url="url"> \n                        <div class="popup-load-files-item-img">\n                            <img src="'.concat(e.data[l].path,'" class="popup-load-files-img-icon">\n                        </div>\n                        <div class="popup-load-files-item-name">\n                            <p>').concat(e.data[l].name,"</p>\n                        </div>\n                    </div>"))}).catch(function(e){console.log("open error",e)}).finally(function(){})}),$(".popup-load-files-item").on("click",function(){console.log("ACTIVE TICK");var e=$(this).children(".popup-load-files-item-tick");e.toggleClass("active");var a=$(this).attr("data-url");e.hasClass("active")?(selectedFiles.map(function(e){return e!==a}),selectedFiles.push(a)):selectedFiles.map(function(e){return e!==a}),selectedFiles.length&&$(".popup-load-files-header.selected-files")}),$(".popup-load-files-search").on("change",function(e){var a=$(this).val();axios.post("/lesson/".concat(classroom.lesson.id,"/classroom/library"),{query:a}).then(function(e){}).catch(function(e){}).finally(function(){})});var filesuploaded=[];