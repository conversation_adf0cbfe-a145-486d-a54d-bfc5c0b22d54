function ImageItem(t,e,i,s){if(this.loaded=!1,this.uploaded=!1,this.sent=!1,this.server_file_name="",this.complete_upload=function(t){this.server_file_name=t,this.uploaded=!0,next_image.loaded&&(cursor.assets.push(next_image),next_image=null)},this.img=new Image,this.size=new Location(0,0),this.type="imageitem",this.moved=!1,this.id=uuidv4().replace(/-/g,""),this.scale_index=3,this.get_lowest_point=function(){return this.loc.y+this.size.y},FileReader&&t&&null!=t){var h=new FileReader;h.onload=function(){next_image.img.src=h.result,next_image.img.onload=function(){next_image.loaded=!0,next_image.size.x=next_image.img.naturalWidth,next_image.size.y=next_image.img.naturalHeight,next_image.uploaded&&(cursor.assets.push(next_image),next_image=null)}},h.readAsDataURL(t)}else this.server_file_name=s,this.img.src=s,this.img.onload=function(){},this.loaded=!0;this.offset=0,this.loc=new Location(e,i),this.loc.x<0&&(this.loc.x=50),this.loc.y<0&&(this.loc.y=50),this.filename=t.name,this.update=ImageItemUpdate,this.draw=ImageItemDraw,this.hittest=ImageItemHitTest,this.bodyhittest=ImageItemBodyHitTest,this.highlight_dragbar=!1,this.scaleImage=ImageItemScale,this.hittestresize=ImageItemHitTestResize,this.assets_deleted=!1,this.assets=[],this.assets_dirty=!1,this.add_asset=ImageItemAddAsset,this.resize=ImageItemResize,this.start_resize=ImageItemStartResize}function ImageItemUpdate(){}function ImageItemDraw(t,e){var i=t.strokeStyle,s=t.fillStyle;if(this.loaded){var h=($(window).width(),$(window).height(),scale[this.scale_index]),o=Math.round(h*this.size.x),l=Math.round(h*this.size.y);!1===this.highlight_dragbar?t.fillStyle="#222222":t.fillStyle="#444444";var a=this.img,n=document.createElement("canvas");n.getContext("2d");n.width=.8*this.size.x,n.height=.8*this.size.y,t.mozImageSmoothingEnabled=!0,t.imageSmoothingQuality="high",t.webkitImageSmoothingEnabled=!0,t.msImageSmoothingEnabled=!0,t.imageSmoothingEnabled=!0,a.src=this.img.src,t.drawImage(a,this.loc.x,this.loc.y-e,.5*n.width,.5*n.height),t.beginPath();var c=.5*n.width;t.fillRect(this.loc.x,this.loc.y-20-e,c,20),t.stroke(),t.closePath(),t.font="14px verdana",t.fillStyle="#eeffee",t.fillText(this.filename,this.loc.x+5,this.loc.y-5-e,.4*this.size.x),t.linewidth=5,t.strokeStyle="#222222",o=.5*n.width,l=.5*n.height,t.beginPath(),t.moveTo(this.loc.x,this.loc.y-e),t.lineTo(this.loc.x+o,this.loc.y-e),t.lineTo(this.loc.x+o,this.loc.y+l-e),t.lineTo(this.loc.x,this.loc.y+l-e),t.lineTo(this.loc.x,this.loc.y-e),t.stroke(),t.closePath();for(var m=0;!cursor.moving&&m<this.assets.length;m++){this.assets[m].draw(t,e,this)}t.strokeStyle="#222222",t.beginPath(),t.moveTo(this.loc.x+o-20,this.loc.y+l-e-2),t.lineTo(this.loc.x+o-2,this.loc.y+l-e-20),t.moveTo(this.loc.x+o-15,this.loc.y+l-e-2),t.lineTo(this.loc.x+o-2,this.loc.y+l-e-15),t.moveTo(this.loc.x+o-10,this.loc.y+l-e-2),t.lineTo(this.loc.x+o-2,this.loc.y+l-e-10),t.moveTo(this.loc.x+o-5,this.loc.y+l-e-2),t.lineTo(this.loc.x+o-2,this.loc.y+l-e-5),t.stroke(),t.closePath()}t.strokeStyle=i,t.fillStyle=s}function ImageItemHitTest(t,e){var i=scale[this.scale_index],s=Math.round(i*this.size.x);Math.round(i*this.size.y);return t>this.loc.x&&t<this.loc.x+s&&e>this.loc.y-20&&e<this.loc.y}function ImageItemScale(t){t?this.scale_index+=1:this.scale_index-=1,this.scale_index<0&&(this.scale_index=0),this.scale_index>scale.length-1&&(this.scale_index=scale.length-1)}function ImageItemHitTestResize(t,e,i){var s=Math.round(.4*this.size.x),h=Math.round(.4*this.size.y);return t>this.loc.x+s-20&&t<this.loc.x+s&&e>this.loc.y+h-i-20&&e<this.loc.y+h-i}function ImageItemResize(t,e){var i=this.size.x,s=this.size.y,h=this.size.x+=t,o=i/s,l=h/o;h=l*o,this.size.x=h,this.size.y=l}function ImageItemStartResize(){}function ImageItemBodyHitTest(t,e){return t>this.loc.x&&t<this.loc.x+this.size.x&&e>this.loc.y&&e<this.loc.y+this.size.y}function ImageItemAddAsset(t){if("line"!=t.type)return void console.log("ERROR: We only handle lines for textboxes");for(var e=0;e<t.points.length;e++)t.points[e].x-=this.loc.x,t.points[e].y-=this.loc.y;console.log(t.points),this.assets.push(t),this.assets_dirty=!0}var next_image=null,scale=[.1,.25,.5,1,1.5,2,4];