function Line(t,i){this.points=[],void 0!=t&&null!=t&&void 0!=i&&null!=i&&this.points.push(new Location(t,i)),this.update=line_update,this.draw=line_draw,this.add_point=line_next_point,this.hittest=line_hittest,this.type="line",this.sent=!1,this.linesize=2,this.color="#ff0000",this.deleted=!1,this.id=uuidv4().replace(/-/g,""),this.div_id=null,this.get_lowest_point=line_get_lowest_point}function line_update(){1==(arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)&&(this.deleted=!0,this.points=[])}function line_get_lowest_point(){for(var t=0,i=0;i<this.points.length;i++)this.points[i].y>t&&(t=this.points[i]);return t}function line_draw(t,i,s){var e=t.strokeStyle;if(t.lineWidth=this.linesize,t.strokeStyle=this.color,t.beginPath(),void 0==s&&this.points.length>0){t.moveTo(this.points[0].x,this.points[0].y-i);for(var n=1;n<this.points.length;n++)t.lineTo(this.points[n].x,this.points[n].y-i)}else for(var o=!0,n=0;n<this.points.length;n++)null==s.size&&console.log("Parent null"),this.points[n].y-s.offset<0||this.points[n].y-s.offset>s.size.y||(o?(t.moveTo(this.points[n].x+s.loc.x,this.points[n].y-i+s.loc.y-s.offset),o=!1):t.lineTo(this.points[n].x+s.loc.x,this.points[n].y-i+s.loc.y-s.offset));t.stroke(),t.closePath(),t.strokeStyle=e}function line_next_point(t,i){this.points.push(new Location(t,i))}function TempLine(t,i){this.update=templine_update,this.draw=templine_draw,this.add_point=line_next_point,this.type="templine",this.points=[],this.hittest=line_hittest,this.sent=!1,this.start_ttl=150,this.ttl=this.start_ttl,this.start_ttd=30,this.ttd=this.start_ttd,this.color="#ff0000",this.status="DRAWING",this.get_lowest_point=line_get_lowest_point}function templine_update(){"DRAWING"!=this.status&&("DYING"==this.status?(this.ttd-=1,this.ttd<1&&(this.status="DEAD")):this.ttl<1?this.status="DYING":this.ttl-=1)}function templine_draw(t,i){if("DEAD"!=this.status&&!(this.points.length<1)){var s=t.strokeStyle;if(t.lineWidth=this.linesize,this.ttd!=this.start_ttd){var e=this.ttd/this.start_ttd;t.strokeStyle="rgba(255, 0, 0, "+e+")"}else t.strokeStyle=this.color;t.beginPath(),t.moveTo(this.points[0].x,this.points[0].y-i);for(var n=1;n<this.points.length;n++)t.lineTo(this.points[n].x,this.points[n].y-i);t.stroke(),t.closePath(),t.strokeStyle=s}}function line_hittest(t,i){}