function Ripple(t,i,s){this.loc=new Location(t,i),this.radius=2,this.MAX_RADIUS=24,this.status="ACTIVE",this.color=s,this.draw=ripple_draw,this.update=ripple_update,this.sent=!1,this.get_lowest_point=function(){return 0}}function ripple_draw(t,i){t.strokeStyle=this.color,t.lineWidth=1,t.beginPath(),t.arc(this.loc.x,this.loc.y-i,this.radius,0,2*Math.PI),t.stroke(),t.closePath()}function ripple_update(){this.radius+=1,this.radius>this.MAX_RADIUS&&(this.status="DONE")}