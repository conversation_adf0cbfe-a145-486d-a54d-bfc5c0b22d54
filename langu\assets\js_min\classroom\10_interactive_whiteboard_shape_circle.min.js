function ShapeCircle(i,e){this.id=uuidv4().replace(/-/g,""),this.div_id=this.id,this.guid=this.id,this.type="shape_circle",this.parent="shape",this.loc=new Location(i,e),this.size=new Location(10,10),this.update=ShapeCircleFunctionUpdate,this.draw=ShapeCircleFunctionDraw,this.resize=ShapeCircleResize,this.finish_resize=ShapeCircleFinishResize,this.hittest=function(){return!1},this.linesize=2,this.color="#ff0000",this.status="active",this.sent=!1,this.resized=!1,this.deleted=!1}function ShapeCircleFunctionUpdate(i){!0!==i&&!1!==i||(this.deleted=i)}function ShapeCircleFunctionDraw(i,e){var t=i.strokeStyle;i.lineWidth=this.linesize,i.strokeStyle=this.color,i.beginPath(),drawEllipse(i,this.loc.x-(e||0),this.loc.y-(e||0),this.size.x,this.size.y),i.stroke(),i.strokeStyle=t}function drawEllipse(i,e,t,s,h){var r=s/2*.5522848,n=h/2*.5522848,o=e+s,c=t+h,a=e+s/2,l=t+h/2;i.beginPath(),i.moveTo(e,l),i.bezierCurveTo(e,l-n,a-r,t,a,t),i.bezierCurveTo(a+r,t,o,l-n,o,l),i.bezierCurveTo(o,l+n,a+r,c,a,c),i.bezierCurveTo(a-r,c,e,l+n,e,l),i.stroke()}function ShapeCircleResize(i,e){this.size.x+=i,this.size.y+=e}function ShapeCircleFinishResize(){this.resized=!0}