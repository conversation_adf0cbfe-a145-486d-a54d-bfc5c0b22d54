function ShapeLine(i,e){this.id=uuidv4().replace(/-/g,""),this.div_id=this.id,this.guid=this.id,this.type="shape_line",this.parent="shape",this.loc=new Location(i,e),this.size=new Location(1,1),this.update=ShapeLineFunctionUpdate,this.draw=ShapeLineFunctionDraw,this.resize=ShapeLineResize,this.finish_resize=ShapeLineFinishResize,this.hittest=ShapeLineHittest,this.linesize=2,this.color="#ff0000",this.status="active",this.sent=!1,this.resized=!1,this.deleted=!1}function ShapeLineFunctionUpdate(i){!0!==i&&!1!==i||(this.deleted=i)}function ShapeLineFunctionDraw(i,e){var t=i.strokeStyle;i.lineWidth=this.linesize,i.strokeStyle=this.color,i.beginPath(),i.moveTo(this.loc.x-(e||0),this.loc.y-(e||0)),i.lineTo(this.loc.x+this.size.x-(e||0),this.loc.y+this.size.y-(e||0)),i.closePath(),i.stroke(),i.strokeStyle=t}function ShapeLineResize(i,e){this.size.x+=i,this.size.y+=e}function ShapeLineFinishResize(){this.resized=!0}function ShapeLineHittest(i,e){return i>=this.loc.x&&i<=this.loc.x+this.size.x&&e>=this.loc.y&&e<=this.loc.y+this.size.x}