function Shape(x, y, type) {
  this.type = "shape";
  this.loc = new Location(x, y);
  this.points = [];
  this.update = ShapeFunctionUpdate;
  this.draw = ShapeFunctionDraw;
  this.add_point = ShapeFunctionAddPoint;
  this.linesize = 2;
  this.color = "#ff0000";
  this.status = 'AWAIT';
  this.deleted = false;
  this.id = uuidv4().replace(/-/g, '');
  this.div_id = this.id;
  this.sent = false;
  this.get_lowest_point = ShapeFunctionGetLowestPoint;
}

function ShapeFunctionUpdate() {
  var deleted = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;

  if (deleted == true) {
    this.deleted = true;
    this.points = [];
  }
}

function ShapeFunctionDraw(ctx, offset, parent) {}

function ShapeFunctionAddPoint(x, y) {
  this.points.push(new Location(x, y));
}

function ShapeFunctionGetLowestPoint() {
  var val = 0;

  for (var i = 0; i < this.points.length; i++) {
    if (this.points[i].y > val) {
      val = this.points[i];
    }
  }

  return val;
}