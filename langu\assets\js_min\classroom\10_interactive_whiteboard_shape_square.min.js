function ShapeSquare(i,e){this.id=uuidv4().replace(/-/g,""),this.div_id=this.id,this.guid=this.id,this.type="shape_square",this.parent="shape",this.loc=new Location(i,e),this.size=new Location(10,10),this.update=ShapeSquareFunctionUpdate,this.draw=ShapeSquareFunctionDraw,this.resize=ShapeSquareResize,this.finish_resize=ShapeSquareFinishResize,this.hittest=function(){return!1},this.linesize=2,this.color="#ff0000",this.status="active",this.sent=!1,this.resized=!1,this.deleted=!1}function ShapeSquareFunctionUpdate(i){!0!==i&&!1!==i||(this.deleted=i)}function ShapeSquareFunctionDraw(i,e){var t=i.strokeStyle;i.lineWidth=this.linesize,i.strokeStyle=this.color,i.beginPath(),i.rect(this.loc.x-(e||0),this.loc.y-(e||0),this.size.x-8,this.size.y-9),i.stroke(),i.strokeStyle=t}function ShapeSquareResize(i,e){this.size.x+=i,this.size.y+=e}function ShapeSquareFinishResize(){this.resized=!0}