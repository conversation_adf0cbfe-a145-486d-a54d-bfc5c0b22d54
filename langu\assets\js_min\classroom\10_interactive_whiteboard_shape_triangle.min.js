function ShapeTriangle(i,e){this.id=uuidv4().replace(/-/g,""),this.div_id=this.id,this.guid=this.id,this.type="shape_triangle",this.parent="shape",this.loc=new Location(i,e),this.size=new Location(10,10),this.update=ShapeTriangleFunctionUpdate,this.draw=ShapeTriangleFunctionDraw,this.resize=ShapeTriangleResize,this.finish_resize=ShapeTriangleFinishResize,this.hittest=function(){return!1},this.linesize=2,this.color="#ff0000",this.status="active",this.sent=!1,this.resized=!1,this.deleted=!1}function ShapeTriangleFunctionUpdate(i){!0!==i&&!1!==i||(this.deleted=i)}function ShapeTriangleFunctionDraw(i,e){var t=i.strokeStyle;i.lineWidth=this.linesize,i.strokeStyle=this.color,i.beginPath(),i.moveTo(this.loc.x-30-(e||0)+2*this.size.x,this.loc.y-30-(e||0)),i.lineTo(this.loc.x-30-(e||0)+this.size.x,this.loc.y+3*this.size.y-30-(e||0)),i.lineTo(this.loc.x-30-(e||0)+3*this.size.x,this.loc.y+3*this.size.y-30-(e||0)),i.closePath(),i.stroke(),i.strokeStyle=t}function ShapeTriangleResize(i,e){this.size.x+=i/3,this.size.y+=e/3}function ShapeTriangleFinishResize(){this.resized=!0}