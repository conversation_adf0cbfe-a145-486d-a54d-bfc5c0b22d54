//////TEXTBOX::::://///////
function TextBox(t,e,s){this.type="textbox",this.loc=new Location(t,e),this.set_size=textbox_set_size,this.size=new Location(0,0),this.state=new State,this.update=function(){},this.draw=function(){},this.aggregateUpdate=textbox_aggregate_update,this.hittest=textbox_hittest,this.bodyhittest=textbox_bodyhittest,this.keydown=textbox_keydown,this.keyup=textbox_keyup,this.offset=0,this.assets_deleted=!1,this.assets=[],this.assets_dirty=!1,this.isSelected=!1,this.update_text=textbox_update_text,this.update_location=textbox_update_location,this.text="Here you can write your message.",this.text_updated=!1,this.linesize=2,this.color="#ff0000",this.last_text="Here you can write your message.",this.display_text_array=[[]],this.cursor_loc=new Location(0,0),this.max_length=10,this.max_height=7,this.status="DRAWING",this.changed=!1,this.guid=uuidv4().replace(/-/g,"_"),this.div_id="text_"+uuidv4().replace(/-/g,"_"),this.sent=!1,this.never_send=!1,this.shift_pressed=!1,this.clicked=textbox_clicked,this.id=1,this.clicked(this.loc.x+10,this.loc.y+10,0),this.moved=!1,this.hittestresize=textbox_hittest_resize,this.hittestclear=textbox_hittest_clear,this.resize=textbox_resize,this.start_resize=textbox_start_resize,this.get_lowest_point=function(){return this.loc.y+this.size.y},this.setup=textbox_setup,this.add_asset=textbox_add_asset,this.hoverColor=getHoverColor(),this.hasUpdatedText=!1,this.changesSent=!0,this.lockListener=!1}function textbox_aggregate_update(t){this.lockListener=!0,$("#text_summernote").summernote("code",t),this.lockListener=!1}function textbox_setup(){}function textbox_util_get_unique_id(){for(var t=1;;)for(var e=0;e<cursor.assets.length;e++)"textbox"==cursor.assets[0].type&&cursor.assets[0].id==t&&(t+=1)}function textbox_draw(t,e){var s=t.strokeStyle,i=t.fillStyle;return t.strokeStyle=s,void(t.fillStyle=i)}function textbox_update(){}function textbox_update_text(){$("#text_summernote").summernote("code",this.text)}function textbox_update_location(){}function textbox_set_size(t,e){console.log("textbox set size event"),this.size.x=t-this.loc.x,this.size.y=e-this.loc.y,this.size.x<500&&(this.size.x=500),this.size.y<200&&(this.size.y=200)}function textbox_resize(t,e){console.log("textbox resize event"),this.size.x+=t,this.size.y+=e,this.size.x<500&&(this.size.x=500),this.size.y<200&&(this.size.y=200),this.state.resized=!0}function textbox_start_resize(){}function textbox_add_asset(t){if("line"!=t.type)return void console.log("ERROR: We only handle lines for textboxes");for(var e=0;e<t.points.length;e++)t.points[e].x-=this.loc.x,t.points[e].y-=this.loc.y-this.offset;this.assets.push(t),this.assets_dirty=!0}function textbox_hittest(t,e){return t>this.loc.x+this.size.x-20&&t<this.loc.x+this.size.x-5&&e>this.loc.y-17&&e<this.loc.y-3?(this.assets_deleted=!0,this.assets=[],!1):t>this.loc.x&&t<this.loc.x+this.size.x&&e>this.loc.y-20&&e<this.loc.y}function textbox_hittest_resize(t,e,s){return t>this.loc.x+this.size.x-20&&t<this.loc.x+this.size.x&&e>this.loc.y+this.size.y-s-20&&e<this.loc.y+this.size.y-s}function textbox_hittest_clear(t,e,s){return t>this.loc.x+this.size.x-20&&t<this.loc.x+this.size.x&&e<this.loc.y-s&&e>this.loc.y-s-17}function textbox_bodyhittest(t,e,s){return t>this.loc.x&&t<this.loc.x+this.size.x&&e>this.loc.y-s&&e<this.loc.y+this.size.y-s}function textbox_scroll(t){var e=$("#"+this.div_id)[0].scrollHeight,s=$("#"+this.div_id).height(),i=t.originalEvent.wheelDelta,o=!0;return this.offset-=i,this.offset>e-s&&(this.offset=e-s,o=!1),this.offset<0&&(this.offset=0,o=!1),$("#"+this.div_id).scrollTop(this.offset),o}function textbox_clicked(t,e,s){if(this.is_selected=!0,this.assets_dirty=!0,t>this.loc.x&&t<this.loc.x+this.size.x&&e>this.loc.y-s&&e<this.loc.y+this.size.y-s)return $("#summernote_wrapper").css({left:60,top:130,height:500,width:this.size.x}),$("#summernote_wrapper").show(),$("#text_summernote").summernote("code",this.text),!0;t>this.loc.x+this.size.x-20&&t<this.loc.x+this.size.x-5&&e>this.loc.y-17&&e<this.loc.y-3&&console.log("&&&&&&&&&&&&&")}function textbox_keydown(t){return void console.log(t)}function get_last_character(t){if(void 0==t)return console.log("ERROR: we have been passed an undefined line (get_last_character)"),0;for(var e=t.length+1;e>0;e--)if(void 0!=t[e]&&""!=t[e])return e+1;return 0}function textbox_keyup(t){"Shift"==t&&(this.shift_pressed=!1)}