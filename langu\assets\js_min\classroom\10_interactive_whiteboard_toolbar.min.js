function Toolbar(){this.loc=new Location(200,25),this.type="toolbar",this.buttons=[],this.draw=toolbar_draw,this.update=toolbar_update,this.mousedown=toolbar_mousedown,this.mouseup=toolbar_mouseup,this.click=toolbar_click,this.mousemove=toolbar_mousemove,this.hitcheck=toolbar_hitcheck,this.dataToSend=!1,this.isHovered=!1,this.hoverColor="",this.highlight_dragbar=!1,this.is_dragging=!1,this.cur_selected="POINTER",this.get_lowest_point=function(){return 0},this.last_move=new Location(0,0)}function toolbar_draw(t){!1===this.highlight_dragbar?t.fillStyle="#646464":t.fillStyle="#000";var o=40*this.buttons.length+10,i=this.loc.y+15;t.beginPath(),t.moveTo(this.loc.x+10,i),t.lineTo(this.loc.x+o-10,i),t.arcTo(this.loc.x+o,i,this.loc.x+o,i+10,10),t.arcTo(this.loc.x+o,i+50,this.loc.x+o-10,i+50,10),t.lineTo(this.loc.x+o-10,i+50),t.arcTo(this.loc.x,i+50,this.loc.x,i+50-10,10),t.arcTo(this.loc.x,i,this.loc.x+10,i,10),t.lineWidth=2,t.strokeStyle="#646464",t.stroke(),this.isHovered&&(t.beginPath(),t.moveTo(this.loc.x+10,i-2),t.lineTo(this.loc.x+o-10,i-2),t.arcTo(this.loc.x+2+o,i-2,this.loc.x+2+o,i-2+10,12),t.arcTo(this.loc.x+2+o,i+2+50,this.loc.x+2+o-10,i+2+50,12),t.lineTo(this.loc.x-2+o-10,i+2+50),t.arcTo(this.loc.x-2,i+2+50,this.loc.x-2,i+2+50-10,12),t.arcTo(this.loc.x-2,i-2,this.loc.x-2+10,i-2,12),t.strokeStyle=this.hoverColor,t.lineWidth=1,t.stroke());for(var s=0;s<this.buttons.length;s++)this.buttons[s].draw(t)}function toolbar_hitcheck(t,o){var i=40*this.buttons.length;if(o>this.loc.y&&o>this.loc.y+60&&o<this.loc.y+69||o<this.loc.y+26&&o>this.loc.y+18){if(t>this.loc.x&&t<this.loc.x+210)return!0}else if((t>this.loc.x&&t<this.loc.x+10||t<this.loc.x+i+10&&t>this.loc.x+i)&&o>this.loc.y&&(o>this.loc.y+25||o<this.loc.y+77))return!0;return!1}function toolbar_update(){}function toolbar_click(t,o){}function toolbar_mousedown(t,o){if(this.hitcheck(t,o))return this.is_dragging=!0,this.last_move=new Location(t,o),!0;for(var i=!1,s=0;s<this.buttons.length;s++){var h=this.buttons[s];h.mousedown(t,o)&&(i=!0,h.verticalButtons.length&&h.drawVerticalButtons(),this.cur_selected=h.type)}return i}function toolbar_mouseup(t,o){this.is_dragging=!1}function toolbar_mousemove(t,o){if(this.highlight_dragbar=!1,this.is_dragging){var i=t-this.last_move.x,s=o-this.last_move.y;this.last_move.x=t,this.last_move.y=o,this.loc.x+=i,this.loc.y+=s;for(var h=0;h<this.buttons.length;h++)this.buttons[h].loc.x+=i,this.buttons[h].loc.y+=s}else if(this.hitcheck(t,o))this.highlight_dragbar=!0,this.isHovered=!0,this.dataToSend=!0,this.hoverColor="teacher"==window.langu_role?"#7FB802":"#3C87F8";else{this.isHovered=!1;for(var h=0;h<this.buttons.length;h++)this.buttons[h].mousemove(t,o)}}