function asyncGeneratorStep(e,t,n,r,o,i,s){try{var a=e[i](s),u=a.value}catch(e){return void n(e)}a.done?t(u):Promise.resolve(u).then(r,o)}function _asyncToGenerator(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){function i(e){asyncGeneratorStep(a,r,o,i,s,"next",e)}function s(e){asyncGeneratorStep(a,r,o,i,s,"throw",e)}var a=e.apply(t,n);i(void 0)})}}function _typeof(e){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function onKeyDown(e){16==e.keyCode&&(cursor.shift_pressed=!0);var t=String.fromCharCode(e.which||e.keyCode);isKeyPressed(t)||(aryCurPressedKeys[aryCurPressedKeys.length]=t)}function onKeyUp(e){var t=String.fromCharCode(e.which||e.keyCode);16==e.keyCode&&(cursor.shift_pressed=!0);for(var n=0;n<aryCurPressedKeys.length;n++)t==aryCurPressedKeys[n]&&removeArrayItem(aryCurPressedKeys,n)}function isKeyPressed(e){for(var t=0;t<aryCurPressedKeys.length;t++)if(aryCurPressedKeys[t]==e)return!0;return!1}function removeArrayItem(e,t){for(var n=t;n<e.length;n++)if(e[n]=e[n+1],n==e.length-1)return void delete e[e.length]}function removeAllKeysFromArray(){aryCurPressedKeys=new Array}function Location(e,t){this.x=e,this.y=t}function getParameterByName(e,t){t||(t=window.location.href),e=e.replace(/[\[\]]/g,"\\$&");var n=new RegExp("[?&]"+e+"(=([^&#]*)|&|#|$)"),r=n.exec(t);return r?r[2]?decodeURIComponent(r[2].replace(/\+/g," ")):"":null}function getParameterFromURL(e){var t=window.location.href;if("roomid"==e){var n=t.split("/lesson/")[1];return n=n.split("/classroom")[0]}}function uuidv4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})}function is_float(e){return!(+e!==e||isFinite(e)&&!(e%1))}function State(){this.hovered=!1,this.focused=!1,this.resized=!1}function getHoverColor(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return window.langu_role==(e?"student":"teacher")?"#7FB802":"#3C87F8"}function is_object(e){return"object"===_typeof(e)}function ucfirst(e){return e.charAt(0).toUpperCase()+e.slice(1)}function findAssetByType(e,t){return _findAssetByType.apply(this,arguments)}function _findAssetByType(){return _findAssetByType=_asyncToGenerator(regeneratorRuntime.mark(function e(t,n){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",t.find(function(e){return e.type===n}));case 1:case"end":return e.stop()}},e)})),_findAssetByType.apply(this,arguments)}function dispatch(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"";return store.dispatch(e,{type:t,ref:n,path:r,data:o})}function setPropertyByKeypath(e,t,n){t="string"==typeof t?t.split("."):t;for(var r=0,o=t.length-1;r<o;r++)e=e[t[r]];e[t[r]]=n}function getCaretCharacterOffsetWithin(e){var t,n=0,r=e.ownerDocument||e.document,o=r.defaultView||r.parentWindow;if(void 0!==o.getSelection){if(t=o.getSelection(),t.rangeCount>0){var i=o.getSelection().getRangeAt(0),s=i.cloneRange();s.selectNodeContents(e),s.setEnd(i.endContainer,i.endOffset),n=s.toString().length}}else if((t=r.selection)&&"Control"!=t.type){var a=t.createRange(),u=r.body.createTextRange();u.moveToElementText(e),u.setEndPoint("EndToEnd",a),n=u.text.length}return n}function setCaretPosition(e,t){for(var n=document.createRange(),r=window.getSelection(),o=null,i=null,s=0;s<e.childNodes.length;s++){for(i=o,o=e.childNodes[s];o.childNodes.length>0;)o=o.childNodes[0];if(null!=i&&(t-=i.length),t<=o.length)break}null!=o&&(n.setStart(o,t),n.collapse(!0),r.removeAllRanges(),r.addRange(n))}function createRange(e,t,n){if(n||(n=document.createRange(),n.selectNode(e),n.setStart(e,0)),0===t.count)n.setEnd(e,t.count);else if(e&&t.count>0)if(e.nodeType===Node.TEXT_NODE)e.textContent.length<t.count?t.count-=e.textContent.length:(n.setEnd(e,t.count),t.count=0);else for(var r=0;r<e.childNodes.length&&(n=createRange(e.childNodes[r],t,n),0!==t.count);r++);return n}function setCurrentCursorPosition(e){if(e>=0){var t=window.getSelection();range=createRange(document.getElementById("test").parentNode,{count:e}),range&&(range.collapse(!1),t.removeAllRanges(),t.addRange(range))}}function isChildOf(e,t){for(;null!==e;){if(e.id===t)return!0;e=e.parentNode}return!1}function getCurrentCursorPosition(e){var t,n=window.getSelection(),r=-1;if(n.focusNode&&isChildOf(n.focusNode,e))for(t=n.focusNode,r=n.focusOffset;t&&t.id!==e;)if(t.previousSibling)t=t.previousSibling,r+=t.textContent.length;else if(null===(t=t.parentNode))break;return r}var aryCurPressedKeys=new Array;