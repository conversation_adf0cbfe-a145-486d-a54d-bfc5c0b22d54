function VideoItem(e,t,s){this.loc=new Location(e,t),this.size=new Location(600,100),this.draw=VideoItemDraw,this.update=VideoItemUpdate,this.sent=!1,this.url_set=!1,this.url=s,this.type="videoitem",this.highlight_dragbar=!1,this.keydown=VideoItemKeyPressed,this.keyup=VideoItemKeyUp,this.hittest=VideoItemHitTest,this.get_lowest_point=VideoItemGetLowestPoint,this.div_id=uuidv4().replace(/-/g,""),this.player=null,this.shift_down=!1,this.control_down=!1,this.load_video=VideoItemLoadVideo,this.video_loaded=!1,this.moved=!1,this.clicked=VideoItemClick,this.deleted=!1,this.reset=VideoItemReset,this.stop_video=VideoItemStopVideo,this.play_video=VideoItemPlayVideo,this.jump_to=VideoItemJumpTo,this.event_to_send=null,this.video_assets=null,this.timeVideo=0,this.handle_event=VideoItemHandleEvent,this.status="stopped",this.hittestresize=VideoItemHitTestResize,this.bodyhittest=VideoItemBodyHitTest,this.is_master=!1,this.resize=VideoItemResize,this.resizing=!1,this.isResized=!1,this.finish_resize=VideoItemFinishResize,this.start_resize=VideoItemStartResize,this.assets=[],this.add_asset=VideoItemAddAsset,this.assets_dirty=!1,this.canvas=null,this.offset=0,this.currentTime=0,this.playbackRate=1,this.mouseCanvasVideoClick=!1,this.points=[],this.videoCanvasDraw=videoCanvasDraw,this.assets_to_send=null,this.assets_to_append=[],this.intervals=[],this.videoCtx=null,this.videoAssets=[],this.lockSocketSend=!0,this.currentPlayerState={currentTime:0,status:!1,playbackRate:1},this.handleVideoAssets=handleVideoAssets,this.start_loc=null}function handleVideoAssets(e){var t={type:void 0,component:null,percent:e.percent};if(e.type&&-1!==e.type.indexOf("shape")){var s=e.type.replace("shape_","");switch(s){case"square":t.component=new ShapeSquare(e.component.loc.x,e.component.loc.y);break;case"circle":t.component=new ShapeCircle(e.component.loc.x,e.component.loc.y);break;case"triangle":t.component=new ShapeTriangle(e.component.loc.x,e.component.loc.y);break;case"line":t.component=new ShapeLine(e.component.loc.x,e.component.loc.y);break;default:console.error("Undefined function called ["+s+"].")}t.component.size=e.component.size,t.component.linesize=e.component.linesize,t.component.color=e.component.color,t.component.id=e.component.id,t.component.div_id=e.component.div_id,t.component.guid=e.component.guid,t.component.sent=!0}else if(e.type){var i=new Line(e.component.points[0].x,e.component.points[0].y);i.points=e.component.points,t.component=i}this.videoAssets.push(t),appendAnnotationsBlock(e.percent,this.div_id)}function appendAnnotationsBlock(e,t){if($("div#container_"+t+" div.plyr__progress").length){var s='<span class="video_annotations" id="video_annotations_'+t+'" data-annotation-percent="annotation_percent_'+e+'"style="left: '+e+'%;"></span>';$("div#container_"+t+" div.plyr__progress").append(s)}else setTimeout(function(){appendAnnotationsBlock(e,t)},1e3)}function VideoItemReset(){}function VideoItemUpdate(){if("DEAD"!=this.status){if(this.assets_to_append&&this.assets_to_append.length>0){for(var e=new Line(this.assets_to_append[0].x,this.assets_to_append[0].y),t=1;t<this.assets_to_append.length;t++)e.add_point(this.assets_to_append[t].x,this.assets_to_append[t].y);this.videoAssets.push({percent:retrievePercentFromPlayerProgress(this.player),component:e}),this.assets_to_append=[]}cursor.moving||this.resizing?$("#container_"+this.div_id).hide():($("#container_"+this.div_id).show(),$("#container_"+this.div_id).css({left:this.loc.x,top:this.loc.y}),$("#canvas_"+this.div_id).css({left:this.loc.x,top:this.loc.y}))}}function VideoItemDraw(e,t){if("DEAD"!=this.status){var s=e.strokeStyle,i=e.fillStyle,o=e.font;!1===this.highlight_dragbar?e.fillStyle="#222222":e.fillStyle="#444444",e.beginPath();var n=this.size.x;if(e.fillRect(this.loc.x,this.loc.y-20-t,n,20),e.stroke(),e.closePath(),e.strokeStyle="#555500",e.fillStyle="white",e.font="20px Arial",e.fillText("X",this.loc.x+this.size.x-20,this.loc.y-t-2,30,28),e.stroke(),e.font="20px Arial",this.url_set){e.fillStyle="#eeeeee";var a="Video";this.player&&this.player.config&&this.player.config.title&&(a=this.player.config.title),e.fillText(a,this.loc.x+10,this.loc.y-2-t,this.size.x-5),this.videoCanvasDraw(this.videoAssets,this.videoCtx)}else console.error("Something went wrong..."),this.deleted=!0;e.strokeStyle=s,e.fillStyle=i,e.font=o}}function VideoItemLoadVideo(){var e=this;if(""==this.url)return void alert("Please enter a video id first");if(this.video_loaded)return void console.log("Attempting to reload the video");if(""!==this.url){var t=function(e,t){var s=e.getBoundingClientRect();return{x:(t.clientX-s.left)/(s.right-s.left)*e.width,y:(t.clientY-s.top)/(s.bottom-s.top)*e.height}};this.size=new Location(600,400);if(!youtube_parser(this.url))return void alert("The given URL is not a video URL");this.url_set=!0,this.player.speed=1,this.currentPlayerState={currentTime:this.player.currentTime,status:getCurrentStatus(this.player),playbackRate:this.player.speed},s=this,this.player.on("play",function(t){.5==t.detail.plyr.speed&&t.detail.plyr.currentTime<15&&(e.lockSocketSend=!0,e.currentPlayerState.playbackRate=1,e.player.speed=1,e.lockSocketSend=!1),$("#canvas_"+e.div_id).hide()}),this.player.on("pause",function(t){$("#canvas_"+e.div_id).show()}),this.player.on("seeking",function(t){e.lockSocketSend=!0,setTimeout(function(){e.lockSocketSend=!1},1e3)}),this.player.on("ready",function(t){e.lockSocketSend=!1}),this.intervals.push(setInterval(function(){if("paused"==getCurrentStatus(s.player)&&$("div.plyr").hasClass("plyr--menu-open")?$("#canvas_"+e.div_id).hide():"paused"!=getCurrentStatus(s.player)||$("div.plyr").hasClass("plyr--menu-open")||$("#canvas_"+e.div_id).show(),"DEAD"!=s.status&&!e.lockSocketSend){var t=parseInt($("#container_"+e.div_id).css("width").slice(0,-2)),i=parseInt($("#container_"+e.div_id).css("height").slice(0,-2));t==e.size.x&&i==e.size.y||(document.getElementById("canvas_"+e.div_id).style.height=i-45+"px",document.getElementById("canvas_"+e.div_id).style.width=t+"px",e.size.x=t,e.size.y=i,e.isResized=!0);var o=!1;e.lockSocketSend||e.currentPlayerState.status==getCurrentStatus(e.player)||(e.currentPlayerState.status=getCurrentStatus(e.player),o=!0),!e.lockSocketSend&&is_float(e.player.speed)&&e.player.currentTime>5&&e.currentPlayerState.playbackRate!=e.player.speed&&(e.currentPlayerState.playbackRate=e.player.speed,o=!0),!e.lockSocketSend&&Math.abs(e.currentPlayerState.currentTime-e.player.currentTime)>=5&&(o=!0),e.currentPlayerState.currentTime=e.player.currentTime,e.lockSocketSend||!0!==o||(e.event_to_send=e.currentPlayerState,o=!1)}},500));var s=this;this.canvas.addEventListener("mousedown",function(s){if(!0===e.annotationsFlashing)return void resolveNewStatus("paused",e.player);e.mouseCanvasVideoClick=!0;var i=t(e.canvas,s),o={type:void 0,component:null,percent:retrievePercentFromPlayerProgress(e.player)};if(-1!==window.cursor.selected.indexOf("SHAPE_")){var n=window.cursor.selected.replace("SHAPE_","");switch(n){case"SQUARE":o.component=new ShapeSquare(i.x,i.y);break;case"CIRCLE":o.component=new ShapeCircle(i.x,i.y);break;case"TRIANGLE":o.component=new ShapeTriangle(i.x,i.y);break;case"LINE":o.component=new ShapeLine(i.x,i.y);break;default:console.error("Undefined function called ["+n+"].")}o.type=o.component.type,e.start_loc=new Location(i.x,i.y),e.videoAssets.push(o)}else e.videoAssets.push({type:"line",component:new Line(i.x,i.y),percent:retrievePercentFromPlayerProgress(e.player)});cursor.disabled=!0}),this.canvas.addEventListener("mouseleave",function(){!0===e.mouseCanvasVideoClick&&e.videoAssets.length&&(s.video_assets=e.videoAssets[e.videoAssets.length-1],appendAnnotationsBlock(s.video_assets.percent,s.div_id)),e.mouseCanvasVideoClick=!1,cursor.disabled=!1}),this.canvas.addEventListener("mouseup",function(){!0===e.mouseCanvasVideoClick&&e.videoAssets.length&&(s.video_assets=e.videoAssets[e.videoAssets.length-1],appendAnnotationsBlock(s.video_assets.percent,s.div_id)),e.start_loc=null,e.mouseCanvasVideoClick=!1,cursor.disabled=!1}),this.canvas.addEventListener("mousemove",function(s){cursor.disabled=!0;var i=t(e.canvas,s);if(1==e.mouseCanvasVideoClick)if(-1!==window.cursor.selected.indexOf("SHAPE_")){var o=i.x-e.start_loc.x,n=i.y-e.start_loc.y;e.videoAssets[e.videoAssets.length-1].component.resize(o,n),e.start_loc.x=i.x,e.start_loc.y=i.y}else e.videoAssets[e.videoAssets.length-1].component.add_point(i.x,i.y)})}}function retrievePercentFromPlayerProgress(e){var t=e.duration/100,s=e.currentTime/t;return s||0}function youtube_parser(e){if(11==e.length)return e;var t=/(?:youtu\.be\/|youtube\.com(?:\/embed\/|\/v\/|\/watch\?v=|\w\/\w\/.*\/))([^\/&]{10,12})/,s=e.match(t);return!(!s||!Array.isArray(s)||11!=s[1].length)&&s[1]}function VideoItemJumpTo(e){}function videoCanvasDraw(e,t){var s=this;if(t.clearRect(0,0,this.size.x,355),0!=e.length)for(var i=retrievePercentFromPlayerProgress(this.player),o=0;o<e.length;o++)"paused"===getCurrentStatus(this.player)&&Math.abs(i-e[o].percent)<=2?(e[o].component.componentsize=2,e[o].component.draw(t,-7.5,void 0)):Math.abs(i-e[o].percent)<=.1&&(e[o].component.componentsize=2,e[o].component.draw(t,-7.5,void 0),this.annotationsFlashing=!0,$("#canvas_"+this.div_id).is(":visible")||($("#canvas_"+this.div_id).show(),setTimeout(function(){$("#canvas_"+s.div_id).hide(),s.annotationsFlashing=!1},500)))}function VideoItemHandleEvent(e){if(this.player)if("size"in e&&e.size&&e.size.x&&e.size.y)this.lockSocketSend=!0,$("#container_"+this.div_id).css("width",e.size.x),$("#container_"+this.div_id).css("height",e.size.y),document.getElementById("canvas_"+this.div_id).style.height=e.size.y-45+"px",document.getElementById("canvas_"+this.div_id).style.width=e.size.x+"px",this.size.x=e.size.x,this.size.y=e.size.y,this.lockSocketSend=!1;else if("assets_to_append"in e&&e.assets_to_append.length>0){for(var t=new Line(e.assets_to_append[0].x,e.assets_to_append[0].y),s=1;s<e.assets_to_append.length;s++)t.add_point(e.assets_to_append[s].x,e.assets_to_append[s].y);this.videoAssets.push({percent:retrievePercentFromPlayerProgress(this.player),component:t})}else this.lockSocketSend=!0,this.currentPlayerState.status==e.status&&Math.abs(this.currentPlayerState.currentTime-e.currentTime)>=5&&(this.player.currentTime=e.currentTime,this.currentPlayerState.currentTime=e.currentTime),this.currentPlayerState.playbackRate=e.playbackRate,this.player.speed=e.playbackRate,this.currentPlayerState.status!=e.status&&(this.currentPlayerState.status=e.status,resolveNewStatus(e.status,this.player),this.currentPlayerState.currentTime=e.currentTime,this.player.currentTime=e.currentTime),this.lockSocketSend=!1}function VideoItemPlayVideo(e){}function VideoItemStopVideo(){}function VideoItemKeyPressed(e){if(!this.url_set)return"Shift"==e?void(this.shift_down=!0):"Control"==e||"Meta"==e?void(this.control_down=!0):!this.url_set&&this.control_down&&"v"==e?void navigator.clipboard.readText().then(function(e){for(var t=!1,s=0;s<cursor.assets.length&&!t;s++)"videoitem"===cursor.assets[s].type&&!1===cursor.assets[s].url_set&&(t=!0,cursor.assets[s].url=e.replace(/\s/g,""))}):"Alt"==e?void(this.alt_down=!0):void("Backspace"==e&&""!=this.url?this.url=this.url.substring(0,this.url.length-1):"Meta"==e||("Enter"==e?this.load_video():"Backspace"!=e&&(this.shift_down?this.url+=e.toUpperCase():this.url+=e)))}function VideoItemKeyUp(e){"Shift"==e&&(this.shift_down=!1),event.ctrlKey&&86===event.keyCode&&navigator.clipboard.readText().then(function(e){for(var t=!1,s=0;s<cursor.assets.length&&!t;s++)"videoitem"===cursor.assets[s].type&&!1===cursor.assets[s].url_set&&(t=!0,cursor.assets[s].url+=e.replace(/\s/g,""))})}function VideoItemClick(e,t,s){return e>this.loc.x+this.size.x-25&&e<this.loc.x+this.size.x+15&&t<this.loc.y-s&&t>this.loc.y-33-s&&(this.deleted=!0),e>this.loc.x+this.size.x-80&&e<this.loc.x+this.size.x&&t>this.loc.y&&t<this.loc.y+40&&(this.load_video(),!0)}function VideoItemHitTest(e,t){return e>this.loc.x&&e<this.loc.x+this.size.x&&t>this.loc.y-20&&t<this.loc.y?(this.highlight_dragbar=!0,!0):(this.highlight_dragbar=!1,!1)}function VideoItemResize(e,t){var s=this.size.x,i=this.size.y,o=this.size.x+=e,n=s/i,a=o/n;o=a*n,this.size.x=o,this.size.y=a,$("#"+this.div_id).hide()}function VideoItemFinishResize(){}function VideoItemStartResize(){}function VideoItemHitTestResize(e,t,s){return e>this.loc.x+this.size.x-20&&e<this.loc.x+this.size.x&&t>this.loc.y+this.size.y-s-20&&t<this.loc.y+this.size.y-s}function VideoItemBodyHitTest(e,t,s){return e>this.loc.x&&e<this.loc.x+this.size.x&&t>this.loc.y-s&&t<this.loc.y+this.size.y-s}function VideoItemGetLowestPoint(){return this.loc.y+this.size.y}function VideoItemAddAsset(e){if("templine"!=e.type)return void console.log("ERROR: We only handle temporary lines for videoitems");for(var t=0;t<e.points.length;t++)e.points[t].x-=this.loc.x,e.points[t].y-=this.loc.y-this.offset;this.assets.push(e),this.assets_dirty=!0}function getCurrentStatus(e){return!0===e.playing||!0===e.buffered?"playing":(e.paused,"paused")}function resolveNewStatus(e,t){"playing"==e?t.play():"paused"==e&&t.pause()}