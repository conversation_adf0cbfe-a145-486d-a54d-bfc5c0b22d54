function VoxeetWindow(t,i){this.loaded=!0,this.uploaded=!0,this.sent="",this.update=VoxeetUpdate,this.draw=VoxeetWindowDraw,this.hittest=VoxeetWindowHitTest,this.assets_deleted=!0,this.assets=[],this.assets_dirty=!1,this.add_asset=VoxeetWindowAddAsset,this.server_file_name="",this.clicked=voxeet_clicked,this.scroll=voxeet_scroll,this.loc=new Location(t,i),this.size=new Location(t,i),this.type="voxeetwindow",this.moved=!1,this.id=uuidv4().replace(/-/g,""),this.isSelected=!1,this.offset=0,!1===this.sent?$("#video-window").css({left:"65%",top:i}):$("#video-window").css({left:t,top:i})}function VoxeetControllerChangeSize(t,i){this.size.x=t,this.size.y=i,this.is_dirty=!0}function voxeet_scroll(){}function VoxeetUpdate(){var t=$("#video-window").offset(),i=t.top,s=t.left;this.is_dirty=!0,this.loc.x=s,this.loc.y=i;var e={width:$(window).width(),height:$(window).height()};this.loc.x>e.width&&(this.loc.x=50),this.loc.y>e.height&&(this.loc.y=50)}function VoxeetWindowDraw(t,i){}function voxeet_clicked(t){if(console.log("Clicked - wox",t),this.is_selected=!0,this.moved=!0,this.sent=!0,"line"!=t.type)return void console.log("ERROR: We only handle lines");for(var i=0;i<t.points.length;i++)t.points[i].x-=this.loc.x,t.points[i].y-=this.loc.y;console.log(t.points),this.assets.push(t),this.assets_dirty=!0}function VoxeetWindowHitTest(t,i){this.moved=!0;var s=$("#video-window").offset(),e=s.top,o=s.left;this.is_dirty=!0,this.loc.x=o,this.loc.y=e;var s=$("#video-window").offset(),e=s.top,o=s.left;this.loc=new Location(o,e),this.loc.x=o,this.loc.y=e;var h=scale[3],n=Math.round(h*this.size.x);Math.round(h*this.size.y);return t>this.loc.x&&t<this.loc.x+n&&i>this.loc.y-20&&i<this.loc.y}function VoxeetWindowAddAsset(t){if("line"!=t.type)return void console.log("ERROR: We only handle lines for textboxes");for(var i=0;i<t.points.length;i++)t.points[i].x-=this.loc.x,t.points[i].y-=this.loc.y;console.log(t.points),this.assets.push(t),this.assets_dirty=!0}