function asyncGeneratorStep(e,s,i,t,o,r,d){try{var a=e[r](d),n=a.value}catch(e){return void i(e)}a.done?s(n):Promise.resolve(n).then(t,o)}function _asyncToGenerator(e){return function(){var s=this,i=arguments;return new Promise(function(t,o){function r(e){asyncGeneratorStep(a,t,o,r,d,"next",e)}function d(e){asyncGeneratorStep(a,t,o,r,d,"throw",e)}var a=e.apply(s,i);r(void 0)})}}function _typeof(e){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}$(document).ready(function(){function e(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];if("pdfitem"===e.type&&r++,"line"===e.type){var s=new Line;return s.points=e.points,s.linesize=e.linesize?e.linesize:e.size,s.color=e.color,s.div_id=e.id,s.id=e.div_id,s}if("templine"===e.type){var s=new TempLine;return s.points=e.points,s.linesize=e.linesize,s.color=e.color,s}if("imageitem"===e.type)createVideo(e.videoId);else{if("textbox"===e.type){var i=textbox=new TextBox(e.loc.x,e.loc.y,e.filename);return i.guid=e.id,i.size.x=e.loc.w,i.size.y=e.loc.h,i.setup(),i.clicked(506,65,0),setTimeout(function(){i.clicked(506,65,0)},500),i}if("pdfitem"===e.type){var t=new PdfItem(null,e.loc.x,e.loc.y,e.filename);return t.img={},t.img.src=e.src,t.size.x=e.size.x,t.size.y=e.size.y,t.id=e.id,t.sent=!0,t.filename=e.filename,t.uploaded=!0,t.loaded=!0,t}if("videoitem"===e.type){var o=new VideoItem(e.loc.x,e.loc.y);return o.size.x=e.size.x,o.size.y=e.size.y,e.sent=!0,o.url=e.url,o.div_id=e.id,o}if("voxeetwindow"===e.type){var d=new VoxeetWindow(e.loc.x,e.loc.y);return e.sent=!1,d.div_id=e.div_id,d.id=e.id,d.filename=e.filename,d}if(e.parent&&"shape"===e.parent){var a=null;switch(e.type){case"shape_circle":a=new ShapeCircle(e.loc.x,e.loc.y);break;case"shape_line":a=new ShapeLine(e.loc.x,e.loc.y);break;case"shape_square":a=new ShapeSquare(e.loc.x,e.loc.y);break;case"shape_triangle":a=new ShapeTriangle(e.loc.x,e.loc.y);break;default:console.error("Undefined shape type ["+e.type+"].")}return a.size=e.size,a.linesize=e.linesize,a.color=e.color,a.id=e.id,a.div_id=e.id,a.guid=e.id,a}}}function s(){for(var e=[],r=[],d=0;d<cursor.assets.length;d++){var a=cursor.assets[d];if("line"==a.type&&0==a.sent){var n={id:a.id,div_id:a.id,type:"line",points:a.points,size:a.linesize,color:a.color,deleted:!1};e.push(n),cursor.assets[d].sent=!0}else if("line"===a.type&&!0===a.deleted){var n={id:a.id,div_id:a.id,type:"linedeleted",deleted:!0};e.push(n),a.deleted=!1,a.status="DEAD"}else if("templine"===a.type&&0==a.sent){var n={type:"templine",points:a.points,size:a.linesize,color:a.color};e.push(n),cursor.assets[d].sent=!0}else if("imageitem"===a.type&&!1===a.sent&&!0===a.uploaded){var n={type:"imageitem",id:a.id,div_id:a.id,filename:a.filename,src:a.img.src,loc:{x:a.loc.x,y:a.loc.y},size:{x:a.size.x,y:a.size.y}};e.push(n),cursor.assets[d].sent=!0}else if("imageitem"!==a.type||!0!==a.isDrag&&!0!==a.isResize)if("videoitem"!==a.type||!0!==a.isDrag&&!0!==a.isResize)if("pdfitem"===a.type&&!1===a.sent&&!0===a.uploaded){cursor.assets[d].sent=!0;var n={type:"pdfitem",id:a.id,div_id:a.id,file:a.file,filename:a.filename,src:a.img.src,loc:{x:a.loc.x,y:a.loc.y},size:{x:a.size.x,y:a.size.y}};a.assets_dirty=!1,e.push(n)}else if("pdfitem"===a.type&&!0===a.sent&&!0===a.moved){var n={type:"pdfitemmove",id:a.id,div_id:a.id,src:a.img.src,loc:{x:a.loc.x,y:a.loc.y},size:{x:a.size.x,y:a.size.y}};cursor.assets[d].moved=!1,e.push(n)}else if("pdfitem"===a.type&&!0===a.resized){var n={id:a.id,type:"pdfitemresized",div_id:a.id,size:a.size};a.resized=!1,e.push(n)}else if("pdfitem"===a.type&&!0===a.deleted){var n={id:a.id,type:"pdfitemdeleted",div_id:a.id,src:"",loc:{x:a.loc.x,y:a.loc.y},status:"DEAD"};cursor.assets[d].moved=!1,a.deleted=!1,a.loaded=!1,a.uploaded=!1,a.sent=!1,e.push(n)}else if("voxeetwindow"===a.type&&!0===a.moved){var n={type:"voxeetwindow",id:a.id,loc:{x:a.loc.x,y:a.loc.y},size:{x:a.size.x,y:a.size.y}};a.moved=!1,e.sent=!0,e.push(n),cursor.assets[d].sent=!0}else if("voxeetwindow"===a.type&&!0===a.sent&&!0===a.assets_dirty){var n={type:"voxeetwindow",id:a.id,loc:{x:a.size.x,y:a.size.y},size:{x:a.size.x,y:a.size.y}};e.push(n),cursor.assets[d].sent=!1}else if("imageitem"===a.type&&!0===a.sent&&!0===a.moved){var n={type:"imageitemmove",id:a.id,div_id:a.id,src:a.img.src,loc:{x:a.loc.x,y:a.loc.y},size:{x:a.size.x,y:a.size.y}};cursor.assets[d].moved=!1,e.push(n)}else if("imageitem"===a.type&&!0===a.deleted){var n={id:a.id,type:"imagedeleted",div_id:a.id,src:"",loc:{x:-1e3,y:-1e3},size:{x:a.size.x,y:a.size.y}};cursor.assets[d].moved=!1,a.deleted=!1,a.loaded=!1,a.uploaded=!1,a.sent=!1,e.push(n)}else if("textbox"===a.type&&!0===a.isResized){console.log("[TEXTBOX] is resized");var n={type:"textboxresize",loc:{x:a.loc.x,y:a.loc.y,w:a.size.x,h:a.size.y},id:a.guid};a.isResized=!1,cursor.assets[d].sent=!0,e.push(n)}else if("textbox"===a.type&&1==a.dataToSend){console.log("[TEXTBOX] data to send");var n={type:"textbox_hover",isHovered:a.isHovered,hoverColor:a.hoverColor,id:a.guid};cursor.assets[d].sent=!0,e.push(n),a.dataToSend=!1}else if("textbox"==a.type&&window.textbox_changed&&!0===window.textbox_changed)console.log("[TEXTBOX] Sending update."),e.push({type:"textbox_text_update",text:a.last_text,id:a.guid}),window.textbox_changed=!1;else if("textbox"==a.type&&1==a.assets_deleted){console.log("[TEXTBOX] Deleted");var n={type:"textboxassetclear",id:a.guid};cursor.assets[d].assets_deleted=!1,e.push(n)}else if("videoitem"==a.type&&0==a.sent&&a.url_set){var n={type:"videoitem",url:a.url,loc:{x:a.loc.x,y:a.loc.y},size:{x:a.size.x,y:a.size.y},id:a.div_id};cursor.assets[d].sent=!0,e.push(n)}else if("videoitem"==a.type&&1==a.sent&&1==a.moved){var n={type:"videoitemmove",loc:{x:a.loc.x,y:a.loc.y},id:a.div_id};cursor.assets[d].moved=!1,e.push(n)}else if("videoitem"===a.type&&!0===a.deleted){var n={type:"videoitemdelete",div_id:a.div_id,id:a.div_id,deleted:!0};e.push(n),cursor.assets[d].deleted=!1,cursor.assets[d].status="DEAD"}else if("vue_videoitem"===a.type&&!0===a.isCreated){var n={type:"videoitemcreate",videoId:a.videoId,playerObject:a.playerObject,isPlay:a.isPlay};e.push(n),cursor.assets[d].isCreated=!1}else if("vue_videoitem"===a.type&&!0===a.videoEvent){var n={type:"vue_videoitemevent",videoId:a.videoId,isPlay:a.isPlay,currentTime:a.currentTime,isSeeking:a.isSeeking,isMute:a.isMute,volumeValue:a.volumeValue,speedValue:a.speedValue,isClose:a.isClose,videoAssets:a.videoAssets};e.push(n),cursor.assets[d].videoEvent=!1,cursor.assets[d].isSeeking=!1}else if("vue_videoitem"===a.type&&!0===a.addCanvasDraw){var n={type:"vue_videoitemdraw",videoId:a.videoId,videoAssets:a.videoAssets};e.push(n),cursor.assets[d].addCanvasDraw=!1}else if("vue_imageitem"===a.type&&!0===a.isCreated){var n={type:"vueimageitemcreate",imageId:a.imageId,imageUrl:a.imageUrl,imageName:a.imageName,isCreated:a.isCreated};e.push(n),cursor.assets[d].isCreated=!1}else if("vue_imageitem"===a.type&&!0===a.isClose){var n={type:"vueimageitemclose",imageId:a.imageId,imageUrl:a.imageUrl,imageName:a.imageName,isClose:a.isClose,isClosed:a.isClosed};e.push(n),cursor.assets[d].isClose=!1}else if("vue_imageitem"!==a.type||!0!==a.isResize&&!0!==a.isDrag)if("vue_videoitem"!==a.type||!0!==a.isResize&&!0!==a.isDrag)if("videoitem"===a.type&&null!=a.event_to_send){var n={type:"videoitemevent",id:a.div_id,data:a.event_to_send};e.push(n),cursor.assets[d].event_to_send=null}else if("videoitem"===a.type&&null!=a.video_assets){var n={type:"videoassets",id:a.div_id,div_id:a.div_id,asset:a.video_assets,status:"active"};e.push(n),cursor.assets[d].video_assets=null}else if("videoitem"===a.type&&1==a.isResized){var n={type:"videoitemresize",id:a.div_id,data:{isResized:a.isResized,size:a.size}};e.push(n),cursor.assets[d].isResized=!1}else if(a.parent&&"shape"==a.parent&&!1===a.sent){var n={type:a.type,parent:a.parent,id:a.id,loc:a.loc,size:a.size,linesize:a.linesize,color:a.color};e.push(n),cursor.assets[d].sent=!0}else if(a.parent&&"shape"==a.parent&&!0===a.deleted){var n={type:a.type+"_deleted",parent:a.parent,id:a.id,div_id:a.div_id,status:"DEAD"};e.push(n),cursor.assets[d].deleted=!1,cursor.assets[d].status="DEAD"}else window.provider&&!0===window.provider.changed&&(window.provider.changed=!1,r.push({type:"video_provider_changed",provider:window.provider.type}));else{var n={type:"vue_videoitemresize",videoId:a.videoId,isDrag:a.isDrag,isResize:a.isResize,position:a.position,size:a.size};e.push(n),cursor.assets[d].isDrag=!1,cursor.assets[d].isResize=!1}else{var n={type:"vue_imageitemresize",imageId:a.imageId,isDrag:a.isDrag,isResize:a.isResize,position:a.position,size:a.size};e.push(n),cursor.assets[d].isDrag=!1,cursor.assets[d].isResize=!1}else{var n={type:a.type,id:a.id,videoId:a.videoId,position:{top:a.position.top,left:a.position.top},size:{width:a.size.width,height:a.size.height}};e.push(n),cursor.assets[d].sent=!0}else{var n={type:a.type,id:a.id,imageUrl:a.imageUrl,imageId:a.imageId,position:{top:a.position.top,left:a.position.top},size:{width:a.size.width,height:a.size.height}};e.push(n),cursor.assets[d].sent=!0}}var u={cursor:{x:cursor.loc.x,y:cursor.loc.y,offset:cursor.offset},screen:{width:l,height:c}},v={id:i,data:u,role:t,assets:e,commands:r};null!==o&&o.emit("classroom update",JSON.stringify(v)),r.length>0&&window.location.reload(!1),setTimeout(s,100)}var i=getParameterFromURL("roomid"),t=window.langu_role;void 0!=t&&""!=t&&null!=t||(t="student");var o=null;void 0!==window.ws&&void 0!==window.ws.socket?o=window.ws.socket:console.log("We don't have a websocket - are we debugging?");var r=0,d=0,a=0,n=[];null!=o&&(o.on("asset update",function(s){if("object"!==_typeof(s))var i=JSON.parse(s);else var i=s;null==window.provider?window.provider={type:i.provider,changed:!1}:window.provider.type!=i.provider&&(window.provider={type:i.provider,changed:!1},window.location.reload(!1)),null==window.othercursor&&(window.othercursor="teacher"==t?new Cursor("#3C87F8",!1):new Cursor("#7FB802",!0)),i.components.forEach(function(e){store.commit("ADD_COMPONENT",e)});var o=[];window.othercursor.loc.x=i.cursor.x,window.othercursor.loc.y=i.cursor.y,void 0!==i.cursor.screen_width&&0!==i.cursor.screen_width&&(window.othercursor.screen.width=i.cursor.screen_width),void 0!==i.cursor.screen_height&&0!==i.cursor.screen_height&&(window.othercursor.screen.height=i.cursor.screen_height),void 0!==i.cursor.offset&&(window.othercursor.offset=i.cursor.offset),o=i.assets;for(var r=0;void 0!==i.refresh_assets&&null!=i.refresh_assets&&r<i.refresh_assets.length;r++)if("textboxchange"==i.refresh_assets[r].type)for(var l=0;l<window.cursor.assets.length;l++)window.cursor.assets[l].guid===i.refresh_assets[r].id&&(window.cursor.assets[l].text=i.refresh_assets[r].text,window.cursor.assets[l].display_text_array=i.refresh_assets[r].data);else if("imageitemmove"==i.refresh_assets[r].type){for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.div_id===i.refresh_assets[r].id&&(cursor.assets[l].loc.x=i.refresh_assets[r].loc.x,cursor.assets[l].loc.y=i.refresh_assets[r].loc.y,cursor.assets[l].size.x=i.refresh_assets[r].size.x,cursor.assets[l].size.y=i.refresh_assets[r].size.y,c=!0)}console.log("We got a refresh item for text box moving")}else if("imagedeleted"==i.refresh_assets[r].type){for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.div_id===i.refresh_assets[r].id&&(cursor.assets[l].loc.x=-1e3,cursor.assets[l].loc.y=-1e3,c=!0)}console.log("We got a refresh item for text box moving")}else if("linedeleted"===i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.id===i.refresh_assets[r].id&&(u.points=[],u.status="DEAD",c=!0)}else if("videoitemdelete"===i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];if(u.div_id===i.refresh_assets[r].div_id){$("[id*="+window.cursor.assets[l].div_id+"]").remove();for(var v=0;v<window.cursor.assets[l].intervals.length;v++)clearInterval(window.cursor.assets[l].intervals[v]);delete window.cursor.assets[l],window.cursor.assets.splice(l,1),c=!0}}else if("pdfitemmove"==i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.id===i.refresh_assets[r].id&&(u.loc=i.refresh_assets[r].loc,c=!0)}else if("pdfitemdeleted"==i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.id===i.refresh_assets[r].id&&(u.status="DEAD",c=!0)}else if("pdfitemresized"==i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.id===i.refresh_assets[r].id&&(u.size=i.refresh_assets[r].size,c=!0)}else if("voxeetwindowmove"==i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.div_id===i.refresh_assets[r].id&&(cursor.assets[l].loc.x=i.refresh_assets[r].loc.x,cursor.assets[l].loc.y=i.refresh_assets[r].loc.y,cursor.assets[l].size.x=i.refresh_assets[r].size.x,cursor.assets[l].size.y=i.refresh_assets[r].size.y,c=!0)}else if("videoitemmove"==i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.div_id==i.refresh_assets[r].id&&(cursor.assets[l].loc.x=i.refresh_assets[r].loc.x,cursor.assets[l].loc.y=i.refresh_assets[r].loc.y,c=!0)}else if("videoitemevent"==i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.div_id==i.refresh_assets[r].id&&(cursor.assets[l].handle_event(i.refresh_assets[r]),c=!0)}else if("videoassets"==i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.div_id==i.refresh_assets[r].div_id&&(cursor.assets[l].handleVideoAssets(i.refresh_assets[r].asset),c=!0)}else if("videoitemresize"==i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.div_id==i.refresh_assets[r].id&&(cursor.assets[l].handle_event(i.refresh_assets[r]),c=!0)}else if("textboxmove"==i.refresh_assets[r].type)for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.guid==i.refresh_assets[r].id&&(cursor.assets[l].loc.x=i.refresh_assets[r].loc.x,cursor.assets[l].loc.y=i.refresh_assets[r].loc.y,cursor.assets[l].update_location(),c=!0)}else if(i.refresh_assets[r].parent&&"shape"===i.refresh_assets[r].parent&&-1!==i.refresh_assets[r].type.indexOf("deleted")){for(var c=!1,l=0;l<cursor.assets.length&&0==c;l++){var u=cursor.assets[l];u.id==i.refresh_assets[r].id&&(cursor.assets[l].status="DEAD",c=!0)}c||console.error("Failed to find shape component ["+i.refresh_assets[r].type+"] by ID ["+i.refresh_assets[r].id+"].")}else{i.refresh_assets.forEach(function(e){"pdfitem"===e.type&&0===a&&d++}),a=1;var p=e(i.refresh_assets[r],d);if(void 0!==p&&("textbox"==p.type||"videoitem"==p.type||"imageitem"==p.type||"voxeetwindow"==p.type||"pdfitem"==p.type||p.parent&&"shape"==p.parent)){for(var c=!1,l=0;l<window.cursor.assets.length;l++){if("textbox"==p.type&&window.cursor.assets[l].guid==p.guid){console.log("we have this asset already (during refresh)"),c=!0;break}if("videoitem"==p.type&&window.cursor.assets[l].div_id==p.div_id){c=!0;break}if("pdfitem"==p.type&&window.cursor.assets[l].div_id==p.id){c=!0;break}if("voxeetwindow"==p.type&&window.cursor.assets[l].div_id==p.div_id){c=!0;break}if("imageitem"==p.type&&window.cursor.assets[l].id==p.id){c=!0;break}if(p.parent&&"shape"===p.parent&&window.cursor.assets[l].id==p.id){c=!0;break}}c||("videoitem"==p.type&&(p.load_video(),p.is_master=!0),p.sent=!0,p.never_send=!0,window.cursor.assets.push(p))}else void 0!==p&&null!==p&&(p.sent=!0,p.never_send=!0,window.cursor.assets.push(p))}void 0!==i.refresh_assets&&null!=i.refresh_assets&&i.refresh_assets.length>0&&window.cursor.refresh_lowest_point();for(var r=0;r<o.length;r++){var p=o[r];if("textbox_text_update"===p.type){console.log("[TEXTBOX] Received update!",p);for(var l=0;l<window.cursor.assets.length;l++)"textbox"===window.cursor.assets[l].type&&(window.cursor.assets[l].text="Sample text")}else if("imageitemmove"==p.type){for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++)window.cursor.assets[l].loc.x=p.loc.x,window.cursor.assets[l].loc.y=p.loc.y,window.cursor.assets[l].size.x=p.size.x,window.cursor.assets[l].size.y=p.size.y,window.cursor.assets[l].moved=!0,c=!0;c||(console.log(p),console.log("failed to find video item with div_id "+p.id))}else if("imagedeleted"===p.type){for(var r=0;void 0!==o&&null!==o&&r<o.length;r++)if("imageitem"===o[r].type)for(var l=0;void 0!==n&&null!==n&&l<n.length;l++)if(o[r].id===n[l].id)for(var w=0;void 0!==window.othercursor.assets&&null!==window.othercursor.assets&&w<window.othercursor.assets.length;w++)window.othercursor.assets[w].id===n[l].id&&(window.othercursor.assets[w].loaded=!1,window.othercursor.assets[w].uploaded=!1,window.othercursor.assets[w].sent=!1,window.othercursor.assets[w].src="")}else if("pdfitemmove"==p.type){for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++){var u=window.cursor.assets[l];"pdfitem"===u.type&&u.id===p.id&&(window.cursor.assets[l].loc=p.loc,window.cursor.assets[l].size=p.size,window.cursor.assets[l].moved=!1,c=!0)}c||(console.log(p),console.log("failed to find pdf item with id "+p.id))}else if("pdfitemdeleted"==p.type){for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++){var u=window.cursor.assets[l];"pdfitem"===u.type&&u.id===p.id&&(window.cursor.assets[l].status="DEAD",c=!0)}c||(console.log(p),console.log("failed to find pdf item with id "+p.id))}else if("pdfitemresized"==p.type){for(var c=!1,l=0;l<window.othercursor.assets.length&&!c;l++){var u=window.cursor.assets[l];"pdfitem"===u.type&&u.id===p.id&&(window.cursor.assets[l].size=p.size,window.cursor.assets[l].resized=!1,c=!0)}c||(console.log(p),console.log("failed to find pdf item with id "+p.id))}else if("voxeetwindowmove"==p.type){for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++){var u=window.cursor.assets[l];"voxeetwindow"===u.type&&u.div_id===p.id&&(window.cursor.assets[l].loc.x=p.loc.x,window.cursor.assets[l].loc.y=p.loc.y,window.cursor.assets[l].size.x=p.size.x,window.cursor.assets[l].size.y=p.size.y,window.cursor.assets[l].moved=!1,c=!0)}c||(console.log(p),console.log("failed to find video item with div_id "+p.id))}else if("textboxmove"==p.type){for(var c=!1,l=0;l<window.cursor.assets.length;l++){var u=window.cursor.assets[l];"textbox"==u.type&&p.id==u.guid&&(window.cursor.assets[l].loc.x=p.loc.x,window.cursor.assets[l].loc.y=p.loc.y,window.cursor.assets[l].moved=!1,c=!0)}c||console.log("Failed to find the textbox with id: "+p.id)}else if("videoitemmove"==p.type)for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++){var u=window.cursor.assets[l];"videoitem"==u.type&&u.div_id==p.id&&(window.cursor.assets[l].loc.x=p.loc.x,window.cursor.assets[l].loc.y=p.loc.y,window.cursor.assets[l].moved=!1,c=!0)}else if("vueimageitemcreate"==p.type){var c=!1,f=document.getElementById("desc-".concat(p.imageId));window.cursor.assets.forEach(function(e){"vue_imageitem"===e.type&&e.imageId==p.imageId&&(itemObject=e)}),!f&&p.isCreated&&createImage(p.imageId,p.imageUrl,p.imageName,!0),p.isClose&&(itemObject.isClosed=!0),c=!0}else if("vueimageitemclose"==p.type){var c=!1;window.cursor.assets.forEach(function(e){"vue_imageitem"===e.type&&e.imageId==p.imageId&&(itemObject=e)}),p.isClose&&(itemObject.isClosed=!0),c=!0}else if("vue_imageitemresize"==p.type){var y=void 0;window.cursor.assets.forEach(function(e){"vue_imageitem"===e.type&&e.imageId===p.imageId&&(y=e)}),p.isDrag?(y.position.top=p.position.top,y.position.left=p.position.left):p.isResize&&(y.size.width=p.size.width,y.size.height=p.size.height)}else if("videoitemcreate"==p.type){var c=!1,h=document.getElementById("desc-".concat(p.videoId));h||(createVideo(p.videoId),window.cursor.assets.forEach(function(e){"vue_videoitem"===e.type&&(e.videoId,p.videoId)})),c=!0}else if("vue_videoitemevent"==p.type){var m=null;window.cursor.assets.forEach(function(e){"vue_videoitem"===e.type&&e.videoId===p.videoId&&(m=e)}),m&&(m.isMute=p.isMute,m.volumeValue=p.volumeValue,m.speedValue=p.speedValue,p.isPlay?m.isPlay=!0:p.isPlay||(m.isPlay=!1),p.isSeeking&&(m.currentTime=p.currentTime,p.isSeeking=!1),p.isClose&&(m.isClose=p.isClose))}else if("vue_videoitemdraw"==p.type){var _=null;window.cursor.assets.forEach(function(e){"vue_videoitem"===e.type&&e.videoId===p.videoId&&(_=e)}),_.canvasGetAssets=!0,_.videoAssets=p.videoAssets}else if("vue_videoitemresize"==p.type)window.cursor.assets.forEach(function(e){"vue_videoitem"===e.type&&e.videoId===p.videoId&&(videoObject=e)}),p.isDrag?(videoObject.position.top=p.position.top,videoObject.position.left=p.position.left):p.isResize&&(videoObject.size.width=p.size.width,videoObject.size.height=p.size.height);else if("cursor_move"==p.type){var g=null;window.cursor.assets.forEach(function(e){"cursor_move"===e.type&&e.id===p.id&&(g=e)}),g&&g.update(p.top,p.left,p.cursor_name)}else if("videoitemevent"==p.type)for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++){var u=window.cursor.assets[l];"videoitem"==u.type&&u.div_id==p.id&&(c=!0,p.currentTime||p.status||p.playbackRate?(window.cursor.assets[l].currentTime=p.currentTime,window.cursor.assets[l].status=p.status,window.cursor.assets[l].playbackRate=p.playbackRate,window.cursor.assets[l].handle_event(p)):window.cursor.assets[l].assets_to_append=p.assets_to_append)}else if("videoassets"==p.type)for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++){var u=window.cursor.assets[l];"videoitem"==u.type&&u.div_id==p.id&&(c=!0,cursor.assets[l].handleVideoAssets(p.asset))}else if("videoitemresize"==p.type)for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++){var u=window.cursor.assets[l];"videoitem"==u.type&&u.div_id==p.id&&(c=!0,p.size&&p.size.x&&p.size.y&&cursor.assets[l].handle_event(p))}else if("videoitemcanvas"==p.type)for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++){var u=window.cursor.assets[l];"videoitem"==u.type&&u.div_id==p.id&&(c=!0,window.cursor.assets[l].assets_to_append=p.assets_to_append)}else if("textbox_hover"==p.type)textbox.isHovered=p.isHovered,textbox.isHovered?$(".note-editor").css({outline:"2px solid ".concat("teacher"==window.langu_role?"#3C87F8":"#7FB802")}):$(".note-editor").css({outline:"none"});else if("textboxresize"===p.type)console.log("textbox size"),textbox.size.x=p.loc.w,textbox.size.y=p.loc.h;else if("linedeleted"===p.type){for(var c=!1,l=0;l<window.othercursor.assets.length&&!c;l++)window.othercursor.assets[l].id===p.id&&(c=!0,window.othercursor.assets[l].status="DEAD");c||console.error("Line component not found.",p,window.cursor.assets)}else if("videoitemdelete"===p.type){for(var c=!1,l=0;l<window.cursor.assets.length&&!c;l++)if(window.cursor.assets[l].div_id===p.div_id){$("[id*="+window.cursor.assets[l].div_id+"]").remove();for(var x=0;x<window.cursor.assets[l].intervals.length;x++)clearInterval(window.cursor.assets[l].intervals[x]);delete window.cursor.assets[l],window.cursor.assets.splice(l,1),c=!0}c||console.error("Video component not found.",p,window.cursor.assets)}else if(p.parent&&"shape"===p.parent&&-1!==p.type.indexOf("deleted")){for(var c=!1,l=0;l<window.othercursor.assets.length&&!c;l++)window.othercursor.assets[l].id===p.id&&(window.othercursor.assets[l].status="DEAD",c=!0);c||console.error("Failed to find shape component ["+p.type+"] by ID ["+p.id+"].")}else{var u=e(p);if(void 0!==u)if("textbox"==u.type){u.sent=!0,u.never_send=!0;for(var c=!1,l=0;l<window.cursor.assets.length;l++)if(window.cursor.assets[l].guid==u.guid){c=!0,console.log("we already have this "+u.type);break}c||("Messaging"==u.name?window.cursor.assets.unshift(u):window.cursor.assets.push(u))}else if("videoitem"==u.type){u.sent=!0,u.never_send=!0;for(var c=!1,l=0;l<window.cursor.assets.length;l++)if(window.cursor.assets[l].div_id==u.div_id){c=!0,console.log("We already have this "+u.type+" , div_id: "+u.id);break}c||(u.load_video(),window.cursor.assets.push(u))}else"pdfitem"==u.type?(u.size.x=p.size.x,u.size.y=p.size.y,window.cursor.assets.push(u)):window.othercursor.assets.push(u)}}return o.length>0&&window.othercursor.refresh_lowest_point(),!1}),o.on("status",function(e){var s=JSON.parse(e);void 0!==s.status&&s.status}),o.on("client_classroom_update",function(){var e=_asyncToGenerator(regeneratorRuntime.mark(function e(s){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(s=JSON.parse(s),window.receiver){e.next=3;break}throw new Error("Receiver instance is not initialized.");case 3:window.receiver.addUpdate(s).then(function(){});case 4:case"end":return e.stop()}},e)}));return function(s){return e.apply(this,arguments)}}()));var l=$(window).width(),c=$(window).height();setTimeout(function(){void 0!==o&&null!==o&&o.emit("refresh",{id:i,role:t})},1e3),setTimeout(s,3e3)});