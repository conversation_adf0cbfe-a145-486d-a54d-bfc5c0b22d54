!function(s){var t=moment(classroom.lesson.startDate),r=t.clone().add(classroom.lesson.length,"m"),a=(moment(),s("#lesson-timer")),e=function(s,t,r){r=r||"0";for(var a="",e=0;e<t;e++)a+=r;return(a+s).slice(-t)},n=function(s,t){var r=Math.abs(t.diff(s,"hours")%24),a=Math.abs(t.diff(s,"minutes")%60),n=Math.abs(t.diff(s,"seconds")%60);return": "+e(r,2)+":"+e(a,2)+":"+e(n,2)},o=function(s,t){if(0===t.diff(s,"days"))return n(s,t);var r=Math.abs(t.diff(s,"weeks")),a=Math.abs(t.diff(s,"days")),e="",o=" ";return r>1?(o+=r,e=Translator.trans("lesson.classroom.timer.weeks")):r>0?e=Translator.trans("lesson.classroom.timer.week"):a>1?(o+=a,e=Translator.trans("lesson.classroom.timer.days")):e=Translator.trans("lesson.classroom.timer.day"),o+e},i=Translator.trans("lesson.classroom.timer.ends_in"),l=Translator.trans("lesson.classroom.timer.starts_in");!function s(t,r){var e=moment();if(t<=e&&e<r){var n=i;n+=o(e,r),a.text()!==n&&a.text(n),setTimeout(s.bind(this,t,r),500)}else if(e<t){var n=l;n+=o(e,t),a.text()!==n&&a.text(n),setTimeout(s.bind(this,t,r),500)}else{var n=Translator.trans("lesson.classroom.timer.ended");a.text(n)}}(t,r)}(jQuery);