!function(){window.initializeTokbox=function(){function e(e){if(e)return void alert("Sorry, we encountered an error trying to connect to streaming session. Please report to Langu Team. Error code: "+e.code+", details: "+e.message);n.publish(r)}$("button#full-screen").hide();var o=classroom.ot.sessionId,s=classroom.ot.token,t=classroom.ot.apiKey,n=null,r=null,i=null,a=null,c=$(".stream-controls.remote"),d=$(".stream-controls.local"),l=$(".stream-controls.screenshare"),h=($("#tokbox-screenshare-stream-placeholder"),$("#video-window-shared")),u=$(".sidebar-tabs-container"),m=$("<div>").append($("#tokbox-other-stream-placeholder").clone()).html();if(1===OT.checkSystemRequirements()){var f={usePreviousDeviceSelection:!0,name:classroom.user.name,style:{nameDisplayMode:"off"},fitMode:"contain",mirror:!0,showControls:!1,frameRate:15};r=OT.initPublisher("tokbox-my-stream-placeholder",f,function(e){e&&"OT_USER_MEDIA_ACCESS_DENIED"!==e.name&&alert("The publishing stream could not be initialized. Please report to Langu Team. Error code: "+e.code+", details: "+e.message)}),r.on({streamCreated:function(e){$(e.target.element).css({width:"100%",height:"100%",position:"absolute"}),"screen"===e.stream.videoType?(l.removeClass("hidden"),h.removeClass("hidden")):d.removeClass("hidden")},accessDenied:function(e){alert("You have denied access to audio/video devices. Without the permission to access these devices we are unable to publish your stream")},streamDestroyed:function(e){e.preventDefault(),"screen"===e.stream.videoType?(l.addClass("hidden"),h.addClass("hidden")):d.addClass("hidden")}}),n=function(e,o){return OT.initSession(e,o)}(t,o),window.tokbox_session=n,n.on({sessionReconnecting:function(e){console.log("OT reconnecting")},sessionReconnected:function(e){console.log("OT reconnected")},sessionDisconnected:function(e){console.log("OT disconnected: "+e.reason)},connectionCreated:function(e){e.connection.connectionId,n.connection.connectionId},connectionDestroyed:function(e){},streamCreated:function(e){if("screen"===e.stream.videoType)return void(a=n.subscribe(e.stream,"tokbox-screenshare-stream-placeholder",{fitMode:"contain",style:{audioLevelDisplayMode:"off",buttonDisplayMode:"off",nameDisplayMode:"off"},mirror:!1},function(e){if(e)return console.log(e);$("#tokbox-screenshare-stream-placeholder").css({width:"100%"}),l.removeClass("hidden"),h.removeClass("hidden"),u.addClass("screenshare-active")}));a=n.subscribe(e.stream,"tokbox-other-stream-placeholder",{fitMode:"contain",style:{audioLevelDisplayMode:"off",buttonDisplayMode:"off",nameDisplayMode:"auto"},mirror:!1},function(e){e?alert("Sorry, we encountered an error trying to connect you to the remote stream. Please report to Langu Team. Error code: "+e.code+", details: "+e.message):($("#tokbox-other-stream-placeholder").css({width:"100%",height:"100%",position:"absolute"}),c.removeClass("hidden"))})},streamDestroyed:function(e){if("screen"===e.stream.videoType){var o=l.closest(".stream-container");return o.prepend('<div id="tokbox-screenshare-stream-placeholder"></div>'),l.addClass("hidden"),h.addClass("hidden"),void u.removeClass("screenshare-active")}c.addClass("hidden");var o=c.closest(".stream-container");o.prepend(m),console.log("OT partner stream destroyed: "+e.reason)}}),n.connect(s,e)}else alert("Sorry. Your browser does not support our streaming protocols. Please try latest version of Google Chrome or Mozilla Firefox.");var p=function(){var e=$(this),o=e.find("i");r.stream.hasVideo?(r.publishVideo(!1),o.text("videocam_off")):(r.publishVideo(!0),o.text("videocam"))},v=function(){var e=$(this),o=e.find("i");r.stream.hasAudio?(r.publishAudio(!1),o.text("mic_off")):(r.publishAudio(!0),o.text("mic"))},b=function(){var e=$(this),o=e.find("i");if(null!=i){i.destroy();l.closest(".stream-container").prepend('<div id="tokbox-screenshare-stream-placeholder"></div>'),u.removeClass("screenshare-active"),h.addClass("hidden"),i=null,o.text("screen_share")}else i=OT.initPublisher("tokbox-screenshare-stream-placeholder",{videoSource:"screen",showControls:!1,resolution:"640x480",frameRate:7}),h.removeClass("hidden"),u.addClass("screenshare-active"),n.publish(i),o.text("stop_screen_share")},y=function(e){switch($(this).data("action")){case"video-toggle":p.call(this);break;case"audio-toggle":v.call(this);break;case"screenshare-toggle":b.call(this)}};$("#video-window-buttons").on("click","button",y)}}();