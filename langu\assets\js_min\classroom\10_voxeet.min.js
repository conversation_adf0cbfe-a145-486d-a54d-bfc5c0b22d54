function asyncGeneratorStep(e,n,t,o,a,r,i){try{var s=e[r](i),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(o,a)}function _asyncToGenerator(e){return function(){var n=this,t=arguments;return new Promise(function(o,a){function r(e){asyncGeneratorStep(s,o,a,r,i,"next",e)}function i(e){asyncGeneratorStep(s,o,a,r,i,"throw",e)}var s=e.apply(n,t);r(void 0)})}}!function(){window.initializeVoxeet=function(){var e={isMuted:!1,isVideoEnabled:!0,isScreenShareEnabled:!1,isFullscreenEnabled:!1};VoxeetSDK.conference.on("streamAdded",function(e,n){if("ScreenShare"===n.type)return s(n);var t=$(e.info&&e.info.name==classroom.voxeet.userId?".local-stream > .stream-container":".remote-stream > .stream-container"),o=$("video-"+e.id);o.length||(o=document.createElement("video"),o.setAttribute("id","video-"+e.id),o.setAttribute("height",240),o.setAttribute("width",320),t.append(o),o.autoplay="autoplay",o.muted=!0),navigator.attachMediaStream(o,n),$("div.remote-stream").removeClass("hidden")}),VoxeetSDK.conference.on("streamRemoved",function(e,n){if("ScreenShare"===n.type)return c(e);$("div.remote-stream video").remove(),$("div.remote-stream").addClass("hidden")});var n=function(){var e=_asyncToGenerator(regeneratorRuntime.mark(function e(n){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("Voxeet initialization started"),null===navigator.webkitGetUserMedia?$("#video-span-no-camera").css("display","inline-block"):$("#video-span-no-camera").css("display","none"),e.prev=2,e.next=5,VoxeetSDK.initialize(classroom.voxeet.consumer_key,classroom.voxeet.consumer_secret_key);case 5:return e.next=7,VoxeetSDK.session.open({name:classroom.voxeet.userId});case 7:VoxeetSDK.conference.create({alias:"classroom_"+getParameterFromURL("roomid")}).then(function(e){VoxeetSDK.conference.join(e,{video:n,constraints:{audio:!0,video:n}}).then(function(e){$(".stream-controls.local").removeClass("hidden")}).catch(function(e){return console.error(e)})}).catch(function(e){return console.log("Something wrong happened : "+e)}),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(2),alert("Something went wrong : "+e.t0);case 13:case"end":return e.stop()}},e,null,[[2,10]])}));return function(n){return e.apply(this,arguments)}}();n(!0);var t=function(e){switch($(this).data("action")){case"audio-toggle":a.call(this);break;case"video-toggle":o.call(this);break;case"screenshare-toggle":r.call(this);break;case"fullscreen-toggle":i.call(this)}};$("#video-window-buttons").on("click","button",t);var o=function(){var t=$(this);$("#play-pause").toggleClass("active"),e.isVideoEnabled=!e.isVideoEnabled,e.isVideoEnabled?$("#video-span-no-camera").css("display","none"):$("#video-span-no-camera").css("display","inline-block");var o=t.find("i");e.isVideoEnabled?(VoxeetSDK.conference.startVideo(VoxeetSDK.session.participant,{}),$("div.local-stream").removeClass("hidden"),o.text("videocam")):(VoxeetSDK.conference.leave(),n(!1),$("div.local-stream").addClass("hidden"),o.text("videocam_off"))},a=function(){var n=$(this);$("#mute").toggleClass("active"),e.isMuted=!e.isMuted,VoxeetSDK.conference.mute(VoxeetSDK.session.participant,e.isMuted),n.find("i").text(e.isMuted?"mic_off":"mic")},r=function(){var n=$(this);e.isScreenShareEnabled?VoxeetSDK.conference.stopScreenShare():VoxeetSDK.conference.startScreenShare(),e.isScreenShareEnabled=!e.isScreenShareEnabled,n.find("i").text(e.isScreenShareEnabled?"stop_screen_sharem":"screen_share"),$('[data-action="fullscreen"]').removeClass("hidden"),$("video#screenshare").remove()},i=function(){if(e.isFullscreenEnabled=!e.isFullscreenEnabled,e.isFullscreenEnabled){var n=document.getElementById("video-window");requestFullScreen(n)}else screenfull.exit()},s=function(e){"screen_share"===$("button#screen-share > i").text()&&$("button#screen-share").prop("disabled",!0);var n=$("#video-window-shared"),t=document.getElementById("screenshare");if(!t){t=document.createElement("video"),t.setAttribute("id","screenshare"),t.autoplay="autoplay",t.controls=!0,navigator.attachMediaStream(t,e),n.append(t);var o=document.getElementById("video-window");$("#video-share-catch").css("left",o.style.left.slice(0,-2)+"px"),$("#video-share-catch").css("top",parseFloat(o.style.top.slice(0,-2))+400+"px"),$("#video-share-catch").removeClass("hidden"),$("#video-window-shared").removeClass("hidden")}},c=function(e){"screen_share"===$("button#screen-share > i").text()&&$("button#screen-share").prop("disabled",!1);var n=$("#video-window-shared > video");n.length&&n.remove(),$("#video-share-catch").addClass("hidden"),$("#video-window-shared").addClass("hidden")};window.onbeforeunload=function(){VoxeetSDK.conference.stopScreenShare(),VoxeetSDK.conference.leave()}}}();