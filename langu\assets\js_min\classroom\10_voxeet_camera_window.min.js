function requestFullScreen(e){var t=e.requestFullScreen||e.webkitRequestFullScreen||e.mozRequestFullScreen||e.msRequestFullScreen;if(t)t.call(e);else if(void 0!==window.ActiveXObject){var i=new ActiveXObject("WScript.Shell");null!==i&&i.SendKeys("{F11}")}}function getBase64(e){var t=new FileReader;t.readAsDataURL(e),t.onload=function(){console.log(t.result)},t.onerror=function(e){console.log("Error: ",e)}}function ImageItem(e,t,i,s){if(this.loaded=!1,this.uploaded=!1,this.sent=!1,this.server_file_name="",this.complete_upload=function(e){this.server_file_name=e,this.uploaded=!0,next_image.loaded&&(cursor.assets.push(next_image),next_image=null)},this.img=new Image,this.size=new Location(0,0),this.type="imageitem",this.moved=!1,this.id=uuidv4().replace(/-/g,""),this.scale_index=3,this.get_lowest_point=function(){return this.loc.y+this.size.y},FileReader&&e&&null!==e){var l=new FileReader;l.onload=function(){null!==next_image&&(next_image.img.src=l.result,next_image.img.onload=function(){null!==next_image.loaded&&(next_image.loaded=!0),next_image.size.x=next_image.img.naturalWidth,next_image.size.y=next_image.img.naturalHeight})},e.id="ssss";var h=getBase64(e);e.id=h,l.readAsDataURL(e)}else this.server_file_name=s,this.img.src=s,this.img.onload=function(){},this.loaded=!0;this.offset=0,this.loc=new Location(t,i),this.loc.x<0&&(this.loc.x=50),this.loc.y<0&&(this.loc.y=50),this.filename=e?e.name:s,this.update=ImageItemUpdate,this.draw=ImageItemDraw,this.clicked=imageitem_clicked,this.hittest=ImageItemHitTest,this.bodyhittest=ImageItemBodyHitTest,this.highlight_dragbar=!1,this.scaleImage=ImageItemScale,this.hittestresize=ImageItemHitTestResize,this.assets_deleted=!1,this.assets=[],this.assets_dirty=!1,this.add_asset=ImageItemAddAsset,this.deleted=!1,this.resize=ImageItemResize,this.start_resize=ImageItemStartResize}function ImageItemUpdate(){}function imageitem_clicked(e,t,i){var s=scale[this.scale_index],l=Math.round(s*this.size.x),h=(Math.round(s*this.size.y),document.createElement("canvas"));h.width=.8*this.size.x,h.height=.8*this.size.y,l=.5*h.width,h.height,e>this.loc.x+l-50&&e<this.loc.x+l&&t<this.loc.y-i&&t>this.loc.y-33-i&&(this.deleted=!0,this.loaded=!1,this.uploaded=!1,this.loc.x=-1e3,this.loc.y=-1e3)}function ImageItemDraw(e,t){var i=e.strokeStyle,s=e.fillStyle;if(this.loaded){var l=($(window).width(),$(window).height(),scale[this.scale_index]),h=Math.round(l*this.size.x),o=Math.round(l*this.size.y);!1===this.highlight_dragbar?e.fillStyle="#222222":e.fillStyle="#444444";var a=this.img,n=document.createElement("canvas");n.getContext("2d");n.width=.8*this.size.x,n.height=.8*this.size.y,e.mozImageSmoothingEnabled=!0,e.imageSmoothingQuality="high",e.webkitImageSmoothingEnabled=!0,e.msImageSmoothingEnabled=!0,e.imageSmoothingEnabled=!0,a.src=this.img.src,e.drawImage(a,this.loc.x,this.loc.y-t,.5*n.width,.5*n.height),e.beginPath();var c=.5*n.width;e.fillRect(this.loc.x,this.loc.y-20-t,c,20),e.stroke(),e.closePath(),e.font="14px verdana",e.fillStyle="#eeffee",e.fillText(this.filename,this.loc.x+5,this.loc.y-5-t,.4*this.size.x),e.linewidth=5,e.strokeStyle="#222222",h=.5*n.width,o=.5*n.height,e.beginPath(),e.moveTo(this.loc.x,this.loc.y-t),e.lineTo(this.loc.x+h,this.loc.y-t),e.lineTo(this.loc.x+h,this.loc.y+o-t),e.lineTo(this.loc.x,this.loc.y+o-t),e.lineTo(this.loc.x,this.loc.y-t),e.stroke(),e.closePath(),e.fillStyle="#222222",e.beginPath(),e.fillRect(this.loc.x+(h-15),this.loc.y-t-34,15,14),e.closePath(),e.fillStyle="red",e.fillText("X",this.loc.x+(h-13),this.loc.y-t-22,30,28),e.stroke();for(var d=0;!cursor.moving&&d<this.assets.length;d++){this.assets[d].draw(e,t,this)}e.strokeStyle="#222222",e.beginPath(),e.moveTo(this.loc.x+h-20,this.loc.y+o-t-2),e.lineTo(this.loc.x+h-2,this.loc.y+o-t-20),e.moveTo(this.loc.x+h-15,this.loc.y+o-t-2),e.lineTo(this.loc.x+h-2,this.loc.y+o-t-15),e.moveTo(this.loc.x+h-10,this.loc.y+o-t-2),e.lineTo(this.loc.x+h-2,this.loc.y+o-t-10),e.moveTo(this.loc.x+h-5,this.loc.y+o-t-2),e.lineTo(this.loc.x+h-2,this.loc.y+o-t-5),e.stroke(),e.closePath()}e.strokeStyle=i,e.fillStyle=s}function ImageItemHitTest(e,t){var i=scale[this.scale_index],s=Math.round(i*this.size.x);Math.round(i*this.size.y);return e>this.loc.x&&e<this.loc.x+s&&t>this.loc.y-20&&t<this.loc.y}function ImageItemScale(e){e?this.scale_index+=1:this.scale_index-=1,this.scale_index<0&&(this.scale_index=0),this.scale_index>scale.length-1&&(this.scale_index=scale.length-1)}function ImageItemHitTestResize(e,t,i){var s=Math.round(.4*this.size.x),l=Math.round(.4*this.size.y);return e>this.loc.x+s-20&&e<this.loc.x+s&&t>this.loc.y+l-i-20&&t<this.loc.y+l-i}function ImageItemResize(e,t){var i=this.size.x,s=this.size.y,l=this.size.x+=e,h=i/s,o=l/h;l=o*h,this.size.x=l,this.size.y=o}function ImageItemStartResize(){}function ImageItemBodyHitTest(e,t){return e>this.loc.x&&e<this.loc.x+this.size.x&&t>this.loc.y&&t<this.loc.y+this.size.y}function ImageItemAddAsset(e){if(console.log("ssss321312321321321312"),"line"!=e.type)return void console.log("ERROR: We only handle lines for textboxes");for(var t=0;t<e.points.length;t++)e.points[t].x-=this.loc.x,e.points[t].y-=this.loc.y;console.log(e.points),this.assets.push(e),this.assets_dirty=!0}!function(){$(function(){$("#catch-pdf").resizable({aspectRatio:!0,minHeight:100,minWidth:100})})}();var next_image=null,scale=[.1,.25,.5,1,1.5,2,4];