// ;
// (function() {
//     // Prepare the voxeet platform and place in placeholder
//     var voxeet = new VoxeetSdk();
//     var consumerKey = classroom_vox_consumerKey;
//     var consumerSecret = classroom_vox_consumerSecret;
//     var userId = classroom_vox_userId;
//     if (performance.navigation.type == 1) {
//         $("video").remove();
//     }
//     voxeet.on('screenShareStarted', (userId, stream) => {
//         var sharedVideo = document.createElement('video');
//         sharedVideo.srcObject = stream;
//         sharedVideo.autoplay = true;
//         sharedVideo.setAttribute("id", 'share-video');
//         sharedVideo.style.width = "100%";
//         sharedVideo.style.height = "100%";
//         sharedVideo.style.visibility = "visible";
//         $(sharedVideo).insertBefore($('#video-window-header-shared'));
//     });
//     voxeet.initialize(consumerKey, consumerSecret, { name: userId })
//         .then((userId) => {
//             var conferenceId = "classroom_" + getParameterFromURL("roomid");
//             var constraints = {
//                 audio: true,
//                 video: {
//                     width: screenfull,
//                     height: screenfull
//                 }
//             };
//             voxeet.joinConference(conferenceId, { video: true, constraints: constraints })
//                 .then(function(info) {
//                     console.log('JOINING THE CONFERENCE');
//                     console.log(info);
//                 }).catch(e => console.error(e));
//             voxeet.on('participantJoined', (participantId, stream) => {
//                 console.log('Participant joined - id ' + participantId);
//                 var participantVideo = document.createElement('video');
//                 participantVideo.srcObject = stream;
//                 participantVideo.muted = true;
//                 participantVideo.autoplay = true;
//                 participantVideo.setAttribute("id", participantId);
//                 $(participantVideo).insertBefore($('#video-window-buttons'));
//                 var element = document.getElementById("container");
//                 var numberOfVideo = element.getElementsByTagName('video').length;
//                 if (numberOfVideo > 1) {
//                     var active, prev, next;
//                     active = prev = next = element.querySelector('video');
//                     do prev = prev.previousSibling; while (prev && prev.nodeType !== 1);
//                     do next = next.nextSibling; while (next && next.nodeType !== 1);
//                     next.style.width = "100%";
//                 }
//                 else {
//                     participantVideo.style.width = "100%";
//                 }
//             });
//             voxeet.on('participantUpdated', (participantId, stream) => {
//                 var participantVideo = document.getElementById(userId);
//                 var element = document.getElementById("video-catch");
//                 var numberOfChildren = element.getElementsByTagName('video').length;
//                 if (numberOfChildren > 1) {
//                     participantVideo.style.width = "20%";
//                     participantVideo.style.height = "";
//                     participantVideo.style.position = "absolute";
//                 }
//             });
//             voxeet.on('participantLeft', (userId) => {
//                 let element = document.getElementById(userId);
//                 if (element != null) element.outerHTML = '';
//             });
//             const muteButton = document.getElementById('mute');
//             muteButton.onclick = function() {
//                 voxeet.toggleMute(userId);
//             };
//             const playPause = document.getElementById('play-pause');
//             playPause.onclick = function() {
//                 icon = $(this).find("i");
//                 if (icon.text() === 'videocam_off') {
//                     voxeet.stopVideoForUser(userId)
//                         .then(() => {
//                             document.getElementById(userId).style.opacity = "0";
//                             document.getElementById(userId).style.position = "relative";
//                         });
//                 }
//                 else {
//                     voxeet.startVideoForUser(userId);
//                     document.getElementById(userId).style.opacity = "1";
//                 }
//             };
//             const fullScreen = document.getElementById('full-screen');
//             fullScreen.onclick = function() {
//                 var container = document.getElementById('video-catch');
//                 var element = document.getElementById("container");
//                 var numberOfVideo = element.getElementsByTagName('video').length;
//                 if (numberOfVideo > 1) {
//                     var active, prev, next;
//                     active = prev = next = element.querySelector('video');
//                     do prev = prev.previousSibling; while (prev && prev.nodeType !== 1);
//                     do next = next.nextSibling; while (next && next.nodeType !== 1);
//                     $('#video-catch').hide();
//                     var width = $(next).width();
//                     $('#video-catch').show();
//                     if (width == "75") {
//                         next.style.width = "100%";
//                     }
//                     else {
//                         next.style.width = "75%";
//                     }
//                 }
//                 requestFullScreen(container);
//             };
//             const screenShare = document.getElementById('screen-share');
//             screenShare.onclick = function() {
//                 icon = $(this).find("i");
//                 if (icon.text() === 'stop_screen_sharem') {
//                     voxeet.startScreenShare()
//                         .then(() => {});
//                 }
//                 else {
//                     voxeet.stopScreenShare()
//                         .then(elementId => {});
//                 }
//             }
//             var switchVideoProviderButton = document.getElementById('switch-video-provider');
//             switchVideoProviderButton.onclick = () => {
//                 window.provider = {
//                     type: (window.provider.type == 'voxeet' ? 'tokbox' : 'voxeet'),
//                     changed: true,
//                 };
//                 console.log('Changing video provider to ' + window.provider.type);
//             };
//             voxeet.on('screenShareStopped', (userId, stream) => {
//                 let sharedVideo = document.getElementById('share-video');
//                 if (sharedVideo != null) sharedVideo.outerHTML = '';
//             });
//         })
//         .catch(e => console.error(e))
//         .catch(function(error) {
//             // An Error has occured, see Error Types
//             console.log(error);
//         });
// })();
