!function(e){e.fn.extend({getFocusPosition:function(){this[0].focus();for(var e=window.getSelection(),o=e.focusNode,t=e.focusOffset;void 0===o.offsetTop;)o=o.parentNode;return{offsetTop:o.offsetTop,offsetLeft:o.offsetLeft,focusOffset:t}},setFocusPosition:function(e){this[0].focus();var o=this[0],t=function(e,o){var t=document.createRange(),s=window.getSelection();t.setStart(e,o),t.collapse(!0),s.removeAllRanges(),s.addRange(t)};o.childNodes.forEach(function(o){if(o.offsetTop===e.offsetTop)if(o.offsetLeft!==e.offsetLeft);else try{t(e.focusOffset?o.firstChild:o,e.focusOffset)}catch(e){t(o,0)}})}});var o=e("#summernote"),t=classroom.lesson.id,s="student"===classroom.user.type?"#FF9C00":"#000000",a=classroom.lesson.saveUrl,n=classroom.lesson.uploadUrl,r=classroom.lesson.text,i=classroom.pusherKey,l="client-whiteboard-"+t+":updated",f=new Pusher(i,{cluster:"eu",encrypted:!0,authEndpoint:classroom.lesson.authUrl,authTransport:"ajax"}),c=f.subscribe("private-whiteboard-"+t);c.bind(l,function(t){var s=e(".note-editable");if(r!==t.text){r=t.text;var a=s.getFocusPosition();o.summernote("code",r),s.setFocusPosition(a)}}),o.summernote({focus:!0,airMode:!1,toolbar:[["style",["bold","underline"]],["font",["strikethrough"]],["color",["color"]],["insert",["link","video","hr","picture","table"]],["para",["ul","ol","paragraph"]]],popover:{air:[["style",["bold","underline"]],["font",["strikethrough"]],["color",["color"]],["insert",["link","video","hr","picture"]],["table",["table"]],["para",["ul","ol","paragraph"]]],image:[["float",["floatLeft","floatRight","floatNone"]],["remove",["removeMedia"]]],link:[["link",["linkDialogShow","unlink"]]]},dialogsFade:!0,dialogsInBody:!0,callbacks:{onInit:function(){e("#summernote-pb").remove(),o.summernote("code",r),o.summernote("foreColor",s)},onChange:function(o){o!==r&&(r=o,e.ajax({async:!0,cache:!1,data:{text:o},type:"POST",url:a,success:function(){c.trigger(l,{text:o})},dataType:"text"}))},onImageUpload:function(t){var s=new FormData;s.append("file",t[0]),e.ajax({data:s,type:"POST",url:n,cache:!1,contentType:!1,processData:!1,success:function(e){o.summernote("insertImage",e.payload.url)}})}}})}(jQuery);