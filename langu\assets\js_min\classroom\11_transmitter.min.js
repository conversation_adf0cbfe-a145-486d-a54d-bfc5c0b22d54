function asyncGeneratorStep(e,t,r,n,a,i,o){try{var u=e[i](o),s=u.value}catch(e){return void r(e)}u.done?t(s):Promise.resolve(s).then(n,a)}function _asyncToGenerator(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){function i(e){asyncGeneratorStep(u,n,a,i,o,"next",e)}function o(e){asyncGeneratorStep(u,n,a,i,o,"throw",e)}var u=e.apply(t,r);i(void 0)})}}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}function _defineProperty(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var UpdateBuilder=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"_type",void 0),_defineProperty(this,"_name",void 0),_defineProperty(this,"_id",void 0),_defineProperty(this,"_updateData",void 0)}return _createClass(e,[{key:"type",value:function(t){if(!t.toLowerCase()===e.TEMPORARY||!t.toLowerCase()===e.PERMANENT)throw new Error("Unacceptable update type given");return this._type=t,this}},{key:"name",value:function(e){return this._name=e,this}},{key:"componentId",value:function(e){return this._id=e,this}},{key:"updateData",value:function(e){return this._updateData=e,this}},{key:"validate",value:function(){if(!window.ws.socket)throw new Error("Socket instance is not initialized");if(!window.transmitter)throw new Error("Transmitter instance is not initialized");return this}},{key:"send",value:function(){return window.transmitter.dispatch(this.validate()).then(function(){}),this}},{key:"wrap",value:function(){return Object.assign({data:this._updateData},{id:this._id,classroom_id:getParameterFromURL("roomid"),name:this._name,role:window.langu_role,user_id:window.classroom.voxeet.userId})}}]),e}();_defineProperty(UpdateBuilder,"TEMPORARY","temporary"),_defineProperty(UpdateBuilder,"PERMANENT","permanent"),_defineProperty(UpdateBuilder,"TEXTBOX_MOUSEOVER","textbox_mouseover"),_defineProperty(UpdateBuilder,"TEXTBOX_MOUSELEAVE","textbox_mouseleave"),_defineProperty(UpdateBuilder,"TEXTBOX_CHANGED","textbox_changed"),_defineProperty(UpdateBuilder,"CURSOR_HIDE","cursor_hide"),_defineProperty(UpdateBuilder,"CURSOR_SHOW","cursor_show"),_defineProperty(UpdateBuilder,"RIPPLE","ripple"),_defineProperty(UpdateBuilder,"TOOLBAR_MOUSEOVER","toolbar_mouseover"),_defineProperty(UpdateBuilder,"TOOLBAR_MOUSELEAVE","toolbar_mouseleave"),_defineProperty(UpdateBuilder,"MOUSE_MOVE","mouse_move"),_defineProperty(UpdateBuilder,"VUE_COMPONENT_UPDATE","vue_component_update");var Transmitter=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"dispatch",value:function(){function e(e){return t.apply(this,arguments)}var t=_asyncToGenerator(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:window.ws.socket.emit("server_classroom_".concat(t._type,"_update"),JSON.stringify(t.wrap()));case 1:case"end":return e.stop()}},e)}));return e}()}]),e}();window.transmitter=new Transmitter,Object.freeze(window.transmitter);var Receiver=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"addUpdate",value:function(e){return this.dispatch(e)}},{key:"dispatch",value:function(e){function t(t){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){var e=_asyncToGenerator(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:e.t0=t.name,e.next=e.t0===UpdateBuilder.VUE_COMPONENT_UPDATE?3:e.t0===UpdateBuilder.TEXTBOX_MOUSEOVER?5:e.t0===UpdateBuilder.TEXTBOX_MOUSELEAVE?7:e.t0===UpdateBuilder.TOOLBAR_MOUSEOVER?9:e.t0===UpdateBuilder.TOOLBAR_MOUSELEAVE?11:e.t0===UpdateBuilder.CURSOR_HIDE?13:e.t0===UpdateBuilder.CURSOR_SHOW?15:e.t0===UpdateBuilder.RIPPLE?17:e.t0===UpdateBuilder.TEXTBOX_CHANGED?19:23;break;case 3:return dispatch("UPDATE_COMPONENT_DATA",null,t.id,t.data.path,t.data.target).then(function(){}),e.abrupt("break",24);case 5:return $(".note-editor").css({outline:"2px solid ".concat(getHoverColor(!0))}),e.abrupt("break",24);case 7:return $(".note-editor").css({outline:"none"}),e.abrupt("break",24);case 9:return $("ul.toolbar-buttons").css({outline:"2px solid ".concat(getHoverColor(!0))}),e.abrupt("break",24);case 11:return $("ul.toolbar-buttons").css({outline:"none"}),e.abrupt("break",24);case 13:return $("div#other_cursor").hide(),e.abrupt("break",24);case 15:return $("div#other_cursor").show(),e.abrupt("break",24);case 17:return window.othercursor.ripples.push(new Ripple(t.data.loc.x,t.data.loc.y,t.data.color)),e.abrupt("break",24);case 19:return e.next=21,findAssetByType(window.cursor.assets,"textbox");case 21:return e.sent.aggregateUpdate(t.data.text),e.abrupt("break",24);case 23:throw new Error("Unhandled event name received");case 24:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}())}]),e}();window.receiver=new Receiver,Object.freeze(window.receiver);