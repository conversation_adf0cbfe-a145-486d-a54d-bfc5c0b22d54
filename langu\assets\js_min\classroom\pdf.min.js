function _typeof3(e){"@babel/helpers - typeof";return(_typeof3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}/**
 * @licstart The following is the entire license notice for the
 * Javascript code in this page
 *
 * Copyright 2019 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * Javascript code in this page
 */
!function(e,t){"object"===("undefined"==typeof exports?"undefined":_typeof3(exports))&&"object"===("undefined"==typeof module?"undefined":_typeof3(module))?module.exports=t():"function"==typeof define&&define.amd?define("pdfjs-dist/build/pdf",[],t):"object"===("undefined"==typeof exports?"undefined":_typeof3(exports))?exports["pdfjs-dist/build/pdf"]=t():e["pdfjs-dist/build/pdf"]=e.pdfjsLib=t()}(this,function(){return function(e){function t(n){if(r[n])return r[n].exports;var i=r[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var r={};return t.m=e,t.c=r,t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{enumerable:!0,get:n})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,r){if(1&r&&(e=t(e)),8&r)return e;if(4&r&&"object"===_typeof3(e)&&e&&e.__esModule)return e;var n=Object.create(null);if(t.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&r&&"string"!=typeof e)for(var i in e)t.d(n,i,function(t){return e[t]}.bind(null,i));return n},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=0)}([function(e,t,r){"use strict";var n=r(1),i=r(147),a=r(162),o=r(163),s=r(151),u=r(164),l=r(156),c=r(153);if(r(4)()){var h=r(165).PDFNodeStream;i.setPDFNetworkStreamFactory(function(e){return new h(e)})}else{var d,f=r(168).PDFNetworkStream;s.isFetchSupported()&&(d=r(169).PDFFetchStream),i.setPDFNetworkStreamFactory(function(e){return d&&s.isValidFetchUrl(e.url)?new d(e):new f(e)})}t.build=i.build,t.version=i.version,t.getDocument=i.getDocument,t.LoopbackPort=i.LoopbackPort,t.PDFDataRangeTransport=i.PDFDataRangeTransport,t.PDFWorker=i.PDFWorker,t.renderTextLayer=a.renderTextLayer,t.AnnotationLayer=o.AnnotationLayer,t.createPromiseCapability=n.createPromiseCapability,t.PasswordResponses=n.PasswordResponses,t.InvalidPDFException=n.InvalidPDFException,t.MissingPDFException=n.MissingPDFException,t.SVGGraphics=u.SVGGraphics,t.NativeImageDecoding=n.NativeImageDecoding,t.CMapCompressionType=n.CMapCompressionType,t.PermissionFlag=n.PermissionFlag,t.UnexpectedResponseException=n.UnexpectedResponseException,t.OPS=n.OPS,t.VerbosityLevel=n.VerbosityLevel,t.UNSUPPORTED_FEATURES=n.UNSUPPORTED_FEATURES,t.createValidAbsoluteUrl=n.createValidAbsoluteUrl,t.createObjectURL=n.createObjectURL,t.removeNullCharacters=n.removeNullCharacters,t.shadow=n.shadow,t.Util=n.Util,t.ReadableStream=n.ReadableStream,t.URL=n.URL,t.RenderingCancelledException=s.RenderingCancelledException,t.getFilenameFromUrl=s.getFilenameFromUrl,t.LinkTarget=s.LinkTarget,t.addLinkAttributes=s.addLinkAttributes,t.loadScript=s.loadScript,t.PDFDateString=s.PDFDateString,t.GlobalWorkerOptions=l.GlobalWorkerOptions,t.apiCompatibilityParams=c.apiCompatibilityParams},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}function i(e){Number.isInteger(e)&&(re=e)}function a(){return re}function o(e){re>=J.INFOS&&console.log("Info: "+e)}function s(e){re>=J.WARNINGS&&console.log("Warning: "+e)}function u(e){throw new Error(e)}function l(e,t){e||u(t)}function c(e,t){try{var r=new N.URL(e);if(!r.origin||"null"===r.origin)return!1}catch(e){return!1}var n=new N.URL(t,r);return r.origin===n.origin}function h(e){if(!e)return!1;switch(e.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function d(e,t){if(!e)return null;try{var r=t?new N.URL(e,t):new N.URL(e);if(h(r))return r}catch(e){}return null}function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!1}),r}function p(e){return"string"!=typeof e?(s("The argument for removeNullCharacters must be a string."),e):e.replace(ce,"")}function v(e){l(null!==e&&"object"===n(e)&&void 0!==e.length,"Invalid argument for bytesToString");var t=e.length;if(t<8192)return String.fromCharCode.apply(null,e);for(var r=[],i=0;i<t;i+=8192){var a=Math.min(i+8192,t),o=e.subarray(i,a);r.push(String.fromCharCode.apply(null,o))}return r.join("")}function m(e){l("string"==typeof e,"Invalid argument for stringToBytes");for(var t=e.length,r=new Uint8Array(t),n=0;n<t;++n)r[n]=255&e.charCodeAt(n);return r}function g(e){return void 0!==e.length?e.length:(l(void 0!==e.byteLength),e.byteLength)}function y(e){if(1===e.length&&e[0]instanceof Uint8Array)return e[0];var t,r,n,i=0,a=e.length;for(t=0;t<a;t++)r=e[t],n=g(r),i+=n;var o=0,s=new Uint8Array(i);for(t=0;t<a;t++)r=e[t],r instanceof Uint8Array||(r="string"==typeof r?m(r):new Uint8Array(r)),n=r.byteLength,s.set(r,o),o+=n;return s}function b(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)}function _(e){return e<=0?0:Math.ceil(Math.log2(e))}function A(e,t){return e[t]<<24>>24}function S(e,t){return e[t]<<8|e[t+1]}function w(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}function k(){var e=new Uint8Array(4);return e[0]=1,1===new Uint32Array(e.buffer,0,1)[0]}function x(){try{return new Function(""),!0}catch(e){return!1}}function P(e){var t,r=e.length,n=[];if("þ"===e[0]&&"ÿ"===e[1])for(t=2;t<r;t+=2)n.push(String.fromCharCode(e.charCodeAt(t)<<8|e.charCodeAt(t+1)));else for(t=0;t<r;++t){var i=de[e.charCodeAt(t)];n.push(i?String.fromCharCode(i):e.charAt(t))}return n.join("")}function C(e){return decodeURIComponent(escape(e))}function R(e){return unescape(encodeURIComponent(e))}function T(e){for(var t in e)return!1;return!0}function E(e){return"boolean"==typeof e}function O(e){return"number"==typeof e}function F(e){return"string"==typeof e}function I(e){return"object"===n(e)&&null!==e&&void 0!==e.byteLength}function L(e,t){return e.length===t.length&&e.every(function(e,r){return e===t[r]})}function j(e){return 32===e||9===e||13===e||10===e}function M(){var e=Object.create(null),t=!1;return Object.defineProperty(e,"settled",{get:function(){return t}}),e.promise=new Promise(function(r,n){e.resolve=function(e){t=!0,r(e)},e.reject=function(e){t=!0,n(e)}}),e}Object.defineProperty(t,"__esModule",{value:!0}),t.arrayByteLength=g,t.arraysToBytes=y,t.assert=l,t.bytesToString=v,t.createPromiseCapability=M,t.getVerbosityLevel=a,t.info=o,t.isArrayBuffer=I,t.isArrayEqual=L,t.isBool=E,t.isEmptyObj=T,t.isNum=O,t.isString=F,t.isSpace=j,t.isSameOrigin=c,t.createValidAbsoluteUrl=d,t.isLittleEndian=k,t.isEvalSupported=x,t.log2=_,t.readInt8=A,t.readUint16=S,t.readUint32=w,t.removeNullCharacters=p,t.setVerbosityLevel=i,t.shadow=f,t.string32=b,t.stringToBytes=m,t.stringToPDFString=P,t.stringToUTF8String=C,t.utf8StringToString=R,t.warn=s,t.unreachable=u,Object.defineProperty(t,"ReadableStream",{enumerable:!0,get:function(){return D.ReadableStream}}),Object.defineProperty(t,"URL",{enumerable:!0,get:function(){return N.URL}}),t.createObjectURL=t.FormatError=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.StreamType=t.PermissionFlag=t.PasswordResponses=t.PasswordException=t.NativeImageDecoding=t.MissingPDFException=t.InvalidPDFException=t.AbortException=t.CMapCompressionType=t.ImageKind=t.FontType=t.AnnotationType=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationBorderStyleType=t.UNSUPPORTED_FEATURES=t.VerbosityLevel=t.OPS=t.IDENTITY_MATRIX=t.FONT_IDENTITY_MATRIX=void 0,r(2);var D=r(143),N=r(145),q=[1,0,0,1,0,0];t.IDENTITY_MATRIX=q;var W=[.001,0,0,.001,0,0];t.FONT_IDENTITY_MATRIX=W;var U={NONE:"none",DECODE:"decode",DISPLAY:"display"};t.NativeImageDecoding=U;var B={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};t.PermissionFlag=B;var z={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};t.TextRenderingMode=z;var G={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};t.ImageKind=G;var H={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};t.AnnotationType=H;var X={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};t.AnnotationFlag=X;var Y={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};t.AnnotationFieldFlag=Y;var V={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};t.AnnotationBorderStyleType=V;var Q={UNKNOWN:0,FLATE:1,LZW:2,DCT:3,JPX:4,JBIG:5,A85:6,AHX:7,CCF:8,RL:9};t.StreamType=Q;var K={UNKNOWN:0,TYPE1:1,TYPE1C:2,CIDFONTTYPE0:3,CIDFONTTYPE0C:4,TRUETYPE:5,CIDFONTTYPE2:6,TYPE3:7,OPENTYPE:8,TYPE0:9,MMTYPE1:10};t.FontType=K;var J={ERRORS:0,WARNINGS:1,INFOS:5};t.VerbosityLevel=J;var Z={NONE:0,BINARY:1,STREAM:2};t.CMapCompressionType=Z;var $={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};t.OPS=$;var ee={unknown:"unknown",forms:"forms",javaScript:"javaScript",smask:"smask",shadingPattern:"shadingPattern",font:"font"};t.UNSUPPORTED_FEATURES=ee;var te={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};t.PasswordResponses=te;var re=J.WARNINGS,ne=function(){function e(e,t){this.name="PasswordException",this.message=e,this.code=t}return e.prototype=new Error,e.constructor=e,e}();t.PasswordException=ne;var ie=function(){function e(e,t){this.name="UnknownErrorException",this.message=e,this.details=t}return e.prototype=new Error,e.constructor=e,e}();t.UnknownErrorException=ie;var ae=function(){function e(e){this.name="InvalidPDFException",this.message=e}return e.prototype=new Error,e.constructor=e,e}();t.InvalidPDFException=ae;var oe=function(){function e(e){this.name="MissingPDFException",this.message=e}return e.prototype=new Error,e.constructor=e,e}();t.MissingPDFException=oe;var se=function(){function e(e,t){this.name="UnexpectedResponseException",this.message=e,this.status=t}return e.prototype=new Error,e.constructor=e,e}();t.UnexpectedResponseException=se;var ue=function(){function e(e){this.message=e}return e.prototype=new Error,e.prototype.name="FormatError",e.constructor=e,e}();t.FormatError=ue;var le=function(){function e(e){this.name="AbortException",this.message=e}return e.prototype=new Error,e.constructor=e,e}();t.AbortException=le;var ce=/\x00/g,he=function(){function e(){}var t=["rgb(",0,",",0,",",0,")"];return e.makeCssRgb=function(e,r,n){return t[1]=e,t[3]=r,t[5]=n,t.join("")},e.transform=function(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]},e.applyTransform=function(e,t){return[e[0]*t[0]+e[1]*t[2]+t[4],e[0]*t[1]+e[1]*t[3]+t[5]]},e.applyInverseTransform=function(e,t){var r=t[0]*t[3]-t[1]*t[2];return[(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/r,(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/r]},e.getAxialAlignedBoundingBox=function(t,r){var n=e.applyTransform(t,r),i=e.applyTransform(t.slice(2,4),r),a=e.applyTransform([t[0],t[3]],r),o=e.applyTransform([t[2],t[1]],r);return[Math.min(n[0],i[0],a[0],o[0]),Math.min(n[1],i[1],a[1],o[1]),Math.max(n[0],i[0],a[0],o[0]),Math.max(n[1],i[1],a[1],o[1])]},e.inverseTransform=function(e){var t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]},e.apply3dTransform=function(e,t){return[e[0]*t[0]+e[1]*t[1]+e[2]*t[2],e[3]*t[0]+e[4]*t[1]+e[5]*t[2],e[6]*t[0]+e[7]*t[1]+e[8]*t[2]]},e.singularValueDecompose2dScale=function(e){var t=[e[0],e[2],e[1],e[3]],r=e[0]*t[0]+e[1]*t[2],n=e[0]*t[1]+e[1]*t[3],i=e[2]*t[0]+e[3]*t[2],a=e[2]*t[1]+e[3]*t[3],o=(r+a)/2,s=Math.sqrt((r+a)*(r+a)-4*(r*a-i*n))/2,u=o+s||1,l=o-s||1;return[Math.sqrt(u),Math.sqrt(l)]},e.normalizeRect=function(e){var t=e.slice(0);return e[0]>e[2]&&(t[0]=e[2],t[2]=e[0]),e[1]>e[3]&&(t[1]=e[3],t[3]=e[1]),t},e.intersect=function(t,r){function n(e,t){return e-t}var i=[t[0],t[2],r[0],r[2]].sort(n),a=[t[1],t[3],r[1],r[3]].sort(n),o=[];return t=e.normalizeRect(t),r=e.normalizeRect(r),(i[0]===t[0]&&i[1]===r[0]||i[0]===r[0]&&i[1]===t[0])&&(o[0]=i[1],o[2]=i[2],(a[0]===t[1]&&a[1]===r[1]||a[0]===r[1]&&a[1]===t[1])&&(o[1]=a[1],o[3]=a[2],o))},e}();t.Util=he;var de=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364],fe=function(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return function(t,r){if(!(arguments.length>2&&void 0!==arguments[2]&&arguments[2])&&N.URL.createObjectURL){var n=new Blob([t],{type:r});return N.URL.createObjectURL(n)}for(var i="data:"+r+";base64,",a=0,o=t.length;a<o;a+=3){var s=255&t[a],u=255&t[a+1],l=255&t[a+2];i+=e[s>>2]+e[(3&s)<<4|u>>4]+e[a+1<o?(15&u)<<2|l>>6:64]+e[a+2<o?63&l:64]}return i}}();t.createObjectURL=fe},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}var i=r(3);if(!i._pdfjsCompatibilityChecked){i._pdfjsCompatibilityChecked=!0;var a=r(4),o="object"===("undefined"==typeof window?"undefined":n(window))&&"object"===("undefined"==typeof document?"undefined":n(document));!function(){!i.btoa&&a()&&(i.btoa=function(e){return Buffer.from(e,"binary").toString("base64")})}(),function(){!i.atob&&a()&&(i.atob=function(e){return Buffer.from(e,"base64").toString("binary")})}(),function(){o&&void 0===Element.prototype.remove&&(Element.prototype.remove=function(){this.parentNode&&this.parentNode.removeChild(this)})}(),function(){if(o&&!a()){var e=document.createElement("div");if(e.classList.add("testOne","testTwo"),!0!==e.classList.contains("testOne")||!0!==e.classList.contains("testTwo")){var t=DOMTokenList.prototype.add,r=DOMTokenList.prototype.remove;DOMTokenList.prototype.add=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];for(var i=0,a=r;i<a.length;i++){var o=a[i];t.call(this,o)}},DOMTokenList.prototype.remove=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var i=0,a=t;i<a.length;i++){var o=a[i];r.call(this,o)}}}}}(),function(){if(o&&!a()){!1!==document.createElement("div").classList.toggle("test",0)&&(DOMTokenList.prototype.toggle=function(e){var t=arguments.length>1?!!arguments[1]:!this.contains(e);return this[t?"add":"remove"](e),t})}}(),function(){String.prototype.startsWith||r(5)}(),function(){String.prototype.endsWith||r(36)}(),function(){String.prototype.includes||r(38)}(),function(){Array.prototype.includes||r(40)}(),function(){Array.from||r(47)}(),function(){Object.assign||r(70)}(),function(){Math.log2||(Math.log2=r(75))}(),function(){Number.isNaN||(Number.isNaN=r(77))}(),function(){Number.isInteger||(Number.isInteger=r(79))}(),function(){i.Promise&&i.Promise.prototype&&i.Promise.prototype.finally||(i.Promise=r(82))}(),function(){i.WeakMap||(i.WeakMap=r(102))}(),function(){i.WeakSet||(i.WeakSet=r(119))}(),function(){String.codePointAt||(String.codePointAt=r(123))}(),function(){String.fromCodePoint||(String.fromCodePoint=r(125))}(),function(){i.Symbol||r(127)}(),function(){String.prototype.padStart||r(134)}(),function(){String.prototype.padEnd||r(138)}(),function(){Object.values||(Object.values=r(140))}()}},function(e,t,r){"use strict";e.exports="undefined"!=typeof window&&window.Math===Math?window:"undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:{}},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}e.exports=function(){return"object"===("undefined"==typeof process?"undefined":n(process))&&process+""=="[object process]"&&!process.versions.nw&&!process.versions.electron}},function(e,t,r){"use strict";r(6),e.exports=r(9).String.startsWith},function(e,t,r){"use strict";var n=r(7),i=r(28),a=r(30),o="".startsWith;n(n.P+n.F*r(35)("startsWith"),"String",{startsWith:function(e){var t=a(this,e,"startsWith"),r=i(Math.min(arguments.length>1?arguments[1]:void 0,t.length)),n=String(e);return o?o.call(t,n,r):t.slice(r,r+n.length)===n}})},function(e,t,r){"use strict";var n=r(8),i=r(9),a=r(10),o=r(20),s=r(26),u=function e(t,r,u){var l,c,h,d,f=t&e.F,p=t&e.G,v=t&e.S,m=t&e.P,g=t&e.B,y=p?n:v?n[r]||(n[r]={}):(n[r]||{}).prototype,b=p?i:i[r]||(i[r]={}),_=b.prototype||(b.prototype={});p&&(u=r);for(l in u)c=!f&&y&&void 0!==y[l],h=(c?y:u)[l],d=g&&c?s(h,n):m&&"function"==typeof h?s(Function.call,h):h,y&&o(y,l,h,t&e.U),b[l]!=h&&a(b,l,d),m&&_[l]!=h&&(_[l]=h)};n.core=i,u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},function(e,t,r){"use strict";var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t,r){"use strict";var n=e.exports={version:"2.6.9"};"number"==typeof __e&&(__e=n)},function(e,t,r){"use strict";var n=r(11),i=r(19);e.exports=r(15)?function(e,t,r){return n.f(e,t,i(1,r))}:function(e,t,r){return e[t]=r,e}},function(e,t,r){"use strict";var n=r(12),i=r(14),a=r(18),o=Object.defineProperty;t.f=r(15)?Object.defineProperty:function(e,t,r){if(n(e),t=a(t,!0),n(r),i)try{return o(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},function(e,t,r){"use strict";var n=r(13);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}e.exports=function(e){return"object"===n(e)?null!==e:"function"==typeof e}},function(e,t,r){"use strict";e.exports=!r(15)&&!r(16)(function(){return 7!=Object.defineProperty(r(17)("div"),"a",{get:function(){return 7}}).a})},function(e,t,r){"use strict";e.exports=!r(16)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t,r){"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t,r){"use strict";var n=r(13),i=r(8).document,a=n(i)&&n(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},function(e,t,r){"use strict";var n=r(13);e.exports=function(e,t){if(!n(e))return e;var r,i;if(t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;if("function"==typeof(r=e.valueOf)&&!n(i=r.call(e)))return i;if(!t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t,r){"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,r){"use strict";var n=r(8),i=r(10),a=r(21),o=r(22)("src"),s=r(23),u=(""+s).split("toString");r(9).inspectSource=function(e){return s.call(e)},(e.exports=function(e,t,r,s){var l="function"==typeof r;l&&(a(r,"name")||i(r,"name",t)),e[t]!==r&&(l&&(a(r,o)||i(r,o,e[t]?""+e[t]:u.join(String(t)))),e===n?e[t]=r:s?e[t]?e[t]=r:i(e,t,r):(delete e[t],i(e,t,r)))})(Function.prototype,"toString",function(){return"function"==typeof this&&this[o]||s.call(this)})},function(e,t,r){"use strict";var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,r){"use strict";var n=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+i).toString(36))}},function(e,t,r){"use strict";e.exports=r(24)("native-function-to-string",Function.toString)},function(e,t,r){"use strict";var n=r(9),i=r(8),a=i["__core-js_shared__"]||(i["__core-js_shared__"]={});(e.exports=function(e,t){return a[e]||(a[e]=void 0!==t?t:{})})("versions",[]).push({version:n.version,mode:r(25)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(e,t,r){"use strict";e.exports=!1},function(e,t,r){"use strict";var n=r(27);e.exports=function(e,t,r){if(n(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,i){return e.call(t,r,n,i)}}return function(){return e.apply(t,arguments)}}},function(e,t,r){"use strict";e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t,r){"use strict";var n=r(29),i=Math.min;e.exports=function(e){return e>0?i(n(e),9007199254740991):0}},function(e,t,r){"use strict";var n=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?i:n)(e)}},function(e,t,r){"use strict";var n=r(31),i=r(34);e.exports=function(e,t,r){if(n(t))throw TypeError("String#"+r+" doesn't accept regex!");return String(i(e))}},function(e,t,r){"use strict";var n=r(13),i=r(32),a=r(33)("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[a])?!!t:"RegExp"==i(e))}},function(e,t,r){"use strict";var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t,r){"use strict";var n=r(24)("wks"),i=r(22),a=r(8).Symbol,o="function"==typeof a;(e.exports=function(e){return n[e]||(n[e]=o&&a[e]||(o?a:i)("Symbol."+e))}).store=n},function(e,t,r){"use strict";e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,r){"use strict";var n=r(33)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[n]=!1,!"/./"[e](t)}catch(e){}}return!0}},function(e,t,r){"use strict";r(37),e.exports=r(9).String.endsWith},function(e,t,r){"use strict";var n=r(7),i=r(28),a=r(30),o="".endsWith;n(n.P+n.F*r(35)("endsWith"),"String",{endsWith:function(e){var t=a(this,e,"endsWith"),r=arguments.length>1?arguments[1]:void 0,n=i(t.length),s=void 0===r?n:Math.min(i(r),n),u=String(e);return o?o.call(t,u,s):t.slice(s-u.length,s)===u}})},function(e,t,r){"use strict";r(39),e.exports=r(9).String.includes},function(e,t,r){"use strict";var n=r(7),i=r(30);n(n.P+n.F*r(35)("includes"),"String",{includes:function(e){return!!~i(this,e,"includes").indexOf(e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,r){"use strict";r(41),e.exports=r(9).Array.includes},function(e,t,r){"use strict";var n=r(7),i=r(42)(!0);n(n.P,"Array",{includes:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),r(46)("includes")},function(e,t,r){"use strict";var n=r(43),i=r(28),a=r(45);e.exports=function(e){return function(t,r,o){var s,u=n(t),l=i(u.length),c=a(o,l);if(e&&r!=r){for(;l>c;)if((s=u[c++])!=s)return!0}else for(;l>c;c++)if((e||c in u)&&u[c]===r)return e||c||0;return!e&&-1}}},function(e,t,r){"use strict";var n=r(44),i=r(34);e.exports=function(e){return n(i(e))}},function(e,t,r){"use strict";var n=r(32);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==n(e)?e.split(""):Object(e)}},function(e,t,r){"use strict";var n=r(29),i=Math.max,a=Math.min;e.exports=function(e,t){return e=n(e),e<0?i(e+t,0):a(e,t)}},function(e,t,r){"use strict";var n=r(33)("unscopables"),i=Array.prototype;void 0==i[n]&&r(10)(i,n,{}),e.exports=function(e){i[n][e]=!0}},function(e,t,r){"use strict";r(48),r(63),e.exports=r(9).Array.from},function(e,t,r){"use strict";var n=r(49)(!0);r(50)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,r=this._i;return r>=t.length?{value:void 0,done:!0}:(e=n(t,r),this._i+=e.length,{value:e,done:!1})})},function(e,t,r){"use strict";var n=r(29),i=r(34);e.exports=function(e){return function(t,r){var a,o,s=String(i(t)),u=n(r),l=s.length;return u<0||u>=l?e?"":void 0:(a=s.charCodeAt(u),a<55296||a>56319||u+1===l||(o=s.charCodeAt(u+1))<56320||o>57343?e?s.charAt(u):a:e?s.slice(u,u+2):o-56320+(a-55296<<10)+65536)}}},function(e,t,r){"use strict";var n=r(25),i=r(7),a=r(20),o=r(10),s=r(51),u=r(52),l=r(60),c=r(61),h=r(33)("iterator"),d=!([].keys&&"next"in[].keys()),f=function(){return this};e.exports=function(e,t,r,p,v,m,g){u(r,t,p);var y,b,_,A=function(e){if(!d&&e in x)return x[e];switch(e){case"keys":case"values":return function(){return new r(this,e)}}return function(){return new r(this,e)}},S=t+" Iterator",w="values"==v,k=!1,x=e.prototype,P=x[h]||x["@@iterator"]||v&&x[v],C=P||A(v),R=v?w?A("entries"):C:void 0,T="Array"==t?x.entries||P:P;if(T&&(_=c(T.call(new e)))!==Object.prototype&&_.next&&(l(_,S,!0),n||"function"==typeof _[h]||o(_,h,f)),w&&P&&"values"!==P.name&&(k=!0,C=function(){return P.call(this)}),n&&!g||!d&&!k&&x[h]||o(x,h,C),s[t]=C,s[S]=f,v)if(y={values:w?C:A("values"),keys:m?C:A("keys"),entries:R},g)for(b in y)b in x||a(x,b,y[b]);else i(i.P+i.F*(d||k),t,y);return y}},function(e,t,r){"use strict";e.exports={}},function(e,t,r){"use strict";var n=r(53),i=r(19),a=r(60),o={};r(10)(o,r(33)("iterator"),function(){return this}),e.exports=function(e,t,r){e.prototype=n(o,{next:i(1,r)}),a(e,t+" Iterator")}},function(e,t,r){"use strict";var n=r(12),i=r(54),a=r(58),o=r(57)("IE_PROTO"),s=function(){},u=function(){var e,t=r(17)("iframe"),n=a.length;for(t.style.display="none",r(59).appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;n--;)delete u.prototype[a[n]];return u()};e.exports=Object.create||function(e,t){var r;return null!==e?(s.prototype=n(e),r=new s,s.prototype=null,r[o]=e):r=u(),void 0===t?r:i(r,t)}},function(e,t,r){"use strict";var n=r(11),i=r(12),a=r(55);e.exports=r(15)?Object.defineProperties:function(e,t){i(e);for(var r,o=a(t),s=o.length,u=0;s>u;)n.f(e,r=o[u++],t[r]);return e}},function(e,t,r){"use strict";var n=r(56),i=r(58);e.exports=Object.keys||function(e){return n(e,i)}},function(e,t,r){"use strict";var n=r(21),i=r(43),a=r(42)(!1),o=r(57)("IE_PROTO");e.exports=function(e,t){var r,s=i(e),u=0,l=[];for(r in s)r!=o&&n(s,r)&&l.push(r);for(;t.length>u;)n(s,r=t[u++])&&(~a(l,r)||l.push(r));return l}},function(e,t,r){"use strict";var n=r(24)("keys"),i=r(22);e.exports=function(e){return n[e]||(n[e]=i(e))}},function(e,t,r){"use strict";e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,r){"use strict";var n=r(8).document;e.exports=n&&n.documentElement},function(e,t,r){"use strict";var n=r(11).f,i=r(21),a=r(33)("toStringTag");e.exports=function(e,t,r){e&&!i(e=r?e:e.prototype,a)&&n(e,a,{configurable:!0,value:t})}},function(e,t,r){"use strict";var n=r(21),i=r(62),a=r(57)("IE_PROTO"),o=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=i(e),n(e,a)?e[a]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?o:null}},function(e,t,r){"use strict";var n=r(34);e.exports=function(e){return Object(n(e))}},function(e,t,r){"use strict";var n=r(26),i=r(7),a=r(62),o=r(64),s=r(65),u=r(28),l=r(66),c=r(67);i(i.S+i.F*!r(69)(function(e){Array.from(e)}),"Array",{from:function(e){var t,r,i,h,d=a(e),f="function"==typeof this?this:Array,p=arguments.length,v=p>1?arguments[1]:void 0,m=void 0!==v,g=0,y=c(d);if(m&&(v=n(v,p>2?arguments[2]:void 0,2)),void 0==y||f==Array&&s(y))for(t=u(d.length),r=new f(t);t>g;g++)l(r,g,m?v(d[g],g):d[g]);else for(h=y.call(d),r=new f;!(i=h.next()).done;g++)l(r,g,m?o(h,v,[i.value,g],!0):i.value);return r.length=g,r}})},function(e,t,r){"use strict";var n=r(12);e.exports=function(e,t,r,i){try{return i?t(n(r)[0],r[1]):t(r)}catch(t){var a=e.return;throw void 0!==a&&n(a.call(e)),t}}},function(e,t,r){"use strict";var n=r(51),i=r(33)("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(n.Array===e||a[i]===e)}},function(e,t,r){"use strict";var n=r(11),i=r(19);e.exports=function(e,t,r){t in e?n.f(e,t,i(0,r)):e[t]=r}},function(e,t,r){"use strict";var n=r(68),i=r(33)("iterator"),a=r(51);e.exports=r(9).getIteratorMethod=function(e){if(void 0!=e)return e[i]||e["@@iterator"]||a[n(e)]}},function(e,t,r){"use strict";var n=r(32),i=r(33)("toStringTag"),a="Arguments"==n(function(){return arguments}()),o=function(e,t){try{return e[t]}catch(e){}};e.exports=function(e){var t,r,s;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=o(t=Object(e),i))?r:a?n(t):"Object"==(s=n(t))&&"function"==typeof t.callee?"Arguments":s}},function(e,t,r){"use strict";var n=r(33)("iterator"),i=!1;try{var a=[7][n]();a.return=function(){i=!0},Array.from(a,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!i)return!1;var r=!1;try{var a=[7],o=a[n]();o.next=function(){return{done:r=!0}},a[n]=function(){return o},e(a)}catch(e){}return r}},function(e,t,r){"use strict";r(71),e.exports=r(9).Object.assign},function(e,t,r){"use strict";var n=r(7);n(n.S+n.F,"Object",{assign:r(72)})},function(e,t,r){"use strict";var n=r(15),i=r(55),a=r(73),o=r(74),s=r(62),u=r(44),l=Object.assign;e.exports=!l||r(16)(function(){var e={},t={},r=Symbol(),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach(function(e){t[e]=e}),7!=l({},e)[r]||Object.keys(l({},t)).join("")!=n})?function(e,t){for(var r=s(e),l=arguments.length,c=1,h=a.f,d=o.f;l>c;)for(var f,p=u(arguments[c++]),v=h?i(p).concat(h(p)):i(p),m=v.length,g=0;m>g;)f=v[g++],n&&!d.call(p,f)||(r[f]=p[f]);return r}:l},function(e,t,r){"use strict";t.f=Object.getOwnPropertySymbols},function(e,t,r){"use strict";t.f={}.propertyIsEnumerable},function(e,t,r){"use strict";r(76),e.exports=r(9).Math.log2},function(e,t,r){"use strict";var n=r(7);n(n.S,"Math",{log2:function(e){return Math.log(e)/Math.LN2}})},function(e,t,r){"use strict";r(78),e.exports=r(9).Number.isNaN},function(e,t,r){"use strict";var n=r(7);n(n.S,"Number",{isNaN:function(e){return e!=e}})},function(e,t,r){"use strict";r(80),
e.exports=r(9).Number.isInteger},function(e,t,r){"use strict";var n=r(7);n(n.S,"Number",{isInteger:r(81)})},function(e,t,r){"use strict";var n=r(13),i=Math.floor;e.exports=function(e){return!n(e)&&isFinite(e)&&i(e)===e}},function(e,t,r){"use strict";r(83),r(48),r(84),r(87),r(100),r(101),e.exports=r(9).Promise},function(e,t,r){"use strict";var n=r(68),i={};i[r(33)("toStringTag")]="z",i+""!="[object z]"&&r(20)(Object.prototype,"toString",function(){return"[object "+n(this)+"]"},!0)},function(e,t,r){"use strict";for(var n=r(85),i=r(55),a=r(20),o=r(8),s=r(10),u=r(51),l=r(33),c=l("iterator"),h=l("toStringTag"),d=u.Array,f={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=i(f),v=0;v<p.length;v++){var m,g=p[v],y=f[g],b=o[g],_=b&&b.prototype;if(_&&(_[c]||s(_,c,d),_[h]||s(_,h,g),u[g]=d,y))for(m in n)_[m]||a(_,m,n[m],!0)}},function(e,t,r){"use strict";var n=r(46),i=r(86),a=r(51),o=r(43);e.exports=r(50)(Array,"Array",function(e,t){this._t=o(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,r=this._i++;return!e||r>=e.length?(this._t=void 0,i(1)):"keys"==t?i(0,r):"values"==t?i(0,e[r]):i(0,[r,e[r]])},"values"),a.Arguments=a.Array,n("keys"),n("values"),n("entries")},function(e,t,r){"use strict";e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,r){"use strict";var n,i,a,o,s=r(25),u=r(8),l=r(26),c=r(68),h=r(7),d=r(13),f=r(27),p=r(88),v=r(89),m=r(90),g=r(91).set,y=r(93)(),b=r(94),_=r(95),A=r(96),S=r(97),w=u.TypeError,k=u.process,x=k&&k.versions,P=x&&x.v8||"",C=u.Promise,R="process"==c(k),T=function(){},E=i=b.f,O=!!function(){try{var e=C.resolve(1),t=(e.constructor={})[r(33)("species")]=function(e){e(T,T)};return(R||"function"==typeof PromiseRejectionEvent)&&e.then(T)instanceof t&&0!==P.indexOf("6.6")&&-1===A.indexOf("Chrome/66")}catch(e){}}(),F=function(e){var t;return!(!d(e)||"function"!=typeof(t=e.then))&&t},I=function(e,t){if(!e._n){e._n=!0;var r=e._c;y(function(){for(var n=e._v,i=1==e._s,a=0;r.length>a;)!function(t){var r,a,o,s=i?t.ok:t.fail,u=t.resolve,l=t.reject,c=t.domain;try{s?(i||(2==e._h&&M(e),e._h=1),!0===s?r=n:(c&&c.enter(),r=s(n),c&&(c.exit(),o=!0)),r===t.promise?l(w("Promise-chain cycle")):(a=F(r))?a.call(r,u,l):u(r)):l(n)}catch(e){c&&!o&&c.exit(),l(e)}}(r[a++]);e._c=[],e._n=!1,t&&!e._h&&L(e)})}},L=function(e){g.call(u,function(){var t,r,n,i=e._v,a=j(e);if(a&&(t=_(function(){R?k.emit("unhandledRejection",i,e):(r=u.onunhandledrejection)?r({promise:e,reason:i}):(n=u.console)&&n.error&&n.error("Unhandled promise rejection",i)}),e._h=R||j(e)?2:1),e._a=void 0,a&&t.e)throw t.v})},j=function(e){return 1!==e._h&&0===(e._a||e._c).length},M=function(e){g.call(u,function(){var t;R?k.emit("rejectionHandled",e):(t=u.onrejectionhandled)&&t({promise:e,reason:e._v})})},D=function(e){var t=this;t._d||(t._d=!0,t=t._w||t,t._v=e,t._s=2,t._a||(t._a=t._c.slice()),I(t,!0))},N=function e(t){var r,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===t)throw w("Promise can't be resolved itself");(r=F(t))?y(function(){var i={_w:n,_d:!1};try{r.call(t,l(e,i,1),l(D,i,1))}catch(e){D.call(i,e)}}):(n._v=t,n._s=1,I(n,!1))}catch(e){D.call({_w:n,_d:!1},e)}}};O||(C=function(e){p(this,C,"Promise","_h"),f(e),n.call(this);try{e(l(N,this,1),l(D,this,1))}catch(e){D.call(this,e)}},n=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1},n.prototype=r(98)(C.prototype,{then:function(e,t){var r=E(m(this,C));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=R?k.domain:void 0,this._c.push(r),this._a&&this._a.push(r),this._s&&I(this,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),a=function(){var e=new n;this.promise=e,this.resolve=l(N,e,1),this.reject=l(D,e,1)},b.f=E=function(e){return e===C||e===o?new a(e):i(e)}),h(h.G+h.W+h.F*!O,{Promise:C}),r(60)(C,"Promise"),r(99)("Promise"),o=r(9).Promise,h(h.S+h.F*!O,"Promise",{reject:function(e){var t=E(this);return(0,t.reject)(e),t.promise}}),h(h.S+h.F*(s||!O),"Promise",{resolve:function(e){return S(s&&this===o?C:this,e)}}),h(h.S+h.F*!(O&&r(69)(function(e){C.all(e).catch(T)})),"Promise",{all:function(e){var t=this,r=E(t),n=r.resolve,i=r.reject,a=_(function(){var r=[],a=0,o=1;v(e,!1,function(e){var s=a++,u=!1;r.push(void 0),o++,t.resolve(e).then(function(e){u||(u=!0,r[s]=e,--o||n(r))},i)}),--o||n(r)});return a.e&&i(a.v),r.promise},race:function(e){var t=this,r=E(t),n=r.reject,i=_(function(){v(e,!1,function(e){t.resolve(e).then(r.resolve,n)})});return i.e&&n(i.v),r.promise}})},function(e,t,r){"use strict";e.exports=function(e,t,r,n){if(!(e instanceof t)||void 0!==n&&n in e)throw TypeError(r+": incorrect invocation!");return e}},function(e,t,r){"use strict";var n=r(26),i=r(64),a=r(65),o=r(12),s=r(28),u=r(67),l={},c={},h=e.exports=function(e,t,r,h,d){var f,p,v,m,g=d?function(){return e}:u(e),y=n(r,h,t?2:1),b=0;if("function"!=typeof g)throw TypeError(e+" is not iterable!");if(a(g)){for(f=s(e.length);f>b;b++)if((m=t?y(o(p=e[b])[0],p[1]):y(e[b]))===l||m===c)return m}else for(v=g.call(e);!(p=v.next()).done;)if((m=i(v,y,p.value,t))===l||m===c)return m};h.BREAK=l,h.RETURN=c},function(e,t,r){"use strict";var n=r(12),i=r(27),a=r(33)("species");e.exports=function(e,t){var r,o=n(e).constructor;return void 0===o||void 0==(r=n(o)[a])?t:i(r)}},function(e,t,r){"use strict";var n,i,a,o=r(26),s=r(92),u=r(59),l=r(17),c=r(8),h=c.process,d=c.setImmediate,f=c.clearImmediate,p=c.MessageChannel,v=c.Dispatch,m=0,g={},y=function(){var e=+this;if(g.hasOwnProperty(e)){var t=g[e];delete g[e],t()}},b=function(e){y.call(e.data)};d&&f||(d=function(e){for(var t=[],r=1;arguments.length>r;)t.push(arguments[r++]);return g[++m]=function(){s("function"==typeof e?e:Function(e),t)},n(m),m},f=function(e){delete g[e]},"process"==r(32)(h)?n=function(e){h.nextTick(o(y,e,1))}:v&&v.now?n=function(e){v.now(o(y,e,1))}:p?(i=new p,a=i.port2,i.port1.onmessage=b,n=o(a.postMessage,a,1)):c.addEventListener&&"function"==typeof postMessage&&!c.importScripts?(n=function(e){c.postMessage(e+"","*")},c.addEventListener("message",b,!1)):n="onreadystatechange"in l("script")?function(e){u.appendChild(l("script")).onreadystatechange=function(){u.removeChild(this),y.call(e)}}:function(e){setTimeout(o(y,e,1),0)}),e.exports={set:d,clear:f}},function(e,t,r){"use strict";e.exports=function(e,t,r){var n=void 0===r;switch(t.length){case 0:return n?e():e.call(r);case 1:return n?e(t[0]):e.call(r,t[0]);case 2:return n?e(t[0],t[1]):e.call(r,t[0],t[1]);case 3:return n?e(t[0],t[1],t[2]):e.call(r,t[0],t[1],t[2]);case 4:return n?e(t[0],t[1],t[2],t[3]):e.call(r,t[0],t[1],t[2],t[3])}return e.apply(r,t)}},function(e,t,r){"use strict";var n=r(8),i=r(91).set,a=n.MutationObserver||n.WebKitMutationObserver,o=n.process,s=n.Promise,u="process"==r(32)(o);e.exports=function(){var e,t,r,l=function(){var n,i;for(u&&(n=o.domain)&&n.exit();e;){i=e.fn,e=e.next;try{i()}catch(n){throw e?r():t=void 0,n}}t=void 0,n&&n.enter()};if(u)r=function(){o.nextTick(l)};else if(!a||n.navigator&&n.navigator.standalone)if(s&&s.resolve){var c=s.resolve(void 0);r=function(){c.then(l)}}else r=function(){i.call(n,l)};else{var h=!0,d=document.createTextNode("");new a(l).observe(d,{characterData:!0}),r=function(){d.data=h=!h}}return function(n){var i={fn:n,next:void 0};t&&(t.next=i),e||(e=i,r()),t=i}}},function(e,t,r){"use strict";function n(e){var t,r;this.promise=new e(function(e,n){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=n}),this.resolve=i(t),this.reject=i(r)}var i=r(27);e.exports.f=function(e){return new n(e)}},function(e,t,r){"use strict";e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},function(e,t,r){"use strict";var n=r(8),i=n.navigator;e.exports=i&&i.userAgent||""},function(e,t,r){"use strict";var n=r(12),i=r(13),a=r(94);e.exports=function(e,t){if(n(e),i(t)&&t.constructor===e)return t;var r=a.f(e);return(0,r.resolve)(t),r.promise}},function(e,t,r){"use strict";var n=r(20);e.exports=function(e,t,r){for(var i in t)n(e,i,t[i],r);return e}},function(e,t,r){"use strict";var n=r(8),i=r(11),a=r(15),o=r(33)("species");e.exports=function(e){var t=n[e];a&&t&&!t[o]&&i.f(t,o,{configurable:!0,get:function(){return this}})}},function(e,t,r){"use strict";var n=r(7),i=r(9),a=r(8),o=r(90),s=r(97);n(n.P+n.R,"Promise",{finally:function(e){var t=o(this,i.Promise||a.Promise),r="function"==typeof e;return this.then(r?function(r){return s(t,e()).then(function(){return r})}:e,r?function(r){return s(t,e()).then(function(){throw r})}:e)}})},function(e,t,r){"use strict";var n=r(7),i=r(94),a=r(95);n(n.S,"Promise",{try:function(e){var t=i.f(this),r=a(e);return(r.e?t.reject:t.resolve)(r.v),t.promise}})},function(e,t,r){"use strict";r(83),r(84),r(103),r(115),r(117),e.exports=r(9).WeakMap},function(e,t,r){"use strict";var n,i=r(8),a=r(104)(0),o=r(20),s=r(108),u=r(72),l=r(109),c=r(13),h=r(110),d=r(110),f=!i.ActiveXObject&&"ActiveXObject"in i,p=s.getWeak,v=Object.isExtensible,m=l.ufstore,g=function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},y={get:function(e){if(c(e)){var t=p(e);return!0===t?m(h(this,"WeakMap")).get(e):t?t[this._i]:void 0}},set:function(e,t){return l.def(h(this,"WeakMap"),e,t)}},b=e.exports=r(111)("WeakMap",g,y,l,!0,!0);d&&f&&(n=l.getConstructor(g,"WeakMap"),u(n.prototype,y),s.NEED=!0,a(["delete","has","get","set"],function(e){var t=b.prototype,r=t[e];o(t,e,function(t,i){if(c(t)&&!v(t)){this._f||(this._f=new n);var a=this._f[e](t,i);return"set"==e?this:a}return r.call(this,t,i)})}))},function(e,t,r){"use strict";var n=r(26),i=r(44),a=r(62),o=r(28),s=r(105);e.exports=function(e,t){var r=1==e,u=2==e,l=3==e,c=4==e,h=6==e,d=5==e||h,f=t||s;return function(t,s,p){for(var v,m,g=a(t),y=i(g),b=n(s,p,3),_=o(y.length),A=0,S=r?f(t,_):u?f(t,0):void 0;_>A;A++)if((d||A in y)&&(v=y[A],m=b(v,A,g),e))if(r)S[A]=m;else if(m)switch(e){case 3:return!0;case 5:return v;case 6:return A;case 2:S.push(v)}else if(c)return!1;return h?-1:l||c?c:S}}},function(e,t,r){"use strict";var n=r(106);e.exports=function(e,t){return new(n(e))(t)}},function(e,t,r){"use strict";var n=r(13),i=r(107),a=r(33)("species");e.exports=function(e){var t;return i(e)&&(t=e.constructor,"function"!=typeof t||t!==Array&&!i(t.prototype)||(t=void 0),n(t)&&null===(t=t[a])&&(t=void 0)),void 0===t?Array:t}},function(e,t,r){"use strict";var n=r(32);e.exports=Array.isArray||function(e){return"Array"==n(e)}},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}var i=r(22)("meta"),a=r(13),o=r(21),s=r(11).f,u=0,l=Object.isExtensible||function(){return!0},c=!r(16)(function(){return l(Object.preventExtensions({}))}),h=function(e){s(e,i,{value:{i:"O"+ ++u,w:{}}})},d=function(e,t){if(!a(e))return"symbol"==n(e)?e:("string"==typeof e?"S":"P")+e;if(!o(e,i)){if(!l(e))return"F";if(!t)return"E";h(e)}return e[i].i},f=function(e,t){if(!o(e,i)){if(!l(e))return!0;if(!t)return!1;h(e)}return e[i].w},p=function(e){return c&&v.NEED&&l(e)&&!o(e,i)&&h(e),e},v=e.exports={KEY:i,NEED:!1,fastKey:d,getWeak:f,onFreeze:p}},function(e,t,r){"use strict";var n=r(98),i=r(108).getWeak,a=r(12),o=r(13),s=r(88),u=r(89),l=r(104),c=r(21),h=r(110),d=l(5),f=l(6),p=0,v=function(e){return e._l||(e._l=new m)},m=function(){this.a=[]},g=function(e,t){return d(e.a,function(e){return e[0]===t})};m.prototype={get:function(e){var t=g(this,e);if(t)return t[1]},has:function(e){return!!g(this,e)},set:function(e,t){var r=g(this,e);r?r[1]=t:this.a.push([e,t])},delete:function(e){var t=f(this.a,function(t){return t[0]===e});return~t&&this.a.splice(t,1),!!~t}},e.exports={getConstructor:function(e,t,r,a){var l=e(function(e,n){s(e,l,t,"_i"),e._t=t,e._i=p++,e._l=void 0,void 0!=n&&u(n,r,e[a],e)});return n(l.prototype,{delete:function(e){if(!o(e))return!1;var r=i(e);return!0===r?v(h(this,t)).delete(e):r&&c(r,this._i)&&delete r[this._i]},has:function(e){if(!o(e))return!1;var r=i(e);return!0===r?v(h(this,t)).has(e):r&&c(r,this._i)}}),l},def:function(e,t,r){var n=i(a(t),!0);return!0===n?v(e).set(t,r):n[e._i]=r,e},ufstore:v}},function(e,t,r){"use strict";var n=r(13);e.exports=function(e,t){if(!n(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},function(e,t,r){"use strict";var n=r(8),i=r(7),a=r(20),o=r(98),s=r(108),u=r(89),l=r(88),c=r(13),h=r(16),d=r(69),f=r(60),p=r(112);e.exports=function(e,t,r,v,m,g){var y=n[e],b=y,_=m?"set":"add",A=b&&b.prototype,S={},w=function(e){var t=A[e];a(A,e,"delete"==e?function(e){return!(g&&!c(e))&&t.call(this,0===e?0:e)}:"has"==e?function(e){return!(g&&!c(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return g&&!c(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,r){return t.call(this,0===e?0:e,r),this})};if("function"==typeof b&&(g||A.forEach&&!h(function(){(new b).entries().next()}))){var k=new b,x=k[_](g?{}:-0,1)!=k,P=h(function(){k.has(1)}),C=d(function(e){new b(e)}),R=!g&&h(function(){for(var e=new b,t=5;t--;)e[_](t,t);return!e.has(-0)});C||(b=t(function(t,r){l(t,b,e);var n=p(new y,t,b);return void 0!=r&&u(r,m,n[_],n),n}),b.prototype=A,A.constructor=b),(P||R)&&(w("delete"),w("has"),m&&w("get")),(R||x)&&w(_),g&&A.clear&&delete A.clear}else b=v.getConstructor(t,e,m,_),o(b.prototype,r),s.NEED=!0;return f(b,e),S[e]=b,i(i.G+i.W+i.F*(b!=y),S),g||v.setStrong(b,e,m),b}},function(e,t,r){"use strict";var n=r(13),i=r(113).set;e.exports=function(e,t,r){var a,o=t.constructor;return o!==r&&"function"==typeof o&&(a=o.prototype)!==r.prototype&&n(a)&&i&&i(e,a),e}},function(e,t,r){"use strict";var n=r(13),i=r(12),a=function(e,t){if(i(e),!n(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{n=r(26)(Function.call,r(114).f(Object.prototype,"__proto__").set,2),n(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,r){return a(e,r),t?e.__proto__=r:n(e,r),e}}({},!1):void 0),check:a}},function(e,t,r){"use strict";var n=r(74),i=r(19),a=r(43),o=r(18),s=r(21),u=r(14),l=Object.getOwnPropertyDescriptor;t.f=r(15)?l:function(e,t){if(e=a(e),t=o(t,!0),u)try{return l(e,t)}catch(e){}if(s(e,t))return i(!n.f.call(e,t),e[t])}},function(e,t,r){"use strict";r(116)("WeakMap")},function(e,t,r){"use strict";var n=r(7);e.exports=function(e){n(n.S,e,{of:function(){for(var e=arguments.length,t=new Array(e);e--;)t[e]=arguments[e];return new this(t)}})}},function(e,t,r){"use strict";r(118)("WeakMap")},function(e,t,r){"use strict";var n=r(7),i=r(27),a=r(26),o=r(89);e.exports=function(e){n(n.S,e,{from:function(e){var t,r,n,s,u=arguments[1];return i(this),t=void 0!==u,t&&i(u),void 0==e?new this:(r=[],t?(n=0,s=a(u,arguments[2],2),o(e,!1,function(e){r.push(s(e,n++))})):o(e,!1,r.push,r),new this(r))}})}},function(e,t,r){"use strict";r(83),r(84),r(120),r(121),r(122),e.exports=r(9).WeakSet},function(e,t,r){"use strict";var n=r(109),i=r(110);r(111)("WeakSet",function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},{add:function(e){return n.def(i(this,"WeakSet"),e,!0)}},n,!1,!0)},function(e,t,r){"use strict";r(116)("WeakSet")},function(e,t,r){"use strict";r(118)("WeakSet")},function(e,t,r){"use strict";r(124),e.exports=r(9).String.codePointAt},function(e,t,r){"use strict";var n=r(7),i=r(49)(!1);n(n.P,"String",{codePointAt:function(e){return i(this,e)}})},function(e,t,r){"use strict";r(126),e.exports=r(9).String.fromCodePoint},function(e,t,r){"use strict";var n=r(7),i=r(45),a=String.fromCharCode,o=String.fromCodePoint;n(n.S+n.F*(!!o&&1!=o.length),"String",{fromCodePoint:function(e){for(var t,r=[],n=arguments.length,o=0;n>o;){if(t=+arguments[o++],i(t,1114111)!==t)throw RangeError(t+" is not a valid code point");r.push(t<65536?a(t):a(55296+((t-=65536)>>10),t%1024+56320))}return r.join("")}})},function(e,t,r){"use strict";r(128),r(83),e.exports=r(9).Symbol},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}var i=r(8),a=r(21),o=r(15),s=r(7),u=r(20),l=r(108).KEY,c=r(16),h=r(24),d=r(60),f=r(22),p=r(33),v=r(129),m=r(130),g=r(131),y=r(107),b=r(12),_=r(13),A=r(62),S=r(43),w=r(18),k=r(19),x=r(53),P=r(132),C=r(114),R=r(73),T=r(11),E=r(55),O=C.f,F=T.f,I=P.f,L=i.Symbol,j=i.JSON,M=j&&j.stringify,D=p("_hidden"),N=p("toPrimitive"),q={}.propertyIsEnumerable,W=h("symbol-registry"),U=h("symbols"),B=h("op-symbols"),z=Object.prototype,G="function"==typeof L&&!!R.f,H=i.QObject,X=!H||!H.prototype||!H.prototype.findChild,Y=o&&c(function(){return 7!=x(F({},"a",{get:function(){return F(this,"a",{value:7}).a}})).a})?function(e,t,r){var n=O(z,t);n&&delete z[t],F(e,t,r),n&&e!==z&&F(z,t,n)}:F,V=function(e){var t=U[e]=x(L.prototype);return t._k=e,t},Q=G&&"symbol"==n(L.iterator)?function(e){return"symbol"==n(e)}:function(e){return e instanceof L},K=function(e,t,r){return e===z&&K(B,t,r),b(e),t=w(t,!0),b(r),a(U,t)?(r.enumerable?(a(e,D)&&e[D][t]&&(e[D][t]=!1),r=x(r,{enumerable:k(0,!1)})):(a(e,D)||F(e,D,k(1,{})),e[D][t]=!0),Y(e,t,r)):F(e,t,r)},J=function(e,t){b(e);for(var r,n=g(t=S(t)),i=0,a=n.length;a>i;)K(e,r=n[i++],t[r]);return e},Z=function(e,t){return void 0===t?x(e):J(x(e),t)},$=function(e){var t=q.call(this,e=w(e,!0));return!(this===z&&a(U,e)&&!a(B,e))&&(!(t||!a(this,e)||!a(U,e)||a(this,D)&&this[D][e])||t)},ee=function(e,t){if(e=S(e),t=w(t,!0),e!==z||!a(U,t)||a(B,t)){var r=O(e,t);return!r||!a(U,t)||a(e,D)&&e[D][t]||(r.enumerable=!0),r}},te=function(e){for(var t,r=I(S(e)),n=[],i=0;r.length>i;)a(U,t=r[i++])||t==D||t==l||n.push(t);return n},re=function(e){for(var t,r=e===z,n=I(r?B:S(e)),i=[],o=0;n.length>o;)!a(U,t=n[o++])||r&&!a(z,t)||i.push(U[t]);return i};G||(L=function(){if(this instanceof L)throw TypeError("Symbol is not a constructor!");var e=f(arguments.length>0?arguments[0]:void 0),t=function t(r){this===z&&t.call(B,r),a(this,D)&&a(this[D],e)&&(this[D][e]=!1),Y(this,e,k(1,r))};return o&&X&&Y(z,e,{configurable:!0,set:t}),V(e)},u(L.prototype,"toString",function(){return this._k}),C.f=ee,T.f=K,r(133).f=P.f=te,r(74).f=$,R.f=re,o&&!r(25)&&u(z,"propertyIsEnumerable",$,!0),v.f=function(e){return V(p(e))}),s(s.G+s.W+s.F*!G,{Symbol:L});for(var ne="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ie=0;ne.length>ie;)p(ne[ie++]);for(var ae=E(p.store),oe=0;ae.length>oe;)m(ae[oe++]);s(s.S+s.F*!G,"Symbol",{for:function(e){return a(W,e+="")?W[e]:W[e]=L(e)},keyFor:function(e){if(!Q(e))throw TypeError(e+" is not a symbol!");for(var t in W)if(W[t]===e)return t},useSetter:function(){X=!0},useSimple:function(){X=!1}}),s(s.S+s.F*!G,"Object",{create:Z,defineProperty:K,defineProperties:J,getOwnPropertyDescriptor:ee,getOwnPropertyNames:te,getOwnPropertySymbols:re});var se=c(function(){R.f(1)});s(s.S+s.F*se,"Object",{getOwnPropertySymbols:function(e){return R.f(A(e))}}),j&&s(s.S+s.F*(!G||c(function(){var e=L();return"[null]"!=M([e])||"{}"!=M({a:e})||"{}"!=M(Object(e))})),"JSON",{stringify:function(e){for(var t,r,n=[e],i=1;arguments.length>i;)n.push(arguments[i++]);if(r=t=n[1],(_(t)||void 0!==e)&&!Q(e))return y(t)||(t=function(e,t){if("function"==typeof r&&(t=r.call(this,e,t)),!Q(t))return t}),n[1]=t,M.apply(j,n)}}),L.prototype[N]||r(10)(L.prototype,N,L.prototype.valueOf),d(L,"Symbol"),d(Math,"Math",!0),d(i.JSON,"JSON",!0)},function(e,t,r){"use strict";t.f=r(33)},function(e,t,r){"use strict";var n=r(8),i=r(9),a=r(25),o=r(129),s=r(11).f;e.exports=function(e){var t=i.Symbol||(i.Symbol=a?{}:n.Symbol||{});"_"==e.charAt(0)||e in t||s(t,e,{value:o.f(e)})}},function(e,t,r){"use strict";var n=r(55),i=r(73),a=r(74);e.exports=function(e){var t=n(e),r=i.f;if(r)for(var o,s=r(e),u=a.f,l=0;s.length>l;)u.call(e,o=s[l++])&&t.push(o);return t}},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}var i=r(43),a=r(133).f,o={}.toString,s="object"==("undefined"==typeof window?"undefined":n(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],u=function(e){try{return a(e)}catch(e){return s.slice()}};e.exports.f=function(e){return s&&"[object Window]"==o.call(e)?u(e):a(i(e))}},function(e,t,r){"use strict";var n=r(56),i=r(58).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,i)}},function(e,t,r){"use strict";r(135),e.exports=r(9).String.padStart},function(e,t,r){"use strict";var n=r(7),i=r(136),a=r(96),o=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(a);n(n.P+n.F*o,"String",{padStart:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0,!0)}})},function(e,t,r){"use strict";var n=r(28),i=r(137),a=r(34);e.exports=function(e,t,r,o){var s=String(a(e)),u=s.length,l=void 0===r?" ":String(r),c=n(t);if(c<=u||""==l)return s;var h=c-u,d=i.call(l,Math.ceil(h/l.length));return d.length>h&&(d=d.slice(0,h)),o?d+s:s+d}},function(e,t,r){"use strict";var n=r(29),i=r(34);e.exports=function(e){var t=String(i(this)),r="",a=n(e);if(a<0||a==1/0)throw RangeError("Count can't be negative");for(;a>0;(a>>>=1)&&(t+=t))1&a&&(r+=t);return r}},function(e,t,r){"use strict";r(139),e.exports=r(9).String.padEnd},function(e,t,r){"use strict";var n=r(7),i=r(136),a=r(96),o=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(a);n(n.P+n.F*o,"String",{padEnd:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0,!1)}})},function(e,t,r){"use strict";r(141),e.exports=r(9).Object.values},function(e,t,r){"use strict";var n=r(7),i=r(142)(!1);n(n.S,"Object",{values:function(e){return i(e)}})},function(e,t,r){"use strict";var n=r(15),i=r(55),a=r(43),o=r(74).f;e.exports=function(e){return function(t){for(var r,s=a(t),u=i(s),l=u.length,c=0,h=[];l>c;)r=u[c++],n&&!o.call(s,r)||h.push(e?[r,s[r]]:s[r]);return h}}},function(e,t,r){"use strict";var n=!1;if("undefined"!=typeof ReadableStream)try{new ReadableStream({start:function(e){e.close()}}),n=!0}catch(e){}t.ReadableStream=n?ReadableStream:r(144).ReadableStream},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}!function(e,t){for(var r in t)e[r]=t[r]}(t,function(e){function t(n){if(r[n])return r[n].exports;var i=r[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var r={};return t.m=e,t.c=r,t.i=function(e){return e},t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=7)}([function(e,t,r){function i(e){return"string"==typeof e||"symbol"===(void 0===e?"undefined":o(e))}function a(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}var o="function"==typeof Symbol&&"symbol"===n(Symbol.iterator)?function(e){return n(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":n(e)},s=r(1),u=s.assert;t.typeIsObject=function(e){return"object"===(void 0===e?"undefined":o(e))&&null!==e||"function"==typeof e},t.createDataProperty=function(e,r,n){u(t.typeIsObject(e)),Object.defineProperty(e,r,{value:n,writable:!0,enumerable:!0,configurable:!0})},t.createArrayFromList=function(e){return e.slice()},t.ArrayBufferCopy=function(e,t,r,n,i){new Uint8Array(e).set(new Uint8Array(r,n,i),t)},t.CreateIterResultObject=function(e,t){u("boolean"==typeof t);var r={};return Object.defineProperty(r,"value",{value:e,enumerable:!0,writable:!0,configurable:!0}),Object.defineProperty(r,"done",{value:t,enumerable:!0,writable:!0,configurable:!0}),r},t.IsFiniteNonNegativeNumber=function(e){return!Number.isNaN(e)&&(e!==1/0&&!(e<0))},t.InvokeOrNoop=function(e,t,r){u(void 0!==e),u(i(t)),u(Array.isArray(r));var n=e[t];if(void 0!==n)return a(n,e,r)},t.PromiseInvokeOrNoop=function(e,r,n){u(void 0!==e),u(i(r)),u(Array.isArray(n));try{return Promise.resolve(t.InvokeOrNoop(e,r,n))}catch(e){return Promise.reject(e)}},t.PromiseInvokeOrPerformFallback=function(e,t,r,n,o){u(void 0!==e),u(i(t)),u(Array.isArray(r)),u(Array.isArray(o));var s=void 0;try{s=e[t]}catch(e){return Promise.reject(e)}if(void 0===s)return n.apply(null,o);try{return Promise.resolve(a(s,e,r))}catch(e){return Promise.reject(e)}},t.TransferArrayBuffer=function(e){return e.slice()},t.ValidateAndNormalizeHighWaterMark=function(e){if(e=Number(e),Number.isNaN(e)||e<0)throw new RangeError("highWaterMark property of a queuing strategy must be non-negative and non-NaN");return e},t.ValidateAndNormalizeQueuingStrategy=function(e,r){if(void 0!==e&&"function"!=typeof e)throw new TypeError("size property of a queuing strategy must be a function");return r=t.ValidateAndNormalizeHighWaterMark(r),{size:e,highWaterMark:r}}},function(e,t,r){function n(e){e&&e.constructor===i&&setTimeout(function(){throw e},0)}function i(e){this.name="AssertionError",this.message=e||"",this.stack=(new Error).stack}function a(e,t){if(!e)throw new i(t)}i.prototype=Object.create(Error.prototype),i.prototype.constructor=i,e.exports={rethrowAssertionErrorRejection:n,AssertionError:i,assert:a}},function(e,t,r){function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e){return new be(e)}function a(e){return!!le(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")}function o(e){return he(!0===a(e),"IsWritableStreamLocked should only be used on known writable streams"),void 0!==e._writer}function s(e,t){var r=e._state;if("closed"===r)return Promise.resolve(void 0);if("errored"===r)return Promise.reject(e._storedError);var n=new TypeError("Requested to abort");if(void 0!==e._pendingAbortRequest)return Promise.reject(n);he("writable"===r||"erroring"===r,"state must be writable or erroring");var i=!1;"erroring"===r&&(i=!0,t=void 0);var a=new Promise(function(r,n){e._pendingAbortRequest={_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:i}});return!1===i&&c(e,n),a}function u(e){return he(!0===o(e)),he("writable"===e._state),new Promise(function(t,r){var n={_resolve:t,_reject:r};e._writeRequests.push(n)})}function l(e,t){var r=e._state;if("writable"===r)return void c(e,t);he("erroring"===r),h(e)}function c(e,t){he(void 0===e._storedError,"stream._storedError === undefined"),he("writable"===e._state,"state must be writable");var r=e._writableStreamController;he(void 0!==r,"controller must not be undefined"),e._state="erroring",e._storedError=t;var n=e._writer;void 0!==n&&C(n,t),!1===g(e)&&!0===r._started&&h(e)}function h(e){he("erroring"===e._state,"stream._state === erroring"),he(!1===g(e),"WritableStreamHasOperationMarkedInFlight(stream) === false"),e._state="errored",e._writableStreamController.__errorSteps();for(var t=e._storedError,r=0;r<e._writeRequests.length;r++){e._writeRequests[r]._reject(t)}if(e._writeRequests=[],void 0===e._pendingAbortRequest)return void _(e);var n=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,!0===n._wasAlreadyErroring)return n._reject(t),void _(e);e._writableStreamController.__abortSteps(n._reason).then(function(){n._resolve(),_(e)},function(t){n._reject(t),_(e)})}function d(e){he(void 0!==e._inFlightWriteRequest),e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}function f(e,t){he(void 0!==e._inFlightWriteRequest),e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,he("writable"===e._state||"erroring"===e._state),l(e,t)}function p(e){he(void 0!==e._inFlightCloseRequest),e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0;var t=e._state;he("writable"===t||"erroring"===t),"erroring"===t&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&K(r),he(void 0===e._pendingAbortRequest,"stream._pendingAbortRequest === undefined"),he(void 0===e._storedError,"stream._storedError === undefined")}function v(e,t){he(void 0!==e._inFlightCloseRequest),e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,he("writable"===e._state||"erroring"===e._state),void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),l(e,t)}function m(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function g(e){return void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest}function y(e){he(void 0===e._inFlightCloseRequest),he(void 0!==e._closeRequest),e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}function b(e){he(void 0===e._inFlightWriteRequest,"there must be no pending write request"),he(0!==e._writeRequests.length,"writeRequests must not be empty"),e._inFlightWriteRequest=e._writeRequests.shift()}function _(e){he("errored"===e._state,'_stream_.[[state]] is `"errored"`'),void 0!==e._closeRequest&&(he(void 0===e._inFlightCloseRequest),e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var t=e._writer;void 0!==t&&(V(t,e._storedError),t._closedPromise.catch(function(){}))}function A(e,t){he("writable"===e._state),he(!1===m(e));var r=e._writer;void 0!==r&&t!==e._backpressure&&(!0===t?te(r):(he(!1===t),ne(r))),e._backpressure=t}function S(e){return!!le(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")}function w(e,t){var r=e._ownerWritableStream;return he(void 0!==r),s(r,t)}function k(e){var t=e._ownerWritableStream;he(void 0!==t);var r=t._state;if("closed"===r||"errored"===r)return Promise.reject(new TypeError("The stream (in "+r+" state) is not in the writable state and cannot be closed"));he("writable"===r||"erroring"===r),he(!1===m(t));var n=new Promise(function(e,r){var n={_resolve:e,_reject:r};t._closeRequest=n});return!0===t._backpressure&&"writable"===r&&ne(e),O(t._writableStreamController),n}function x(e){var t=e._ownerWritableStream;he(void 0!==t);var r=t._state;return!0===m(t)||"closed"===r?Promise.resolve():"errored"===r?Promise.reject(t._storedError):(he("writable"===r||"erroring"===r),k(e))}function P(e,t){"pending"===e._closedPromiseState?V(e,t):Q(e,t),e._closedPromise.catch(function(){})}function C(e,t){"pending"===e._readyPromiseState?ee(e,t):re(e,t),e._readyPromise.catch(function(){})}function R(e){var t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:I(t._writableStreamController)}function T(e){var t=e._ownerWritableStream;he(void 0!==t),he(t._writer===e);var r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");C(e,r),P(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function E(e,t){var r=e._ownerWritableStream;he(void 0!==r);var n=r._writableStreamController,i=F(n,t);if(r!==e._ownerWritableStream)return Promise.reject(G("write to"));var a=r._state;if("errored"===a)return Promise.reject(r._storedError);if(!0===m(r)||"closed"===a)return Promise.reject(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return Promise.reject(r._storedError);he("writable"===a);var o=u(r)
;return L(n,t,i),o}function O(e){ve(e,"close",0),M(e)}function F(e,t){var r=e._strategySize;if(void 0===r)return 1;try{return r(t)}catch(t){return D(e,t),1}}function I(e){return e._strategyHWM-e._queueTotalSize}function L(e,t,r){var n={chunk:t};try{ve(e,n,r)}catch(t){return void D(e,t)}var i=e._controlledWritableStream;if(!1===m(i)&&"writable"===i._state){A(i,W(e))}M(e)}function j(e){return!!le(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingSink")}function M(e){var t=e._controlledWritableStream;if(!1!==e._started&&void 0===t._inFlightWriteRequest){var r=t._state;if("closed"!==r&&"errored"!==r){if("erroring"===r)return void h(t);if(0!==e._queue.length){var n=me(e);"close"===n?N(e):q(e,n.chunk)}}}}function D(e,t){"writable"===e._controlledWritableStream._state&&U(e,t)}function N(e){var t=e._controlledWritableStream;y(t),pe(e),he(0===e._queue.length,"queue must be empty once the final write record is dequeued"),se(e._underlyingSink,"close",[]).then(function(){p(t)},function(e){v(t,e)}).catch(de)}function q(e,t){var r=e._controlledWritableStream;b(r),se(e._underlyingSink,"write",[t,e]).then(function(){d(r);var t=r._state;if(he("writable"===t||"erroring"===t),pe(e),!1===m(r)&&"writable"===t){var n=W(e);A(r,n)}M(e)},function(e){f(r,e)}).catch(de)}function W(e){return I(e)<=0}function U(e,t){var r=e._controlledWritableStream;he("writable"===r._state),c(r,t)}function B(e){return new TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream")}function z(e){return new TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter")}function G(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function H(e){e._closedPromise=new Promise(function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}function X(e,t){e._closedPromise=Promise.reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}function Y(e){e._closedPromise=Promise.resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}function V(e,t){he(void 0!==e._closedPromise_resolve,"writer._closedPromise_resolve !== undefined"),he(void 0!==e._closedPromise_reject,"writer._closedPromise_reject !== undefined"),he("pending"===e._closedPromiseState,"writer._closedPromiseState is pending"),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}function Q(e,t){he(void 0===e._closedPromise_resolve,"writer._closedPromise_resolve === undefined"),he(void 0===e._closedPromise_reject,"writer._closedPromise_reject === undefined"),he("pending"!==e._closedPromiseState,"writer._closedPromiseState is not pending"),e._closedPromise=Promise.reject(t),e._closedPromiseState="rejected"}function K(e){he(void 0!==e._closedPromise_resolve,"writer._closedPromise_resolve !== undefined"),he(void 0!==e._closedPromise_reject,"writer._closedPromise_reject !== undefined"),he("pending"===e._closedPromiseState,"writer._closedPromiseState is pending"),e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}function J(e){e._readyPromise=new Promise(function(t,r){e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function Z(e,t){e._readyPromise=Promise.reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}function $(e){e._readyPromise=Promise.resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}function ee(e,t){he(void 0!==e._readyPromise_resolve,"writer._readyPromise_resolve !== undefined"),he(void 0!==e._readyPromise_reject,"writer._readyPromise_reject !== undefined"),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}function te(e){he(void 0===e._readyPromise_resolve,"writer._readyPromise_resolve === undefined"),he(void 0===e._readyPromise_reject,"writer._readyPromise_reject === undefined"),e._readyPromise=new Promise(function(t,r){e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function re(e,t){he(void 0===e._readyPromise_resolve,"writer._readyPromise_resolve === undefined"),he(void 0===e._readyPromise_reject,"writer._readyPromise_reject === undefined"),e._readyPromise=Promise.reject(t),e._readyPromiseState="rejected"}function ne(e){he(void 0!==e._readyPromise_resolve,"writer._readyPromise_resolve !== undefined"),he(void 0!==e._readyPromise_reject,"writer._readyPromise_reject !== undefined"),e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}var ie=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),ae=r(0),oe=ae.InvokeOrNoop,se=ae.PromiseInvokeOrNoop,ue=ae.ValidateAndNormalizeQueuingStrategy,le=ae.typeIsObject,ce=r(1),he=ce.assert,de=ce.rethrowAssertionErrorRejection,fe=r(3),pe=fe.DequeueValue,ve=fe.EnqueueValueWithSize,me=fe.PeekQueueValue,ge=fe.ResetQueue,ye=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r.size,a=r.highWaterMark,o=void 0===a?1:a;if(n(this,e),this._state="writable",this._storedError=void 0,this._writer=void 0,this._writableStreamController=void 0,this._writeRequests=[],this._inFlightWriteRequest=void 0,this._closeRequest=void 0,this._inFlightCloseRequest=void 0,this._pendingAbortRequest=void 0,this._backpressure=!1,void 0!==t.type)throw new RangeError("Invalid type is specified");this._writableStreamController=new _e(this,t,i,o),this._writableStreamController.__startSteps()}return ie(e,[{key:"abort",value:function(e){return!1===a(this)?Promise.reject(B("abort")):!0===o(this)?Promise.reject(new TypeError("Cannot abort a stream that already has a writer")):s(this,e)}},{key:"getWriter",value:function(){if(!1===a(this))throw B("getWriter");return i(this)}},{key:"locked",get:function(){if(!1===a(this))throw B("locked");return o(this)}}]),e}();e.exports={AcquireWritableStreamDefaultWriter:i,IsWritableStream:a,IsWritableStreamLocked:o,WritableStream:ye,WritableStreamAbort:s,WritableStreamDefaultControllerError:U,WritableStreamDefaultWriterCloseWithErrorPropagation:x,WritableStreamDefaultWriterRelease:T,WritableStreamDefaultWriterWrite:E,WritableStreamCloseQueuedOrInFlight:m};var be=function(){function e(t){if(n(this,e),!1===a(t))throw new TypeError("WritableStreamDefaultWriter can only be constructed with a WritableStream instance");if(!0===o(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;var r=t._state;if("writable"===r)!1===m(t)&&!0===t._backpressure?J(this):$(this),H(this);else if("erroring"===r)Z(this,t._storedError),this._readyPromise.catch(function(){}),H(this);else if("closed"===r)$(this),Y(this);else{he("errored"===r,"state must be errored");var i=t._storedError;Z(this,i),this._readyPromise.catch(function(){}),X(this,i),this._closedPromise.catch(function(){})}}return ie(e,[{key:"abort",value:function(e){return!1===S(this)?Promise.reject(z("abort")):void 0===this._ownerWritableStream?Promise.reject(G("abort")):w(this,e)}},{key:"close",value:function(){if(!1===S(this))return Promise.reject(z("close"));var e=this._ownerWritableStream;return void 0===e?Promise.reject(G("close")):!0===m(e)?Promise.reject(new TypeError("cannot close an already-closing stream")):k(this)}},{key:"releaseLock",value:function(){if(!1===S(this))throw z("releaseLock");var e=this._ownerWritableStream;void 0!==e&&(he(void 0!==e._writer),T(this))}},{key:"write",value:function(e){return!1===S(this)?Promise.reject(z("write")):void 0===this._ownerWritableStream?Promise.reject(G("write to")):E(this,e)}},{key:"closed",get:function(){return!1===S(this)?Promise.reject(z("closed")):this._closedPromise}},{key:"desiredSize",get:function(){if(!1===S(this))throw z("desiredSize");if(void 0===this._ownerWritableStream)throw G("desiredSize");return R(this)}},{key:"ready",get:function(){return!1===S(this)?Promise.reject(z("ready")):this._readyPromise}}]),e}(),_e=function(){function e(t,r,i,o){if(n(this,e),!1===a(t))throw new TypeError("WritableStreamDefaultController can only be constructed with a WritableStream instance");if(void 0!==t._writableStreamController)throw new TypeError("WritableStreamDefaultController instances can only be created by the WritableStream constructor");this._controlledWritableStream=t,this._underlyingSink=r,this._queue=void 0,this._queueTotalSize=void 0,ge(this),this._started=!1;var s=ue(i,o);this._strategySize=s.size,this._strategyHWM=s.highWaterMark,A(t,W(this))}return ie(e,[{key:"error",value:function(e){if(!1===j(this))throw new TypeError("WritableStreamDefaultController.prototype.error can only be used on a WritableStreamDefaultController");"writable"===this._controlledWritableStream._state&&U(this,e)}},{key:"__abortSteps",value:function(e){return se(this._underlyingSink,"abort",[e])}},{key:"__errorSteps",value:function(){ge(this)}},{key:"__startSteps",value:function(){var e=this,t=oe(this._underlyingSink,"start",[this]),r=this._controlledWritableStream;Promise.resolve(t).then(function(){he("writable"===r._state||"erroring"===r._state),e._started=!0,M(e)},function(t){he("writable"===r._state||"erroring"===r._state),e._started=!0,l(r,t)}).catch(de)}}]),e}()},function(e,t,r){var n=r(0),i=n.IsFiniteNonNegativeNumber,a=r(1),o=a.assert;t.DequeueValue=function(e){o("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: DequeueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."),o(e._queue.length>0,"Spec-level failure: should never dequeue from an empty queue.");var t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value},t.EnqueueValueWithSize=function(e,t,r){if(o("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: EnqueueValueWithSize should only be used on containers with [[queue]] and [[queueTotalSize]]."),r=Number(r),!i(r))throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r},t.PeekQueueValue=function(e){return o("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: PeekQueueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."),o(e._queue.length>0,"Spec-level failure: should never peek at an empty queue."),e._queue[0].value},t.ResetQueue=function(e){o("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: ResetQueue should only be used on containers with [[queue]] and [[queueTotalSize]]."),e._queue=[],e._queueTotalSize=0}},function(e,t,r){function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e){return new tt(e)}function a(e){return new et(e)}function o(e){return!!Me(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")}function s(e){return Ne(!0===o(e),"IsReadableStreamDisturbed should only be used on known readable streams"),e._disturbed}function u(e){return Ne(!0===o(e),"IsReadableStreamLocked should only be used on known readable streams"),void 0!==e._reader}function l(e,t){Ne(!0===o(e)),Ne("boolean"==typeof t);var r=a(e),n={closedOrErrored:!1,canceled1:!1,canceled2:!1,reason1:void 0,reason2:void 0};n.promise=new Promise(function(e){n._resolve=e});var i=c();i._reader=r,i._teeState=n,i._cloneForBranch2=t;var s=h();s._stream=e,s._teeState=n;var u=d();u._stream=e,u._teeState=n;var l=Object.create(Object.prototype);je(l,"pull",i),je(l,"cancel",s);var f=new $e(l),p=Object.create(Object.prototype);je(p,"pull",i),je(p,"cancel",u);var v=new $e(p);return i._branch1=f._readableStreamController,i._branch2=v._readableStreamController,r._closedPromise.catch(function(e){!0!==n.closedOrErrored&&(M(i._branch1,e),M(i._branch2,e),n.closedOrErrored=!0)}),[f,v]}function c(){function e(){var t=e._reader,r=e._branch1,n=e._branch2,i=e._teeState;return E(t).then(function(e){Ne(Me(e));var t=e.value,a=e.done;if(Ne("boolean"==typeof a),!0===a&&!1===i.closedOrErrored&&(!1===i.canceled1&&L(r),!1===i.canceled2&&L(n),i.closedOrErrored=!0),!0!==i.closedOrErrored){var o=t,s=t;!1===i.canceled1&&j(r,o),!1===i.canceled2&&j(n,s)}})}return e}function h(){function e(t){var r=e._stream,n=e._teeState;if(n.canceled1=!0,n.reason1=t,!0===n.canceled2){var i=Le([n.reason1,n.reason2]),a=v(r,i);n._resolve(a)}return n.promise}return e}function d(){function e(t){var r=e._stream,n=e._teeState;if(n.canceled2=!0,n.reason2=t,!0===n.canceled1){var i=Le([n.reason1,n.reason2]),a=v(r,i);n._resolve(a)}return n.promise}return e}function f(e){return Ne(!0===k(e._reader)),Ne("readable"===e._state||"closed"===e._state),new Promise(function(t,r){var n={_resolve:t,_reject:r};e._reader._readIntoRequests.push(n)})}function p(e){return Ne(!0===x(e._reader)),Ne("readable"===e._state),new Promise(function(t,r){var n={_resolve:t,_reject:r};e._reader._readRequests.push(n)})}function v(e,t){return e._disturbed=!0,"closed"===e._state?Promise.resolve(void 0):"errored"===e._state?Promise.reject(e._storedError):(m(e),e._readableStreamController.__cancelSteps(t).then(function(){}))}function m(e){Ne("readable"===e._state),e._state="closed";var t=e._reader;if(void 0!==t){if(!0===x(t)){for(var r=0;r<t._readRequests.length;r++){(0,t._readRequests[r]._resolve)(Pe(void 0,!0))}t._readRequests=[]}ge(t)}}function g(e,t){Ne(!0===o(e),"stream must be ReadableStream"),Ne("readable"===e._state,"state must be readable"),e._state="errored",e._storedError=t;var r=e._reader;if(void 0!==r){if(!0===x(r)){for(var n=0;n<r._readRequests.length;n++){r._readRequests[n]._reject(t)}r._readRequests=[]}else{Ne(k(r),"reader must be ReadableStreamBYOBReader");for(var i=0;i<r._readIntoRequests.length;i++){r._readIntoRequests[i]._reject(t)}r._readIntoRequests=[]}ve(r,t),r._closedPromise.catch(function(){})}}function y(e,t,r){var n=e._reader;Ne(n._readIntoRequests.length>0),n._readIntoRequests.shift()._resolve(Pe(t,r))}function b(e,t,r){var n=e._reader;Ne(n._readRequests.length>0),n._readRequests.shift()._resolve(Pe(t,r))}function _(e){return e._reader._readIntoRequests.length}function A(e){return e._reader._readRequests.length}function S(e){var t=e._reader;return void 0!==t&&!1!==k(t)}function w(e){var t=e._reader;return void 0!==t&&!1!==x(t)}function k(e){return!!Me(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")}function x(e){return!!Me(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")}function P(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?de(e):"closed"===t._state?pe(e):(Ne("errored"===t._state,"state must be errored"),fe(e,t._storedError),e._closedPromise.catch(function(){}))}function C(e,t){var r=e._ownerReadableStream;return Ne(void 0!==r),v(r,t)}function R(e){Ne(void 0!==e._ownerReadableStream),Ne(e._ownerReadableStream._reader===e),"readable"===e._ownerReadableStream._state?ve(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):me(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._closedPromise.catch(function(){}),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function T(e,t){var r=e._ownerReadableStream;return Ne(void 0!==r),r._disturbed=!0,"errored"===r._state?Promise.reject(r._storedError):J(r._readableStreamController,t)}function E(e){var t=e._ownerReadableStream;return Ne(void 0!==t),t._disturbed=!0,"closed"===t._state?Promise.resolve(Pe(void 0,!0)):"errored"===t._state?Promise.reject(t._storedError):(Ne("readable"===t._state),t._readableStreamController.__pullSteps())}function O(e){return!!Me(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingSource")}function F(e){if(!1!==I(e)){if(!0===e._pulling)return void(e._pullAgain=!0);Ne(!1===e._pullAgain),e._pulling=!0,Te(e._underlyingSource,"pull",[e]).then(function(){if(e._pulling=!1,!0===e._pullAgain)return e._pullAgain=!1,F(e)},function(t){D(e,t)}).catch(qe)}}function I(e){var t=e._controlledReadableStream;return"closed"!==t._state&&"errored"!==t._state&&(!0!==e._closeRequested&&(!1!==e._started&&(!0===u(t)&&A(t)>0||N(e)>0)))}function L(e){var t=e._controlledReadableStream;Ne(!1===e._closeRequested),Ne("readable"===t._state),e._closeRequested=!0,0===e._queue.length&&m(t)}function j(e,t){var r=e._controlledReadableStream;if(Ne(!1===e._closeRequested),Ne("readable"===r._state),!0===u(r)&&A(r)>0)b(r,t,!1);else{var n=1;if(void 0!==e._strategySize){var i=e._strategySize;try{n=i(t)}catch(t){throw D(e,t),t}}try{Be(e,t,n)}catch(t){throw D(e,t),t}}F(e)}function M(e,t){var r=e._controlledReadableStream;Ne("readable"===r._state),ze(e),g(r,t)}function D(e,t){"readable"===e._controlledReadableStream._state&&M(e,t)}function N(e){var t=e._controlledReadableStream,r=t._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function q(e){return!!Me(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingByteSource")}function W(e){return!!Me(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")}function U(e){if(!1!==re(e)){if(!0===e._pulling)return void(e._pullAgain=!0);Ne(!1===e._pullAgain),e._pulling=!0,Te(e._underlyingByteSource,"pull",[e]).then(function(){e._pulling=!1,!0===e._pullAgain&&(e._pullAgain=!1,U(e))},function(t){"readable"===e._controlledReadableStream._state&&ae(e,t)}).catch(qe)}}function B(e){Q(e),e._pendingPullIntos=[]}function z(e,t){Ne("errored"!==e._state,"state must not be errored");var r=!1;"closed"===e._state&&(Ne(0===t.bytesFilled),r=!0);var n=G(t);"default"===t.readerType?b(e,n,r):(Ne("byob"===t.readerType),y(e,n,r))}function G(e){var t=e.bytesFilled,r=e.elementSize;return Ne(t<=e.byteLength),Ne(t%r==0),new e.ctor(e.buffer,e.byteOffset,t/r)}function H(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function X(e,t){var r=t.elementSize,n=t.bytesFilled-t.bytesFilled%r,i=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),a=t.bytesFilled+i,o=a-a%r,s=i,u=!1;o>n&&(s=o-t.bytesFilled,u=!0);for(var l=e._queue;s>0;){var c=l[0],h=Math.min(s,c.byteLength),d=t.byteOffset+t.bytesFilled;xe(t.buffer,d,c.buffer,c.byteOffset,h),c.byteLength===h?l.shift():(c.byteOffset+=h,c.byteLength-=h),e._queueTotalSize-=h,Y(e,h,t),s-=h}return!1===u&&(Ne(0===e._queueTotalSize,"queue must be empty"),Ne(t.bytesFilled>0),Ne(t.bytesFilled<t.elementSize)),u}function Y(e,t,r){Ne(0===e._pendingPullIntos.length||e._pendingPullIntos[0]===r),Q(e),r.bytesFilled+=t}function V(e){Ne("readable"===e._controlledReadableStream._state),0===e._queueTotalSize&&!0===e._closeRequested?m(e._controlledReadableStream):U(e)}function Q(e){void 0!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=void 0,e._byobRequest=void 0)}function K(e){for(Ne(!1===e._closeRequested);e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var t=e._pendingPullIntos[0];!0===X(e,t)&&(te(e),z(e._controlledReadableStream,t))}}function J(e,t){var r=e._controlledReadableStream,n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);var i=t.constructor,a={buffer:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,ctor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return a.buffer=Ee(a.buffer),e._pendingPullIntos.push(a),f(r);if("closed"===r._state){var o=new t.constructor(a.buffer,a.byteOffset,0);return Promise.resolve(Pe(o,!0))}if(e._queueTotalSize>0){if(!0===X(e,a)){var s=G(a);return V(e),Promise.resolve(Pe(s,!1))}if(!0===e._closeRequested){var u=new TypeError("Insufficient bytes to fill elements in the given buffer");return ae(e,u),Promise.reject(u)}}a.buffer=Ee(a.buffer),e._pendingPullIntos.push(a);var l=f(r);return U(e),l}function Z(e,t){t.buffer=Ee(t.buffer),Ne(0===t.bytesFilled,"bytesFilled must be 0");var r=e._controlledReadableStream;if(!0===S(r))for(;_(r)>0;){var n=te(e);z(r,n)}}function $(e,t,r){if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range");if(Y(e,t,r),!(r.bytesFilled<r.elementSize)){te(e);var n=r.bytesFilled%r.elementSize;if(n>0){var i=r.byteOffset+r.bytesFilled,a=r.buffer.slice(i-n,i);H(e,a,0,a.byteLength)}r.buffer=Ee(r.buffer),r.bytesFilled-=n,z(e._controlledReadableStream,r),K(e)}}function ee(e,t){var r=e._pendingPullIntos[0],n=e._controlledReadableStream;if("closed"===n._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream");Z(e,r)}else Ne("readable"===n._state),$(e,t,r)}function te(e){var t=e._pendingPullIntos.shift();return Q(e),t}function re(e){var t=e._controlledReadableStream;return"readable"===t._state&&(!0!==e._closeRequested&&(!1!==e._started&&(!0===w(t)&&A(t)>0||(!0===S(t)&&_(t)>0||oe(e)>0))))}function ne(e){var t=e._controlledReadableStream;if(Ne(!1===e._closeRequested),Ne("readable"===t._state),e._queueTotalSize>0)return void(e._closeRequested=!0);if(e._pendingPullIntos.length>0){if(e._pendingPullIntos[0].bytesFilled>0){var r=new TypeError("Insufficient bytes to fill elements in the given buffer");throw ae(e,r),r}}m(t)}function ie(e,t){var r=e._controlledReadableStream;Ne(!1===e._closeRequested),Ne("readable"===r._state);var n=t.buffer,i=t.byteOffset,a=t.byteLength,o=Ee(n);if(!0===w(r))if(0===A(r))H(e,o,i,a);else{Ne(0===e._queue.length);var s=new Uint8Array(o,i,a);b(r,s,!1)}else!0===S(r)?(H(e,o,i,a),K(e)):(Ne(!1===u(r),"stream must not be locked"),H(e,o,i,a))}function ae(e,t){var r=e._controlledReadableStream;Ne("readable"===r._state),B(e),ze(e),g(r,t)}function oe(e){var t=e._controlledReadableStream,r=t._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function se(e,t){if(t=Number(t),!1===Ce(t))throw new RangeError("bytesWritten must be a finite");Ne(e._pendingPullIntos.length>0),ee(e,t)}function ue(e,t){Ne(e._pendingPullIntos.length>0);var r=e._pendingPullIntos[0];if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.byteLength!==t.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");r.buffer=t.buffer,ee(e,t.byteLength)}function le(e){return new TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream")}function ce(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function he(e){return new TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader")}function de(e){e._closedPromise=new Promise(function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r})}function fe(e,t){e._closedPromise=Promise.reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function pe(e){e._closedPromise=Promise.resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function ve(e,t){Ne(void 0!==e._closedPromise_resolve),Ne(void 0!==e._closedPromise_reject),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function me(e,t){Ne(void 0===e._closedPromise_resolve),Ne(void 0===e._closedPromise_reject),e._closedPromise=Promise.reject(t)}function ge(e){Ne(void 0!==e._closedPromise_resolve),Ne(void 0!==e._closedPromise_reject),e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function ye(e){return new TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader")}function be(e){return new TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController")}function _e(e){return new TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest")}function Ae(e){return new TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController")}function Se(e){try{Promise.prototype.then.call(e,void 0,function(){})}catch(e){}}var we=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),ke=r(0),xe=ke.ArrayBufferCopy,Pe=ke.CreateIterResultObject,Ce=ke.IsFiniteNonNegativeNumber,Re=ke.InvokeOrNoop,Te=ke.PromiseInvokeOrNoop,Ee=ke.TransferArrayBuffer,Oe=ke.ValidateAndNormalizeQueuingStrategy,Fe=ke.ValidateAndNormalizeHighWaterMark,Ie=r(0),Le=Ie.createArrayFromList,je=Ie.createDataProperty,Me=Ie.typeIsObject,De=r(1),Ne=De.assert,qe=De.rethrowAssertionErrorRejection,We=r(3),Ue=We.DequeueValue,Be=We.EnqueueValueWithSize,ze=We.ResetQueue,Ge=r(2),He=Ge.AcquireWritableStreamDefaultWriter,Xe=Ge.IsWritableStream,Ye=Ge.IsWritableStreamLocked,Ve=Ge.WritableStreamAbort,Qe=Ge.WritableStreamDefaultWriterCloseWithErrorPropagation,Ke=Ge.WritableStreamDefaultWriterRelease,Je=Ge.WritableStreamDefaultWriterWrite,Ze=Ge.WritableStreamCloseQueuedOrInFlight,$e=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r.size,a=r.highWaterMark;n(this,e),this._state="readable",this._reader=void 0,this._storedError=void 0,this._disturbed=!1,this._readableStreamController=void 0;var o=t.type;if("bytes"===String(o))void 0===a&&(a=0),this._readableStreamController=new it(this,t,a);else{if(void 0!==o)throw new RangeError("Invalid type is specified");void 0===a&&(a=1),this._readableStreamController=new rt(this,t,i,a)}}return we(e,[{key:"cancel",value:function(e){return!1===o(this)?Promise.reject(le("cancel")):!0===u(this)?Promise.reject(new TypeError("Cannot cancel a stream that already has a reader")):v(this,e)}},{key:"getReader",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.mode;if(!1===o(this))throw le("getReader");if(void 0===t)return a(this);if("byob"===(t=String(t)))return i(this);throw new RangeError("Invalid mode is specified")}},{key:"pipeThrough",value:function(e,t){var r=e.writable,n=e.readable;return Se(this.pipeTo(r,t)),n}},{key:"pipeTo",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.preventClose,i=r.preventAbort,s=r.preventCancel;if(!1===o(this))return Promise.reject(le("pipeTo"));if(!1===Xe(e))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));if(n=Boolean(n),i=Boolean(i),s=Boolean(s),!0===u(this))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream"));if(!0===Ye(e))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream"));var l=a(this),c=He(e),h=!1,d=Promise.resolve();return new Promise(function(r,a){function o(){return d=Promise.resolve(),!0===h?Promise.resolve():c._readyPromise.then(function(){return E(l).then(function(e){var t=e.value;!0!==e.done&&(d=Je(c,t).catch(function(){}))})}).then(o)}function u(){var e=d;return d.then(function(){return e!==d?u():void 0})}function f(e,t,r){"errored"===e._state?r(e._storedError):t.catch(r).catch(qe)}function p(t,r,n){function i(){t().then(function(){return g(r,n)},function(e){return g(!0,e)}).catch(qe)}!0!==h&&(h=!0,"writable"===e._state&&!1===Ze(e)?u().then(i):i())}function m(t,r){!0!==h&&(h=!0,"writable"===e._state&&!1===Ze(e)?u().then(function(){return g(t,r)}).catch(qe):g(t,r))}function g(e,t){Ke(c),R(l),e?a(t):r(void 0)}if(f(t,l._closedPromise,function(t){!1===i?p(function(){return Ve(e,t)},!0,t):m(!0,t)}),f(e,c._closedPromise,function(e){!1===s?p(function(){return v(t,e)},!0,e):m(!0,e)}),function(e,t,r){"closed"===e._state?r():t.then(r).catch(qe)}(t,l._closedPromise,function(){!1===n?p(function(){return Qe(c)}):m()}),!0===Ze(e)||"closed"===e._state){var y=new TypeError("the destination writable stream closed before all data could be piped to it");!1===s?p(function(){return v(t,y)},!0,y):m(!0,y)}o().catch(function(e){d=Promise.resolve(),qe(e)})})}},{key:"tee",value:function(){if(!1===o(this))throw le("tee");var e=l(this,!1);return Le(e)}},{key:"locked",get:function(){if(!1===o(this))throw le("locked");return u(this)}}]),e}();e.exports={ReadableStream:$e,IsReadableStreamDisturbed:s,ReadableStreamDefaultControllerClose:L,ReadableStreamDefaultControllerEnqueue:j,ReadableStreamDefaultControllerError:M,ReadableStreamDefaultControllerGetDesiredSize:N};var et=function(){function e(t){if(n(this,e),!1===o(t))throw new TypeError("ReadableStreamDefaultReader can only be constructed with a ReadableStream instance");if(!0===u(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");P(this,t),this._readRequests=[]}return we(e,[{key:"cancel",value:function(e){return!1===x(this)?Promise.reject(he("cancel")):void 0===this._ownerReadableStream?Promise.reject(ce("cancel")):C(this,e)}},{key:"read",value:function(){return!1===x(this)?Promise.reject(he("read")):void 0===this._ownerReadableStream?Promise.reject(ce("read from")):E(this)}},{key:"releaseLock",value:function(){if(!1===x(this))throw he("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");R(this)}}},{key:"closed",get:function(){return!1===x(this)?Promise.reject(he("closed")):this._closedPromise}}]),e}(),tt=function(){function e(t){if(n(this,e),!o(t))throw new TypeError("ReadableStreamBYOBReader can only be constructed with a ReadableStream instance given a byte source");if(!1===q(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");if(u(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");P(this,t),this._readIntoRequests=[]}return we(e,[{key:"cancel",value:function(e){return k(this)?void 0===this._ownerReadableStream?Promise.reject(ce("cancel")):C(this,e):Promise.reject(ye("cancel"))}},{key:"read",value:function(e){return k(this)?void 0===this._ownerReadableStream?Promise.reject(ce("read from")):ArrayBuffer.isView(e)?0===e.byteLength?Promise.reject(new TypeError("view must have non-zero byteLength")):T(this,e):Promise.reject(new TypeError("view must be an array buffer view")):Promise.reject(ye("read"))}},{key:"releaseLock",value:function(){if(!k(this))throw ye("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");R(this)}}},{key:"closed",get:function(){return k(this)?this._closedPromise:Promise.reject(ye("closed"))}}]),e}(),rt=function(){function e(t,r,i,a){if(n(this,e),!1===o(t))throw new TypeError("ReadableStreamDefaultController can only be constructed with a ReadableStream instance");if(void 0!==t._readableStreamController)throw new TypeError("ReadableStreamDefaultController instances can only be created by the ReadableStream constructor");this._controlledReadableStream=t,this._underlyingSource=r,this._queue=void 0,this._queueTotalSize=void 0,ze(this),this._started=!1,this._closeRequested=!1,this._pullAgain=!1,this._pulling=!1;var s=Oe(i,a);this._strategySize=s.size,this._strategyHWM=s.highWaterMark;var u=this,l=Re(r,"start",[this]);Promise.resolve(l).then(function(){u._started=!0,Ne(!1===u._pulling),Ne(!1===u._pullAgain),F(u)},function(e){D(u,e)}).catch(qe)}return we(e,[{key:"close",value:function(){if(!1===O(this))throw be("close")
;if(!0===this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");L(this)}},{key:"enqueue",value:function(e){if(!1===O(this))throw be("enqueue");if(!0===this._closeRequested)throw new TypeError("stream is closed or draining");var t=this._controlledReadableStream._state;if("readable"!==t)throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to");return j(this,e)}},{key:"error",value:function(e){if(!1===O(this))throw be("error");var t=this._controlledReadableStream;if("readable"!==t._state)throw new TypeError("The stream is "+t._state+" and so cannot be errored");M(this,e)}},{key:"__cancelSteps",value:function(e){return ze(this),Te(this._underlyingSource,"cancel",[e])}},{key:"__pullSteps",value:function(){var e=this._controlledReadableStream;if(this._queue.length>0){var t=Ue(this);return!0===this._closeRequested&&0===this._queue.length?m(e):F(this),Promise.resolve(Pe(t,!1))}var r=p(e);return F(this),r}},{key:"desiredSize",get:function(){if(!1===O(this))throw be("desiredSize");return N(this)}}]),e}(),nt=function(){function e(t,r){n(this,e),this._associatedReadableByteStreamController=t,this._view=r}return we(e,[{key:"respond",value:function(e){if(!1===W(this))throw _e("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");se(this._associatedReadableByteStreamController,e)}},{key:"respondWithNewView",value:function(e){if(!1===W(this))throw _e("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");ue(this._associatedReadableByteStreamController,e)}},{key:"view",get:function(){return this._view}}]),e}(),it=function(){function e(t,r,i){if(n(this,e),!1===o(t))throw new TypeError("ReadableByteStreamController can only be constructed with a ReadableStream instance given a byte source");if(void 0!==t._readableStreamController)throw new TypeError("ReadableByteStreamController instances can only be created by the ReadableStream constructor given a byte source");this._controlledReadableStream=t,this._underlyingByteSource=r,this._pullAgain=!1,this._pulling=!1,B(this),this._queue=this._queueTotalSize=void 0,ze(this),this._closeRequested=!1,this._started=!1,this._strategyHWM=Fe(i);var a=r.autoAllocateChunkSize;if(void 0!==a&&(!1===Number.isInteger(a)||a<=0))throw new RangeError("autoAllocateChunkSize must be a positive integer");this._autoAllocateChunkSize=a,this._pendingPullIntos=[];var s=this,u=Re(r,"start",[this]);Promise.resolve(u).then(function(){s._started=!0,Ne(!1===s._pulling),Ne(!1===s._pullAgain),U(s)},function(e){"readable"===t._state&&ae(s,e)}).catch(qe)}return we(e,[{key:"close",value:function(){if(!1===q(this))throw Ae("close");if(!0===this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");ne(this)}},{key:"enqueue",value:function(e){if(!1===q(this))throw Ae("enqueue");if(!0===this._closeRequested)throw new TypeError("stream is closed or draining");var t=this._controlledReadableStream._state;if("readable"!==t)throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to");if(!ArrayBuffer.isView(e))throw new TypeError("You can only enqueue array buffer views when using a ReadableByteStreamController");ie(this,e)}},{key:"error",value:function(e){if(!1===q(this))throw Ae("error");var t=this._controlledReadableStream;if("readable"!==t._state)throw new TypeError("The stream is "+t._state+" and so cannot be errored");ae(this,e)}},{key:"__cancelSteps",value:function(e){if(this._pendingPullIntos.length>0){this._pendingPullIntos[0].bytesFilled=0}return ze(this),Te(this._underlyingByteSource,"cancel",[e])}},{key:"__pullSteps",value:function(){var e=this._controlledReadableStream;if(Ne(!0===w(e)),this._queueTotalSize>0){Ne(0===A(e));var t=this._queue.shift();this._queueTotalSize-=t.byteLength,V(this);var r=void 0;try{r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}catch(e){return Promise.reject(e)}return Promise.resolve(Pe(r,!1))}var n=this._autoAllocateChunkSize;if(void 0!==n){var i=void 0;try{i=new ArrayBuffer(n)}catch(e){return Promise.reject(e)}var a={buffer:i,byteOffset:0,byteLength:n,bytesFilled:0,elementSize:1,ctor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(a)}var o=p(e);return U(this),o}},{key:"byobRequest",get:function(){if(!1===q(this))throw Ae("byobRequest");if(void 0===this._byobRequest&&this._pendingPullIntos.length>0){var e=this._pendingPullIntos[0],t=new Uint8Array(e.buffer,e.byteOffset+e.bytesFilled,e.byteLength-e.bytesFilled);this._byobRequest=new nt(this,t)}return this._byobRequest}},{key:"desiredSize",get:function(){if(!1===q(this))throw Ae("desiredSize");return oe(this)}}]),e}()},function(e,t,r){var n=r(6),i=r(4),a=r(2);t.TransformStream=n.TransformStream,t.ReadableStream=i.ReadableStream,t.IsReadableStreamDisturbed=i.IsReadableStreamDisturbed,t.ReadableStreamDefaultControllerClose=i.ReadableStreamDefaultControllerClose,t.ReadableStreamDefaultControllerEnqueue=i.ReadableStreamDefaultControllerEnqueue,t.ReadableStreamDefaultControllerError=i.ReadableStreamDefaultControllerError,t.ReadableStreamDefaultControllerGetDesiredSize=i.ReadableStreamDefaultControllerGetDesiredSize,t.AcquireWritableStreamDefaultWriter=a.AcquireWritableStreamDefaultWriter,t.IsWritableStream=a.IsWritableStream,t.IsWritableStreamLocked=a.IsWritableStreamLocked,t.WritableStream=a.WritableStream,t.WritableStreamAbort=a.WritableStreamAbort,t.WritableStreamDefaultControllerError=a.WritableStreamDefaultControllerError,t.WritableStreamDefaultWriterCloseWithErrorPropagation=a.WritableStreamDefaultWriterCloseWithErrorPropagation,t.WritableStreamDefaultWriterRelease=a.WritableStreamDefaultWriterRelease,t.WritableStreamDefaultWriterWrite=a.WritableStreamDefaultWriterWrite},function(e,t,r){function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e){if(!0===e._errored)throw new TypeError("TransformStream is already errored");if(!0===e._readableClosed)throw new TypeError("Readable side is already closed");s(e)}function a(e,t){if(!0===e._errored)throw new TypeError("TransformStream is already errored");if(!0===e._readableClosed)throw new TypeError("Readable side is already closed");var r=e._readableController;try{T(r,t)}catch(t){throw e._readableClosed=!0,u(e,t),e._storedError}!0==O(r)<=0&&!1===e._backpressure&&h(e,!0)}function o(e,t){if(!0===e._errored)throw new TypeError("TransformStream is already errored");l(e,t)}function s(e){_(!1===e._errored),_(!1===e._readableClosed);try{R(e._readableController)}catch(e){_(!1)}e._readableClosed=!0}function u(e,t){!1===e._errored&&l(e,t)}function l(e,t){_(!1===e._errored),e._errored=!0,e._storedError=t,!1===e._writableDone&&L(e._writableController,t),!1===e._readableClosed&&E(e._readableController,t)}function c(e){return _(void 0!==e._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),!1===e._backpressure?Promise.resolve():(_(!0===e._backpressure,"_backpressure should have been initialized"),e._backpressureChangePromise)}function h(e,t){_(e._backpressure!==t,"TransformStreamSetBackpressure() should be called only when backpressure is changed"),void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(t),e._backpressureChangePromise=new Promise(function(t){e._backpressureChangePromise_resolve=t}),e._backpressureChangePromise.then(function(e){_(e!==t,"_backpressureChangePromise should be fulfilled only when backpressure is changed")}),e._backpressure=t}function d(e,t){return a(t._controlledTransformStream,e),Promise.resolve()}function f(e,t){_(!1===e._errored),_(!1===e._transforming),_(!1===e._backpressure),e._transforming=!0;var r=e._transformer,n=e._transformStreamController;return w(r,"transform",[t,n],d,[t,n]).then(function(){return e._transforming=!1,c(e)},function(t){return u(e,t),Promise.reject(t)})}function p(e){return!!x(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")}function v(e){return!!x(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")}function m(e){return new TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController")}function g(e){return new TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream")}var y=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),b=r(1),_=b.assert,A=r(0),S=A.InvokeOrNoop,w=A.PromiseInvokeOrPerformFallback,k=A.PromiseInvokeOrNoop,x=A.typeIsObject,P=r(4),C=P.ReadableStream,R=P.ReadableStreamDefaultControllerClose,T=P.ReadableStreamDefaultControllerEnqueue,E=P.ReadableStreamDefaultControllerError,O=P.ReadableStreamDefaultControllerGetDesiredSize,F=r(2),I=F.WritableStream,L=F.WritableStreamDefaultControllerError,j=function(){function e(t,r){n(this,e),this._transformStream=t,this._startPromise=r}return y(e,[{key:"start",value:function(e){var t=this._transformStream;return t._writableController=e,this._startPromise.then(function(){return c(t)})}},{key:"write",value:function(e){return f(this._transformStream,e)}},{key:"abort",value:function(){var e=this._transformStream;e._writableDone=!0,l(e,new TypeError("Writable side aborted"))}},{key:"close",value:function(){var e=this._transformStream;return _(!1===e._transforming),e._writableDone=!0,k(e._transformer,"flush",[e._transformStreamController]).then(function(){return!0===e._errored?Promise.reject(e._storedError):(!1===e._readableClosed&&s(e),Promise.resolve())}).catch(function(t){return u(e,t),Promise.reject(e._storedError)})}}]),e}(),M=function(){function e(t,r){n(this,e),this._transformStream=t,this._startPromise=r}return y(e,[{key:"start",value:function(e){var t=this._transformStream;return t._readableController=e,this._startPromise.then(function(){return _(void 0!==t._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),!0===t._backpressure?Promise.resolve():(_(!1===t._backpressure,"_backpressure should have been initialized"),t._backpressureChangePromise)})}},{key:"pull",value:function(){var e=this._transformStream;return _(!0===e._backpressure,"pull() should be never called while _backpressure is false"),_(void 0!==e._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),h(e,!1),e._backpressureChangePromise}},{key:"cancel",value:function(){var e=this._transformStream;e._readableClosed=!0,l(e,new TypeError("Readable side canceled"))}}]),e}(),D=function(){function e(t){if(n(this,e),!1===v(t))throw new TypeError("TransformStreamDefaultController can only be constructed with a TransformStream instance");if(void 0!==t._transformStreamController)throw new TypeError("TransformStreamDefaultController instances can only be created by the TransformStream constructor");this._controlledTransformStream=t}return y(e,[{key:"enqueue",value:function(e){if(!1===p(this))throw m("enqueue");a(this._controlledTransformStream,e)}},{key:"close",value:function(){if(!1===p(this))throw m("close");i(this._controlledTransformStream)}},{key:"error",value:function(e){if(!1===p(this))throw m("error");o(this._controlledTransformStream,e)}},{key:"desiredSize",get:function(){if(!1===p(this))throw m("desiredSize");var e=this._controlledTransformStream,t=e._readableController;return O(t)}}]),e}(),N=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};n(this,e),this._transformer=t;var r=t.readableStrategy,i=t.writableStrategy;this._transforming=!1,this._errored=!1,this._storedError=void 0,this._writableController=void 0,this._readableController=void 0,this._transformStreamController=void 0,this._writableDone=!1,this._readableClosed=!1,this._backpressure=void 0,this._backpressureChangePromise=void 0,this._backpressureChangePromise_resolve=void 0,this._transformStreamController=new D(this);var a=void 0,o=new Promise(function(e){a=e}),s=new M(this,o);this._readable=new C(s,r);var u=new j(this,o);this._writable=new I(u,i),_(void 0!==this._writableController),_(void 0!==this._readableController),h(this,O(this._readableController)<=0);var l=this,c=S(t,"start",[l._transformStreamController]);a(c),o.catch(function(e){!1===l._errored&&(l._errored=!0,l._storedError=e)})}return y(e,[{key:"readable",get:function(){if(!1===v(this))throw g("readable");return this._readable}},{key:"writable",get:function(){if(!1===v(this))throw g("writable");return this._writable}}]),e}();e.exports={TransformStream:N}},function(e,t,r){e.exports=r(5)}]))},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}var i=!1;try{if("function"==typeof URL&&"object"===n(URL.prototype)&&"origin"in URL.prototype){var a=new URL("b","http://a");a.pathname="c%20d",i="http://a/c%20d"===a.href}}catch(e){}if(i)t.URL=URL;else{var o=r(146).URL,s=r(3).URL;s&&(o.createObjectURL=function(e){return s.createObjectURL.apply(s,arguments)},o.revokeObjectURL=function(e){s.revokeObjectURL(e)}),t.URL=o}},function(e,t,r){"use strict";!function(){function e(e){return void 0!==l[e]}function r(){s.call(this),this._isInvalid=!0}function n(e){return""===e&&r.call(this),e.toLowerCase()}function i(e){var t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,63,96].indexOf(t)?e:encodeURIComponent(e)}function a(e){var t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,96].indexOf(t)?e:encodeURIComponent(e)}function o(t,o,s){function u(e){b.push(e)}var p=o||"scheme start",v=0,m="",g=!1,y=!1,b=[];e:for(;(t[v-1]!==h||0===v)&&!this._isInvalid;){var _=t[v];switch(p){case"scheme start":if(!_||!d.test(_)){if(o){u("Invalid scheme.");break e}m="",p="no scheme";continue}m+=_.toLowerCase(),p="scheme";break;case"scheme":if(_&&f.test(_))m+=_.toLowerCase();else{if(":"!==_){if(o){if(_===h)break e;u("Code point not allowed in scheme: "+_);break e}m="",v=0,p="no scheme";continue}if(this._scheme=m,m="",o)break e;e(this._scheme)&&(this._isRelative=!0),p="file"===this._scheme?"relative":this._isRelative&&s&&s._scheme===this._scheme?"relative or authority":this._isRelative?"authority first slash":"scheme data"}break;case"scheme data":"?"===_?(this._query="?",p="query"):"#"===_?(this._fragment="#",p="fragment"):_!==h&&"\t"!==_&&"\n"!==_&&"\r"!==_&&(this._schemeData+=i(_));break;case"no scheme":if(s&&e(s._scheme)){p="relative";continue}u("Missing scheme."),r.call(this);break;case"relative or authority":if("/"!==_||"/"!==t[v+1]){u("Expected /, got: "+_),p="relative";continue}p="authority ignore slashes";break;case"relative":if(this._isRelative=!0,"file"!==this._scheme&&(this._scheme=s._scheme),_===h){this._host=s._host,this._port=s._port,this._path=s._path.slice(),this._query=s._query,this._username=s._username,this._password=s._password;break e}if("/"===_||"\\"===_)"\\"===_&&u("\\ is an invalid code point."),p="relative slash";else if("?"===_)this._host=s._host,this._port=s._port,this._path=s._path.slice(),this._query="?",this._username=s._username,this._password=s._password,p="query";else{if("#"!==_){var A=t[v+1],S=t[v+2];("file"!==this._scheme||!d.test(_)||":"!==A&&"|"!==A||S!==h&&"/"!==S&&"\\"!==S&&"?"!==S&&"#"!==S)&&(this._host=s._host,this._port=s._port,this._username=s._username,this._password=s._password,this._path=s._path.slice(),this._path.pop()),p="relative path";continue}this._host=s._host,this._port=s._port,this._path=s._path.slice(),this._query=s._query,this._fragment="#",this._username=s._username,this._password=s._password,p="fragment"}break;case"relative slash":if("/"!==_&&"\\"!==_){"file"!==this._scheme&&(this._host=s._host,this._port=s._port,this._username=s._username,this._password=s._password),p="relative path";continue}"\\"===_&&u("\\ is an invalid code point."),p="file"===this._scheme?"file host":"authority ignore slashes";break;case"authority first slash":if("/"!==_){u("Expected '/', got: "+_),p="authority ignore slashes";continue}p="authority second slash";break;case"authority second slash":if(p="authority ignore slashes","/"!==_){u("Expected '/', got: "+_);continue}break;case"authority ignore slashes":if("/"!==_&&"\\"!==_){p="authority";continue}u("Expected authority, got: "+_);break;case"authority":if("@"===_){g&&(u("@ already seen."),m+="%40"),g=!0;for(var w=0;w<m.length;w++){var k=m[w];if("\t"!==k&&"\n"!==k&&"\r"!==k)if(":"!==k||null!==this._password){var x=i(k);null!==this._password?this._password+=x:this._username+=x}else this._password="";else u("Invalid whitespace in authority.")}m=""}else{if(_===h||"/"===_||"\\"===_||"?"===_||"#"===_){v-=m.length,m="",p="host";continue}m+=_}break;case"file host":if(_===h||"/"===_||"\\"===_||"?"===_||"#"===_){2!==m.length||!d.test(m[0])||":"!==m[1]&&"|"!==m[1]?0===m.length?p="relative path start":(this._host=n.call(this,m),m="",p="relative path start"):p="relative path";continue}"\t"===_||"\n"===_||"\r"===_?u("Invalid whitespace in file host."):m+=_;break;case"host":case"hostname":if(":"!==_||y){if(_===h||"/"===_||"\\"===_||"?"===_||"#"===_){if(this._host=n.call(this,m),m="",p="relative path start",o)break e;continue}"\t"!==_&&"\n"!==_&&"\r"!==_?("["===_?y=!0:"]"===_&&(y=!1),m+=_):u("Invalid code point in host/hostname: "+_)}else if(this._host=n.call(this,m),m="",p="port","hostname"===o)break e;break;case"port":if(/[0-9]/.test(_))m+=_;else{if(_===h||"/"===_||"\\"===_||"?"===_||"#"===_||o){if(""!==m){var P=parseInt(m,10);P!==l[this._scheme]&&(this._port=P+""),m=""}if(o)break e;p="relative path start";continue}"\t"===_||"\n"===_||"\r"===_?u("Invalid code point in port: "+_):r.call(this)}break;case"relative path start":if("\\"===_&&u("'\\' not allowed in path."),p="relative path","/"!==_&&"\\"!==_)continue;break;case"relative path":if(_!==h&&"/"!==_&&"\\"!==_&&(o||"?"!==_&&"#"!==_))"\t"!==_&&"\n"!==_&&"\r"!==_&&(m+=i(_));else{"\\"===_&&u("\\ not allowed in relative path.");var C;(C=c[m.toLowerCase()])&&(m=C),".."===m?(this._path.pop(),"/"!==_&&"\\"!==_&&this._path.push("")):"."===m&&"/"!==_&&"\\"!==_?this._path.push(""):"."!==m&&("file"===this._scheme&&0===this._path.length&&2===m.length&&d.test(m[0])&&"|"===m[1]&&(m=m[0]+":"),this._path.push(m)),m="","?"===_?(this._query="?",p="query"):"#"===_&&(this._fragment="#",p="fragment")}break;case"query":o||"#"!==_?_!==h&&"\t"!==_&&"\n"!==_&&"\r"!==_&&(this._query+=a(_)):(this._fragment="#",p="fragment");break;case"fragment":_!==h&&"\t"!==_&&"\n"!==_&&"\r"!==_&&(this._fragment+=_)}v++}}function s(){this._scheme="",this._schemeData="",this._username="",this._password=null,this._host="",this._port="",this._path=[],this._query="",this._fragment="",this._isInvalid=!1,this._isRelative=!1}function u(e,t){void 0===t||t instanceof u||(t=new u(String(t))),this._url=e,s.call(this);var r=e.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,"");o.call(this,r,null,t)}var l=Object.create(null);l.ftp=21,l.file=0,l.gopher=70,l.http=80,l.https=443,l.ws=80,l.wss=443;var c=Object.create(null);c["%2e"]=".",c[".%2e"]="..",c["%2e."]="..",c["%2e%2e"]="..";var h,d=/[a-zA-Z]/,f=/[a-zA-Z0-9\+\-\.]/;u.prototype={toString:function(){return this.href},get href(){if(this._isInvalid)return this._url;var e="";return""===this._username&&null===this._password||(e=this._username+(null!==this._password?":"+this._password:"")+"@"),this.protocol+(this._isRelative?"//"+e+this.host:"")+this.pathname+this._query+this._fragment},set href(e){s.call(this),o.call(this,e)},get protocol(){return this._scheme+":"},set protocol(e){this._isInvalid||o.call(this,e+":","scheme start")},get host(){return this._isInvalid?"":this._port?this._host+":"+this._port:this._host},set host(e){!this._isInvalid&&this._isRelative&&o.call(this,e,"host")},get hostname(){return this._host},set hostname(e){!this._isInvalid&&this._isRelative&&o.call(this,e,"hostname")},get port(){return this._port},set port(e){!this._isInvalid&&this._isRelative&&o.call(this,e,"port")},get pathname(){return this._isInvalid?"":this._isRelative?"/"+this._path.join("/"):this._schemeData},set pathname(e){!this._isInvalid&&this._isRelative&&(this._path=[],o.call(this,e,"relative path start"))},get search(){return this._isInvalid||!this._query||"?"===this._query?"":this._query},set search(e){!this._isInvalid&&this._isRelative&&(this._query="?","?"===e[0]&&(e=e.slice(1)),o.call(this,e,"query"))},get hash(){return this._isInvalid||!this._fragment||"#"===this._fragment?"":this._fragment},set hash(e){this._isInvalid||(this._fragment="#","#"===e[0]&&(e=e.slice(1)),o.call(this,e,"fragment"))},get origin(){var e;if(this._isInvalid||!this._scheme)return"";switch(this._scheme){case"data":case"file":case"javascript":case"mailto":return"null";case"blob":try{return new u(this._schemeData).origin||"null"}catch(e){}return"null"}return e=this.host,e?this._scheme+"://"+e:""}},t.URL=u}()},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function i(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function a(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){function o(e){i(u,n,a,o,s,"next",e)}function s(e){i(u,n,a,o,s,"throw",e)}var u=e.apply(t,r);o(void 0)})}}function o(e,t){return l(e)||u(e,t)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}function u(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}function l(e){if(Array.isArray(e))return e}function c(e){return f(e)||d(e)||h()}function h(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function d(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}function f(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function v(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function m(e,t,r){return t&&v(e.prototype,t),r&&v(e,r),e}function g(e){return(g="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}function y(e){W=e}function b(e){var t,r=new U;if("string"==typeof e)t={url:e};else if((0,w.isArrayBuffer)(e))t={data:e};else if(e instanceof B)t={range:e};else{if("object"!==g(e))throw new Error("Invalid parameter in getDocument, need either Uint8Array, string or a parameter object");if(!e.url&&!e.data&&!e.range)throw new Error("Invalid parameter object: need either .data, .range or .url");t=e}var n=Object.create(null),i=null,a=null;for(var o in t)if("url"!==o||"undefined"==typeof window)if("range"!==o)if("worker"!==o)if("data"!==o||t[o]instanceof Uint8Array)n[o]=t[o];else{var s=t[o];if("string"==typeof s)n[o]=(0,w.stringToBytes)(s);else if("object"!==g(s)||null===s||isNaN(s.length)){if(!(0,w.isArrayBuffer)(s))throw new Error("Invalid PDF binary data: either typed array, string or array-like object is expected in the data property.");n[o]=new Uint8Array(s)}else n[o]=new Uint8Array(s)}else a=t[o];else i=t[o];else n[o]=new w.URL(t[o],window.location).href;n.rangeChunkSize=n.rangeChunkSize||L,n.CMapReaderFactory=n.CMapReaderFactory||k.DOMCMapReaderFactory,n.ignoreErrors=!0!==n.stopAtErrors,n.pdfBug=!0===n.pdfBug;var u=Object.values(w.NativeImageDecoding);if(void 0!==n.nativeImageDecoderSupport&&u.includes(n.nativeImageDecoderSupport)||(n.nativeImageDecoderSupport=P.apiCompatibilityParams.nativeImageDecoderSupport||w.NativeImageDecoding.DECODE),Number.isInteger(n.maxImageSize)||(n.maxImageSize=-1),"boolean"!=typeof n.isEvalSupported&&(n.isEvalSupported=!0),"boolean"!=typeof n.disableFontFace&&(n.disableFontFace=P.apiCompatibilityParams.disableFontFace||!1),"boolean"!=typeof n.disableRange&&(n.disableRange=!1),"boolean"!=typeof n.disableStream&&(n.disableStream=!1),"boolean"!=typeof n.disableAutoFetch&&(n.disableAutoFetch=!1),"boolean"!=typeof n.disableCreateObjectURL&&(n.disableCreateObjectURL=P.apiCompatibilityParams.disableCreateObjectURL||!1),(0,w.setVerbosityLevel)(n.verbosity),!a){var l={postMessageTransfers:n.postMessageTransfers,verbosity:n.verbosity,port:T.GlobalWorkerOptions.workerPort};a=l.port?X.fromPort(l):new X(l),r._worker=a}var c=r.docId;return a.promise.then(function(){if(r.destroyed)throw new Error("Loading aborted");return _(a,n,i,c).then(function(e){if(r.destroyed)throw new Error("Loading aborted");var t;i?t=new F.PDFDataTransportStream({length:n.length,initialData:n.initialData,progressiveDone:n.progressiveDone,disableRange:n.disableRange,disableStream:n.disableStream},i):n.data||(t=W({url:n.url,length:n.length,httpHeaders:n.httpHeaders,withCredentials:n.withCredentials,rangeChunkSize:n.rangeChunkSize,disableRange:n.disableRange,disableStream:n.disableStream}));var o=new E.MessageHandler(c,e,a.port);o.postMessageTransfers=a.postMessageTransfers;var s=new Y(o,r,t,n);r._transport=s,o.send("Ready",null)})}).catch(r._capability.reject),r}function _(e,t,r,n){return e.destroyed?Promise.reject(new Error("Worker was destroyed")):(r&&(t.length=r.length,t.initialData=r.initialData,t.progressiveDone=r.progressiveDone),e.messageHandler.sendWithPromise("GetDocRequest",{docId:n,apiVersion:"2.2.228",source:{data:t.data,url:t.url,password:t.password,disableAutoFetch:t.disableAutoFetch,rangeChunkSize:t.rangeChunkSize,length:t.length},maxImageSize:t.maxImageSize,disableFontFace:t.disableFontFace,disableCreateObjectURL:t.disableCreateObjectURL,postMessageTransfers:e.postMessageTransfers,docBaseUrl:t.docBaseUrl,nativeImageDecoderSupport:t.nativeImageDecoderSupport,ignoreErrors:t.ignoreErrors,isEvalSupported:t.isEvalSupported}).then(function(t){if(e.destroyed)throw new Error("Worker was destroyed");return t}))}Object.defineProperty(t,"__esModule",{value:!0}),t.getDocument=b,t.setPDFNetworkStreamFactory=y,t.build=t.version=t.PDFPageProxy=t.PDFDocumentProxy=t.PDFWorker=t.PDFDataRangeTransport=t.LoopbackPort=void 0;var A,S=n(r(148)),w=r(1),k=r(151),x=r(152),P=r(153),C=r(154),R=n(r(3)),T=r(156),E=r(157),O=r(158),F=r(160),I=r(161),L=65536,j=!1,M=null,D=!1;"undefined"==typeof window?(j=!0,void 0===require.ensure&&(require.ensure=require("node-ensure")),D=!0):"undefined"!=typeof require&&"function"==typeof require.ensure&&(D=!0),"undefined"!=typeof requirejs&&requirejs.toUrl&&(A=requirejs.toUrl("pdfjs-dist/build/pdf.worker.js"));var N="undefined"!=typeof requirejs&&requirejs.load;if(M=D?function(){return new Promise(function(e,t){require.ensure([],function(){try{var r;r=require("./pdf.worker.js"),e(r.WorkerMessageHandler)}catch(e){t(e)}},t,"pdfjsWorker")})}:N?function(){return new Promise(function(e,t){requirejs(["pdfjs-dist/build/pdf.worker"],function(r){try{e(r.WorkerMessageHandler)}catch(e){t(e)}},t)})}:null,!A&&"object"===("undefined"==typeof document?"undefined":g(document))&&"currentScript"in document){var q=document.currentScript&&document.currentScript.src;q&&(A=q.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}var W,U=function(){var e=0;return function(){function t(){p(this,t),this._capability=(0,w.createPromiseCapability)(),this._transport=null,this._worker=null,this.docId="d"+e++,this.destroyed=!1,this.onPassword=null,this.onProgress=null,this.onUnsupportedFeature=null}return m(t,[{key:"destroy",value:function(){var e=this;return this.destroyed=!0,(this._transport?this._transport.destroy():Promise.resolve()).then(function(){e._transport=null,e._worker&&(e._worker.destroy(),e._worker=null)})}},{key:"then",value:function(e,t){return(0,k.deprecated)("PDFDocumentLoadingTask.then method, use the `promise` getter instead."),this.promise.then.apply(this.promise,arguments)}},{key:"promise",get:function(){return this._capability.promise}}]),t}()}(),B=function(){function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];p(this,e),this.length=t,this.initialData=r,this.progressiveDone=n,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=(0,w.createPromiseCapability)()}return m(e,[{key:"addRangeListener",value:function(e){this._rangeListeners.push(e)}},{key:"addProgressListener",value:function(e){this._progressListeners.push(e)}},{key:"addProgressiveReadListener",value:function(e){this._progressiveReadListeners.push(e)}},{key:"addProgressiveDoneListener",value:function(e){this._progressiveDoneListeners.push(e)}},{key:"onDataRange",value:function(e,t){var r=!0,n=!1,i=void 0;try{for(var a,o=this._rangeListeners[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){(0,a.value)(e,t)}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}}},{key:"onDataProgress",value:function(e,t){var r=this;this._readyCapability.promise.then(function(){var n=!0,i=!1,a=void 0;try{for(var o,s=r._progressListeners[Symbol.iterator]();!(n=(o=s.next()).done);n=!0){(0,o.value)(e,t)}}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}})}},{key:"onDataProgressiveRead",value:function(e){var t=this;this._readyCapability.promise.then(function(){var r=!0,n=!1,i=void 0;try{for(var a,o=t._progressiveReadListeners[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){(0,a.value)(e)}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}})}},{key:"onDataProgressiveDone",value:function(){var e=this;this._readyCapability.promise.then(function(){var t=!0,r=!1,n=void 0;try{for(var i,a=e._progressiveDoneListeners[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){(0,i.value)()}}catch(e){r=!0,n=e}finally{try{t||null==a.return||a.return()}finally{if(r)throw n}}})}},{key:"transportReady",value:function(){this._readyCapability.resolve()}},{key:"requestDataRange",value:function(e,t){(0,w.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}},{key:"abort",value:function(){}}]),e}();t.PDFDataRangeTransport=B;var z=function(){function e(t,r){p(this,e),this._pdfInfo=t,this._transport=r}return m(e,[{key:"getPage",value:function(e){return this._transport.getPage(e)}},{key:"getPageIndex",value:function(e){return this._transport.getPageIndex(e)}},{key:"getDestinations",value:function(){return this._transport.getDestinations()}},{key:"getDestination",value:function(e){return this._transport.getDestination(e)}},{key:"getPageLabels",value:function(){return this._transport.getPageLabels()}},{key:"getPageLayout",value:function(){return this._transport.getPageLayout()}},{key:"getPageMode",value:function(){return this._transport.getPageMode()}},{key:"getViewerPreferences",value:function(){return this._transport.getViewerPreferences()}},{key:"getOpenActionDestination",value:function(){return this._transport.getOpenActionDestination()}},{key:"getAttachments",value:function(){return this._transport.getAttachments()}},{key:"getJavaScript",value:function(){return this._transport.getJavaScript()}},{key:"getOutline",value:function(){return this._transport.getOutline()}
},{key:"getPermissions",value:function(){return this._transport.getPermissions()}},{key:"getMetadata",value:function(){return this._transport.getMetadata()}},{key:"getData",value:function(){return this._transport.getData()}},{key:"getDownloadInfo",value:function(){return this._transport.downloadInfoCapability.promise}},{key:"getStats",value:function(){return this._transport.getStats()}},{key:"cleanup",value:function(){this._transport.startCleanup()}},{key:"destroy",value:function(){return this.loadingTask.destroy()}},{key:"numPages",get:function(){return this._pdfInfo.numPages}},{key:"fingerprint",get:function(){return this._pdfInfo.fingerprint}},{key:"loadingParams",get:function(){return this._transport.loadingParams}},{key:"loadingTask",get:function(){return this._transport.loadingTask}}]),e}();t.PDFDocumentProxy=z;var G=function(){function e(t,r,n){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];p(this,e),this.pageIndex=t,this._pageInfo=r,this._transport=n,this._stats=i?new k.StatTimer:k.DummyStatTimer,this._pdfBug=i,this.commonObjs=n.commonObjs,this.objs=new V,this.cleanupAfterRender=!1,this.pendingCleanup=!1,this.intentStates=Object.create(null),this.destroyed=!1}return m(e,[{key:"getViewport",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.scale,r=e.rotation,n=void 0===r?this.rotate:r,i=e.dontFlip,a=void 0!==i&&i;return(arguments.length>1||"number"==typeof arguments[0])&&((0,k.deprecated)("getViewport is called with obsolete arguments."),t=arguments[0],n="number"==typeof arguments[1]?arguments[1]:this.rotate,a="boolean"==typeof arguments[2]&&arguments[2]),new k.PageViewport({viewBox:this.view,scale:t,rotation:n,dontFlip:a})}},{key:"getAnnotations",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.intent,r=void 0===t?null:t;return this.annotationsPromise&&this.annotationsIntent===r||(this.annotationsPromise=this._transport.getAnnotations(this.pageIndex,r),this.annotationsIntent=r),this.annotationsPromise}},{key:"render",value:function(e){var t=this,r=e.canvasContext,n=e.viewport,i=e.intent,a=void 0===i?"display":i,o=e.enableWebGL,s=void 0!==o&&o,u=e.renderInteractiveForms,l=void 0!==u&&u,c=e.transform,h=void 0===c?null:c,d=e.imageLayer,f=void 0===d?null:d,p=e.canvasFactory,v=void 0===p?null:p,m=e.background,g=void 0===m?null:m,y=this._stats;y.time("Overall"),this.pendingCleanup=!1;var b="print"===a?"print":"display",_=v||new k.DOMCanvasFactory,A=new I.WebGLContext({enable:s});this.intentStates[b]||(this.intentStates[b]=Object.create(null));var S=this.intentStates[b];S.displayReadyCapability||(S.receivingOperatorList=!0,S.displayReadyCapability=(0,w.createPromiseCapability)(),S.operatorList={fnArray:[],argsArray:[],lastChunk:!1},y.time("Page Request"),this._transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageNumber-1,intent:b,renderInteractiveForms:!0===l}));var x=function(e){var r=S.renderTasks.indexOf(P);r>=0&&S.renderTasks.splice(r,1),(t.cleanupAfterRender||"print"===b)&&(t.pendingCleanup=!0),t._tryCleanup(),e?P.capability.reject(e):P.capability.resolve(),y.timeEnd("Rendering"),y.timeEnd("Overall")},P=new K({callback:x,params:{canvasContext:r,viewport:n,transform:h,imageLayer:f,background:g},objs:this.objs,commonObjs:this.commonObjs,operatorList:S.operatorList,pageNumber:this.pageNumber,canvasFactory:_,webGLContext:A,useRequestAnimationFrame:"print"!==b,pdfBug:this._pdfBug});S.renderTasks||(S.renderTasks=[]),S.renderTasks.push(P);var C=P.task;return S.displayReadyCapability.promise.then(function(e){if(t.pendingCleanup)return void x();y.time("Rendering"),P.initializeGraphics(e),P.operatorListChanged()}).catch(x),C}},{key:"getOperatorList",value:function(){function e(){if(r.operatorList.lastChunk){r.opListReadCapability.resolve(r.operatorList);var e=r.renderTasks.indexOf(t);e>=0&&r.renderTasks.splice(e,1)}}this.intentStates.oplist||(this.intentStates.oplist=Object.create(null));var t,r=this.intentStates.oplist;return r.opListReadCapability||(t={},t.operatorListChanged=e,r.receivingOperatorList=!0,r.opListReadCapability=(0,w.createPromiseCapability)(),r.renderTasks=[],r.renderTasks.push(t),r.operatorList={fnArray:[],argsArray:[],lastChunk:!1},this._stats.time("Page Request"),this._transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageIndex,intent:"oplist"})),r.opListReadCapability.promise}},{key:"streamTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.normalizeWhitespace,r=void 0!==t&&t,n=e.disableCombineTextItems,i=void 0!==n&&n;return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this.pageNumber-1,normalizeWhitespace:!0===r,combineTextItems:!0!==i},{highWaterMark:100,size:function(e){return e.items.length}})}},{key:"getTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.streamTextContent(e);return new Promise(function(e,r){function n(){i.read().then(function(t){var r,i=t.value;if(t.done)return void e(a);Object.assign(a.styles,i.styles),(r=a.items).push.apply(r,c(i.items)),n()},r)}var i=t.getReader(),a={items:[],styles:Object.create(null)};n()})}},{key:"_destroy",value:function(){this.destroyed=!0,this._transport.pageCache[this.pageIndex]=null;var e=[];return Object.keys(this.intentStates).forEach(function(t){if("oplist"!==t){this.intentStates[t].renderTasks.forEach(function(t){var r=t.capability.promise.catch(function(){});e.push(r),t.cancel()})}},this),this.objs.clear(),this.annotationsPromise=null,this.pendingCleanup=!1,Promise.all(e)}},{key:"cleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.pendingCleanup=!0,this._tryCleanup(e)}},{key:"_tryCleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.pendingCleanup&&!Object.keys(this.intentStates).some(function(e){var t=this.intentStates[e];return 0!==t.renderTasks.length||t.receivingOperatorList},this)&&(Object.keys(this.intentStates).forEach(function(e){delete this.intentStates[e]},this),this.objs.clear(),this.annotationsPromise=null,e&&this._stats instanceof k.StatTimer&&(this._stats=new k.StatTimer),this.pendingCleanup=!1)}},{key:"_startRenderPage",value:function(e,t){var r=this.intentStates[t];r.displayReadyCapability&&r.displayReadyCapability.resolve(e)}},{key:"_renderPageChunk",value:function(e,t){for(var r=this.intentStates[t],n=0,i=e.length;n<i;n++)r.operatorList.fnArray.push(e.fnArray[n]),r.operatorList.argsArray.push(e.argsArray[n]);r.operatorList.lastChunk=e.lastChunk;for(var a=0;a<r.renderTasks.length;a++)r.renderTasks[a].operatorListChanged();e.lastChunk&&(r.receivingOperatorList=!1,this._tryCleanup())}},{key:"pageNumber",get:function(){return this.pageIndex+1}},{key:"rotate",get:function(){return this._pageInfo.rotate}},{key:"ref",get:function(){return this._pageInfo.ref}},{key:"userUnit",get:function(){return this._pageInfo.userUnit}},{key:"view",get:function(){return this._pageInfo.view}},{key:"stats",get:function(){return this._stats instanceof k.StatTimer?this._stats:null}}]),e}();t.PDFPageProxy=G;var H=function(){function e(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];p(this,e),this._listeners=[],this._defer=t,this._deferred=Promise.resolve(void 0)}return m(e,[{key:"postMessage",value:function(e,t){function r(e){if("object"!==g(e)||null===e)return e;if(i.has(e))return i.get(e);var n,a;if((n=e.buffer)&&(0,w.isArrayBuffer)(n)){var o=t&&t.includes(n);return a=e===n?e:o?new e.constructor(n,e.byteOffset,e.byteLength):new e.constructor(e),i.set(e,a),a}a=Array.isArray(e)?[]:{},i.set(e,a);for(var s in e){for(var u=void 0,l=e;!(u=Object.getOwnPropertyDescriptor(l,s));)l=Object.getPrototypeOf(l);void 0!==u.value&&"function"!=typeof u.value&&(a[s]=r(u.value))}return a}var n=this;if(!this._defer)return void this._listeners.forEach(function(t){t.call(this,{data:e})},this);var i=new WeakMap,a={data:r(e)};this._deferred.then(function(){n._listeners.forEach(function(e){e.call(this,a)},n)})}},{key:"addEventListener",value:function(e,t){this._listeners.push(t)}},{key:"removeEventListener",value:function(e,t){var r=this._listeners.indexOf(t);this._listeners.splice(r,1)}},{key:"terminate",value:function(){this._listeners.length=0}}]),e}();t.LoopbackPort=H;var X=function(){function e(){if(T.GlobalWorkerOptions.workerSrc)return T.GlobalWorkerOptions.workerSrc;if(void 0!==A)return A;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}function t(){try{if("undefined"!=typeof window)return window.pdfjsWorker&&window.pdfjsWorker.WorkerMessageHandler}catch(e){}return null}function r(){if(i)return i.promise;i=(0,w.createPromiseCapability)();var r=t();return r?(i.resolve(r),i.promise):((M||function(){return(0,k.loadScript)(e()).then(function(){return window.pdfjsWorker.WorkerMessageHandler})})().then(i.resolve,i.reject),i.promise)}function n(e){var t="importScripts('"+e+"');";return w.URL.createObjectURL(new Blob([t]))}var i,a=new WeakMap,o=0;return function(){function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,r=void 0===t?null:t,n=e.port,o=void 0===n?null:n,s=e.postMessageTransfers,u=void 0===s||s,l=e.verbosity,c=void 0===l?(0,w.getVerbosityLevel)():l;if(p(this,i),o&&a.has(o))throw new Error("Cannot use more than one PDFWorker per port");if(this.name=r,this.destroyed=!1,this.postMessageTransfers=!1!==u,this.verbosity=c,this._readyCapability=(0,w.createPromiseCapability)(),this._port=null,this._webWorker=null,this._messageHandler=null,o)return a.set(o,this),void this._initializeFromPort(o);this._initialize()}return m(i,[{key:"_initializeFromPort",value:function(e){this._port=e,this._messageHandler=new E.MessageHandler("main","worker",e),this._messageHandler.on("ready",function(){}),this._readyCapability.resolve()}},{key:"_initialize",value:function(){var r=this;if("undefined"!=typeof Worker&&!j&&!t()){var i=e();try{(0,w.isSameOrigin)(window.location.href,i)||(i=n(new w.URL(i,window.location).href));var a=new Worker(i),o=new E.MessageHandler("main","worker",a),s=function(){a.removeEventListener("error",u),o.destroy(),a.terminate(),r.destroyed?r._readyCapability.reject(new Error("Worker was destroyed")):r._setupFakeWorker()},u=function(){r._webWorker||s()};a.addEventListener("error",u),o.on("test",function(e){if(a.removeEventListener("error",u),r.destroyed)return void s();e&&e.supportTypedArray?(r._messageHandler=o,r._port=a,r._webWorker=a,e.supportTransfers||(r.postMessageTransfers=!1),r._readyCapability.resolve(),o.send("configure",{verbosity:r.verbosity})):(r._setupFakeWorker(),o.destroy(),a.terminate())}),o.on("ready",function(e){if(a.removeEventListener("error",u),r.destroyed)return void s();try{l()}catch(e){r._setupFakeWorker()}});var l=function(){var e=new Uint8Array([r.postMessageTransfers?255:0]);try{o.send("test",e,[e.buffer])}catch(t){(0,w.info)("Cannot use postMessage transfers"),e[0]=0,o.send("test",e)}};return void l()}catch(e){(0,w.info)("The worker has been disabled.")}}this._setupFakeWorker()}},{key:"_setupFakeWorker",value:function(){var e=this;j||((0,w.warn)("Setting up fake worker."),j=!0),r().then(function(t){if(e.destroyed)return void e._readyCapability.reject(new Error("Worker was destroyed"));var r=new H;e._port=r;var n="fake"+o++,i=new E.MessageHandler(n+"_worker",n,r);t.setup(i,r);var a=new E.MessageHandler(n,n+"_worker",r);e._messageHandler=a,e._readyCapability.resolve()}).catch(function(t){e._readyCapability.reject(new Error('Setting up fake worker failed: "'.concat(t.message,'".')))})}},{key:"destroy",value:function(){this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),a.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}},{key:"promise",get:function(){return this._readyCapability.promise}},{key:"port",get:function(){return this._port}},{key:"messageHandler",get:function(){return this._messageHandler}}],[{key:"fromPort",value:function(e){if(!e||!e.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return a.has(e.port)?a.get(e.port):new i(e)}},{key:"getWorkerSrc",value:function(){return e()}}]),i}()}();t.PDFWorker=X;var Y=function(){function e(t,r,n,i){p(this,e),this.messageHandler=t,this.loadingTask=r,this.commonObjs=new V,this.fontLoader=new x.FontLoader({docId:r.docId,onUnsupportedFeature:this._onUnsupportedFeature.bind(this)}),this._params=i,this.CMapReaderFactory=new i.CMapReaderFactory({baseUrl:i.cMapUrl,isCompressed:i.cMapPacked}),this.destroyed=!1,this.destroyCapability=null,this._passwordCapability=null,this._networkStream=n,this._fullReader=null,this._lastProgress=null,this.pageCache=[],this.pagePromises=[],this.downloadInfoCapability=(0,w.createPromiseCapability)(),this.setupMessageHandler()}return m(e,[{key:"destroy",value:function(){var e=this;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=(0,w.createPromiseCapability)(),this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"));var t=[];this.pageCache.forEach(function(e){e&&t.push(e._destroy())}),this.pageCache.length=0,this.pagePromises.length=0;var r=this.messageHandler.sendWithPromise("Terminate",null);return t.push(r),Promise.all(t).then(function(){e.fontLoader.clear(),e._networkStream&&e._networkStream.cancelAllRequests(),e.messageHandler&&(e.messageHandler.destroy(),e.messageHandler=null),e.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}},{key:"setupMessageHandler",value:function(){var e=this.messageHandler,t=this.loadingTask;e.on("GetReader",function(e,t){var r=this;(0,w.assert)(this._networkStream),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=function(e){r._lastProgress={loaded:e.loaded,total:e.total}},t.onPull=function(){r._fullReader.read().then(function(e){var r=e.value;if(e.done)return void t.close();(0,w.assert)((0,w.isArrayBuffer)(r)),t.enqueue(new Uint8Array(r),1,[r])}).catch(function(e){t.error(e)})},t.onCancel=function(e){r._fullReader.cancel(e)}},this),e.on("ReaderHeadersReady",function(e){var r=this,n=(0,w.createPromiseCapability)(),i=this._fullReader;return i.headersReady.then(function(){i.isStreamingSupported&&i.isRangeSupported||(r._lastProgress&&t.onProgress&&t.onProgress(r._lastProgress),i.onProgress=function(e){t.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})}),n.resolve({isStreamingSupported:i.isStreamingSupported,isRangeSupported:i.isRangeSupported,contentLength:i.contentLength})},n.reject),n.promise},this),e.on("GetRangeReader",function(e,t){(0,w.assert)(this._networkStream);var r=this._networkStream.getRangeReader(e.begin,e.end);if(!r)return void t.close();t.onPull=function(){r.read().then(function(e){var r=e.value;if(e.done)return void t.close();(0,w.assert)((0,w.isArrayBuffer)(r)),t.enqueue(new Uint8Array(r),1,[r])}).catch(function(e){t.error(e)})},t.onCancel=function(e){r.cancel(e)}},this),e.on("GetDoc",function(e){var r=e.pdfInfo;this._numPages=r.numPages,t._capability.resolve(new z(r,this))},this),e.on("PasswordRequest",function(e){var r=this;if(this._passwordCapability=(0,w.createPromiseCapability)(),t.onPassword){var n=function(e){r._passwordCapability.resolve({password:e})};try{t.onPassword(n,e.code)}catch(e){this._passwordCapability.reject(e)}}else this._passwordCapability.reject(new w.PasswordException(e.message,e.code));return this._passwordCapability.promise},this),e.on("PasswordException",function(e){t._capability.reject(new w.PasswordException(e.message,e.code))},this),e.on("InvalidPDF",function(e){t._capability.reject(new w.InvalidPDFException(e.message))},this),e.on("MissingPDF",function(e){t._capability.reject(new w.MissingPDFException(e.message))},this),e.on("UnexpectedResponse",function(e){t._capability.reject(new w.UnexpectedResponseException(e.message,e.status))},this),e.on("UnknownError",function(e){t._capability.reject(new w.UnknownErrorException(e.message,e.details))},this),e.on("DataLoaded",function(e){t.onProgress&&t.onProgress({loaded:e.length,total:e.length}),this.downloadInfoCapability.resolve(e)},this),e.on("StartRenderPage",function(e){if(!this.destroyed){var t=this.pageCache[e.pageIndex];t._stats.timeEnd("Page Request"),t._startRenderPage(e.transparency,e.intent)}},this),e.on("RenderPageChunk",function(e){if(!this.destroyed){this.pageCache[e.pageIndex]._renderPageChunk(e.operatorList,e.intent)}},this),e.on("commonobj",function(t){var r=this;if(!this.destroyed){var n=o(t,3),i=n[0],a=n[1],s=n[2];if(!this.commonObjs.has(i))switch(a){case"Font":var u=this._params;if("error"in s){var l=s.error;(0,w.warn)("Error during font loading: ".concat(l)),this.commonObjs.resolve(i,l);break}var c=null;u.pdfBug&&R.default.FontInspector&&R.default.FontInspector.enabled&&(c={registerFont:function(e,t){R.default.FontInspector.fontAdded(e,t)}});var h=new x.FontFaceObject(s,{isEvalSupported:u.isEvalSupported,disableFontFace:u.disableFontFace,ignoreErrors:u.ignoreErrors,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),fontRegistry:c});this.fontLoader.bind(h).then(function(){r.commonObjs.resolve(i,h)},function(t){e.sendWithPromise("FontFallback",{id:i}).finally(function(){r.commonObjs.resolve(i,h)})});break;case"FontPath":case"FontType3Res":this.commonObjs.resolve(i,s);break;default:throw new Error("Got unknown common object type ".concat(a))}}},this),e.on("obj",function(e){if(!this.destroyed){var t=o(e,4),r=t[0],n=t[1],i=t[2],a=t[3],s=this.pageCache[n];if(!s.objs.has(r))switch(i){case"JpegStream":return new Promise(function(e,t){var r=new Image;r.onload=function(){e(r)},r.onerror=function(){t(new Error("Error during JPEG image loading")),(0,k.releaseImageResources)(r)},r.src=a}).then(function(e){s.objs.resolve(r,e)});case"Image":s.objs.resolve(r,a);a&&"data"in a&&a.data.length>8e6&&(s.cleanupAfterRender=!0);break;default:throw new Error("Got unknown object type ".concat(i))}}},this),e.on("DocProgress",function(e){this.destroyed||t.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})},this),e.on("PageError",function(e){if(!this.destroyed){var t=this.pageCache[e.pageIndex],r=t.intentStates[e.intent];if(!r.displayReadyCapability)throw new Error(e.error);if(r.displayReadyCapability.reject(new Error(e.error)),r.operatorList){r.operatorList.lastChunk=!0;for(var n=0;n<r.renderTasks.length;n++)r.renderTasks[n].operatorListChanged()}}},this),e.on("UnsupportedFeature",this._onUnsupportedFeature,this),e.on("JpegDecode",function(e){if(this.destroyed)return Promise.reject(new Error("Worker was destroyed"));if("undefined"==typeof document)return Promise.reject(new Error('"document" is not defined.'));var t=o(e,2),r=t[0],n=t[1];return 3!==n&&1!==n?Promise.reject(new Error("Only 3 components or 1 component can be returned")):new Promise(function(e,t){var i=new Image;i.onload=function(){var t=i.width,r=i.height,a=t*r,o=4*a,s=new Uint8ClampedArray(a*n),u=document.createElement("canvas");u.width=t,u.height=r;var l=u.getContext("2d");l.drawImage(i,0,0);var c=l.getImageData(0,0,t,r).data;if(3===n)for(var h=0,d=0;h<o;h+=4,d+=3)s[d]=c[h],s[d+1]=c[h+1],s[d+2]=c[h+2];else if(1===n)for(var f=0,p=0;f<o;f+=4,p++)s[p]=c[f];e({data:s,width:t,height:r}),(0,k.releaseImageResources)(i),u.width=0,u.height=0,u=null,l=null},i.onerror=function(){t(new Error("JpegDecode failed to load image")),(0,k.releaseImageResources)(i)},i.src=r})},this),e.on("FetchBuiltInCMap",function(e){return this.destroyed?Promise.reject(new Error("Worker was destroyed")):this.CMapReaderFactory.fetch({name:e.name})},this)}},{key:"_onUnsupportedFeature",value:function(e){var t=e.featureId;this.destroyed||this.loadingTask.onUnsupportedFeature&&this.loadingTask.onUnsupportedFeature(t)}},{key:"getData",value:function(){return this.messageHandler.sendWithPromise("GetData",null)}},{key:"getPage",value:function(e){var t=this;if(!Number.isInteger(e)||e<=0||e>this._numPages)return Promise.reject(new Error("Invalid page request"));var r=e-1;if(r in this.pagePromises)return this.pagePromises[r];var n=this.messageHandler.sendWithPromise("GetPage",{pageIndex:r}).then(function(e){if(t.destroyed)throw new Error("Transport destroyed");var n=new G(r,e,t,t._params.pdfBug);return t.pageCache[r]=n,n});return this.pagePromises[r]=n,n}},{key:"getPageIndex",value:function(e){return this.messageHandler.sendWithPromise("GetPageIndex",{ref:e}).catch(function(e){return Promise.reject(new Error(e))})}},{key:"getAnnotations",value:function(e,t){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:e,intent:t})}},{key:"getDestinations",value:function(){return this.messageHandler.sendWithPromise("GetDestinations",null)}},{key:"getDestination",value:function(e){return"string"!=typeof e?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:e})}},{key:"getPageLabels",value:function(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}},{key:"getPageLayout",value:function(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}},{key:"getPageMode",value:function(){return this.messageHandler.sendWithPromise("GetPageMode",null)}},{key:"getViewerPreferences",value:function(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}},{key:"getOpenActionDestination",value:function(){return this.messageHandler.sendWithPromise("GetOpenActionDestination",null)}},{key:"getAttachments",value:function(){return this.messageHandler.sendWithPromise("GetAttachments",null)}},{key:"getJavaScript",value:function(){return this.messageHandler.sendWithPromise("GetJavaScript",null)}},{key:"getOutline",value:function(){return this.messageHandler.sendWithPromise("GetOutline",null)}},{key:"getPermissions",value:function(){return this.messageHandler.sendWithPromise("GetPermissions",null)}},{key:"getMetadata",value:function(){var e=this;return this.messageHandler.sendWithPromise("GetMetadata",null).then(function(t){return{info:t[0],metadata:t[1]?new O.Metadata(t[1]):null,contentDispositionFilename:e._fullReader?e._fullReader.filename:null}})}},{key:"getStats",value:function(){return this.messageHandler.sendWithPromise("GetStats",null)}},{key:"startCleanup",value:function(){var e=this;this.messageHandler.sendWithPromise("Cleanup",null).then(function(){for(var t=0,r=e.pageCache.length;t<r;t++){var n=e.pageCache[t];n&&n.cleanup()}e.commonObjs.clear(),e.fontLoader.clear()})}},{key:"loadingParams",get:function(){var e=this._params;return(0,w.shadow)(this,"loadingParams",{disableAutoFetch:e.disableAutoFetch,disableCreateObjectURL:e.disableCreateObjectURL,disableFontFace:e.disableFontFace,nativeImageDecoderSupport:e.nativeImageDecoderSupport})}}]),e}(),V=function(){function e(){p(this,e),this._objs=Object.create(null)}return m(e,[{key:"_ensureObj",value:function(e){return this._objs[e]?this._objs[e]:this._objs[e]={capability:(0,w.createPromiseCapability)(),data:null,resolved:!1}}},{key:"get",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(t)return this._ensureObj(e).capability.promise.then(t),null;var r=this._objs[e];if(!r||!r.resolved)throw new Error("Requesting object that isn't resolved yet ".concat(e,"."));return r.data}},{key:"has",value:function(e){var t=this._objs[e];return!!t&&t.resolved}},{key:"resolve",value:function(e,t){var r=this._ensureObj(e);r.resolved=!0,r.data=t,r.capability.resolve(t)}},{key:"clear",value:function(){for(var e in this._objs){var t=this._objs[e].data;"undefined"!=typeof Image&&t instanceof Image&&(0,k.releaseImageResources)(t)}this._objs=Object.create(null)}}]),e}(),Q=function(){function e(t){p(this,e),this._internalRenderTask=t,this.onContinue=null}return m(e,[{key:"cancel",value:function(){this._internalRenderTask.cancel()}},{key:"then",value:function(e,t){return(0,k.deprecated)("RenderTask.then method, use the `promise` getter instead."),this.promise.then.apply(this.promise,arguments)}},{key:"promise",get:function(){return this._internalRenderTask.capability.promise}}]),e}(),K=function(){var e=new WeakSet;return function(){function t(e){var r=e.callback,n=e.params,i=e.objs,a=e.commonObjs,o=e.operatorList,s=e.pageNumber,u=e.canvasFactory,l=e.webGLContext,c=e.useRequestAnimationFrame,h=void 0!==c&&c,d=e.pdfBug,f=void 0!==d&&d;p(this,t),this.callback=r,this.params=n,this.objs=i,this.commonObjs=a,this.operatorListIdx=null,this.operatorList=o,this.pageNumber=s,this.canvasFactory=u,this.webGLContext=l,this._pdfBug=f,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window,this.cancelled=!1,this.capability=(0,w.createPromiseCapability)(),this.task=new Q(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=n.canvasContext.canvas}return m(t,[{key:"initializeGraphics",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.cancelled){if(this._canvas){if(e.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");e.add(this._canvas)}this._pdfBug&&R.default.StepperManager&&R.default.StepperManager.enabled&&(this.stepper=R.default.StepperManager.create(this.pageNumber-1),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());var r=this.params,n=r.canvasContext,i=r.viewport,a=r.transform,o=r.imageLayer,s=r.background;this.gfx=new C.CanvasGraphics(n,this.commonObjs,this.objs,this.canvasFactory,this.webGLContext,o),this.gfx.beginDrawing({transform:a,viewport:i,transparency:t,background:s}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback&&this.graphicsReadyCallback()}}},{key:"cancel",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.running=!1,this.cancelled=!0,this.gfx&&this.gfx.endDrawing(),this._canvas&&e.delete(this._canvas),this.callback(t||new k.RenderingCancelledException("Rendering cancelled, page ".concat(this.pageNumber),"canvas"))}},{key:"operatorListChanged",value:function(){if(!this.graphicsReady)return void(this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound));this.stepper&&this.stepper.updateOperatorList(this.operatorList),this.running||this._continue()}},{key:"_continue",value:function(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}},{key:"_scheduleNext",value:function(){var e=this;this._useRequestAnimationFrame?window.requestAnimationFrame(function(){e._nextBound().catch(e.cancel.bind(e))}):Promise.resolve().then(this._nextBound).catch(this.cancel.bind(this))}},{key:"_next",value:function(){function t(){return r.apply(this,arguments)}var r=a(S.default.mark(function t(){return S.default.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.cancelled){t.next=2;break}return t.abrupt("return");case 2:this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),this._canvas&&e.delete(this._canvas),this.callback()));case 4:case"end":return t.stop()}},t,this)}));return t}()}]),t}()}();t.version="2.2.228";t.build="d7afb74a"},function(e,t,r){"use strict";e.exports=r(149)},function(e,t,r){"use strict";(function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}var r=function(e){function r(e,t,r,n){var a=t&&t.prototype instanceof i?t:i,o=Object.create(a.prototype),s=new f(n||[]);return o._invoke=l(e,r,s),o}function n(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}function i(){}function a(){}function o(){}function s(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function u(e){function r(i,a,o,s){var u=n(e[i],e,a);if("throw"!==u.type){var l=u.arg,c=l.value;return c&&"object"===t(c)&&y.call(c,"__await")?Promise.resolve(c.__await).then(function(e){r("next",e,o,s)},function(e){r("throw",e,o,s)}):Promise.resolve(c).then(function(e){l.value=e,o(l)},function(e){return r("throw",e,o,s)})}s(u.arg)}function i(e,t){function n(){return new Promise(function(n,i){r(e,t,n,i)})}return a=a?a.then(n,n):n()}var a;this._invoke=i}function l(e,t,r){var i=w;return function(a,o){if(i===x)throw new Error("Generator is already running");if(i===P){if("throw"===a)throw o;return v()}for(r.method=a,r.arg=o;;){var s=r.delegate;if(s){var u=c(s,r);if(u){if(u===C)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===w)throw i=P,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=x;var l=n(e,t,r);if("normal"===l.type){if(i=r.done?P:k,l.arg===C)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=P,r.method="throw",r.arg=l.arg)}}}function c(e,t){var r=e.iterator[t.method];if(r===m){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=m,c(e,t),"throw"===t.method))return C;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return C}var i=n(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,C;var a=i.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=m),t.delegate=null,C):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,C)}function h(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function d(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function f(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(h,this),this.reset(!0)}function p(e){if(e){var t=e[_];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function t(){for(;++r<e.length;)if(y.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=m,t.done=!0,t};return n.next=n}}return{next:v}}function v(){return{value:m,done:!0}}var m,g=Object.prototype,y=g.hasOwnProperty,b="function"==typeof Symbol?Symbol:{},_=b.iterator||"@@iterator",A=b.asyncIterator||"@@asyncIterator",S=b.toStringTag||"@@toStringTag";e.wrap=r;var w="suspendedStart",k="suspendedYield",x="executing",P="completed",C={},R={};R[_]=function(){return this};var T=Object.getPrototypeOf,E=T&&T(T(p([])));E&&E!==g&&y.call(E,_)&&(R=E);var O=o.prototype=i.prototype=Object.create(R);return a.prototype=O.constructor=o,o.constructor=a,o[S]=a.displayName="GeneratorFunction",e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===a||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,o):(e.__proto__=o,S in e||(e[S]="GeneratorFunction")),e.prototype=Object.create(O),e},e.awrap=function(e){return{__await:e}},s(u.prototype),u.prototype[A]=function(){return this},e.AsyncIterator=u,e.async=function(t,n,i,a){var o=new u(r(t,n,i,a));return e.isGeneratorFunction(n)?o:o.next().then(function(e){return e.done?e.value:o.next()})},s(O),O[S]="Generator",O[_]=function(){return this},O.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=p,f.prototype={constructor:f,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=m,this.done=!1,this.delegate=null,this.method="next",this.arg=m,
this.tryEntries.forEach(d),!e)for(var t in this)"t"===t.charAt(0)&&y.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=m)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){function t(t,n){return a.type="throw",a.arg=e,r.next=t,n&&(r.method="next",r.arg=m),!!n}if(this.done)throw e;for(var r=this,n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],a=i.completion;if("root"===i.tryLoc)return t("end");if(i.tryLoc<=this.prev){var o=y.call(i,"catchLoc"),s=y.call(i,"finallyLoc");if(o&&s){if(this.prev<i.catchLoc)return t(i.catchLoc,!0);if(this.prev<i.finallyLoc)return t(i.finallyLoc)}else if(o){if(this.prev<i.catchLoc)return t(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return t(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&y.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,C):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),C},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),d(r),C}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;d(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:p(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=m),C}},e}("object"===t(e)?e.exports:{});try{regeneratorRuntime=r}catch(e){Function("r","regeneratorRuntime = r")(r)}}).call(this,r(150)(e))},function(e,t,r){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,r){"use strict";function n(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function i(e){return function(){var t=this,r=arguments;return new Promise(function(i,a){function o(e){n(u,i,a,o,s,"next",e)}function s(e){n(u,i,a,o,s,"throw",e)}var u=e.apply(t,r);o(void 0)})}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}function u(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.url,n=t.target,i=t.rel;if(e.href=e.title=r?(0,m.removeNullCharacters)(r):"",r){var a=Object.values(k),o=a.includes(n)?n:k.NONE;e.target=x[o],e.rel="string"==typeof i?i:g}}function l(e){var t=e.indexOf("#"),r=e.indexOf("?"),n=Math.min(t>0?t:e.length,r>0?r:e.length);return e.substring(e.lastIndexOf("/",n)+1,n)}function c(){return"undefined"!=typeof fetch&&"undefined"!=typeof Response&&"body"in Response.prototype&&"undefined"!=typeof ReadableStream}function h(e,t){try{var r=t?new m.URL(e,t):new m.URL(e),n=r.protocol;return"http:"===n||"https:"===n}catch(e){return!1}}function d(e){return new Promise(function(t,r){var n=document.createElement("script");n.src=e,n.onload=t,n.onerror=function(){r(new Error("Cannot load script at: ".concat(n.src)))},(document.head||document.documentElement).appendChild(n)})}function f(e){console.log("Deprecated API usage: "+e)}function p(e){(0,m.assert)(e instanceof Image,"Invalid `img` parameter.");var t=e.src;"string"==typeof t&&t.startsWith("blob:")&&m.URL.revokeObjectURL&&m.URL.revokeObjectURL(t),e.removeAttribute("src")}Object.defineProperty(t,"__esModule",{value:!0}),t.addLinkAttributes=u,t.getFilenameFromUrl=l,t.isFetchSupported=c,t.isValidFetchUrl=h,t.loadScript=d,t.deprecated=f,t.releaseImageResources=p,t.PDFDateString=t.DummyStatTimer=t.StatTimer=t.DOMSVGFactory=t.DOMCMapReaderFactory=t.DOMCanvasFactory=t.DEFAULT_LINK_REL=t.LinkTarget=t.RenderingCancelledException=t.PageViewport=void 0;var v=function(e){return e&&e.__esModule?e:{default:e}}(r(148)),m=r(1),g="noopener noreferrer nofollow";t.DEFAULT_LINK_REL=g;var y="http://www.w3.org/2000/svg",b=function(){function e(){a(this,e)}return s(e,[{key:"create",value:function(e,t){if(e<=0||t<=0)throw new Error("Invalid canvas size");var r=document.createElement("canvas"),n=r.getContext("2d");return r.width=e,r.height=t,{canvas:r,context:n}}},{key:"reset",value:function(e,t,r){if(!e.canvas)throw new Error("Canvas is not specified");if(t<=0||r<=0)throw new Error("Invalid canvas size");e.canvas.width=t,e.canvas.height=r}},{key:"destroy",value:function(e){if(!e.canvas)throw new Error("Canvas is not specified");e.canvas.width=0,e.canvas.height=0,e.canvas=null,e.context=null}}]),e}();t.DOMCanvasFactory=b;var _=function(){function e(t){var r=t.baseUrl,n=void 0===r?null:r,i=t.isCompressed,o=void 0!==i&&i;a(this,e),this.baseUrl=n,this.isCompressed=o}return s(e,[{key:"fetch",value:function(e){function t(t){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){var e=i(v.default.mark(function e(t){var r,n,a,o=this;return v.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.name,this.baseUrl){e.next=3;break}throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');case 3:if(r){e.next=5;break}throw new Error("CMap name must be specified.");case 5:if(n=this.baseUrl+r+(this.isCompressed?".bcmap":""),a=this.isCompressed?m.CMapCompressionType.BINARY:m.CMapCompressionType.NONE,!c()||!h(n,document.baseURI)){e.next=9;break}return e.abrupt("return",fetch(n).then(function(){var e=i(v.default.mark(function e(t){var r;return v.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.ok){e.next=2;break}throw new Error(t.statusText);case 2:if(!o.isCompressed){e.next=10;break}return e.t0=Uint8Array,e.next=6,t.arrayBuffer();case 6:e.t1=e.sent,r=new e.t0(e.t1),e.next=15;break;case 10:return e.t2=m.stringToBytes,e.next=13,t.text();case 13:e.t3=e.sent,r=(0,e.t2)(e.t3);case 15:return e.abrupt("return",{cMapData:r,compressionType:a});case 16:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}()).catch(function(e){throw new Error("Unable to load ".concat(o.isCompressed?"binary ":"")+"CMap at: ".concat(n))}));case 9:return e.abrupt("return",new Promise(function(e,t){var r=new XMLHttpRequest;r.open("GET",n,!0),o.isCompressed&&(r.responseType="arraybuffer"),r.onreadystatechange=function(){if(r.readyState===XMLHttpRequest.DONE){if(200===r.status||0===r.status){var n;if(o.isCompressed&&r.response?n=new Uint8Array(r.response):!o.isCompressed&&r.responseText&&(n=(0,m.stringToBytes)(r.responseText)),n)return void e({cMapData:n,compressionType:a})}t(new Error(r.statusText))}},r.send(null)}).catch(function(e){throw new Error("Unable to load ".concat(o.isCompressed?"binary ":"")+"CMap at: ".concat(n))}));case 10:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}())}]),e}();t.DOMCMapReaderFactory=_;var A=function(){function e(){a(this,e)}return s(e,[{key:"create",value:function(e,t){(0,m.assert)(e>0&&t>0,"Invalid SVG dimensions");var r=document.createElementNS(y,"svg:svg");return r.setAttribute("version","1.1"),r.setAttribute("width",e+"px"),r.setAttribute("height",t+"px"),r.setAttribute("preserveAspectRatio","none"),r.setAttribute("viewBox","0 0 "+e+" "+t),r}},{key:"createElement",value:function(e){return(0,m.assert)("string"==typeof e,"Invalid SVG element type"),document.createElementNS(y,e)}}]),e}();t.DOMSVGFactory=A;var S=function(){function e(t){var r=t.viewBox,n=t.scale,i=t.rotation,o=t.offsetX,s=void 0===o?0:o,u=t.offsetY,l=void 0===u?0:u,c=t.dontFlip,h=void 0!==c&&c;a(this,e),this.viewBox=r,this.scale=n,this.rotation=i,this.offsetX=s,this.offsetY=l;var d,f,p,v,m=(r[2]+r[0])/2,g=(r[3]+r[1])/2;switch(i%=360,i=i<0?i+360:i){case 180:d=-1,f=0,p=0,v=1;break;case 90:d=0,f=1,p=1,v=0;break;case 270:d=0,f=-1,p=-1,v=0;break;default:d=1,f=0,p=0,v=-1}h&&(p=-p,v=-v);var y,b,_,A;0===d?(y=Math.abs(g-r[1])*n+s,b=Math.abs(m-r[0])*n+l,_=Math.abs(r[3]-r[1])*n,A=Math.abs(r[2]-r[0])*n):(y=Math.abs(m-r[0])*n+s,b=Math.abs(g-r[1])*n+l,_=Math.abs(r[2]-r[0])*n,A=Math.abs(r[3]-r[1])*n),this.transform=[d*n,f*n,p*n,v*n,y-d*n*m-p*n*g,b-f*n*m-v*n*g],this.width=_,this.height=A}return s(e,[{key:"clone",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.scale,n=void 0===r?this.scale:r,i=t.rotation,a=void 0===i?this.rotation:i,o=t.dontFlip,s=void 0!==o&&o;return new e({viewBox:this.viewBox.slice(),scale:n,rotation:a,offsetX:this.offsetX,offsetY:this.offsetY,dontFlip:s})}},{key:"convertToViewportPoint",value:function(e,t){return m.Util.applyTransform([e,t],this.transform)}},{key:"convertToViewportRectangle",value:function(e){var t=m.Util.applyTransform([e[0],e[1]],this.transform),r=m.Util.applyTransform([e[2],e[3]],this.transform);return[t[0],t[1],r[0],r[1]]}},{key:"convertToPdfPoint",value:function(e,t){return m.Util.applyInverseTransform([e,t],this.transform)}}]),e}();t.PageViewport=S;var w=function(){function e(e,t){this.message=e,this.type=t}return e.prototype=new Error,e.prototype.name="RenderingCancelledException",e.constructor=e,e}();t.RenderingCancelledException=w;var k={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};t.LinkTarget=k;var x=["","_self","_blank","_parent","_top"],P=function(){function e(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];a(this,e),this.enabled=!!t,this.started=Object.create(null),this.times=[]}return s(e,[{key:"time",value:function(e){this.enabled&&(e in this.started&&(0,m.warn)("Timer is already running for "+e),this.started[e]=Date.now())}},{key:"timeEnd",value:function(e){this.enabled&&(e in this.started||(0,m.warn)("Timer has not been started for "+e),this.times.push({name:e,start:this.started[e],end:Date.now()}),delete this.started[e])}},{key:"toString",value:function(){var e="",t=0,r=!0,n=!1,i=void 0;try{for(var a,o=this.times[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var s=a.value,u=s.name;u.length>t&&(t=u.length)}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}var l=!0,c=!1,h=void 0;try{for(var d,f=this.times[Symbol.iterator]();!(l=(d=f.next()).done);l=!0){var p=d.value,v=p.end-p.start;e+="".concat(p.name.padEnd(t)," ").concat(v,"ms\n")}}catch(e){c=!0,h=e}finally{try{l||null==f.return||f.return()}finally{if(c)throw h}}return e}}]),e}();t.StatTimer=P;var C=function(){function e(){a(this,e),(0,m.unreachable)("Cannot initialize DummyStatTimer.")}return s(e,null,[{key:"time",value:function(e){}},{key:"timeEnd",value:function(e){}},{key:"toString",value:function(){return""}}]),e}();t.DummyStatTimer=C;var R,T=function(){function e(){a(this,e)}return s(e,null,[{key:"toDateObject",value:function(e){if(!e||!(0,m.isString)(e))return null;R||(R=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));var t=R.exec(e);if(!t)return null;var r=parseInt(t[1],10),n=parseInt(t[2],10);n=n>=1&&n<=12?n-1:0;var i=parseInt(t[3],10);i=i>=1&&i<=31?i:1;var a=parseInt(t[4],10);a=a>=0&&a<=23?a:0;var o=parseInt(t[5],10);o=o>=0&&o<=59?o:0;var s=parseInt(t[6],10);s=s>=0&&s<=59?s:0;var u=t[7]||"Z",l=parseInt(t[8],10);l=l>=0&&l<=23?l:0;var c=parseInt(t[9],10)||0;return c=c>=0&&c<=59?c:0,"-"===u?(a+=l,o+=c):"+"===u&&(a-=l,o-=c),new Date(Date.UTC(r,n,i,a,o,s))}}]),e}();t.PDFDateString=T},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}function i(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?a(e):t}function a(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return(u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function l(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function c(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){function a(e){l(s,n,i,a,o,"next",e)}function o(e){l(s,n,i,a,o,"throw",e)}var s=e.apply(t,r);a(void 0)})}}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function f(e,t,r){return t&&d(e.prototype,t),r&&d(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.FontLoader=t.FontFaceObject=void 0;var p,v=function(e){return e&&e.__esModule?e:{default:e}}(r(148)),m=r(1),g=function(){function e(t){var r=t.docId,n=t.onUnsupportedFeature;h(this,e),this.constructor===e&&(0,m.unreachable)("Cannot initialize BaseFontLoader."),this.docId=r,this._onUnsupportedFeature=n,this.nativeFontFaces=[],this.styleElement=null}return f(e,[{key:"addNativeFontFace",value:function(e){this.nativeFontFaces.push(e),document.fonts.add(e)}},{key:"insertRule",value:function(e){var t=this.styleElement;t||(t=this.styleElement=document.createElement("style"),t.id="PDFJS_FONT_STYLE_TAG_".concat(this.docId),document.documentElement.getElementsByTagName("head")[0].appendChild(t));var r=t.sheet;r.insertRule(e,r.cssRules.length)}},{key:"clear",value:function(){this.nativeFontFaces.forEach(function(e){document.fonts.delete(e)}),this.nativeFontFaces.length=0,this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}},{key:"bind",value:function(){function e(e){return t.apply(this,arguments)}var t=c(v.default.mark(function e(t){var r,n,i=this;return v.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.attached&&!t.missingFile){e.next=2;break}return e.abrupt("return",void 0);case 2:if(t.attached=!0,!this.isFontLoadingAPISupported){e.next=19;break}if(!(r=t.createNativeFontFace())){e.next=18;break}return this.addNativeFontFace(r),e.prev=7,e.next=10,r.loaded;case 10:e.next=18;break;case 12:throw e.prev=12,e.t0=e.catch(7),this._onUnsupportedFeature({featureId:m.UNSUPPORTED_FEATURES.font}),(0,m.warn)("Failed to load font '".concat(r.family,"': '").concat(e.t0,"'.")),t.disableFontFace=!0,e.t0;case 18:return e.abrupt("return",void 0);case 19:if(!(n=t.createFontFaceRule())){e.next=25;break}if(this.insertRule(n),!this.isSyncFontLoadingSupported){e.next=24;break}return e.abrupt("return",void 0);case 24:return e.abrupt("return",new Promise(function(e){var r=i._queueLoadingCallback(e);i._prepareFontLoadEvent([n],[t],r)}));case 25:return e.abrupt("return",void 0);case 26:case"end":return e.stop()}},e,this,[[7,12]])}));return e}()},{key:"_queueLoadingCallback",value:function(e){(0,m.unreachable)("Abstract method `_queueLoadingCallback`.")}},{key:"_prepareFontLoadEvent",value:function(e,t,r){(0,m.unreachable)("Abstract method `_prepareFontLoadEvent`.")}},{key:"isFontLoadingAPISupported",get:function(){(0,m.unreachable)("Abstract method `isFontLoadingAPISupported`.")}},{key:"isSyncFontLoadingSupported",get:function(){(0,m.unreachable)("Abstract method `isSyncFontLoadingSupported`.")}},{key:"_loadTestFont",get:function(){(0,m.unreachable)("Abstract method `_loadTestFont`.")}}]),e}();t.FontLoader=p,t.FontLoader=p=function(e){function t(e){var r;return h(this,t),r=i(this,o(t).call(this,e)),r.loadingContext={requests:[],nextRequestId:0},r.loadTestFontId=0,r}return s(t,e),f(t,[{key:"_queueLoadingCallback",value:function(e){function t(){for((0,m.assert)(!n.done,"completeRequest() cannot be called twice."),n.done=!0;r.requests.length>0&&r.requests[0].done;){var e=r.requests.shift();setTimeout(e.callback,0)}}var r=this.loadingContext,n={id:"pdfjs-font-loading-".concat(r.nextRequestId++),done:!1,complete:t,callback:e};return r.requests.push(n),n}},{key:"_prepareFontLoadEvent",value:function(e,t,r){function n(e,t){return e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|255&e.charCodeAt(t+3)}function i(e,t,r,n){return e.substring(0,t)+n+e.substring(t+r)}function a(e,t){return++c>30?((0,m.warn)("Load test font never loaded."),void t()):(l.font="30px "+e,l.fillText(".",0,20),l.getImageData(0,0,1,1).data[3]>0?void t():void setTimeout(a.bind(null,e,t)))}var o,s,u=document.createElement("canvas");u.width=1,u.height=1;var l=u.getContext("2d"),c=0,h="lt".concat(Date.now()).concat(this.loadTestFontId++),d=this._loadTestFont;d=i(d,976,h.length,h);var f=n(d,16);for(o=0,s=h.length-3;o<s;o+=4)f=f-1482184792+n(h,o)|0;o<h.length&&(f=f-1482184792+n(h+"XXX",o)|0),d=i(d,16,4,(0,m.string32)(f));var p="url(data:font/opentype;base64,".concat(btoa(d),");"),v='@font-face {font-family:"'.concat(h,'";src:').concat(p,"}");this.insertRule(v);var g=[];for(o=0,s=t.length;o<s;o++)g.push(t[o].loadedName);g.push(h);var y=document.createElement("div");for(y.setAttribute("style","visibility: hidden;width: 10px; height: 10px;position: absolute; top: 0px; left: 0px;"),o=0,s=g.length;o<s;++o){var b=document.createElement("span");b.textContent="Hi",b.style.fontFamily=g[o],y.appendChild(b)}document.body.appendChild(y),a(h,function(){document.body.removeChild(y),r.complete()})}},{key:"isFontLoadingAPISupported",get:function(){var e="undefined"!=typeof document&&!!document.fonts;if(e&&"undefined"!=typeof navigator){var t=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);t&&t[1]<63&&(e=!1)}return(0,m.shadow)(this,"isFontLoadingAPISupported",e)}},{key:"isSyncFontLoadingSupported",get:function(){var e=!1;if("undefined"==typeof navigator)e=!0;else{var t=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);t&&t[1]>=14&&(e=!0)}return(0,m.shadow)(this,"isSyncFontLoadingSupported",e)}},{key:"_loadTestFont",get:function(){return(0,m.shadow)(this,"_loadTestFont",function(){return atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==")}())}}]),t}(g);var y={get value(){return(0,m.shadow)(this,"value",(0,m.isEvalSupported)())}},b=function(){function e(t,r){var n=r.isEvalSupported,i=void 0===n||n,a=r.disableFontFace,o=void 0!==a&&a,s=r.ignoreErrors,u=void 0!==s&&s,l=r.onUnsupportedFeature,c=void 0===l?null:l,d=r.fontRegistry,f=void 0===d?null:d;h(this,e),this.compiledGlyphs=Object.create(null);for(var p in t)this[p]=t[p];this.isEvalSupported=!1!==i,this.disableFontFace=!0===o,this.ignoreErrors=!0===u,this._onUnsupportedFeature=c,this.fontRegistry=f}return f(e,[{key:"createNativeFontFace",value:function(){if(!this.data||this.disableFontFace)return null;var e=new FontFace(this.loadedName,this.data,{});return this.fontRegistry&&this.fontRegistry.registerFont(this),e}},{key:"createFontFaceRule",value:function(){if(!this.data||this.disableFontFace)return null;var e=(0,m.bytesToString)(new Uint8Array(this.data)),t="url(data:".concat(this.mimetype,";base64,").concat(btoa(e),");"),r='@font-face {font-family:"'.concat(this.loadedName,'";src:').concat(t,"}");return this.fontRegistry&&this.fontRegistry.registerFont(this,t),r}},{key:"getPathGenerator",value:function(e,t){if(void 0!==this.compiledGlyphs[t])return this.compiledGlyphs[t];var r,n;try{r=e.get(this.loadedName+"_path_"+t)}catch(e){if(!this.ignoreErrors)throw e;return this._onUnsupportedFeature&&this._onUnsupportedFeature({featureId:m.UNSUPPORTED_FEATURES.font}),(0,m.warn)('getPathGenerator - ignoring character: "'.concat(e,'".')),this.compiledGlyphs[t]=function(e,t){}}if(this.isEvalSupported&&y.value){for(var i,a="",o=0,s=r.length;o<s;o++)n=r[o],i=void 0!==n.args?n.args.join(","):"",a+="c."+n.cmd+"("+i+");\n";return this.compiledGlyphs[t]=new Function("c","size",a)}return this.compiledGlyphs[t]=function(e,t){for(var i=0,a=r.length;i<a;i++)n=r[i],"scale"===n.cmd&&(n.args=[t,-t]),e[n.cmd].apply(e,n.args)}}}]),e}();t.FontFaceObject=b},function(e,t,r){"use strict";var n=Object.create(null),i=r(4),a="undefined"!=typeof navigator&&navigator.userAgent||"",o=/Trident/.test(a),s=/CriOS/.test(a);!function(){(o||s)&&(n.disableCreateObjectURL=!0)}(),function(){i()&&(n.disableFontFace=!0,n.nativeImageDecoderSupport="none")}(),t.apiCompatibilityParams=Object.freeze(n)},function(e,t,r){"use strict";function n(e){e.mozCurrentTransform||(e._originalSave=e.save,e._originalRestore=e.restore,e._originalRotate=e.rotate,e._originalScale=e.scale,e._originalTranslate=e.translate,e._originalTransform=e.transform,e._originalSetTransform=e.setTransform,e._transformMatrix=e._transformMatrix||[1,0,0,1,0,0],e._transformStack=[],Object.defineProperty(e,"mozCurrentTransform",{get:function(){return this._transformMatrix}}),Object.defineProperty(e,"mozCurrentTransformInverse",{get:function(){var e=this._transformMatrix,t=e[0],r=e[1],n=e[2],i=e[3],a=e[4],o=e[5],s=t*i-r*n,u=r*n-t*i;return[i/s,r/u,n/u,t/s,(i*a-n*o)/u,(r*a-t*o)/s]}}),e.save=function(){var e=this._transformMatrix;this._transformStack.push(e),this._transformMatrix=e.slice(0,6),this._originalSave()},e.restore=function(){var e=this._transformStack.pop();e&&(this._transformMatrix=e,this._originalRestore())},e.translate=function(e,t){var r=this._transformMatrix;r[4]=r[0]*e+r[2]*t+r[4],r[5]=r[1]*e+r[3]*t+r[5],this._originalTranslate(e,t)},e.scale=function(e,t){var r=this._transformMatrix;r[0]=r[0]*e,r[1]=r[1]*e,r[2]=r[2]*t,r[3]=r[3]*t,this._originalScale(e,t)},e.transform=function(t,r,n,i,a,o){var s=this._transformMatrix;this._transformMatrix=[s[0]*t+s[2]*r,s[1]*t+s[3]*r,s[0]*n+s[2]*i,s[1]*n+s[3]*i,s[0]*a+s[2]*o+s[4],s[1]*a+s[3]*o+s[5]],e._originalTransform(t,r,n,i,a,o)},e.setTransform=function(t,r,n,i,a,o){this._transformMatrix=[t,r,n,i,a,o],e._originalSetTransform(t,r,n,i,a,o)},e.rotate=function(e){var t=Math.cos(e),r=Math.sin(e),n=this._transformMatrix;this._transformMatrix=[n[0]*t+n[2]*r,n[1]*t+n[3]*r,n[0]*-r+n[2]*t,n[1]*-r+n[3]*t,n[4],n[5]],this._originalRotate(e)})}function i(e){var t,r,n,i,a=e.width,o=e.height,s=a+1,u=new Uint8Array(s*(o+1)),l=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),c=a+7&-8,h=e.data,d=new Uint8Array(c*o),f=0;for(t=0,i=h.length;t<i;t++)for(var p=128,v=h[t];p>0;)d[f++]=v&p?0:255,p>>=1;var m=0;for(f=0,0!==d[f]&&(u[0]=1,++m),r=1;r<a;r++)d[f]!==d[f+1]&&(u[r]=d[f]?2:1,++m),f++;for(0!==d[f]&&(u[r]=2,++m),t=1;t<o;t++){f=t*c,n=t*s,d[f-c]!==d[f]&&(u[n]=d[f]?1:8,++m);var g=(d[f]?4:0)+(d[f-c]?8:0);for(r=1;r<a;r++)g=(g>>2)+(d[f+1]?4:0)+(d[f-c+1]?8:0),l[g]&&(u[n+r]=l[g],++m),f++;if(d[f-c]!==d[f]&&(u[n+r]=d[f]?2:4,++m),m>1e3)return null}for(f=c*(o-1),n=t*s,0!==d[f]&&(u[n]=8,++m),r=1;r<a;r++)d[f]!==d[f+1]&&(u[n+r]=d[f]?4:8,++m),f++;if(0!==d[f]&&(u[n+r]=4,++m),m>1e3)return null;var y=new Int32Array([0,s,-1,0,-s,0,0,0,1]),b=[];for(t=0;m&&t<=o;t++){for(var _=t*s,A=_+a;_<A&&!u[_];)_++;if(_!==A){var S,w=[_%s,t],k=u[_],x=_;do{var P=y[k];do{_+=P}while(!u[_]);S=u[_],5!==S&&10!==S?(k=S,u[_]=0):(k=S&51*k>>4,u[_]&=k>>2|k<<2),w.push(_%s),w.push(_/s|0),u[_]||--m}while(x!==_);b.push(w),--t}}return function(e){e.save(),e.scale(1/a,-1/o),e.translate(0,-o),e.beginPath();for(var t=0,r=b.length;t<r;t++){var n=b[t];e.moveTo(n[0],n[1]);for(var i=2,s=n.length;i<s;i+=2)e.lineTo(n[i],n[i+1])}e.fill(),e.beginPath(),e.restore()}}Object.defineProperty(t,"__esModule",{value:!0}),t.CanvasGraphics=void 0;var a=r(1),o=r(155),s=16,u={get value(){return(0,a.shadow)(u,"value",(0,a.isLittleEndian)())}},l=function(){function e(e){this.canvasFactory=e,this.cache=Object.create(null)}return e.prototype={getCanvas:function(e,t,r,i){var a;return void 0!==this.cache[e]?(a=this.cache[e],this.canvasFactory.reset(a,t,r),a.context.setTransform(1,0,0,1,0,0)):(a=this.canvasFactory.create(t,r),this.cache[e]=a),i&&n(a.context),a},clear:function(){for(var e in this.cache){var t=this.cache[e];this.canvasFactory.destroy(t),delete this.cache[e]}}},e}(),c=function(){function e(){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=a.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=a.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=a.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.resumeSMaskCtx=null}return e.prototype={clone:function(){return Object.create(this)},setCurrentPoint:function(e,t){this.x=e,this.y=t}},e}(),h=function(){function e(e,t,r,i,a,o){this.ctx=e,this.current=new c,this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=t,this.objs=r,this.canvasFactory=i,this.webGLContext=a,this.imageLayer=o,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.cachedCanvases=new l(this.canvasFactory),e&&n(e),this._cachedGetSinglePixelWidth=null}function t(e,t){if("undefined"!=typeof ImageData&&t instanceof ImageData)return void e.putImageData(t,0,0);var r,n,i,o,l,c=t.height,h=t.width,d=c%s,f=(c-d)/s,p=0===d?f:f+1,v=e.createImageData(h,s),m=0,g=t.data,y=v.data;if(t.kind===a.ImageKind.GRAYSCALE_1BPP){var b=g.byteLength,_=new Uint32Array(y.buffer,0,y.byteLength>>2),A=_.length,S=h+7>>3,w=4294967295,k=u.value?4278190080:255;for(n=0;n<p;n++){for(o=n<f?s:d,r=0,i=0;i<o;i++){for(var x=b-m,P=0,C=x>S?h:8*x-7,R=-8&C,T=0,E=0;P<R;P+=8)E=g[m++],_[r++]=128&E?w:k,_[r++]=64&E?w:k,_[r++]=32&E?w:k,_[r++]=16&E?w:k,_[r++]=8&E?w:k,_[r++]=4&E?w:k,_[r++]=2&E?w:k,_[r++]=1&E?w:k;for(;P<C;P++)0===T&&(E=g[m++],T=128),_[r++]=E&T?w:k,T>>=1}for(;r<A;)_[r++]=0;e.putImageData(v,0,n*s)}}else if(t.kind===a.ImageKind.RGBA_32BPP){for(i=0,l=h*s*4,n=0;n<f;n++)y.set(g.subarray(m,m+l)),m+=l,e.putImageData(v,0,i),i+=s;n<p&&(l=h*d*4,y.set(g.subarray(m,m+l)),e.putImageData(v,0,i))}else{if(t.kind!==a.ImageKind.RGB_24BPP)throw new Error("bad image kind: ".concat(t.kind));for(o=s,l=h*o,n=0;n<p;n++){for(n>=f&&(o=d,l=h*o),r=0,i=l;i--;)y[r++]=g[m++],y[r++]=g[m++],y[r++]=g[m++],y[r++]=255;e.putImageData(v,0,n*s)}}}function r(e,t){for(var r=t.height,n=t.width,i=r%s,a=(r-i)/s,o=0===i?a:a+1,u=e.createImageData(n,s),l=0,c=t.data,h=u.data,d=0;d<o;d++){for(var f=d<a?s:i,p=3,v=0;v<f;v++)for(var m=0,g=0;g<n;g++){if(!m){var y=c[l++];m=128}h[p]=y&m?0:255,p+=4,m>>=1}e.putImageData(u,0,d*s)}}function h(e,t){for(var r=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"],n=0,i=r.length;n<i;n++){var a=r[n];void 0!==e[a]&&(t[a]=e[a])}void 0!==e.setLineDash&&(t.setLineDash(e.getLineDash()),t.lineDashOffset=e.lineDashOffset)}function d(e){e.strokeStyle="#000000",e.fillStyle="#000000",e.fillRule="nonzero",e.globalAlpha=1,e.lineWidth=1,e.lineCap="butt",e.lineJoin="miter",e.miterLimit=10,e.globalCompositeOperation="source-over",e.font="10px sans-serif",void 0!==e.setLineDash&&(e.setLineDash([]),e.lineDashOffset=0)}function f(e,t,r,n){for(var i=e.length,a=3;a<i;a+=4){var o=e[a];if(0===o)e[a-3]=t,e[a-2]=r,e[a-1]=n;else if(o<255){var s=255-o;e[a-3]=e[a-3]*o+t*s>>8,e[a-2]=e[a-2]*o+r*s>>8,e[a-1]=e[a-1]*o+n*s>>8}}}function p(e,t,r){for(var n=e.length,i=3;i<n;i+=4){var a=r?r[e[i]]:e[i];t[i]=t[i]*a*(1/255)|0}}function v(e,t,r){for(var n=e.length,i=3;i<n;i+=4){var a=77*e[i-3]+152*e[i-2]+28*e[i-1];t[i]=r?t[i]*r[a>>8]>>8:t[i]*a>>16}}function m(e,t,r,n,i,a,o){var s,u=!!a,l=u?a[0]:0,c=u?a[1]:0,h=u?a[2]:0;s="Luminosity"===i?v:p;for(var d=Math.min(n,Math.ceil(1048576/r)),m=0;m<n;m+=d){var g=Math.min(d,n-m),y=e.getImageData(0,m,r,g),b=t.getImageData(0,m,r,g);u&&f(y.data,l,c,h),s(y.data,b.data,o),e.putImageData(b,0,m)}}function g(e,t,r,n){var i=t.canvas,a=t.context;e.setTransform(t.scaleX,0,0,t.scaleY,t.offsetX,t.offsetY);var o=t.backdrop||null;if(!t.transferMap&&n.isEnabled){var s=n.composeSMask({layer:r.canvas,mask:i,properties:{subtype:t.subtype,backdrop:o}});return e.setTransform(1,0,0,1,0,0),void e.drawImage(s,t.offsetX,t.offsetY)}m(a,r,i.width,i.height,t.subtype,o,t.transferMap),e.drawImage(i,0,0)}var y=["butt","round","square"],b=["miter","round","bevel"],_={},A={};e.prototype={beginDrawing:function(e){var t=e.transform,r=e.viewport,n=e.transparency,i=void 0!==n&&n,a=e.background,o=void 0===a?null:a,s=this.ctx.canvas.width,u=this.ctx.canvas.height;if(this.ctx.save(),this.ctx.fillStyle=o||"rgb(255, 255, 255)",this.ctx.fillRect(0,0,s,u),this.ctx.restore(),i){var l=this.cachedCanvases.getCanvas("transparent",s,u,!0);this.compositeCtx=this.ctx,this.transparentCanvas=l.canvas,this.ctx=l.context,this.ctx.save(),this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform)}this.ctx.save(),d(this.ctx),t&&this.ctx.transform.apply(this.ctx,t),this.ctx.transform.apply(this.ctx,r.transform),this.baseTransform=this.ctx.mozCurrentTransform.slice(),this.imageLayer&&this.imageLayer.beginLayout()},executeOperatorList:function(e,t,r,n){var i=e.argsArray,o=e.fnArray,s=t||0,u=i.length;if(u===s)return s;for(var l,c=u-s>10&&"function"==typeof r,h=c?Date.now()+15:0,d=0,f=this.commonObjs,p=this.objs;;){if(void 0!==n&&s===n.nextBreakPoint)return n.breakIt(s,r),s;if((l=o[s])!==a.OPS.dependency)this[l].apply(this,i[s]);else{var v=!0,m=!1,g=void 0;try{for(var y,b=i[s][Symbol.iterator]();!(v=(y=b.next()).done);v=!0){var _=y.value,A=_.startsWith("g_")?f:p;if(!A.has(_))return A.get(_,r),s}}catch(e){m=!0,g=e}finally{try{v||null==b.return||b.return()}finally{if(m)throw g}}}if(++s===u)return s;if(c&&++d>10){if(Date.now()>h)return r(),s;d=0}}},endDrawing:function(){null!==this.current.activeSMask&&this.endSMaskGroup(),this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),
this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null),this.cachedCanvases.clear(),this.webGLContext.clear(),this.imageLayer&&this.imageLayer.endLayout()},setLineWidth:function(e){this.current.lineWidth=e,this.ctx.lineWidth=e},setLineCap:function(e){this.ctx.lineCap=y[e]},setLineJoin:function(e){this.ctx.lineJoin=b[e]},setMiterLimit:function(e){this.ctx.miterLimit=e},setDash:function(e,t){var r=this.ctx;void 0!==r.setLineDash&&(r.setLineDash(e),r.lineDashOffset=t)},setRenderingIntent:function(e){},setFlatness:function(e){},setGState:function(e){for(var t=0,r=e.length;t<r;t++){var n=e[t],i=n[0],a=n[1];switch(i){case"LW":this.setLineWidth(a);break;case"LC":this.setLineCap(a);break;case"LJ":this.setLineJoin(a);break;case"ML":this.setMiterLimit(a);break;case"D":this.setDash(a[0],a[1]);break;case"RI":this.setRenderingIntent(a);break;case"FL":this.setFlatness(a);break;case"Font":this.setFont(a[0],a[1]);break;case"CA":this.current.strokeAlpha=n[1];break;case"ca":this.current.fillAlpha=n[1],this.ctx.globalAlpha=n[1];break;case"BM":this.ctx.globalCompositeOperation=a;break;case"SMask":this.current.activeSMask&&(this.stateStack.length>0&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask?this.suspendSMaskGroup():this.endSMaskGroup()),this.current.activeSMask=a?this.tempSMask:null,this.current.activeSMask&&this.beginSMaskGroup(),this.tempSMask=null}}},beginSMaskGroup:function(){var e=this.current.activeSMask,t=e.canvas.width,r=e.canvas.height,n="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(n,t,r,!0),a=this.ctx,o=a.mozCurrentTransform;this.ctx.save();var s=i.context;s.scale(1/e.scaleX,1/e.scaleY),s.translate(-e.offsetX,-e.offsetY),s.transform.apply(s,o),e.startTransformInverse=s.mozCurrentTransformInverse,h(a,s),this.ctx=s,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(a),this.groupLevel++},suspendSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),g(this.ctx,this.current.activeSMask,e,this.webGLContext),this.ctx.restore(),this.ctx.save(),h(e,this.ctx),this.current.resumeSMaskCtx=e;var t=a.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t),e.save(),e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,e.canvas.width,e.canvas.height),e.restore()},resumeSMaskGroup:function(){var e=this.current.resumeSMaskCtx,t=this.ctx;this.ctx=e,this.groupStack.push(t),this.groupLevel++},endSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),g(this.ctx,this.current.activeSMask,e,this.webGLContext),this.ctx.restore(),h(e,this.ctx);var t=a.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t)},save:function(){this.ctx.save();var e=this.current;this.stateStack.push(e),this.current=e.clone(),this.current.resumeSMaskCtx=null},restore:function(){this.current.resumeSMaskCtx&&this.resumeSMaskGroup(),null===this.current.activeSMask||0!==this.stateStack.length&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask||this.endSMaskGroup(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.ctx.restore(),this.pendingClip=null,this._cachedGetSinglePixelWidth=null)},transform:function(e,t,r,n,i,a){this.ctx.transform(e,t,r,n,i,a),this._cachedGetSinglePixelWidth=null},constructPath:function(e,t){for(var r=this.ctx,n=this.current,i=n.x,o=n.y,s=0,u=0,l=e.length;s<l;s++)switch(0|e[s]){case a.OPS.rectangle:i=t[u++],o=t[u++];var c=t[u++],h=t[u++];0===c&&(c=this.getSinglePixelWidth()),0===h&&(h=this.getSinglePixelWidth());var d=i+c,f=o+h;this.ctx.moveTo(i,o),this.ctx.lineTo(d,o),this.ctx.lineTo(d,f),this.ctx.lineTo(i,f),this.ctx.lineTo(i,o),this.ctx.closePath();break;case a.OPS.moveTo:i=t[u++],o=t[u++],r.moveTo(i,o);break;case a.OPS.lineTo:i=t[u++],o=t[u++],r.lineTo(i,o);break;case a.OPS.curveTo:i=t[u+4],o=t[u+5],r.bezierCurveTo(t[u],t[u+1],t[u+2],t[u+3],i,o),u+=6;break;case a.OPS.curveTo2:r.bezierCurveTo(i,o,t[u],t[u+1],t[u+2],t[u+3]),i=t[u+2],o=t[u+3],u+=4;break;case a.OPS.curveTo3:i=t[u+2],o=t[u+3],r.bezierCurveTo(t[u],t[u+1],i,o,i,o),u+=4;break;case a.OPS.closePath:r.closePath()}n.setCurrentPoint(i,o)},closePath:function(){this.ctx.closePath()},stroke:function(e){e=void 0===e||e;var t=this.ctx,r=this.current.strokeColor;t.lineWidth=Math.max(.65*this.getSinglePixelWidth(),this.current.lineWidth),t.globalAlpha=this.current.strokeAlpha,r&&r.hasOwnProperty("type")&&"Pattern"===r.type?(t.save(),t.strokeStyle=r.getPattern(t,this),t.stroke(),t.restore()):t.stroke(),e&&this.consumePath(),t.globalAlpha=this.current.fillAlpha},closeStroke:function(){this.closePath(),this.stroke()},fill:function(e){e=void 0===e||e;var t=this.ctx,r=this.current.fillColor,n=this.current.patternFill,i=!1;n&&(t.save(),this.baseTransform&&t.setTransform.apply(t,this.baseTransform),t.fillStyle=r.getPattern(t,this),i=!0),this.pendingEOFill?(t.fill("evenodd"),this.pendingEOFill=!1):t.fill(),i&&t.restore(),e&&this.consumePath()},eoFill:function(){this.pendingEOFill=!0,this.fill()},fillStroke:function(){this.fill(!1),this.stroke(!1),this.consumePath()},eoFillStroke:function(){this.pendingEOFill=!0,this.fillStroke()},closeFillStroke:function(){this.closePath(),this.fillStroke()},closeEOFillStroke:function(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()},endPath:function(){this.consumePath()},clip:function(){this.pendingClip=_},eoClip:function(){this.pendingClip=A},beginText:function(){this.current.textMatrix=a.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},endText:function(){var e=this.pendingTextPaths,t=this.ctx;if(void 0===e)return void t.beginPath();t.save(),t.beginPath();for(var r=0;r<e.length;r++){var n=e[r];t.setTransform.apply(t,n.transform),t.translate(n.x,n.y),n.addToPath(t,n.fontSize)}t.restore(),t.clip(),t.beginPath(),delete this.pendingTextPaths},setCharSpacing:function(e){this.current.charSpacing=e},setWordSpacing:function(e){this.current.wordSpacing=e},setHScale:function(e){this.current.textHScale=e/100},setLeading:function(e){this.current.leading=-e},setFont:function(e,t){var r=this.commonObjs.get(e),n=this.current;if(!r)throw new Error("Can't find font for ".concat(e));if(n.fontMatrix=r.fontMatrix?r.fontMatrix:a.FONT_IDENTITY_MATRIX,0!==n.fontMatrix[0]&&0!==n.fontMatrix[3]||(0,a.warn)("Invalid font matrix for font "+e),t<0?(t=-t,n.fontDirection=-1):n.fontDirection=1,this.current.font=r,this.current.fontSize=t,!r.isType3Font){var i=r.loadedName||"sans-serif",o=r.black?"900":r.bold?"bold":"normal",s=r.italic?"italic":"normal",u='"'.concat(i,'", ').concat(r.fallbackName),l=t<16?16:t>100?100:t;this.current.fontSizeScale=t/l,this.ctx.font="".concat(s," ").concat(o," ").concat(l,"px ").concat(u)}},setTextRenderingMode:function(e){this.current.textRenderingMode=e},setTextRise:function(e){this.current.textRise=e},moveText:function(e,t){this.current.x=this.current.lineX+=e,this.current.y=this.current.lineY+=t},setLeadingMoveText:function(e,t){this.setLeading(-t),this.moveText(e,t)},setTextMatrix:function(e,t,r,n,i,a){this.current.textMatrix=[e,t,r,n,i,a],this.current.textMatrixScale=Math.sqrt(e*e+t*t),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},nextLine:function(){this.moveText(0,this.current.leading)},paintChar:function(e,t,r,n){var i,o=this.ctx,s=this.current,u=s.font,l=s.textRenderingMode,c=s.fontSize/s.fontSizeScale,h=l&a.TextRenderingMode.FILL_STROKE_MASK,d=!!(l&a.TextRenderingMode.ADD_TO_PATH_FLAG),f=s.patternFill&&u.data;if((u.disableFontFace||d||f)&&(i=u.getPathGenerator(this.commonObjs,e)),u.disableFontFace||f?(o.save(),o.translate(t,r),o.beginPath(),i(o,c),n&&o.setTransform.apply(o,n),h!==a.TextRenderingMode.FILL&&h!==a.TextRenderingMode.FILL_STROKE||o.fill(),h!==a.TextRenderingMode.STROKE&&h!==a.TextRenderingMode.FILL_STROKE||o.stroke(),o.restore()):(h!==a.TextRenderingMode.FILL&&h!==a.TextRenderingMode.FILL_STROKE||o.fillText(e,t,r),h!==a.TextRenderingMode.STROKE&&h!==a.TextRenderingMode.FILL_STROKE||o.strokeText(e,t,r)),d){(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:o.mozCurrentTransform,x:t,y:r,fontSize:c,addToPath:i})}},get isFontSubpixelAAEnabled(){var e=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10),t=e.context;t.scale(1.5,1),t.fillText("I",0,10);for(var r=t.getImageData(0,0,10,10).data,n=!1,i=3;i<r.length;i+=4)if(r[i]>0&&r[i]<255){n=!0;break}return(0,a.shadow)(this,"isFontSubpixelAAEnabled",n)},showText:function(e){var t=this.current,r=t.font;if(r.isType3Font)return this.showType3Text(e);var n=t.fontSize;if(0!==n){var i=this.ctx,o=t.fontSizeScale,s=t.charSpacing,u=t.wordSpacing,l=t.fontDirection,c=t.textHScale*l,h=e.length,d=r.vertical,f=d?1:-1,p=r.defaultVMetrics,v=n*t.fontMatrix[0],m=t.textRenderingMode===a.TextRenderingMode.FILL&&!r.disableFontFace&&!t.patternFill;i.save();var g;if(t.patternFill){i.save();var y=t.fillColor.getPattern(i,this);g=i.mozCurrentTransform,i.restore(),i.fillStyle=y}i.transform.apply(i,t.textMatrix),i.translate(t.x,t.y+t.textRise),l>0?i.scale(c,-1):i.scale(c,1);var b=t.lineWidth,_=t.textMatrixScale;if(0===_||0===b){var A=t.textRenderingMode&a.TextRenderingMode.FILL_STROKE_MASK;A!==a.TextRenderingMode.STROKE&&A!==a.TextRenderingMode.FILL_STROKE||(this._cachedGetSinglePixelWidth=null,b=.65*this.getSinglePixelWidth())}else b/=_;1!==o&&(i.scale(o,o),b/=o),i.lineWidth=b;var S,w=0;for(S=0;S<h;++S){var k=e[S];if((0,a.isNum)(k))w+=f*k*n/1e3;else{var x,P,C,R,T=!1,E=(k.isSpace?u:0)+s,O=k.fontChar,F=k.accent,I=k.width;if(d){var L,j,M;L=k.vmetric||p,j=k.vmetric?L[1]:.5*I,j=-j*v,M=L[2]*v,I=L?-L[0]:I,x=j/o,P=(w+M)/o}else x=w/o,P=0;if(r.remeasure&&I>0){var D=1e3*i.measureText(O).width/n*o;if(I<D&&this.isFontSubpixelAAEnabled){var N=I/D;T=!0,i.save(),i.scale(N,1),x/=N}else I!==D&&(x+=(I-D)/2e3*n/o)}(k.isInFont||r.missingFile)&&(m&&!F?i.fillText(O,x,P):(this.paintChar(O,x,P,g),F&&(C=x+F.offset.x/o,R=P-F.offset.y/o,this.paintChar(F.fontChar,C,R,g))));w+=I*v+E*l,T&&i.restore()}}d?t.y-=w*c:t.x+=w*c,i.restore()}},showType3Text:function(e){var t,r,n,i,o=this.ctx,s=this.current,u=s.font,l=s.fontSize,c=s.fontDirection,h=u.vertical?1:-1,d=s.charSpacing,f=s.wordSpacing,p=s.textHScale*c,v=s.fontMatrix||a.FONT_IDENTITY_MATRIX,m=e.length,g=s.textRenderingMode===a.TextRenderingMode.INVISIBLE;if(!g&&0!==l){for(this._cachedGetSinglePixelWidth=null,o.save(),o.transform.apply(o,s.textMatrix),o.translate(s.x,s.y),o.scale(p,c),t=0;t<m;++t)if(r=e[t],(0,a.isNum)(r))i=h*r*l/1e3,this.ctx.translate(i,0),s.x+=i*p;else{var y=(r.isSpace?f:0)+d,b=u.charProcOperatorList[r.operatorListId];if(b){this.processingType3=r,this.save(),o.scale(l,l),o.transform.apply(o,v),this.executeOperatorList(b),this.restore();var _=a.Util.applyTransform([r.width,0],v);n=_[0]*l+y,o.translate(n,0),s.x+=n*p}else(0,a.warn)('Type3 character "'.concat(r.operatorListId,'" is not available.'))}o.restore(),this.processingType3=null}},setCharWidth:function(e,t){},setCharWidthAndBounds:function(e,t,r,n,i,a){this.ctx.rect(r,n,i-r,a-n),this.clip(),this.endPath()},getColorN_Pattern:function(t){var r,n=this;if("TilingPattern"===t[0]){var i=t[1],a=this.baseTransform||this.ctx.mozCurrentTransform.slice(),s={createCanvasGraphics:function(t){return new e(t,n.commonObjs,n.objs,n.canvasFactory,n.webGLContext)}};r=new o.TilingPattern(t,i,this.ctx,s,a)}else r=(0,o.getShadingPatternFromIR)(t);return r},setStrokeColorN:function(){this.current.strokeColor=this.getColorN_Pattern(arguments)},setFillColorN:function(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0},setStrokeRGBColor:function(e,t,r){var n=a.Util.makeCssRgb(e,t,r);this.ctx.strokeStyle=n,this.current.strokeColor=n},setFillRGBColor:function(e,t,r){var n=a.Util.makeCssRgb(e,t,r);this.ctx.fillStyle=n,this.current.fillColor=n,this.current.patternFill=!1},shadingFill:function(e){var t=this.ctx;this.save();var r=(0,o.getShadingPatternFromIR)(e);t.fillStyle=r.getPattern(t,this,!0);var n=t.mozCurrentTransformInverse;if(n){var i=t.canvas,s=i.width,u=i.height,l=a.Util.applyTransform([0,0],n),c=a.Util.applyTransform([0,u],n),h=a.Util.applyTransform([s,0],n),d=a.Util.applyTransform([s,u],n),f=Math.min(l[0],c[0],h[0],d[0]),p=Math.min(l[1],c[1],h[1],d[1]),v=Math.max(l[0],c[0],h[0],d[0]),m=Math.max(l[1],c[1],h[1],d[1]);this.ctx.fillRect(f,p,v-f,m-p)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.restore()},beginInlineImage:function(){(0,a.unreachable)("Should not call beginInlineImage")},beginImageData:function(){(0,a.unreachable)("Should not call beginImageData")},paintFormXObjectBegin:function(e,t){if(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(e)&&6===e.length&&this.transform.apply(this,e),this.baseTransform=this.ctx.mozCurrentTransform,t){var r=t[2]-t[0],n=t[3]-t[1];this.ctx.rect(t[0],t[1],r,n),this.clip(),this.endPath()}},paintFormXObjectEnd:function(){this.restore(),this.baseTransform=this.baseTransformStack.pop()},beginGroup:function(e){this.save();var t=this.ctx;e.isolated||(0,a.info)("TODO: Support non-isolated groups."),e.knockout&&(0,a.warn)("Knockout groups not supported.");var r=t.mozCurrentTransform;if(e.matrix&&t.transform.apply(t,e.matrix),!e.bbox)throw new Error("Bounding box is required.");var n=a.Util.getAxialAlignedBoundingBox(e.bbox,t.mozCurrentTransform),i=[0,0,t.canvas.width,t.canvas.height];n=a.Util.intersect(n,i)||[0,0,0,0];var o=Math.floor(n[0]),s=Math.floor(n[1]),u=Math.max(Math.ceil(n[2])-o,1),l=Math.max(Math.ceil(n[3])-s,1),c=1,d=1;u>4096&&(c=u/4096,u=4096),l>4096&&(d=l/4096,l=4096);var f="groupAt"+this.groupLevel;e.smask&&(f+="_smask_"+this.smaskCounter++%2);var p=this.cachedCanvases.getCanvas(f,u,l,!0),v=p.context;v.scale(1/c,1/d),v.translate(-o,-s),v.transform.apply(v,r),e.smask?this.smaskStack.push({canvas:p.canvas,context:v,offsetX:o,offsetY:s,scaleX:c,scaleY:d,subtype:e.smask.subtype,backdrop:e.smask.backdrop,transferMap:e.smask.transferMap||null,startTransformInverse:null}):(t.setTransform(1,0,0,1,0,0),t.translate(o,s),t.scale(c,d)),h(t,v),this.ctx=v,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(t),this.groupLevel++,this.current.activeSMask=null},endGroup:function(e){this.groupLevel--;var t=this.ctx;this.ctx=this.groupStack.pop(),void 0!==this.ctx.imageSmoothingEnabled?this.ctx.imageSmoothingEnabled=!1:this.ctx.mozImageSmoothingEnabled=!1,e.smask?this.tempSMask=this.smaskStack.pop():this.ctx.drawImage(t.canvas,0,0),this.restore()},beginAnnotations:function(){this.save(),this.baseTransform&&this.ctx.setTransform.apply(this.ctx,this.baseTransform)},endAnnotations:function(){this.restore()},beginAnnotation:function(e,t,r){if(this.save(),d(this.ctx),this.current=new c,Array.isArray(e)&&4===e.length){var n=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],n,i),this.clip(),this.endPath()}this.transform.apply(this,t),this.transform.apply(this,r)},endAnnotation:function(){this.restore()},paintJpegXObject:function(e,t,r){var n=this.processingType3?this.commonObjs.get(e):this.objs.get(e);if(!n)return void(0,a.warn)("Dependent image isn't ready yet");this.save();var i=this.ctx;if(i.scale(1/t,-1/r),i.drawImage(n,0,0,n.width,n.height,0,-r,t,r),this.imageLayer){var o=i.mozCurrentTransformInverse,s=this.getCanvasPosition(0,0);this.imageLayer.appendImage({objId:e,left:s[0],top:s[1],width:t/o[0],height:r/o[3]})}this.restore()},paintImageMaskXObject:function(e){var t=this.ctx,n=e.width,a=e.height,o=this.current.fillColor,s=this.current.patternFill,u=this.processingType3;if(u&&void 0===u.compiled&&(u.compiled=n<=1e3&&a<=1e3?i({data:e.data,width:n,height:a}):null),u&&u.compiled)return void u.compiled(t);var l=this.cachedCanvases.getCanvas("maskCanvas",n,a),c=l.context;c.save(),r(c,e),c.globalCompositeOperation="source-in",c.fillStyle=s?o.getPattern(c,this):o,c.fillRect(0,0,n,a),c.restore(),this.paintInlineImageXObject(l.canvas)},paintImageMaskXObjectRepeat:function(e,t,n,i){var a=e.width,o=e.height,s=this.current.fillColor,u=this.current.patternFill,l=this.cachedCanvases.getCanvas("maskCanvas",a,o),c=l.context;c.save(),r(c,e),c.globalCompositeOperation="source-in",c.fillStyle=u?s.getPattern(c,this):s,c.fillRect(0,0,a,o),c.restore();for(var h=this.ctx,d=0,f=i.length;d<f;d+=2)h.save(),h.transform(t,0,0,n,i[d],i[d+1]),h.scale(1,-1),h.drawImage(l.canvas,0,0,a,o,0,-1,1,1),h.restore()},paintImageMaskXObjectGroup:function(e){for(var t=this.ctx,n=this.current.fillColor,i=this.current.patternFill,a=0,o=e.length;a<o;a++){var s=e[a],u=s.width,l=s.height,c=this.cachedCanvases.getCanvas("maskCanvas",u,l),h=c.context;h.save(),r(h,s),h.globalCompositeOperation="source-in",h.fillStyle=i?n.getPattern(h,this):n,h.fillRect(0,0,u,l),h.restore(),t.save(),t.transform.apply(t,s.transform),t.scale(1,-1),t.drawImage(c.canvas,0,0,u,l,0,-1,1,1),t.restore()}},paintImageXObject:function(e){var t=this.processingType3?this.commonObjs.get(e):this.objs.get(e);if(!t)return void(0,a.warn)("Dependent image isn't ready yet");this.paintInlineImageXObject(t)},paintImageXObjectRepeat:function(e,t,r,n){var i=this.processingType3?this.commonObjs.get(e):this.objs.get(e);if(!i)return void(0,a.warn)("Dependent image isn't ready yet");for(var o=i.width,s=i.height,u=[],l=0,c=n.length;l<c;l+=2)u.push({transform:[t,0,0,r,n[l],n[l+1]],x:0,y:0,w:o,h:s});this.paintInlineImageXObjectGroup(i,u)},paintInlineImageXObject:function(e){var r=e.width,n=e.height,i=this.ctx;this.save(),i.scale(1/r,-1/n);var a,o,s=i.mozCurrentTransformInverse,u=s[0],l=s[1],c=Math.max(Math.sqrt(u*u+l*l),1),h=s[2],d=s[3],f=Math.max(Math.sqrt(h*h+d*d),1);if("function"==typeof HTMLElement&&e instanceof HTMLElement||!e.data)a=e;else{o=this.cachedCanvases.getCanvas("inlineImage",r,n);var p=o.context;t(p,e),a=o.canvas}for(var v=r,m=n,g="prescale1";c>2&&v>1||f>2&&m>1;){var y=v,b=m;c>2&&v>1&&(y=Math.ceil(v/2),c/=v/y),f>2&&m>1&&(b=Math.ceil(m/2),f/=m/b),o=this.cachedCanvases.getCanvas(g,y,b),p=o.context,p.clearRect(0,0,y,b),p.drawImage(a,0,0,v,m,0,0,y,b),a=o.canvas,v=y,m=b,g="prescale1"===g?"prescale2":"prescale1"}if(i.drawImage(a,0,0,v,m,0,-n,r,n),this.imageLayer){var _=this.getCanvasPosition(0,-n);this.imageLayer.appendImage({imgData:e,left:_[0],top:_[1],width:r/s[0],height:n/s[3]})}this.restore()},paintInlineImageXObjectGroup:function(e,r){var n=this.ctx,i=e.width,a=e.height,o=this.cachedCanvases.getCanvas("inlineImage",i,a);t(o.context,e);for(var s=0,u=r.length;s<u;s++){var l=r[s];if(n.save(),n.transform.apply(n,l.transform),n.scale(1,-1),n.drawImage(o.canvas,l.x,l.y,l.w,l.h,0,-1,1,1),this.imageLayer){var c=this.getCanvasPosition(l.x,l.y);this.imageLayer.appendImage({imgData:e,left:c[0],top:c[1],width:i,height:a})}n.restore()}},paintSolidColorImageMask:function(){this.ctx.fillRect(0,0,1,1)},paintXObject:function(){(0,a.warn)("Unsupported 'paintXObject' command.")},markPoint:function(e){},markPointProps:function(e,t){},beginMarkedContent:function(e){},beginMarkedContentProps:function(e,t){},endMarkedContent:function(){},beginCompat:function(){},endCompat:function(){},consumePath:function(){var e=this.ctx;this.pendingClip&&(this.pendingClip===A?e.clip("evenodd"):e.clip(),this.pendingClip=null),e.beginPath()},getSinglePixelWidth:function(e){if(null===this._cachedGetSinglePixelWidth){var t=this.ctx.mozCurrentTransformInverse;this._cachedGetSinglePixelWidth=Math.sqrt(Math.max(t[0]*t[0]+t[1]*t[1],t[2]*t[2]+t[3]*t[3]))}return this._cachedGetSinglePixelWidth},getCanvasPosition:function(e,t){var r=this.ctx.mozCurrentTransform;return[r[0]*e+r[2]*t+r[4],r[1]*e+r[3]*t+r[5]]}};for(var S in a.OPS)e.prototype[a.OPS[S]]=e.prototype[S];return e}();t.CanvasGraphics=h},function(e,t,r){"use strict";function n(e){var t=a[e[0]];if(!t)throw new Error("Unknown IR type: ".concat(e[0]));return t.fromIR(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.getShadingPatternFromIR=n,t.TilingPattern=void 0;var i=r(1),a={};a.RadialAxial={fromIR:function(e){var t=e[1],r=e[2],n=e[3],i=e[4],a=e[5],o=e[6];return{type:"Pattern",getPattern:function(e){var s;"axial"===t?s=e.createLinearGradient(n[0],n[1],i[0],i[1]):"radial"===t&&(s=e.createRadialGradient(n[0],n[1],a,i[0],i[1],o));for(var u=0,l=r.length;u<l;++u){var c=r[u];s.addColorStop(c[0],c[1])}return s}}}};var o=function(){function e(e,t,r,n,i,a,o,s){var u,l=t.coords,c=t.colors,h=e.data,d=4*e.width;l[r+1]>l[n+1]&&(u=r,r=n,n=u,u=a,a=o,o=u),l[n+1]>l[i+1]&&(u=n,n=i,i=u,u=o,o=s,s=u),l[r+1]>l[n+1]&&(u=r,r=n,n=u,u=a,a=o,o=u);var f=(l[r]+t.offsetX)*t.scaleX,p=(l[r+1]+t.offsetY)*t.scaleY,v=(l[n]+t.offsetX)*t.scaleX,m=(l[n+1]+t.offsetY)*t.scaleY,g=(l[i]+t.offsetX)*t.scaleX,y=(l[i+1]+t.offsetY)*t.scaleY;if(!(p>=y))for(var b,_,A,S,w,k,x,P,C,R=c[a],T=c[a+1],E=c[a+2],O=c[o],F=c[o+1],I=c[o+2],L=c[s],j=c[s+1],M=c[s+2],D=Math.round(p),N=Math.round(y),q=D;q<=N;q++){q<m?(C=q<p?0:p===m?1:(p-q)/(p-m),b=f-(f-v)*C,_=R-(R-O)*C,A=T-(T-F)*C,S=E-(E-I)*C):(C=q>y?1:m===y?0:(m-q)/(m-y),b=v-(v-g)*C,_=O-(O-L)*C,A=F-(F-j)*C,S=I-(I-M)*C),C=q<p?0:q>y?1:(p-q)/(p-y),w=f-(f-g)*C,k=R-(R-L)*C,x=T-(T-j)*C,P=E-(E-M)*C;for(var W=Math.round(Math.min(b,w)),U=Math.round(Math.max(b,w)),B=d*q+4*W,z=W;z<=U;z++)C=(b-z)/(b-w),C=C<0?0:C>1?1:C,h[B++]=_-(_-k)*C|0,h[B++]=A-(A-x)*C|0,h[B++]=S-(S-P)*C|0,h[B++]=255}}function t(t,r,n){var i,a,o=r.coords,s=r.colors;switch(r.type){case"lattice":var u=r.verticesPerRow,l=Math.floor(o.length/u)-1,c=u-1;for(i=0;i<l;i++)for(var h=i*u,d=0;d<c;d++,h++)e(t,n,o[h],o[h+1],o[h+u],s[h],s[h+1],s[h+u]),e(t,n,o[h+u+1],o[h+1],o[h+u],s[h+u+1],s[h+1],s[h+u]);break;case"triangles":for(i=0,a=o.length;i<a;i+=3)e(t,n,o[i],o[i+1],o[i+2],s[i],s[i+1],s[i+2]);break;default:throw new Error("illegal figure")}}function r(e,r,n,i,a,o,s,u){var l,c,h,d,f=Math.floor(e[0]),p=Math.floor(e[1]),v=Math.ceil(e[2])-f,m=Math.ceil(e[3])-p,g=Math.min(Math.ceil(Math.abs(v*r[0]*1.1)),3e3),y=Math.min(Math.ceil(Math.abs(m*r[1]*1.1)),3e3),b=v/g,_=m/y,A={coords:n,colors:i,offsetX:-f,offsetY:-p,scaleX:1/b,scaleY:1/_},S=g+4,w=y+4;if(u.isEnabled)l=u.drawFigures({width:g,height:y,backgroundColor:o,figures:a,context:A}),c=s.getCanvas("mesh",S,w,!1),c.context.drawImage(l,2,2),l=c.canvas;else{c=s.getCanvas("mesh",S,w,!1);var k=c.context,x=k.createImageData(g,y);if(o){var P=x.data;for(h=0,d=P.length;h<d;h+=4)P[h]=o[0],P[h+1]=o[1],P[h+2]=o[2],P[h+3]=255}for(h=0;h<a.length;h++)t(x,a[h],A);k.putImageData(x,2,2),l=c.canvas}return{canvas:l,offsetX:f-2*b,offsetY:p-2*_,scaleX:b,scaleY:_}}return r}();a.Mesh={fromIR:function(e){var t=e[2],r=e[3],n=e[4],a=e[5],s=e[6],u=e[8];return{type:"Pattern",getPattern:function(e,l,c){var h;if(c)h=i.Util.singularValueDecompose2dScale(e.mozCurrentTransform);else if(h=i.Util.singularValueDecompose2dScale(l.baseTransform),s){var d=i.Util.singularValueDecompose2dScale(s);h=[h[0]*d[0],h[1]*d[1]]}var f=o(a,h,t,r,n,c?null:u,l.cachedCanvases,l.webGLContext);return c||(e.setTransform.apply(e,l.baseTransform),s&&e.transform.apply(e,s)),e.translate(f.offsetX,f.offsetY),e.scale(f.scaleX,f.scaleY),e.createPattern(f.canvas,"no-repeat")}}}},a.Dummy={fromIR:function(){return{type:"Pattern",getPattern:function(){return"hotpink"}}}};var s=function(){function e(e,t,r,n,i){this.operatorList=e[2],this.matrix=e[3]||[1,0,0,1,0,0],this.bbox=e[4],this.xstep=e[5],this.ystep=e[6],this.paintType=e[7],this.tilingType=e[8],this.color=t,this.canvasGraphicsFactory=n,this.baseTransform=i,this.type="Pattern",this.ctx=r}var t={COLORED:1,UNCOLORED:2};return e.prototype={createPatternCanvas:function(e){var t=this.operatorList,r=this.bbox,n=this.xstep,a=this.ystep,o=this.paintType,s=this.tilingType,u=this.color,l=this.canvasGraphicsFactory;(0,i.info)("TilingType: "+s);var c=r[0],h=r[1],d=r[2],f=r[3],p=i.Util.singularValueDecompose2dScale(this.matrix),v=i.Util.singularValueDecompose2dScale(this.baseTransform),m=[p[0]*v[0],p[1]*v[1]],g=this.getSizeAndScale(n,this.ctx.canvas.width,m[0]),y=this.getSizeAndScale(a,this.ctx.canvas.height,m[1]),b=e.cachedCanvases.getCanvas("pattern",g.size,y.size,!0),_=b.context,A=l.createCanvasGraphics(_);return A.groupLevel=e.groupLevel,this.setFillAndStrokeStyleToContext(A,o,u),A.transform(g.scale,0,0,y.scale,0,0),A.transform(1,0,0,1,-c,-h),this.clipBbox(A,r,c,h,d,f),A.executeOperatorList(t),this.ctx.transform(1,0,0,1,c,h),this.ctx.scale(1/g.scale,1/y.scale),b.canvas},getSizeAndScale:function(e,t,r){e=Math.abs(e);var n=Math.max(3e3,t),i=Math.ceil(e*r);return i>=n?i=n:r=i/e,{scale:r,size:i}},clipBbox:function(e,t,r,n,i,a){if(Array.isArray(t)&&4===t.length){var o=i-r,s=a-n;e.ctx.rect(r,n,o,s),e.clip(),e.endPath()}},setFillAndStrokeStyleToContext:function(e,r,n){var a=e.ctx,o=e.current;switch(r){case t.COLORED:var s=this.ctx;a.fillStyle=s.fillStyle,a.strokeStyle=s.strokeStyle,o.fillColor=s.fillStyle,o.strokeColor=s.strokeStyle;break;case t.UNCOLORED:var u=i.Util.makeCssRgb(n[0],n[1],n[2]);a.fillStyle=u,a.strokeStyle=u,o.fillColor=u,o.strokeColor=u;break;default:throw new i.FormatError("Unsupported paint type: ".concat(r))}},getPattern:function(e,t){e=this.ctx,e.setTransform.apply(e,this.baseTransform),e.transform.apply(e,this.matrix);var r=this.createPatternCanvas(t);return e.createPattern(r,"repeat")}},e}();t.TilingPattern=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GlobalWorkerOptions=void 0;var n=Object.create(null);t.GlobalWorkerOptions=n,n.workerPort=void 0===n.workerPort?null:n.workerPort,n.workerSrc=void 0===n.workerSrc?"":n.workerSrc},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}function i(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function a(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){function o(e){i(u,n,a,o,s,"next",e)}function s(e){i(u,n,a,o,s,"throw",e)}var u=e.apply(t,r);o(void 0)})}}function o(e,t){return s.apply(this,arguments)}function s(){return s=a(f.default.mark(function e(t,r){var n,i=arguments;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=i.length>2&&void 0!==i[2]?i[2]:null,t){e.next=3;break}return e.abrupt("return",void 0);case 3:return e.abrupt("return",t.apply(n,r));case 4:case"end":return e.stop()}},e)})),s.apply(this,arguments)}function u(e){if("object"!==n(e))return e;switch(e.name){case"AbortException":return new p.AbortException(e.message);case"MissingPDFException":return new p.MissingPDFException(e.message);case"UnexpectedResponseException":return new p.UnexpectedResponseException(e.message,e.status);default:return new p.UnknownErrorException(e.message,e.details)}}function l(e){return!(e instanceof Error)||e instanceof p.AbortException||e instanceof p.MissingPDFException||e instanceof p.UnexpectedResponseException||e instanceof p.UnknownErrorException?e:new p.UnknownErrorException(e.message,e.toString())}function c(e,t,r){t?e.resolve():e.reject(r)}function h(e){return Promise.resolve(e).catch(function(){})}function d(e,t,r){var n=this;this.sourceName=e,this.targetName=t,this.comObj=r,this.callbackId=1,this.streamId=1,this.postMessageTransfers=!0,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null);var i=this.callbacksCapabilities=Object.create(null),a=this.actionHandler=Object.create(null);this._onComObjOnMessage=function(e){var t=e.data;if(t.targetName===n.sourceName)if(t.stream)n._processStreamMessage(t);else if(t.isReply){var o=t.callbackId;if(!(t.callbackId in i))throw new Error("Cannot resolve callback ".concat(o));var s=i[o];delete i[o],"error"in t?s.reject(u(t.error)):s.resolve(t.data)}else{if(!(t.action in a))throw new Error("Unknown action from worker: ".concat(t.action));var c=a[t.action];if(t.callbackId){var h=n.sourceName,d=t.sourceName;Promise.resolve().then(function(){return c[0].call(c[1],t.data)}).then(function(e){r.postMessage({sourceName:h,targetName:d,isReply:!0,callbackId:t.callbackId,data:e})},function(e){r.postMessage({sourceName:h,targetName:d,isReply:!0,callbackId:t.callbackId,error:l(e)})})}else t.streamId?n._createStreamSink(t):c[0].call(c[1],t.data)}},r.addEventListener("message",this._onComObjOnMessage)}Object.defineProperty(t,"__esModule",{value:!0}),t.MessageHandler=d;var f=function(e){return e&&e.__esModule?e:{default:e}}(r(148)),p=r(1);d.prototype={on:function(e,t,r){var n=this.actionHandler;if(n[e])throw new Error('There is already an actionName called "'.concat(e,'"'));n[e]=[t,r]},send:function(e,t,r){var n={sourceName:this.sourceName,targetName:this.targetName,action:e,data:t};this.postMessage(n,r)},sendWithPromise:function(e,t,r){var n=this.callbackId++,i={sourceName:this.sourceName,targetName:this.targetName,action:e,data:t,callbackId:n},a=(0,p.createPromiseCapability)();this.callbacksCapabilities[n]=a;try{this.postMessage(i,r)}catch(e){a.reject(e)}return a.promise},sendWithStream:function(e,t,r,n){var i=this,a=this.streamId++,o=this.sourceName,s=this.targetName;return new p.ReadableStream({start:function(r){var n=(0,p.createPromiseCapability)();return i.streamControllers[a]={controller:r,startCall:n,isClosed:!1},i.postMessage({sourceName:o,targetName:s,action:e,streamId:a,data:t,desiredSize:r.desiredSize}),n.promise},pull:function(e){var t=(0,p.createPromiseCapability)();return i.streamControllers[a].pullCall=t,i.postMessage({sourceName:o,targetName:s,stream:"pull",streamId:a,desiredSize:e.desiredSize}),t.promise},cancel:function(e){var t=(0,p.createPromiseCapability)();return i.streamControllers[a].cancelCall=t,i.streamControllers[a].isClosed=!0,i.postMessage({sourceName:o,targetName:s,stream:"cancel",reason:e,streamId:a}),t.promise}},r)},_createStreamSink:function(e){var t=this,r=this,n=this.actionHandler[e.action],i=e.streamId,a=e.desiredSize,s=this.sourceName,u=e.sourceName,l=(0,p.createPromiseCapability)(),c=function(e){var r=e.stream,n=e.chunk,a=e.transfers,o=e.success,l=e.reason;t.postMessage({sourceName:s,targetName:u,stream:r,streamId:i,chunk:n,success:o,reason:l},a)},h={enqueue:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2?arguments[2]:void 0;if(!this.isCancelled){var n=this.desiredSize;this.desiredSize-=t,n>0&&this.desiredSize<=0&&(this.sinkCapability=(0,p.createPromiseCapability)(),this.ready=this.sinkCapability.promise),c({stream:"enqueue",chunk:e,transfers:r})}},close:function(){this.isCancelled||(this.isCancelled=!0,c({stream:"close"}),delete r.streamSinks[i])},error:function(e){this.isCancelled||(this.isCancelled=!0,c({stream:"error",reason:e}))},sinkCapability:l,onPull:null,onCancel:null,isCancelled:!1,desiredSize:a,ready:null};h.sinkCapability.resolve(),h.ready=h.sinkCapability.promise,this.streamSinks[i]=h,o(n[0],[e.data,h],n[1]).then(function(){c({stream:"start_complete",success:!0})},function(e){c({stream:"start_complete",success:!1,reason:e})})},_processStreamMessage:function(e){var t=this,r=this.sourceName,n=e.sourceName,i=e.streamId,a=function(e){var a=e.stream,o=e.success,s=e.reason;t.comObj.postMessage({sourceName:r,targetName:n,stream:a,success:o,streamId:i,reason:s})},s=function(){Promise.all([t.streamControllers[e.streamId].startCall,t.streamControllers[e.streamId].pullCall,t.streamControllers[e.streamId].cancelCall].map(function(e){return e&&h(e.promise)})).then(function(){delete t.streamControllers[e.streamId]})};switch(e.stream){case"start_complete":c(this.streamControllers[e.streamId].startCall,e.success,u(e.reason));break;case"pull_complete":c(this.streamControllers[e.streamId].pullCall,e.success,u(e.reason));break;case"pull":if(!this.streamSinks[e.streamId]){a({stream:"pull_complete",success:!0});break}this.streamSinks[e.streamId].desiredSize<=0&&e.desiredSize>0&&this.streamSinks[e.streamId].sinkCapability.resolve(),this.streamSinks[e.streamId].desiredSize=e.desiredSize,o(this.streamSinks[e.streamId].onPull).then(function(){a({stream:"pull_complete",success:!0})
},function(e){a({stream:"pull_complete",success:!1,reason:e})});break;case"enqueue":(0,p.assert)(this.streamControllers[e.streamId],"enqueue should have stream controller"),this.streamControllers[e.streamId].isClosed||this.streamControllers[e.streamId].controller.enqueue(e.chunk);break;case"close":if((0,p.assert)(this.streamControllers[e.streamId],"close should have stream controller"),this.streamControllers[e.streamId].isClosed)break;this.streamControllers[e.streamId].isClosed=!0,this.streamControllers[e.streamId].controller.close(),s();break;case"error":(0,p.assert)(this.streamControllers[e.streamId],"error should have stream controller"),this.streamControllers[e.streamId].controller.error(u(e.reason)),s();break;case"cancel_complete":c(this.streamControllers[e.streamId].cancelCall,e.success,u(e.reason)),s();break;case"cancel":if(!this.streamSinks[e.streamId])break;o(this.streamSinks[e.streamId].onCancel,[u(e.reason)]).then(function(){a({stream:"cancel_complete",success:!0})},function(e){a({stream:"cancel_complete",success:!1,reason:e})}),this.streamSinks[e.streamId].sinkCapability.reject(u(e.reason)),this.streamSinks[e.streamId].isCancelled=!0,delete this.streamSinks[e.streamId];break;default:throw new Error("Unexpected stream case")}},postMessage:function(e,t){t&&this.postMessageTransfers?this.comObj.postMessage(e,t):this.comObj.postMessage(e)},destroy:function(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}},function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.Metadata=void 0;var o=r(1),s=r(159),u=function(){function e(t){n(this,e),(0,o.assert)("string"==typeof t,"Metadata: input is not a string"),t=this._repair(t);var r=new s.SimpleXMLParser,i=r.parseFromString(t);this._metadata=Object.create(null),i&&this._parse(i)}return a(e,[{key:"_repair",value:function(e){return e.replace(/^([^<]+)/,"").replace(/>\\376\\377([^<]+)/g,function(e,t){for(var r=t.replace(/\\([0-3])([0-7])([0-7])/g,function(e,t,r,n){return String.fromCharCode(64*t+8*r+1*n)}).replace(/&(amp|apos|gt|lt|quot);/g,function(e,t){switch(t){case"amp":return"&";case"apos":return"'";case"gt":return">";case"lt":return"<";case"quot":return'"'}throw new Error("_repair: ".concat(t," isn't defined."))}),n="",i=0,a=r.length;i<a;i+=2){var o=256*r.charCodeAt(i)+r.charCodeAt(i+1);n+=o>=32&&o<127&&60!==o&&62!==o&&38!==o?String.fromCharCode(o):"&#x"+(65536+o).toString(16).substring(1)+";"}return">"+n})}},{key:"_parse",value:function(e){var t=e.documentElement;if("rdf:rdf"!==t.nodeName.toLowerCase())for(t=t.firstChild;t&&"rdf:rdf"!==t.nodeName.toLowerCase();)t=t.nextSibling;var r=t?t.nodeName.toLowerCase():null;if(t&&"rdf:rdf"===r&&t.hasChildNodes())for(var n=t.childNodes,i=0,a=n.length;i<a;i++){var o=n[i];if("rdf:description"===o.nodeName.toLowerCase())for(var s=0,u=o.childNodes.length;s<u;s++)if("#text"!==o.childNodes[s].nodeName.toLowerCase()){var l=o.childNodes[s],c=l.nodeName.toLowerCase();this._metadata[c]=l.textContent.trim()}}}},{key:"get",value:function(e){var t=this._metadata[e];return void 0!==t?t:null}},{key:"getAll",value:function(){return this._metadata}},{key:"has",value:function(e){return void 0!==this._metadata[e]}}]),e}();t.Metadata=u},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}function i(e,t){return s(e)||o(e,t)||a()}function a(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}function o(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}function s(e){if(Array.isArray(e))return e}function u(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?l(e):t}function l(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function c(e,t,r){return(c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=h(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}})(e,t,r||e)}function h(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=d(e)););return e}function d(e){return(d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function f(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&p(e,t)}function p(e,t){return(p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function g(e,t,r){return t&&m(e.prototype,t),r&&m(e,r),e}function y(e,t){var r=e[t];return" "===r||"\n"===r||"\r"===r||"\t"===r}function b(e){for(var t=0,r=e.length;t<r;t++)if(!y(e,t))return!1;return!0}Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleXMLParser=void 0;var _={NoError:0,EndOfDocument:-1,UnterminatedCdat:-2,UnterminatedXmlDeclaration:-3,UnterminatedDoctypeDeclaration:-4,UnterminatedComment:-5,MalformedElement:-6,OutOfMemory:-7,UnterminatedAttributeValue:-8,UnterminatedElement:-9,ElementNeverBegun:-10},A=function(){function e(){v(this,e)}return g(e,[{key:"_resolveEntities",value:function(e){var t=this;return e.replace(/&([^;]+);/g,function(e,r){if("#x"===r.substring(0,2))return String.fromCharCode(parseInt(r.substring(2),16));if("#"===r.substring(0,1))return String.fromCharCode(parseInt(r.substring(1),10));switch(r){case"lt":return"<";case"gt":return">";case"amp":return"&";case"quot":return'"'}return t.onResolveEntity(r)})}},{key:"_parseContent",value:function(e,t){function r(){for(;i<e.length&&y(e,i);)++i}for(var n,i=t,a=[];i<e.length&&!y(e,i)&&">"!==e[i]&&"/"!==e[i];)++i;for(n=e.substring(t,i),r();i<e.length&&">"!==e[i]&&"/"!==e[i]&&"?"!==e[i];){r();for(var o="",s="";i<e.length&&!y(e,i)&&"="!==e[i];)o+=e[i],++i;if(r(),"="!==e[i])return null;++i,r();var u=e[i];if('"'!==u&&"'"!==u)return null;var l=e.indexOf(u,++i);if(l<0)return null;s=e.substring(i,l),a.push({name:o,value:this._resolveEntities(s)}),i=l+1,r()}return{name:n,attributes:a,parsed:i-t}}},{key:"_parseProcessingInstruction",value:function(e,t){for(var r,n,i=t;i<e.length&&!y(e,i)&&">"!==e[i]&&"/"!==e[i];)++i;r=e.substring(t,i),function(){for(;i<e.length&&y(e,i);)++i}();for(var a=i;i<e.length&&("?"!==e[i]||">"!==e[i+1]);)++i;return n=e.substring(a,i),{name:r,value:n,parsed:i-t}}},{key:"parseXml",value:function(e){for(var t=0;t<e.length;){var r=e[t],n=t;if("<"===r){++n;var i=e[n],a=void 0;switch(i){case"/":if(++n,(a=e.indexOf(">",n))<0)return void this.onError(_.UnterminatedElement);this.onEndElement(e.substring(n,a)),n=a+1;break;case"?":++n;var o=this._parseProcessingInstruction(e,n);if("?>"!==e.substring(n+o.parsed,n+o.parsed+2))return void this.onError(_.UnterminatedXmlDeclaration);this.onPi(o.name,o.value),n+=o.parsed+2;break;case"!":if("--"===e.substring(n+1,n+3)){if((a=e.indexOf("--\x3e",n+3))<0)return void this.onError(_.UnterminatedComment);this.onComment(e.substring(n+3,a)),n=a+3}else if("[CDATA["===e.substring(n+1,n+8)){if((a=e.indexOf("]]>",n+8))<0)return void this.onError(_.UnterminatedCdat);this.onCdata(e.substring(n+8,a)),n=a+3}else{if("DOCTYPE"!==e.substring(n+1,n+8))return void this.onError(_.MalformedElement);var s=e.indexOf("[",n+8),u=!1;if((a=e.indexOf(">",n+8))<0)return void this.onError(_.UnterminatedDoctypeDeclaration);if(s>0&&a>s){if((a=e.indexOf("]>",n+8))<0)return void this.onError(_.UnterminatedDoctypeDeclaration);u=!0}var l=e.substring(n+8,a+(u?1:0));this.onDoctype(l),n=a+(u?2:1)}break;default:var c=this._parseContent(e,n);if(null===c)return void this.onError(_.MalformedElement);var h=!1;if("/>"===e.substring(n+c.parsed,n+c.parsed+2))h=!0;else if(">"!==e.substring(n+c.parsed,n+c.parsed+1))return void this.onError(_.UnterminatedElement);this.onBeginElement(c.name,c.attributes,h),n+=c.parsed+(h?2:1)}}else{for(;n<e.length&&"<"!==e[n];)n++;var d=e.substring(t,n);this.onText(this._resolveEntities(d))}t=n}}},{key:"onResolveEntity",value:function(e){return"&".concat(e,";")}},{key:"onPi",value:function(e,t){}},{key:"onComment",value:function(e){}},{key:"onCdata",value:function(e){}},{key:"onDoctype",value:function(e){}},{key:"onText",value:function(e){}},{key:"onBeginElement",value:function(e,t,r){}},{key:"onEndElement",value:function(e){}},{key:"onError",value:function(e){}}]),e}(),S=function(){function e(t,r){v(this,e),this.nodeName=t,this.nodeValue=r,Object.defineProperty(this,"parentNode",{value:null,writable:!0})}return g(e,[{key:"hasChildNodes",value:function(){return this.childNodes&&this.childNodes.length>0}},{key:"firstChild",get:function(){return this.childNodes&&this.childNodes[0]}},{key:"nextSibling",get:function(){var e=this.parentNode.childNodes;if(e){var t=e.indexOf(this);if(-1!==t)return e[t+1]}}},{key:"textContent",get:function(){return this.childNodes?this.childNodes.map(function(e){return e.textContent}).join(""):this.nodeValue||""}}]),e}(),w=function(e){function t(){var e;return v(this,t),e=u(this,d(t).call(this)),e._currentFragment=null,e._stack=null,e._errorCode=_.NoError,e}return f(t,e),g(t,[{key:"parseFromString",value:function(e){if(this._currentFragment=[],this._stack=[],this._errorCode=_.NoError,this.parseXml(e),this._errorCode===_.NoError){var t=i(this._currentFragment,1),r=t[0];if(r)return{documentElement:r}}}},{key:"onResolveEntity",value:function(e){switch(e){case"apos":return"'"}return c(d(t.prototype),"onResolveEntity",this).call(this,e)}},{key:"onText",value:function(e){if(!b(e)){var t=new S("#text",e);this._currentFragment.push(t)}}},{key:"onCdata",value:function(e){var t=new S("#text",e);this._currentFragment.push(t)}},{key:"onBeginElement",value:function(e,t,r){var n=new S(e);n.childNodes=[],this._currentFragment.push(n),r||(this._stack.push(this._currentFragment),this._currentFragment=n.childNodes)}},{key:"onEndElement",value:function(e){this._currentFragment=this._stack.pop()||[];var t=this._currentFragment[this._currentFragment.length-1];if(t)for(var r=0,n=t.childNodes.length;r<n;r++)t.childNodes[r].parentNode=t}},{key:"onError",value:function(e){this._errorCode=e}}]),t}(A);t.SimpleXMLParser=w},function(e,t,r){"use strict";function n(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function i(e){return function(){var t=this,r=arguments;return new Promise(function(i,a){function o(e){n(u,i,a,o,s,"next",e)}function s(e){n(u,i,a,o,s,"throw",e)}var u=e.apply(t,r);o(void 0)})}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.PDFDataTransportStream=void 0;var u=function(e){return e&&e.__esModule?e:{default:e}}(r(148)),l=r(1),c=function(){function e(t,r){var n=this;a(this,e),(0,l.assert)(r),this._queuedChunks=[],this._progressiveDone=t.progressiveDone||!1;var i=t.initialData;if(i&&i.length>0){var o=new Uint8Array(i).buffer;this._queuedChunks.push(o)}this._pdfDataRangeTransport=r,this._isStreamingSupported=!t.disableStream,this._isRangeSupported=!t.disableRange,this._contentLength=t.length,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener(function(e,t){n._onReceiveData({begin:e,chunk:t})}),this._pdfDataRangeTransport.addProgressListener(function(e,t){n._onProgress({loaded:e,total:t})}),this._pdfDataRangeTransport.addProgressiveReadListener(function(e){n._onReceiveData({chunk:e})}),this._pdfDataRangeTransport.addProgressiveDoneListener(function(){n._onProgressiveDone()}),this._pdfDataRangeTransport.transportReady()}return s(e,[{key:"_onReceiveData",value:function(e){var t=new Uint8Array(e.chunk).buffer;if(void 0===e.begin)this._fullRequestReader?this._fullRequestReader._enqueue(t):this._queuedChunks.push(t);else{var r=this._rangeReaders.some(function(r){return r._begin===e.begin&&(r._enqueue(t),!0)});(0,l.assert)(r)}}},{key:"_onProgress",value:function(e){if(void 0===e.total){var t=this._rangeReaders[0];t&&t.onProgress&&t.onProgress({loaded:e.loaded})}else{var r=this._fullRequestReader;r&&r.onProgress&&r.onProgress({loaded:e.loaded,total:e.total})}}},{key:"_onProgressiveDone",value:function(){this._fullRequestReader&&this._fullRequestReader.progressiveDone(),this._progressiveDone=!0}},{key:"_removeRangeReader",value:function(e){var t=this._rangeReaders.indexOf(e);t>=0&&this._rangeReaders.splice(t,1)}},{key:"getFullReader",value:function(){(0,l.assert)(!this._fullRequestReader);var e=this._queuedChunks;return this._queuedChunks=null,new h(this,e,this._progressiveDone)}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var r=new d(this,e,t);return this._pdfDataRangeTransport.requestDataRange(e,t),this._rangeReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeReaders.slice(0).forEach(function(t){t.cancel(e)}),this._pdfDataRangeTransport.abort()}},{key:"_progressiveDataLength",get:function(){return this._fullRequestReader?this._fullRequestReader._loaded:0}}]),e}();t.PDFDataTransportStream=c;var h=function(){function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];a(this,e),this._stream=t,this._done=n||!1,this._filename=null,this._queuedChunks=r||[],this._loaded=0;var i=!0,o=!1,s=void 0;try{for(var u,l=this._queuedChunks[Symbol.iterator]();!(i=(u=l.next()).done);i=!0){var c=u.value;this._loaded+=c.byteLength}}catch(e){o=!0,s=e}finally{try{i||null==l.return||l.return()}finally{if(o)throw s}}this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}return s(e,[{key:"_enqueue",value:function(e){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunks.push(e);this._loaded+=e.byteLength}}},{key:"read",value:function(){function e(){return t.apply(this,arguments)}var t=i(u.default.mark(function e(){var t,r;return u.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(this._queuedChunks.length>0)){e.next=3;break}return t=this._queuedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 3:if(!this._done){e.next=5;break}return e.abrupt("return",{value:void 0,done:!0});case 5:return r=(0,l.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 8:case"end":return e.stop()}},e,this)}));return e}()},{key:"cancel",value:function(e){this._done=!0,this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[]}},{key:"progressiveDone",value:function(){this._done||(this._done=!0)}},{key:"headersReady",get:function(){return this._headersReady}},{key:"filename",get:function(){return this._filename}},{key:"isRangeSupported",get:function(){return this._stream._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._stream._isStreamingSupported}},{key:"contentLength",get:function(){return this._stream._contentLength}}]),e}(),d=function(){function e(t,r,n){a(this,e),this._stream=t,this._begin=r,this._end=n,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}return s(e,[{key:"_enqueue",value:function(e){if(!this._done){if(0===this._requests.length)this._queuedChunk=e;else{this._requests.shift().resolve({value:e,done:!1}),this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[]}this._done=!0,this._stream._removeRangeReader(this)}}},{key:"read",value:function(){function e(){return t.apply(this,arguments)}var t=i(u.default.mark(function e(){var t,r;return u.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._queuedChunk){e.next=4;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 4:if(!this._done){e.next=6;break}return e.abrupt("return",{value:void 0,done:!0});case 6:return r=(0,l.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 9:case"end":return e.stop()}},e,this)}));return e}()},{key:"cancel",value:function(e){this._done=!0,this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[],this._stream._removeRangeReader(this)}},{key:"isStreamingSupported",get:function(){return!1}}]),e}()},function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.WebGLContext=void 0;var o=r(1),s=function(){function e(t){var r=t.enable,i=void 0!==r&&r;n(this,e),this._enabled=!0===i}return a(e,[{key:"composeSMask",value:function(e){var t=e.layer,r=e.mask,n=e.properties;return u.composeSMask(t,r,n)}},{key:"drawFigures",value:function(e){var t=e.width,r=e.height,n=e.backgroundColor,i=e.figures,a=e.context;return u.drawFigures(t,r,n,i,a)}},{key:"clear",value:function(){u.cleanup()}},{key:"isEnabled",get:function(){var e=this._enabled;return e&&(e=u.tryInitGL()),(0,o.shadow)(this,"isEnabled",e)}}]),e}();t.WebGLContext=s;var u=function(){function e(e,t,r){var n=e.createShader(r);if(e.shaderSource(n,t),e.compileShader(n),!e.getShaderParameter(n,e.COMPILE_STATUS)){var i=e.getShaderInfoLog(n);throw new Error("Error during shader compilation: "+i)}return n}function t(t,r){return e(t,r,t.VERTEX_SHADER)}function r(t,r){return e(t,r,t.FRAGMENT_SHADER)}function n(e,t){for(var r=e.createProgram(),n=0,i=t.length;n<i;++n)e.attachShader(r,t[n]);if(e.linkProgram(r),!e.getProgramParameter(r,e.LINK_STATUS)){var a=e.getProgramInfoLog(r);throw new Error("Error during program linking: "+a)}return r}function i(e,t,r){e.activeTexture(r);var n=e.createTexture();return e.bindTexture(e.TEXTURE_2D,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t),n}function a(){c||(h=document.createElement("canvas"),c=h.getContext("webgl",{premultipliedalpha:!1}))}function o(){var e,i;a(),e=h,h=null,i=c,c=null;var o=t(i,d),s=r(i,f),u=n(i,[o,s]);i.useProgram(u);var l={};l.gl=i,l.canvas=e,l.resolutionLocation=i.getUniformLocation(u,"u_resolution"),l.positionLocation=i.getAttribLocation(u,"a_position"),l.backdropLocation=i.getUniformLocation(u,"u_backdrop"),l.subtypeLocation=i.getUniformLocation(u,"u_subtype");var v=i.getAttribLocation(u,"a_texCoord"),m=i.getUniformLocation(u,"u_image"),g=i.getUniformLocation(u,"u_mask"),y=i.createBuffer();i.bindBuffer(i.ARRAY_BUFFER,y),i.bufferData(i.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),i.STATIC_DRAW),i.enableVertexAttribArray(v),i.vertexAttribPointer(v,2,i.FLOAT,!1,0,0),i.uniform1i(m,0),i.uniform1i(g,1),p=l}function s(e,t,r){var n=e.width,a=e.height;p||o();var s=p,u=s.canvas,l=s.gl;u.width=n,u.height=a,l.viewport(0,0,l.drawingBufferWidth,l.drawingBufferHeight),l.uniform2f(s.resolutionLocation,n,a),r.backdrop?l.uniform4f(s.resolutionLocation,r.backdrop[0],r.backdrop[1],r.backdrop[2],1):l.uniform4f(s.resolutionLocation,0,0,0,0),l.uniform1i(s.subtypeLocation,"Luminosity"===r.subtype?1:0);var c=i(l,e,l.TEXTURE0),h=i(l,t,l.TEXTURE1),d=l.createBuffer();return l.bindBuffer(l.ARRAY_BUFFER,d),l.bufferData(l.ARRAY_BUFFER,new Float32Array([0,0,n,0,0,a,0,a,n,0,n,a]),l.STATIC_DRAW),l.enableVertexAttribArray(s.positionLocation),l.vertexAttribPointer(s.positionLocation,2,l.FLOAT,!1,0,0),l.clearColor(0,0,0,0),l.enable(l.BLEND),l.blendFunc(l.ONE,l.ONE_MINUS_SRC_ALPHA),l.clear(l.COLOR_BUFFER_BIT),l.drawArrays(l.TRIANGLES,0,6),l.flush(),l.deleteTexture(c),l.deleteTexture(h),l.deleteBuffer(d),u}function u(){var e,i;a(),e=h,h=null,i=c,c=null;var o=t(i,v),s=r(i,m),u=n(i,[o,s]);i.useProgram(u);var l={};l.gl=i,l.canvas=e,l.resolutionLocation=i.getUniformLocation(u,"u_resolution"),l.scaleLocation=i.getUniformLocation(u,"u_scale"),l.offsetLocation=i.getUniformLocation(u,"u_offset"),l.positionLocation=i.getAttribLocation(u,"a_position"),l.colorLocation=i.getAttribLocation(u,"a_color"),g=l}function l(e,t,r,n,i){g||u();var a=g,o=a.canvas,s=a.gl;o.width=e,o.height=t,s.viewport(0,0,s.drawingBufferWidth,s.drawingBufferHeight),s.uniform2f(a.resolutionLocation,e,t);var l,c,h,d=0;for(l=0,c=n.length;l<c;l++)switch(n[l].type){case"lattice":h=n[l].coords.length/n[l].verticesPerRow|0,d+=(h-1)*(n[l].verticesPerRow-1)*6;break;case"triangles":d+=n[l].coords.length}var f=new Float32Array(2*d),p=new Uint8Array(3*d),v=i.coords,m=i.colors,y=0,b=0;for(l=0,c=n.length;l<c;l++){var _=n[l],A=_.coords,S=_.colors;switch(_.type){case"lattice":var w=_.verticesPerRow;h=A.length/w|0;for(var k=1;k<h;k++)for(var x=k*w+1,P=1;P<w;P++,x++)f[y]=v[A[x-w-1]],f[y+1]=v[A[x-w-1]+1],f[y+2]=v[A[x-w]],f[y+3]=v[A[x-w]+1],f[y+4]=v[A[x-1]],f[y+5]=v[A[x-1]+1],p[b]=m[S[x-w-1]],p[b+1]=m[S[x-w-1]+1],p[b+2]=m[S[x-w-1]+2],p[b+3]=m[S[x-w]],p[b+4]=m[S[x-w]+1],p[b+5]=m[S[x-w]+2],p[b+6]=m[S[x-1]],p[b+7]=m[S[x-1]+1],p[b+8]=m[S[x-1]+2],f[y+6]=f[y+2],f[y+7]=f[y+3],f[y+8]=f[y+4],f[y+9]=f[y+5],f[y+10]=v[A[x]],f[y+11]=v[A[x]+1],p[b+9]=p[b+3],p[b+10]=p[b+4],p[b+11]=p[b+5],p[b+12]=p[b+6],p[b+13]=p[b+7],p[b+14]=p[b+8],p[b+15]=m[S[x]],p[b+16]=m[S[x]+1],p[b+17]=m[S[x]+2],y+=12,b+=18;break;case"triangles":for(var C=0,R=A.length;C<R;C++)f[y]=v[A[C]],f[y+1]=v[A[C]+1],p[b]=m[S[C]],p[b+1]=m[S[C]+1],p[b+2]=m[S[C]+2],y+=2,b+=3}}r?s.clearColor(r[0]/255,r[1]/255,r[2]/255,1):s.clearColor(0,0,0,0),s.clear(s.COLOR_BUFFER_BIT);var T=s.createBuffer();s.bindBuffer(s.ARRAY_BUFFER,T),s.bufferData(s.ARRAY_BUFFER,f,s.STATIC_DRAW),s.enableVertexAttribArray(a.positionLocation),s.vertexAttribPointer(a.positionLocation,2,s.FLOAT,!1,0,0);var E=s.createBuffer();return s.bindBuffer(s.ARRAY_BUFFER,E),s.bufferData(s.ARRAY_BUFFER,p,s.STATIC_DRAW),s.enableVertexAttribArray(a.colorLocation),s.vertexAttribPointer(a.colorLocation,3,s.UNSIGNED_BYTE,!1,0,0),s.uniform2f(a.scaleLocation,i.scaleX,i.scaleY),s.uniform2f(a.offsetLocation,i.offsetX,i.offsetY),s.drawArrays(s.TRIANGLES,0,d),s.flush(),s.deleteBuffer(T),s.deleteBuffer(E),o}var c,h,d="  attribute vec2 a_position;                                      attribute vec2 a_texCoord;                                                                                                      uniform vec2 u_resolution;                                                                                                      varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec2 clipSpace = (a_position / u_resolution) * 2.0 - 1.0;       gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_texCoord = a_texCoord;                                      }                                                             ",f="  precision mediump float;                                                                                                        uniform vec4 u_backdrop;                                        uniform int u_subtype;                                          uniform sampler2D u_image;                                      uniform sampler2D u_mask;                                                                                                       varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec4 imageColor = texture2D(u_image, v_texCoord);               vec4 maskColor = texture2D(u_mask, v_texCoord);                 if (u_backdrop.a > 0.0) {                                         maskColor.rgb = maskColor.rgb * maskColor.a +                                   u_backdrop.rgb * (1.0 - maskColor.a);         }                                                               float lum;                                                      if (u_subtype == 0) {                                             lum = maskColor.a;                                            } else {                                                          lum = maskColor.r * 0.3 + maskColor.g * 0.59 +                        maskColor.b * 0.11;                                     }                                                               imageColor.a *= lum;                                            imageColor.rgb *= imageColor.a;                                 gl_FragColor = imageColor;                                    }                                                             ",p=null,v="  attribute vec2 a_position;                                      attribute vec3 a_color;                                                                                                         uniform vec2 u_resolution;                                      uniform vec2 u_scale;                                           uniform vec2 u_offset;                                                                                                          varying vec4 v_color;                                                                                                           void main() {                                                     vec2 position = (a_position + u_offset) * u_scale;              vec2 clipSpace = (position / u_resolution) * 2.0 - 1.0;         gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_color = vec4(a_color / 255.0, 1.0);                         }                                                             ",m="  precision mediump float;                                                                                                        varying vec4 v_color;                                                                                                           void main() {                                                     gl_FragColor = v_color;                                       }                                                             ",g=null;return{tryInitGL:function(){try{return a(),!!c}catch(e){}return!1},composeSMask:s,drawFigures:l,cleanup:function(){p&&p.canvas&&(p.canvas.width=0,p.canvas.height=0),g&&g.canvas&&(g.canvas.width=0,g.canvas.height=0),p=null,g=null}}}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.renderTextLayer=void 0;var n=r(1),i=function(e){return e&&e.__esModule?e:{default:e}}(r(3)),a=function(){function e(e){return!h.test(e)}function t(t,r,i){var a=document.createElement("span"),o={style:null,angle:0,canvasWidth:0,isWhitespace:!1,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1};if(t._textDivs.push(a),e(r.str))return o.isWhitespace=!0,void t._textDivProperties.set(a,o);var s=n.Util.transform(t._viewport.transform,r.transform),u=Math.atan2(s[1],s[0]),l=i[r.fontName];l.vertical&&(u+=Math.PI/2);var c=Math.sqrt(s[2]*s[2]+s[3]*s[3]),h=c;l.ascent?h=l.ascent*h:l.descent&&(h=(1+l.descent)*h);var f,p;if(0===u?(f=s[4],p=s[5]-h):(f=s[4]+h*Math.sin(u),p=s[5]-h*Math.cos(u)),d[1]=f,d[3]=p,d[5]=c,d[7]=l.fontFamily,o.style=d.join(""),a.setAttribute("style",o.style),a.textContent=r.str,t._fontInspectorEnabled&&(a.dataset.fontName=r.fontName),0!==u&&(o.angle=u*(180/Math.PI)),r.str.length>1&&(l.vertical?o.canvasWidth=r.height*t._viewport.scale:o.canvasWidth=r.width*t._viewport.scale),t._textDivProperties.set(a,o),t._textContentStream&&t._layoutText(a),t._enhanceTextSelection){var v=1,m=0;0!==u&&(v=Math.cos(u),m=Math.sin(u));var g,y,b=(l.vertical?r.height:r.width)*t._viewport.scale,_=c;0!==u?(g=[v,m,-m,v,f,p],y=n.Util.getAxialAlignedBoundingBox([0,0,b,_],g)):y=[f,p,f+b,p+_],t._bounds.push({left:y[0],top:y[1],right:y[2],bottom:y[3],div:a,size:[b,_],m:g})}}function r(e){if(!e._canceled){var t=e._textDivs,r=e._capability,n=t.length;if(n>c)return e._renderingDone=!0,void r.resolve();if(!e._textContentStream)for(var i=0;i<n;i++)e._layoutText(t[i]);e._renderingDone=!0,r.resolve()}}function a(e){for(var t=e._bounds,r=e._viewport,i=o(r.width,r.height,t),a=0;a<i.length;a++){var s=t[a].div,u=e._textDivProperties.get(s);if(0!==u.angle){var l=i[a],c=t[a],h=c.m,d=h[0],f=h[1],p=[[0,0],[0,c.size[1]],[c.size[0],0],c.size],v=new Float64Array(64);p.forEach(function(e,t){var r=n.Util.applyTransform(e,h);v[t+0]=d&&(l.left-r[0])/d,v[t+4]=f&&(l.top-r[1])/f,v[t+8]=d&&(l.right-r[0])/d,v[t+12]=f&&(l.bottom-r[1])/f,v[t+16]=f&&(l.left-r[0])/-f,v[t+20]=d&&(l.top-r[1])/d,v[t+24]=f&&(l.right-r[0])/-f,v[t+28]=d&&(l.bottom-r[1])/d,v[t+32]=d&&(l.left-r[0])/-d,v[t+36]=f&&(l.top-r[1])/-f,v[t+40]=d&&(l.right-r[0])/-d,v[t+44]=f&&(l.bottom-r[1])/-f,v[t+48]=f&&(l.left-r[0])/f,v[t+52]=d&&(l.top-r[1])/-d,v[t+56]=f&&(l.right-r[0])/f,v[t+60]=d&&(l.bottom-r[1])/-d});var m=function(e,t,r){for(var n=0,i=0;i<r;i++){var a=e[t++];a>0&&(n=n?Math.min(a,n):a)}return n},g=1+Math.min(Math.abs(d),Math.abs(f));u.paddingLeft=m(v,32,16)/g,u.paddingTop=m(v,48,16)/g,u.paddingRight=m(v,0,16)/g,u.paddingBottom=m(v,16,16)/g,e._textDivProperties.set(s,u)}else u.paddingLeft=t[a].left-i[a].left,u.paddingTop=t[a].top-i[a].top,u.paddingRight=i[a].right-t[a].right,u.paddingBottom=i[a].bottom-t[a].bottom,e._textDivProperties.set(s,u)}}function o(e,t,r){var n=r.map(function(e,t){return{x1:e.left,y1:e.top,x2:e.right,y2:e.bottom,index:t,x1New:void 0,x2New:void 0}});s(e,n);var i=new Array(r.length);return n.forEach(function(e){var t=e.index;i[t]={left:e.x1New,top:0,right:e.x2New,bottom:0}}),r.map(function(t,r){var a=i[r],o=n[r];o.x1=t.top,o.y1=e-a.right,o.x2=t.bottom,o.y2=e-a.left,o.index=r,o.x1New=void 0,o.x2New=void 0}),s(t,n),n.forEach(function(e){var t=e.index;i[t].top=e.x1New,i[t].bottom=e.x2New}),i}function s(e,t){t.sort(function(e,t){return e.x1-t.x1||e.index-t.index});var r={x1:-1/0,y1:-1/0,x2:0,y2:1/0,index:-1,x1New:0,x2New:0},n=[{start:-1/0,end:1/0,boundary:r}];t.forEach(function(e){for(var t=0;t<n.length&&n[t].end<=e.y1;)t++;for(var r=n.length-1;r>=0&&n[r].start>=e.y2;)r--;var i,a,o,s,u=-1/0;for(o=t;o<=r;o++){i=n[o],a=i.boundary;var l;l=a.x2>e.x1?a.index>e.index?a.x1New:e.x1:void 0===a.x2New?(a.x2+e.x1)/2:a.x2New,l>u&&(u=l)}for(e.x1New=u,o=t;o<=r;o++)i=n[o],a=i.boundary,void 0===a.x2New?a.x2>e.x1?a.index>e.index&&(a.x2New=a.x2):a.x2New=u:a.x2New>u&&(a.x2New=Math.max(u,a.x2));var c=[],h=null;for(o=t;o<=r;o++){i=n[o],a=i.boundary;var d=a.x2>e.x2?a:e;h===d?c[c.length-1].end=i.end:(c.push({start:i.start,end:i.end,boundary:d}),h=d)}for(n[t].start<e.y1&&(c[0].start=e.y1,c.unshift({start:n[t].start,end:e.y1,boundary:n[t].boundary})),e.y2<n[r].end&&(c[c.length-1].end=e.y2,c.push({start:e.y2,end:n[r].end,boundary:n[r].boundary})),o=t;o<=r;o++)if(i=n[o],a=i.boundary,void 0===a.x2New){var f=!1
;for(s=t-1;!f&&s>=0&&n[s].start>=a.y1;s--)f=n[s].boundary===a;for(s=r+1;!f&&s<n.length&&n[s].end<=a.y2;s++)f=n[s].boundary===a;for(s=0;!f&&s<c.length;s++)f=c[s].boundary===a;f||(a.x2New=u)}Array.prototype.splice.apply(n,[t,r-t+1].concat(c))}),n.forEach(function(t){var r=t.boundary;void 0===r.x2New&&(r.x2New=Math.max(e,r.x2))})}function u(e){var t=this,r=e.textContent,a=e.textContentStream,o=e.container,s=e.viewport,u=e.textDivs,l=e.textContentItemsStr,c=e.enhanceTextSelection;this._textContent=r,this._textContentStream=a,this._container=o,this._viewport=s,this._textDivs=u||[],this._textContentItemsStr=l||[],this._enhanceTextSelection=!!c,this._fontInspectorEnabled=!(!i.default.FontInspector||!i.default.FontInspector.enabled),this._reader=null,this._layoutTextLastFontSize=null,this._layoutTextLastFontFamily=null,this._layoutTextCtx=null,this._textDivProperties=new WeakMap,this._renderingDone=!1,this._canceled=!1,this._capability=(0,n.createPromiseCapability)(),this._renderTimer=null,this._bounds=[],this._capability.promise.finally(function(){t._layoutTextCtx&&(t._layoutTextCtx.canvas.width=0,t._layoutTextCtx.canvas.height=0,t._layoutTextCtx=null)})}function l(e){var t=new u({textContent:e.textContent,textContentStream:e.textContentStream,container:e.container,viewport:e.viewport,textDivs:e.textDivs,textContentItemsStr:e.textContentItemsStr,enhanceTextSelection:e.enhanceTextSelection});return t._render(e.timeout),t}var c=1e5,h=/\S/,d=["left: ",0,"px; top: ",0,"px; font-size: ",0,"px; font-family: ","",";"];return u.prototype={get promise(){return this._capability.promise},cancel:function(){this._canceled=!0,this._reader&&(this._reader.cancel(new n.AbortException("TextLayer task cancelled.")),this._reader=null),null!==this._renderTimer&&(clearTimeout(this._renderTimer),this._renderTimer=null),this._capability.reject(new Error("TextLayer task cancelled."))},_processItems:function(e,r){for(var n=0,i=e.length;n<i;n++)this._textContentItemsStr.push(e[n].str),t(this,e[n],r)},_layoutText:function(e){var t=this._container,r=this._textDivProperties.get(e);if(!r.isWhitespace){var n=e.style.fontSize,i=e.style.fontFamily;n===this._layoutTextLastFontSize&&i===this._layoutTextLastFontFamily||(this._layoutTextCtx.font=n+" "+i,this._layoutTextLastFontSize=n,this._layoutTextLastFontFamily=i);var a=this._layoutTextCtx.measureText(e.textContent).width,o="";0!==r.canvasWidth&&a>0&&(r.scale=r.canvasWidth/a,o="scaleX(".concat(r.scale,")")),0!==r.angle&&(o="rotate(".concat(r.angle,"deg) ").concat(o)),o.length>0&&(r.originalTransform=o,e.style.transform=o),this._textDivProperties.set(e,r),t.appendChild(e)}},_render:function(e){var t=this,i=(0,n.createPromiseCapability)(),a=Object.create(null),o=document.createElement("canvas");if(o.mozOpaque=!0,this._layoutTextCtx=o.getContext("2d",{alpha:!1}),this._textContent){var s=this._textContent.items,u=this._textContent.styles;this._processItems(s,u),i.resolve()}else{if(!this._textContentStream)throw new Error('Neither "textContent" nor "textContentStream" parameters specified.');this._reader=this._textContentStream.getReader(),function e(){t._reader.read().then(function(r){var n=r.value;if(r.done)return void i.resolve();Object.assign(a,n.styles),t._processItems(n.items,a),e()},i.reject)}()}i.promise.then(function(){a=null,e?t._renderTimer=setTimeout(function(){r(t),t._renderTimer=null},e):r(t)},this._capability.reject)},expandTextDivs:function(e){if(this._enhanceTextSelection&&this._renderingDone){null!==this._bounds&&(a(this),this._bounds=null);for(var t=0,r=this._textDivs.length;t<r;t++){var n=this._textDivs[t],i=this._textDivProperties.get(n);if(!i.isWhitespace)if(e){var o="",s="";1!==i.scale&&(o="scaleX("+i.scale+")"),0!==i.angle&&(o="rotate("+i.angle+"deg) "+o),0!==i.paddingLeft&&(s+=" padding-left: "+i.paddingLeft/i.scale+"px;",o+=" translateX("+-i.paddingLeft/i.scale+"px)"),0!==i.paddingTop&&(s+=" padding-top: "+i.paddingTop+"px;",o+=" translateY("+-i.paddingTop+"px)"),0!==i.paddingRight&&(s+=" padding-right: "+i.paddingRight/i.scale+"px;"),0!==i.paddingBottom&&(s+=" padding-bottom: "+i.paddingBottom+"px;"),""!==s&&n.setAttribute("style",i.style+s),""!==o&&(n.style.transform=o)}else n.style.padding=0,n.style.transform=i.originalTransform||""}}}},l}();t.renderTextLayer=a},function(e,t,r){"use strict";function n(e,t,r){return(n="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=i(e,t);if(n){var a=Object.getOwnPropertyDescriptor(n,t);return a.get?a.get.call(r):a.value}})(e,t,r||e)}function i(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=u(e)););return e}function a(e){return(a="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}function o(e,t){return!t||"object"!==a(t)&&"function"!=typeof t?s(e):t}function s(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function u(e){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&c(e,t)}function c(e,t){return(c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function f(e,t,r){return t&&d(e.prototype,t),r&&d(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationLayer=void 0;var p=r(151),v=r(1),m=function(){function e(){h(this,e)}return f(e,null,[{key:"create",value:function(e){switch(e.data.annotationType){case v.AnnotationType.LINK:return new y(e);case v.AnnotationType.TEXT:return new b(e);case v.AnnotationType.WIDGET:switch(e.data.fieldType){case"Tx":return new A(e);case"Btn":return e.data.radioButton?new w(e):e.data.checkBox?new S(e):new k(e);case"Ch":return new x(e)}return new _(e);case v.AnnotationType.POPUP:return new P(e);case v.AnnotationType.FREETEXT:return new R(e);case v.AnnotationType.LINE:return new T(e);case v.AnnotationType.SQUARE:return new E(e);case v.AnnotationType.CIRCLE:return new O(e);case v.AnnotationType.POLYLINE:return new F(e);case v.AnnotationType.CARET:return new L(e);case v.AnnotationType.INK:return new j(e);case v.AnnotationType.POLYGON:return new I(e);case v.AnnotationType.HIGHLIGHT:return new M(e);case v.AnnotationType.UNDERLINE:return new D(e);case v.AnnotationType.SQUIGGLY:return new N(e);case v.AnnotationType.STRIKEOUT:return new q(e);case v.AnnotationType.STAMP:return new W(e);case v.AnnotationType.FILEATTACHMENT:return new U(e);default:return new g(e)}}}]),e}(),g=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];h(this,e),this.isRenderable=r,this.data=t.data,this.layer=t.layer,this.page=t.page,this.viewport=t.viewport,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderInteractiveForms=t.renderInteractiveForms,this.svgFactory=t.svgFactory,r&&(this.container=this._createContainer(n))}return f(e,[{key:"_createContainer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.data,r=this.page,n=this.viewport,i=document.createElement("section"),a=t.rect[2]-t.rect[0],o=t.rect[3]-t.rect[1];i.setAttribute("data-annotation-id",t.id);var s=v.Util.normalizeRect([t.rect[0],r.view[3]-t.rect[1]+r.view[1],t.rect[2],r.view[3]-t.rect[3]+r.view[1]]);if(i.style.transform="matrix("+n.transform.join(",")+")",i.style.transformOrigin=-s[0]+"px "+-s[1]+"px",!e&&t.borderStyle.width>0){i.style.borderWidth=t.borderStyle.width+"px",t.borderStyle.style!==v.AnnotationBorderStyleType.UNDERLINE&&(a-=2*t.borderStyle.width,o-=2*t.borderStyle.width);var u=t.borderStyle.horizontalCornerRadius,l=t.borderStyle.verticalCornerRadius;if(u>0||l>0){var c=u+"px / "+l+"px";i.style.borderRadius=c}switch(t.borderStyle.style){case v.AnnotationBorderStyleType.SOLID:i.style.borderStyle="solid";break;case v.AnnotationBorderStyleType.DASHED:i.style.borderStyle="dashed";break;case v.AnnotationBorderStyleType.BEVELED:(0,v.warn)("Unimplemented border style: beveled");break;case v.AnnotationBorderStyleType.INSET:(0,v.warn)("Unimplemented border style: inset");break;case v.AnnotationBorderStyleType.UNDERLINE:i.style.borderBottomStyle="solid"}t.color?i.style.borderColor=v.Util.makeCssRgb(0|t.color[0],0|t.color[1],0|t.color[2]):i.style.borderWidth=0}return i.style.left=s[0]+"px",i.style.top=s[1]+"px",i.style.width=a+"px",i.style.height=o+"px",i}},{key:"_createPopup",value:function(e,t,r){t||(t=document.createElement("div"),t.style.height=e.style.height,t.style.width=e.style.width,e.appendChild(t));var n=new C({container:e,trigger:t,color:r.color,title:r.title,modificationDate:r.modificationDate,contents:r.contents,hideWrapper:!0}),i=n.render();i.style.left=e.style.width,e.appendChild(i)}},{key:"render",value:function(){(0,v.unreachable)("Abstract method `AnnotationElement.render` called")}}]),e}(),y=function(e){function t(e){h(this,t);var r=!!(e.data.url||e.data.dest||e.data.action);return o(this,u(t).call(this,e,r))}return l(t,e),f(t,[{key:"render",value:function(){this.container.className="linkAnnotation";var e=this.data,t=this.linkService,r=document.createElement("a");return(0,p.addLinkAttributes)(r,{url:e.url,target:e.newWindow?p.LinkTarget.BLANK:t.externalLinkTarget,rel:t.externalLinkRel}),e.url||(e.action?this._bindNamedAction(r,e.action):this._bindLink(r,e.dest)),this.container.appendChild(r),this.container}},{key:"_bindLink",value:function(e,t){var r=this;e.href=this.linkService.getDestinationHash(t),e.onclick=function(){return t&&r.linkService.navigateTo(t),!1},t&&(e.className="internalLink")}},{key:"_bindNamedAction",value:function(e,t){var r=this;e.href=this.linkService.getAnchorUrl(""),e.onclick=function(){return r.linkService.executeNamedAction(t),!1},e.className="internalLink"}}]),t}(g),b=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r))}return l(t,e),f(t,[{key:"render",value:function(){this.container.className="textAnnotation";var e=document.createElement("img");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",e.alt="[{{type}} Annotation]",e.dataset.l10nId="text_annotation_type",e.dataset.l10nArgs=JSON.stringify({type:this.data.name}),this.data.hasPopup||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container}}]),t}(g),_=function(e){function t(){return h(this,t),o(this,u(t).apply(this,arguments))}return l(t,e),f(t,[{key:"render",value:function(){return this.container}}]),t}(g),A=function(e){function t(e){h(this,t);var r=e.renderInteractiveForms||!e.data.hasAppearance&&!!e.data.fieldValue;return o(this,u(t).call(this,e,r))}return l(t,e),f(t,[{key:"render",value:function(){var e=["left","center","right"];this.container.className="textWidgetAnnotation";var t=null;if(this.renderInteractiveForms){if(this.data.multiLine?(t=document.createElement("textarea"),t.textContent=this.data.fieldValue):(t=document.createElement("input"),t.type="text",t.setAttribute("value",this.data.fieldValue)),t.disabled=this.data.readOnly,null!==this.data.maxLen&&(t.maxLength=this.data.maxLen),this.data.comb){var r=this.data.rect[2]-this.data.rect[0],n=r/this.data.maxLen;t.classList.add("comb"),t.style.letterSpacing="calc("+n+"px - 1ch)"}}else{t=document.createElement("div"),t.textContent=this.data.fieldValue,t.style.verticalAlign="middle",t.style.display="table-cell";var i=null;this.data.fontRefName&&this.page.commonObjs.has(this.data.fontRefName)&&(i=this.page.commonObjs.get(this.data.fontRefName)),this._setTextStyle(t,i)}return null!==this.data.textAlignment&&(t.style.textAlign=e[this.data.textAlignment]),this.container.appendChild(t),this.container}},{key:"_setTextStyle",value:function(e,t){var r=e.style;if(r.fontSize=this.data.fontSize+"px",r.direction=this.data.fontDirection<0?"rtl":"ltr",t){r.fontWeight=t.black?t.bold?"900":"bold":t.bold?"bold":"normal",r.fontStyle=t.italic?"italic":"normal";var n=t.loadedName?'"'+t.loadedName+'", ':"",i=t.fallbackName||"Helvetica, sans-serif";r.fontFamily=n+i}}}]),t}(_),S=function(e){function t(e){return h(this,t),o(this,u(t).call(this,e,e.renderInteractiveForms))}return l(t,e),f(t,[{key:"render",value:function(){this.container.className="buttonWidgetAnnotation checkBox";var e=document.createElement("input");return e.disabled=this.data.readOnly,e.type="checkbox",this.data.fieldValue&&"Off"!==this.data.fieldValue&&e.setAttribute("checked",!0),this.container.appendChild(e),this.container}}]),t}(_),w=function(e){function t(e){return h(this,t),o(this,u(t).call(this,e,e.renderInteractiveForms))}return l(t,e),f(t,[{key:"render",value:function(){this.container.className="buttonWidgetAnnotation radioButton";var e=document.createElement("input");return e.disabled=this.data.readOnly,e.type="radio",e.name=this.data.fieldName,this.data.fieldValue===this.data.buttonValue&&e.setAttribute("checked",!0),this.container.appendChild(e),this.container}}]),t}(_),k=function(e){function t(){return h(this,t),o(this,u(t).apply(this,arguments))}return l(t,e),f(t,[{key:"render",value:function(){var e=n(u(t.prototype),"render",this).call(this);return e.className="buttonWidgetAnnotation pushButton",e}}]),t}(y),x=function(e){function t(e){return h(this,t),o(this,u(t).call(this,e,e.renderInteractiveForms))}return l(t,e),f(t,[{key:"render",value:function(){this.container.className="choiceWidgetAnnotation";var e=document.createElement("select");e.disabled=this.data.readOnly,this.data.combo||(e.size=this.data.options.length,this.data.multiSelect&&(e.multiple=!0));for(var t=0,r=this.data.options.length;t<r;t++){var n=this.data.options[t],i=document.createElement("option");i.textContent=n.displayValue,i.value=n.exportValue,this.data.fieldValue.includes(n.displayValue)&&i.setAttribute("selected",!0),e.appendChild(i)}return this.container.appendChild(e),this.container}}]),t}(_),P=function(e){function t(e){h(this,t);var r=!(!e.data.title&&!e.data.contents);return o(this,u(t).call(this,e,r))}return l(t,e),f(t,[{key:"render",value:function(){var e=["Line","Square","Circle","PolyLine","Polygon","Ink"];if(this.container.className="popupAnnotation",e.includes(this.data.parentType))return this.container;var t='[data-annotation-id="'+this.data.parentId+'"]',r=this.layer.querySelector(t);if(!r)return this.container;var n=new C({container:this.container,trigger:r,color:this.data.color,title:this.data.title,modificationDate:this.data.modificationDate,contents:this.data.contents}),i=parseFloat(r.style.left),a=parseFloat(r.style.width);return this.container.style.transformOrigin=-(i+a)+"px -"+r.style.top,this.container.style.left=i+a+"px",this.container.appendChild(n.render()),this.container}}]),t}(g),C=function(){function e(t){h(this,e),this.container=t.container,this.trigger=t.trigger,this.color=t.color,this.title=t.title,this.modificationDate=t.modificationDate,this.contents=t.contents,this.hideWrapper=t.hideWrapper||!1,this.pinned=!1}return f(e,[{key:"render",value:function(){var e=document.createElement("div");e.className="popupWrapper",this.hideElement=this.hideWrapper?e:this.container,this.hideElement.setAttribute("hidden",!0);var t=document.createElement("div");t.className="popup";var r=this.color;if(r){var n=.7*(255-r[0])+r[0],i=.7*(255-r[1])+r[1],a=.7*(255-r[2])+r[2];t.style.backgroundColor=v.Util.makeCssRgb(0|n,0|i,0|a)}var o=document.createElement("h1");o.textContent=this.title,t.appendChild(o);var s=p.PDFDateString.toDateObject(this.modificationDate);if(s){var u=document.createElement("span");u.textContent="{{date}}, {{time}}",u.dataset.l10nId="annotation_date_string",u.dataset.l10nArgs=JSON.stringify({date:s.toLocaleDateString(),time:s.toLocaleTimeString()}),t.appendChild(u)}var l=this._formatContents(this.contents);return t.appendChild(l),this.trigger.addEventListener("click",this._toggle.bind(this)),this.trigger.addEventListener("mouseover",this._show.bind(this,!1)),this.trigger.addEventListener("mouseout",this._hide.bind(this,!1)),t.addEventListener("click",this._hide.bind(this,!0)),e.appendChild(t),e}},{key:"_formatContents",value:function(e){for(var t=document.createElement("p"),r=e.split(/(?:\r\n?|\n)/),n=0,i=r.length;n<i;++n){var a=r[n];t.appendChild(document.createTextNode(a)),n<i-1&&t.appendChild(document.createElement("br"))}return t}},{key:"_toggle",value:function(){this.pinned?this._hide(!0):this._show(!0)}},{key:"_show",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&(this.pinned=!0),this.hideElement.hasAttribute("hidden")&&(this.hideElement.removeAttribute("hidden"),this.container.style.zIndex+=1)}},{key:"_hide",value:function(){(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.pinned=!1),this.hideElement.hasAttribute("hidden")||this.pinned||(this.hideElement.setAttribute("hidden",!0),this.container.style.zIndex-=1)}}]),e}(),R=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){return this.container.className="freeTextAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(g),T=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){this.container.className="lineAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=this.svgFactory.createElement("svg:line");return i.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),i.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),i.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),i.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),i.setAttribute("stroke-width",e.borderStyle.width),i.setAttribute("stroke","transparent"),n.appendChild(i),this.container.append(n),this._createPopup(this.container,i,e),this.container}}]),t}(g),E=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){this.container.className="squareAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.borderStyle.width,a=this.svgFactory.createElement("svg:rect");return a.setAttribute("x",i/2),a.setAttribute("y",i/2),a.setAttribute("width",t-i),a.setAttribute("height",r-i),a.setAttribute("stroke-width",i),a.setAttribute("stroke","transparent"),a.setAttribute("fill","none"),n.appendChild(a),this.container.append(n),this._createPopup(this.container,a,e),this.container}}]),t}(g),O=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){this.container.className="circleAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.borderStyle.width,a=this.svgFactory.createElement("svg:ellipse");return a.setAttribute("cx",t/2),a.setAttribute("cy",r/2),a.setAttribute("rx",t/2-i/2),a.setAttribute("ry",r/2-i/2),a.setAttribute("stroke-width",i),a.setAttribute("stroke","transparent"),a.setAttribute("fill","none"),n.appendChild(a),this.container.append(n),this._createPopup(this.container,a,e),this.container}}]),t}(g),F=function(e){function t(e){var r;h(this,t);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return r=o(this,u(t).call(this,e,n,!0)),r.containerClassName="polylineAnnotation",r.svgElementName="svg:polyline",r}return l(t,e),f(t,[{key:"render",value:function(){this.container.className=this.containerClassName;for(var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.vertices,a=[],o=0,s=i.length;o<s;o++){var u=i[o].x-e.rect[0],l=e.rect[3]-i[o].y;a.push(u+","+l)}a=a.join(" ");var c=e.borderStyle.width,h=this.svgFactory.createElement(this.svgElementName);return h.setAttribute("points",a),h.setAttribute("stroke-width",c),h.setAttribute("stroke","transparent"),h.setAttribute("fill","none"),n.appendChild(h),this.container.append(n),this._createPopup(this.container,h,e),this.container}}]),t}(g),I=function(e){function t(e){var r;return h(this,t),r=o(this,u(t).call(this,e)),r.containerClassName="polygonAnnotation",r.svgElementName="svg:polygon",r}return l(t,e),t}(F),L=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){return this.container.className="caretAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(g),j=function(e){function t(e){var r;h(this,t);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return r=o(this,u(t).call(this,e,n,!0)),r.containerClassName="inkAnnotation",r.svgElementName="svg:polyline",r}return l(t,e),f(t,[{key:"render",value:function(){this.container.className=this.containerClassName;for(var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.inkLists,a=0,o=i.length;a<o;a++){for(var s=i[a],u=[],l=0,c=s.length;l<c;l++){var h=s[l].x-e.rect[0],d=e.rect[3]-s[l].y;u.push(h+","+d)}u=u.join(" ");var f=e.borderStyle.width,p=this.svgFactory.createElement(this.svgElementName);p.setAttribute("points",u),p.setAttribute("stroke-width",f),p.setAttribute("stroke","transparent"),p.setAttribute("fill","none"),this._createPopup(this.container,p,e),n.appendChild(p)}return this.container.append(n),this.container}}]),t}(g),M=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){return this.container.className="highlightAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(g),D=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){return this.container.className="underlineAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(g),N=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){return this.container.className="squigglyAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(g),q=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){return this.container.className="strikeoutAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(g),W=function(e){function t(e){h(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return o(this,u(t).call(this,e,r,!0))}return l(t,e),f(t,[{key:"render",value:function(){return this.container.className="stampAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(g),U=function(e){function t(e){var r;h(this,t),r=o(this,u(t).call(this,e,!0));var n=r.data.file,i=n.filename,a=n.content;return r.filename=(0,p.getFilenameFromUrl)(i),r.content=a,r.linkService.eventBus&&r.linkService.eventBus.dispatch("fileattachmentannotation",{source:s(r),id:(0,v.stringToPDFString)(i),filename:i,content:a}),r}return l(t,e),f(t,[{key:"render",value:function(){this.container.className="fileAttachmentAnnotation";var e=document.createElement("div");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.addEventListener("dblclick",this._download.bind(this)),this.data.hasPopup||!this.data.title&&!this.data.contents||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container}},{key:"_download",value:function(){if(!this.downloadManager)return void(0,v.warn)("Download cannot be started due to unavailable download manager");this.downloadManager.downloadData(this.content,this.filename,"")}}]),t}(g),B=function(){function e(){h(this,e)}return f(e,null,[{key:"render",value:function(e){for(var t=0,r=e.annotations.length;t<r;t++){var n=e.annotations[t];if(n){var i=m.create({data:n,layer:e.div,page:e.page,viewport:e.viewport,linkService:e.linkService,downloadManager:e.downloadManager,imageResourcesPath:e.imageResourcesPath||"",renderInteractiveForms:e.renderInteractiveForms||!1,svgFactory:new p.DOMSVGFactory});i.isRenderable&&e.div.appendChild(i.render())}}}},{key:"update",value:function(e){for(var t=0,r=e.annotations.length;t<r;t++){var n=e.annotations[t],i=e.div.querySelector('[data-annotation-id="'+n.id+'"]');i&&(i.style.transform="matrix("+e.viewport.transform.join(",")+")")}e.div.removeAttribute("hidden")}}]),e}();t.AnnotationLayer=B},function(e,t,r){"use strict";function n(e){return o(e)||a(e)||i()}function i(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function a(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}function o(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}function s(e,t){return c(e)||l(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}function l(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}function c(e){if(Array.isArray(e))return e}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function f(e,t,r){return t&&d(e.prototype,t),r&&d(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.SVGGraphics=void 0;var p=r(1),v=r(151),m=function(e){return e&&e.__esModule?e:{default:e}}(r(4)),g=function(){throw new Error("Not implemented: SVGGraphics")};t.SVGGraphics=g;var y=function(e){var t=[],r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0){var u=o.value;"save"!==u.fn?"restore"===u.fn?t=r.pop():t.push(u):(t.push({fnId:92,fn:"group",items:[]}),r.push(t),t=t[t.length-1].items)}}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return t},b=function(e){if(Number.isInteger(e))return e.toString();var t=e.toFixed(10),r=t.length-1;if("0"!==t[r])return t;do{r--}while("0"===t[r]);return t.substring(0,"."===t[r]?r:r+1)},_=function(e){if(0===e[4]&&0===e[5]){if(0===e[1]&&0===e[2])return 1===e[0]&&1===e[3]?"":"scale(".concat(b(e[0])," ").concat(b(e[3]),")");if(e[0]===e[3]&&e[1]===-e[2]){var t=180*Math.acos(e[0])/Math.PI;return"rotate(".concat(b(t),")")}}else if(1===e[0]&&0===e[1]&&0===e[2]&&1===e[3])return"translate(".concat(b(e[4])," ").concat(b(e[5]),")");return"matrix(".concat(b(e[0])," ").concat(b(e[1])," ").concat(b(e[2])," ").concat(b(e[3])," ").concat(b(e[4])," ")+"".concat(b(e[5]),")")},A={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},S="http://www.w3.org/1999/xlink",w=["butt","round","square"],k=["miter","round","bevel"],x=function(){function e(e,t,r){for(var n=-1,i=t;i<r;i++){var a=255&(n^e[i]);n=n>>>8^u[a]}return-1^n}function t(t,r,n,i){var a=i,o=r.length;n[a]=o>>24&255,n[a+1]=o>>16&255,n[a+2]=o>>8&255,n[a+3]=255&o,a+=4,n[a]=255&t.charCodeAt(0),n[a+1]=255&t.charCodeAt(1),n[a+2]=255&t.charCodeAt(2),n[a+3]=255&t.charCodeAt(3),a+=4,n.set(r,a),a+=r.length;var s=e(n,i+4,a);n[a]=s>>24&255,n[a+1]=s>>16&255,n[a+2]=s>>8&255,n[a+3]=255&s}function r(e,t,r){for(var n=1,i=0,a=t;a<r;++a)n=(n+(255&e[a]))%65521,i=(i+n)%65521;return i<<16|n}function n(e){if(!(0,m.default)())return i(e);try{var t;t=parseInt(process.versions.node)>=8?e:new Buffer(e);var r=require("zlib").deflateSync(t,{level:9});return r instanceof Uint8Array?r:new Uint8Array(r)}catch(e){(0,p.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+e)}return i(e)}function i(e){var t=e.length,n=Math.ceil(t/65535),i=new Uint8Array(2+t+5*n+4),a=0;i[a++]=120,i[a++]=156;for(var o=0;t>65535;)i[a++]=0,i[a++]=255,i[a++]=255,i[a++]=0,i[a++]=0,i.set(e.subarray(o,o+65535),a),a+=65535,o+=65535,t-=65535;i[a++]=1,i[a++]=255&t,i[a++]=t>>8&255,i[a++]=255&~t,i[a++]=(65535&~t)>>8&255,i.set(e.subarray(o),a),a+=e.length-o;var s=r(e,0,e.length);return i[a++]=s>>24&255,i[a++]=s>>16&255,i[a++]=s>>8&255,i[a++]=255&s,i}function a(e,r,i,a){var u,l,c,h=e.width,d=e.height,f=e.data;switch(r){case p.ImageKind.GRAYSCALE_1BPP:l=0,u=1,c=h+7>>3;break;case p.ImageKind.RGB_24BPP:l=2,u=8,c=3*h;break;case p.ImageKind.RGBA_32BPP:l=6,u=8,c=4*h;break;default:throw new Error("invalid format")}for(var v=new Uint8Array((1+c)*d),m=0,g=0,y=0;y<d;++y)v[m++]=0,v.set(f.subarray(g,g+c),m),g+=c,m+=c;if(r===p.ImageKind.GRAYSCALE_1BPP&&a){m=0;for(var b=0;b<d;b++){m++;for(var _=0;_<c;_++)v[m++]^=255}}var A=new Uint8Array([h>>24&255,h>>16&255,h>>8&255,255&h,d>>24&255,d>>16&255,d>>8&255,255&d,u,l,0,0,0]),S=n(v),w=o.length+3*s+A.length+S.length,k=new Uint8Array(w),x=0;return k.set(o,x),x+=o.length,t("IHDR",A,k,x),x+=s+A.length,t("IDATA",S,k,x),x+=s+S.length,t("IEND",new Uint8Array(0),k,x),(0,p.createObjectURL)(k,"image/png",i)}for(var o=new Uint8Array([137,80,78,71,13,10,26,10]),s=12,u=new Int32Array(256),l=0;l<256;l++){for(var c=l,h=0;h<8;h++)c=1&c?3988292384^c>>1&2147483647:c>>1&2147483647;u[l]=c}return function(e,t,r){return a(e,void 0===e.kind?p.ImageKind.GRAYSCALE_1BPP:e.kind,t,r)}}(),P=function(){function e(){h(this,e),this.fontSizeScale=1,this.fontWeight=A.fontWeight,this.fontSize=0,this.textMatrix=p.IDENTITY_MATRIX,this.fontMatrix=p.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=p.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=A.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}return f(e,[{key:"clone",value:function(){return Object.create(this)}},{key:"setCurrentPoint",value:function(e,t){this.x=e,this.y=t}}]),e}(),C=0,R=0,T=0;t.SVGGraphics=g=function(){function e(t,r,n){h(this,e),this.svgFactory=new v.DOMSVGFactory,this.current=new P,this.transformMatrix=p.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=t,this.objs=r,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!n,this._operatorIdMapping=[];for(var i in p.OPS)this._operatorIdMapping[p.OPS[i]]=i}return f(e,[{key:"save",value:function(){this.transformStack.push(this.transformMatrix);var e=this.current;this.extraStack.push(e),this.current=e.clone()}},{key:"restore",value:function(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}},{
key:"group",value:function(e){this.save(),this.executeOpTree(e),this.restore()}},{key:"loadDependencies",value:function(e){for(var t=this,r=e.fnArray,n=e.argsArray,i=0,a=r.length;i<a;i++)if(r[i]===p.OPS.dependency){var o=!0,s=!1,u=void 0;try{for(var l,c=n[i][Symbol.iterator]();!(o=(l=c.next()).done);o=!0)!function(){var e=l.value,r=e.startsWith("g_")?t.commonObjs:t.objs,n=new Promise(function(t){r.get(e,t)});t.current.dependencies.push(n)}()}catch(e){s=!0,u=e}finally{try{o||null==c.return||c.return()}finally{if(s)throw u}}}return Promise.all(this.current.dependencies)}},{key:"transform",value:function(e,t,r,n,i,a){var o=[e,t,r,n,i,a];this.transformMatrix=p.Util.transform(this.transformMatrix,o),this.tgrp=null}},{key:"getSVG",value:function(e,t){var r=this;this.viewport=t;var n=this._initialize(t);return this.loadDependencies(e).then(function(){return r.transformMatrix=p.IDENTITY_MATRIX,r.executeOpTree(r.convertOpList(e)),n})}},{key:"convertOpList",value:function(e){for(var t=this._operatorIdMapping,r=e.argsArray,n=e.fnArray,i=[],a=0,o=n.length;a<o;a++){var s=n[a];i.push({fnId:s,fn:t[s],args:r[a]})}return y(i)}},{key:"executeOpTree",value:function(e){var t=!0,r=!1,n=void 0;try{for(var i,a=e[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){var o=i.value,s=o.fn,u=o.fnId,l=o.args;switch(0|u){case p.OPS.beginText:this.beginText();break;case p.OPS.dependency:break;case p.OPS.setLeading:this.setLeading(l);break;case p.OPS.setLeadingMoveText:this.setLeadingMoveText(l[0],l[1]);break;case p.OPS.setFont:this.setFont(l);break;case p.OPS.showText:case p.OPS.showSpacedText:this.showText(l[0]);break;case p.OPS.endText:this.endText();break;case p.OPS.moveText:this.moveText(l[0],l[1]);break;case p.OPS.setCharSpacing:this.setCharSpacing(l[0]);break;case p.OPS.setWordSpacing:this.setWordSpacing(l[0]);break;case p.OPS.setHScale:this.setHScale(l[0]);break;case p.OPS.setTextMatrix:this.setTextMatrix(l[0],l[1],l[2],l[3],l[4],l[5]);break;case p.OPS.setTextRise:this.setTextRise(l[0]);break;case p.OPS.setTextRenderingMode:this.setTextRenderingMode(l[0]);break;case p.OPS.setLineWidth:this.setLineWidth(l[0]);break;case p.OPS.setLineJoin:this.setLineJoin(l[0]);break;case p.OPS.setLineCap:this.setLineCap(l[0]);break;case p.OPS.setMiterLimit:this.setMiterLimit(l[0]);break;case p.OPS.setFillRGBColor:this.setFillRGBColor(l[0],l[1],l[2]);break;case p.OPS.setStrokeRGBColor:this.setStrokeRGBColor(l[0],l[1],l[2]);break;case p.OPS.setStrokeColorN:this.setStrokeColorN(l);break;case p.OPS.setFillColorN:this.setFillColorN(l);break;case p.OPS.shadingFill:this.shadingFill(l[0]);break;case p.OPS.setDash:this.setDash(l[0],l[1]);break;case p.OPS.setRenderingIntent:this.setRenderingIntent(l[0]);break;case p.OPS.setFlatness:this.setFlatness(l[0]);break;case p.OPS.setGState:this.setGState(l[0]);break;case p.OPS.fill:this.fill();break;case p.OPS.eoFill:this.eoFill();break;case p.OPS.stroke:this.stroke();break;case p.OPS.fillStroke:this.fillStroke();break;case p.OPS.eoFillStroke:this.eoFillStroke();break;case p.OPS.clip:this.clip("nonzero");break;case p.OPS.eoClip:this.clip("evenodd");break;case p.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case p.OPS.paintJpegXObject:this.paintJpegXObject(l[0],l[1],l[2]);break;case p.OPS.paintImageXObject:this.paintImageXObject(l[0]);break;case p.OPS.paintInlineImageXObject:this.paintInlineImageXObject(l[0]);break;case p.OPS.paintImageMaskXObject:this.paintImageMaskXObject(l[0]);break;case p.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(l[0],l[1]);break;case p.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case p.OPS.closePath:this.closePath();break;case p.OPS.closeStroke:this.closeStroke();break;case p.OPS.closeFillStroke:this.closeFillStroke();break;case p.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case p.OPS.nextLine:this.nextLine();break;case p.OPS.transform:this.transform(l[0],l[1],l[2],l[3],l[4],l[5]);break;case p.OPS.constructPath:this.constructPath(l[0],l[1]);break;case p.OPS.endPath:this.endPath();break;case 92:this.group(o.items);break;default:(0,p.warn)("Unimplemented operator ".concat(s))}}}catch(e){r=!0,n=e}finally{try{t||null==a.return||a.return()}finally{if(r)throw n}}}},{key:"setWordSpacing",value:function(e){this.current.wordSpacing=e}},{key:"setCharSpacing",value:function(e){this.current.charSpacing=e}},{key:"nextLine",value:function(){this.moveText(0,this.current.leading)}},{key:"setTextMatrix",value:function(e,t,r,n,i,a){var o=this.current;o.textMatrix=o.lineMatrix=[e,t,r,n,i,a],o.textMatrixScale=Math.sqrt(e*e+t*t),o.x=o.lineX=0,o.y=o.lineY=0,o.xcoords=[],o.tspan=this.svgFactory.createElement("svg:tspan"),o.tspan.setAttributeNS(null,"font-family",o.fontFamily),o.tspan.setAttributeNS(null,"font-size","".concat(b(o.fontSize),"px")),o.tspan.setAttributeNS(null,"y",b(-o.y)),o.txtElement=this.svgFactory.createElement("svg:text"),o.txtElement.appendChild(o.tspan)}},{key:"beginText",value:function(){var e=this.current;e.x=e.lineX=0,e.y=e.lineY=0,e.textMatrix=p.IDENTITY_MATRIX,e.lineMatrix=p.IDENTITY_MATRIX,e.textMatrixScale=1,e.tspan=this.svgFactory.createElement("svg:tspan"),e.txtElement=this.svgFactory.createElement("svg:text"),e.txtgrp=this.svgFactory.createElement("svg:g"),e.xcoords=[]}},{key:"moveText",value:function(e,t){var r=this.current;r.x=r.lineX+=e,r.y=r.lineY+=t,r.xcoords=[],r.tspan=this.svgFactory.createElement("svg:tspan"),r.tspan.setAttributeNS(null,"font-family",r.fontFamily),r.tspan.setAttributeNS(null,"font-size","".concat(b(r.fontSize),"px")),r.tspan.setAttributeNS(null,"y",b(-r.y))}},{key:"showText",value:function(e){var t=this.current,r=t.font,n=t.fontSize;if(0!==n){var i=t.charSpacing,a=t.wordSpacing,o=t.fontDirection,s=t.textHScale*o,u=r.vertical,l=n*t.fontMatrix[0],c=0,h=!0,d=!1,f=void 0;try{for(var v,m=e[Symbol.iterator]();!(h=(v=m.next()).done);h=!0){var g=v.value;if(null!==g)if((0,p.isNum)(g))c+=-g*n*.001;else{var y=g.width,S=g.fontChar,w=(g.isSpace?a:0)+i,k=y*l+w*o;g.isInFont||r.missingFile?(t.xcoords.push(t.x+c*s),t.tspan.textContent+=S,c+=k):c+=k}else c+=o*a}}catch(e){d=!0,f=e}finally{try{h||null==m.return||m.return()}finally{if(d)throw f}}u?t.y-=c*s:t.x+=c*s,t.tspan.setAttributeNS(null,"x",t.xcoords.map(b).join(" ")),t.tspan.setAttributeNS(null,"y",b(-t.y)),t.tspan.setAttributeNS(null,"font-family",t.fontFamily),t.tspan.setAttributeNS(null,"font-size","".concat(b(t.fontSize),"px")),t.fontStyle!==A.fontStyle&&t.tspan.setAttributeNS(null,"font-style",t.fontStyle),t.fontWeight!==A.fontWeight&&t.tspan.setAttributeNS(null,"font-weight",t.fontWeight);var x=t.textRenderingMode&p.TextRenderingMode.FILL_STROKE_MASK;if(x===p.TextRenderingMode.FILL||x===p.TextRenderingMode.FILL_STROKE?(t.fillColor!==A.fillColor&&t.tspan.setAttributeNS(null,"fill",t.fillColor),t.fillAlpha<1&&t.tspan.setAttributeNS(null,"fill-opacity",t.fillAlpha)):t.textRenderingMode===p.TextRenderingMode.ADD_TO_PATH?t.tspan.setAttributeNS(null,"fill","transparent"):t.tspan.setAttributeNS(null,"fill","none"),x===p.TextRenderingMode.STROKE||x===p.TextRenderingMode.FILL_STROKE){var P=1/(t.textMatrixScale||1);this._setStrokeAttributes(t.tspan,P)}var C=t.textMatrix;0!==t.textRise&&(C=C.slice(),C[5]+=t.textRise),t.txtElement.setAttributeNS(null,"transform","".concat(_(C)," scale(1, -1)")),t.txtElement.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),t.txtElement.appendChild(t.tspan),t.txtgrp.appendChild(t.txtElement),this._ensureTransformGroup().appendChild(t.txtElement)}}},{key:"setLeadingMoveText",value:function(e,t){this.setLeading(-t),this.moveText(e,t)}},{key:"addFontStyle",value:function(e){this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.appendChild(this.cssStyle));var t=(0,p.createObjectURL)(e.data,e.mimetype,this.forceDataSchema);this.cssStyle.textContent+='@font-face { font-family: "'.concat(e.loadedName,'";')+" src: url(".concat(t,"); }\n")}},{key:"setFont",value:function(e){var t=this.current,r=this.commonObjs.get(e[0]),n=e[1];t.font=r,this.embedFonts&&r.data&&!this.embeddedFonts[r.loadedName]&&(this.addFontStyle(r),this.embeddedFonts[r.loadedName]=r),t.fontMatrix=r.fontMatrix?r.fontMatrix:p.FONT_IDENTITY_MATRIX;var i=r.black?r.bold?"bolder":"bold":r.bold?"bold":"normal",a=r.italic?"italic":"normal";n<0?(n=-n,t.fontDirection=-1):t.fontDirection=1,t.fontSize=n,t.fontFamily=r.loadedName,t.fontWeight=i,t.fontStyle=a,t.tspan=this.svgFactory.createElement("svg:tspan"),t.tspan.setAttributeNS(null,"y",b(-t.y)),t.xcoords=[]}},{key:"endText",value:function(){var e=this.current;e.textRenderingMode&p.TextRenderingMode.ADD_TO_PATH_FLAG&&e.txtElement&&e.txtElement.hasChildNodes()&&(e.element=e.txtElement,this.clip("nonzero"),this.endPath())}},{key:"setLineWidth",value:function(e){e>0&&(this.current.lineWidth=e)}},{key:"setLineCap",value:function(e){this.current.lineCap=w[e]}},{key:"setLineJoin",value:function(e){this.current.lineJoin=k[e]}},{key:"setMiterLimit",value:function(e){this.current.miterLimit=e}},{key:"setStrokeAlpha",value:function(e){this.current.strokeAlpha=e}},{key:"setStrokeRGBColor",value:function(e,t,r){this.current.strokeColor=p.Util.makeCssRgb(e,t,r)}},{key:"setFillAlpha",value:function(e){this.current.fillAlpha=e}},{key:"setFillRGBColor",value:function(e,t,r){this.current.fillColor=p.Util.makeCssRgb(e,t,r),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[]}},{key:"setStrokeColorN",value:function(e){this.current.strokeColor=this._makeColorN_Pattern(e)}},{key:"setFillColorN",value:function(e){this.current.fillColor=this._makeColorN_Pattern(e)}},{key:"shadingFill",value:function(e){var t=this.viewport.width,r=this.viewport.height,n=p.Util.inverseTransform(this.transformMatrix),i=p.Util.applyTransform([0,0],n),a=p.Util.applyTransform([0,r],n),o=p.Util.applyTransform([t,0],n),s=p.Util.applyTransform([t,r],n),u=Math.min(i[0],a[0],o[0],s[0]),l=Math.min(i[1],a[1],o[1],s[1]),c=Math.max(i[0],a[0],o[0],s[0]),h=Math.max(i[1],a[1],o[1],s[1]),d=this.svgFactory.createElement("svg:rect");d.setAttributeNS(null,"x",u),d.setAttributeNS(null,"y",l),d.setAttributeNS(null,"width",c-u),d.setAttributeNS(null,"height",h-l),d.setAttributeNS(null,"fill",this._makeShadingPattern(e)),this._ensureTransformGroup().appendChild(d)}},{key:"_makeColorN_Pattern",value:function(e){return"TilingPattern"===e[0]?this._makeTilingPattern(e):this._makeShadingPattern(e)}},{key:"_makeTilingPattern",value:function(e){var t=e[1],r=e[2],i=e[3]||p.IDENTITY_MATRIX,a=s(e[4],4),o=a[0],u=a[1],l=a[2],c=a[3],h=e[5],d=e[6],f=e[7],v="shading".concat(T++),m=p.Util.applyTransform([o,u],i),g=s(m,2),y=g[0],b=g[1],_=p.Util.applyTransform([l,c],i),A=s(_,2),S=A[0],w=A[1],k=p.Util.singularValueDecompose2dScale(i),x=s(k,2),P=x[0],C=x[1],R=h*P,E=d*C,O=this.svgFactory.createElement("svg:pattern");O.setAttributeNS(null,"id",v),O.setAttributeNS(null,"patternUnits","userSpaceOnUse"),O.setAttributeNS(null,"width",R),O.setAttributeNS(null,"height",E),O.setAttributeNS(null,"x","".concat(y)),O.setAttributeNS(null,"y","".concat(b));var F=this.svg,I=this.transformMatrix,L=this.current.fillColor,j=this.current.strokeColor,M=this.svgFactory.create(S-y,w-b);if(this.svg=M,this.transformMatrix=i,2===f){var D=p.Util.makeCssRgb.apply(p.Util,n(t));this.current.fillColor=D,this.current.strokeColor=D}return this.executeOpTree(this.convertOpList(r)),this.svg=F,this.transformMatrix=I,this.current.fillColor=L,this.current.strokeColor=j,O.appendChild(M.childNodes[0]),this.defs.appendChild(O),"url(#".concat(v,")")}},{key:"_makeShadingPattern",value:function(e){switch(e[0]){case"RadialAxial":var t,r="shading".concat(T++),n=e[2];switch(e[1]){case"axial":var i=e[3],a=e[4];t=this.svgFactory.createElement("svg:linearGradient"),t.setAttributeNS(null,"id",r),t.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),t.setAttributeNS(null,"x1",i[0]),t.setAttributeNS(null,"y1",i[1]),t.setAttributeNS(null,"x2",a[0]),t.setAttributeNS(null,"y2",a[1]);break;case"radial":var o=e[3],s=e[4],u=e[5],l=e[6];t=this.svgFactory.createElement("svg:radialGradient"),t.setAttributeNS(null,"id",r),t.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),t.setAttributeNS(null,"cx",s[0]),t.setAttributeNS(null,"cy",s[1]),t.setAttributeNS(null,"r",l),t.setAttributeNS(null,"fx",o[0]),t.setAttributeNS(null,"fy",o[1]),t.setAttributeNS(null,"fr",u);break;default:throw new Error("Unknown RadialAxial type: ".concat(e[1]))}var c=!0,h=!1,d=void 0;try{for(var f,v=n[Symbol.iterator]();!(c=(f=v.next()).done);c=!0){var m=f.value,g=this.svgFactory.createElement("svg:stop");g.setAttributeNS(null,"offset",m[0]),g.setAttributeNS(null,"stop-color",m[1]),t.appendChild(g)}}catch(e){h=!0,d=e}finally{try{c||null==v.return||v.return()}finally{if(h)throw d}}return this.defs.appendChild(t),"url(#".concat(r,")");case"Mesh":return(0,p.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error("Unknown IR type: ".concat(e[0]))}}},{key:"setDash",value:function(e,t){this.current.dashArray=e,this.current.dashPhase=t}},{key:"constructPath",value:function(e,t){var r=this.current,n=r.x,i=r.y,a=[],o=0,s=!0,u=!1,l=void 0;try{for(var c,h=e[Symbol.iterator]();!(s=(c=h.next()).done);s=!0){switch(0|c.value){case p.OPS.rectangle:n=t[o++],i=t[o++];var d=t[o++],f=t[o++],v=n+d,m=i+f;a.push("M",b(n),b(i),"L",b(v),b(i),"L",b(v),b(m),"L",b(n),b(m),"Z");break;case p.OPS.moveTo:n=t[o++],i=t[o++],a.push("M",b(n),b(i));break;case p.OPS.lineTo:n=t[o++],i=t[o++],a.push("L",b(n),b(i));break;case p.OPS.curveTo:n=t[o+4],i=t[o+5],a.push("C",b(t[o]),b(t[o+1]),b(t[o+2]),b(t[o+3]),b(n),b(i)),o+=6;break;case p.OPS.curveTo2:n=t[o+2],i=t[o+3],a.push("C",b(n),b(i),b(t[o]),b(t[o+1]),b(t[o+2]),b(t[o+3])),o+=4;break;case p.OPS.curveTo3:n=t[o+2],i=t[o+3],a.push("C",b(t[o]),b(t[o+1]),b(n),b(i),b(n),b(i)),o+=4;break;case p.OPS.closePath:a.push("Z")}}}catch(e){u=!0,l=e}finally{try{s||null==h.return||h.return()}finally{if(u)throw l}}a=a.join(" "),r.path&&e.length>0&&e[0]!==p.OPS.rectangle&&e[0]!==p.OPS.moveTo?a=r.path.getAttributeNS(null,"d")+a:(r.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().appendChild(r.path)),r.path.setAttributeNS(null,"d",a),r.path.setAttributeNS(null,"fill","none"),r.element=r.path,r.setCurrentPoint(n,i)}},{key:"endPath",value:function(){var e=this.current;if(e.path=null,this.pendingClip){if(!e.element)return void(this.pendingClip=null);var t="clippath".concat(C++),r=this.svgFactory.createElement("svg:clipPath");r.setAttributeNS(null,"id",t),r.setAttributeNS(null,"transform",_(this.transformMatrix));var n=e.element.cloneNode(!0);"evenodd"===this.pendingClip?n.setAttributeNS(null,"clip-rule","evenodd"):n.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,r.appendChild(n),this.defs.appendChild(r),e.activeClipUrl&&(e.clipGroup=null,this.extraStack.forEach(function(e){e.clipGroup=null}),r.setAttributeNS(null,"clip-path",e.activeClipUrl)),e.activeClipUrl="url(#".concat(t,")"),this.tgrp=null}}},{key:"clip",value:function(e){this.pendingClip=e}},{key:"closePath",value:function(){var e=this.current;if(e.path){var t="".concat(e.path.getAttributeNS(null,"d"),"Z");e.path.setAttributeNS(null,"d",t)}}},{key:"setLeading",value:function(e){this.current.leading=-e}},{key:"setTextRise",value:function(e){this.current.textRise=e}},{key:"setTextRenderingMode",value:function(e){this.current.textRenderingMode=e}},{key:"setHScale",value:function(e){this.current.textHScale=e/100}},{key:"setRenderingIntent",value:function(e){}},{key:"setFlatness",value:function(e){}},{key:"setGState",value:function(e){var t=!0,r=!1,n=void 0;try{for(var i,a=e[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){var o=s(i.value,2),u=o[0],l=o[1];switch(u){case"LW":this.setLineWidth(l);break;case"LC":this.setLineCap(l);break;case"LJ":this.setLineJoin(l);break;case"ML":this.setMiterLimit(l);break;case"D":this.setDash(l[0],l[1]);break;case"RI":this.setRenderingIntent(l);break;case"FL":this.setFlatness(l);break;case"Font":this.setFont(l);break;case"CA":this.setStrokeAlpha(l);break;case"ca":this.setFillAlpha(l);break;default:(0,p.warn)("Unimplemented graphic state operator ".concat(u))}}}catch(e){r=!0,n=e}finally{try{t||null==a.return||a.return()}finally{if(r)throw n}}}},{key:"fill",value:function(){var e=this.current;e.element&&(e.element.setAttributeNS(null,"fill",e.fillColor),e.element.setAttributeNS(null,"fill-opacity",e.fillAlpha),this.endPath())}},{key:"stroke",value:function(){var e=this.current;e.element&&(this._setStrokeAttributes(e.element),e.element.setAttributeNS(null,"fill","none"),this.endPath())}},{key:"_setStrokeAttributes",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=this.current,n=r.dashArray;1!==t&&n.length>0&&(n=n.map(function(e){return t*e})),e.setAttributeNS(null,"stroke",r.strokeColor),e.setAttributeNS(null,"stroke-opacity",r.strokeAlpha),e.setAttributeNS(null,"stroke-miterlimit",b(r.miterLimit)),e.setAttributeNS(null,"stroke-linecap",r.lineCap),e.setAttributeNS(null,"stroke-linejoin",r.lineJoin),e.setAttributeNS(null,"stroke-width",b(t*r.lineWidth)+"px"),e.setAttributeNS(null,"stroke-dasharray",n.map(b).join(" ")),e.setAttributeNS(null,"stroke-dashoffset",b(t*r.dashPhase)+"px")}},{key:"eoFill",value:function(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}},{key:"fillStroke",value:function(){this.stroke(),this.fill()}},{key:"eoFillStroke",value:function(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}},{key:"closeStroke",value:function(){this.closePath(),this.stroke()}},{key:"closeFillStroke",value:function(){this.closePath(),this.fillStroke()}},{key:"closeEOFillStroke",value:function(){this.closePath(),this.eoFillStroke()}},{key:"paintSolidColorImageMask",value:function(){var e=this.svgFactory.createElement("svg:rect");e.setAttributeNS(null,"x","0"),e.setAttributeNS(null,"y","0"),e.setAttributeNS(null,"width","1px"),e.setAttributeNS(null,"height","1px"),e.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().appendChild(e)}},{key:"paintJpegXObject",value:function(e,t,r){var n=this.objs.get(e),i=this.svgFactory.createElement("svg:image");i.setAttributeNS(S,"xlink:href",n.src),i.setAttributeNS(null,"width",b(t)),i.setAttributeNS(null,"height",b(r)),i.setAttributeNS(null,"x","0"),i.setAttributeNS(null,"y",b(-r)),i.setAttributeNS(null,"transform","scale(".concat(b(1/t)," ").concat(b(-1/r),")")),this._ensureTransformGroup().appendChild(i)}},{key:"paintImageXObject",value:function(e){var t=this.objs.get(e);if(!t)return void(0,p.warn)("Dependent image with object ID ".concat(e," is not ready yet"));this.paintInlineImageXObject(t)}},{key:"paintInlineImageXObject",value:function(e,t){var r=e.width,n=e.height,i=x(e,this.forceDataSchema,!!t),a=this.svgFactory.createElement("svg:rect");a.setAttributeNS(null,"x","0"),a.setAttributeNS(null,"y","0"),a.setAttributeNS(null,"width",b(r)),a.setAttributeNS(null,"height",b(n)),this.current.element=a,this.clip("nonzero");var o=this.svgFactory.createElement("svg:image");o.setAttributeNS(S,"xlink:href",i),o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y",b(-n)),o.setAttributeNS(null,"width",b(r)+"px"),o.setAttributeNS(null,"height",b(n)+"px"),o.setAttributeNS(null,"transform","scale(".concat(b(1/r)," ").concat(b(-1/n),")")),t?t.appendChild(o):this._ensureTransformGroup().appendChild(o)}},{key:"paintImageMaskXObject",value:function(e){var t=this.current,r=e.width,n=e.height,i=t.fillColor;t.maskId="mask".concat(R++);var a=this.svgFactory.createElement("svg:mask");a.setAttributeNS(null,"id",t.maskId);var o=this.svgFactory.createElement("svg:rect");o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y","0"),o.setAttributeNS(null,"width",b(r)),o.setAttributeNS(null,"height",b(n)),o.setAttributeNS(null,"fill",i),o.setAttributeNS(null,"mask","url(#".concat(t.maskId,")")),this.defs.appendChild(a),this._ensureTransformGroup().appendChild(o),this.paintInlineImageXObject(e,a)}},{key:"paintFormXObjectBegin",value:function(e,t){if(Array.isArray(e)&&6===e.length&&this.transform(e[0],e[1],e[2],e[3],e[4],e[5]),t){var r=t[2]-t[0],n=t[3]-t[1],i=this.svgFactory.createElement("svg:rect");i.setAttributeNS(null,"x",t[0]),i.setAttributeNS(null,"y",t[1]),i.setAttributeNS(null,"width",b(r)),i.setAttributeNS(null,"height",b(n)),this.current.element=i,this.clip("nonzero"),this.endPath()}}},{key:"paintFormXObjectEnd",value:function(){}},{key:"_initialize",value:function(e){var t=this.svgFactory.create(e.width,e.height),r=this.svgFactory.createElement("svg:defs");t.appendChild(r),this.defs=r;var n=this.svgFactory.createElement("svg:g");return n.setAttributeNS(null,"transform",_(e.transform)),t.appendChild(n),this.svg=n,t}},{key:"_ensureClipGroup",value:function(){if(!this.current.clipGroup){var e=this.svgFactory.createElement("svg:g");e.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.appendChild(e),this.current.clipGroup=e}return this.current.clipGroup}},{key:"_ensureTransformGroup",value:function(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",_(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().appendChild(this.tgrp):this.svg.appendChild(this.tgrp)),this.tgrp}}]),e}()},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"===_typeof3(Symbol.iterator)?function(e){return _typeof3(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof3(e)})(e)}function i(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?a(e):t}function a(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return(u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function l(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function c(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){function a(e){l(s,n,i,a,o,"next",e)}function o(e){l(s,n,i,a,o,"throw",e)}var s=e.apply(t,r);a(void 0)})}}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function f(e,t,r){return t&&d(e.prototype,t),r&&d(e,r),e}function p(e){var t=S.parse(e);return"file:"===t.protocol||t.host?t:/^[a-z]:[\/\\]/i.test(e)?S.parse("file:///".concat(e)):(t.host||(t.protocol="file:"),t)}function v(e,t){return{protocol:e.protocol,auth:e.auth,host:e.hostname,port:e.port,path:e.path,method:"GET",headers:t}}Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNodeStream=void 0;var m=function(e){return e&&e.__esModule?e:{default:e}}(r(148)),g=r(1),y=r(166),b=require("fs"),_=require("http"),A=require("https"),S=require("url"),w=/^file:\/\/\/[a-zA-Z]:\//,k=function(){function e(t){h(this,e),this.source=t,this.url=p(t.url),this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol,this.isFsUrl="file:"===this.url.protocol,this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}return f(e,[{key:"getFullReader",value:function(){return(0,g.assert)(!this._fullRequestReader),this._fullRequestReader=this.isFsUrl?new T(this):new C(this),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var r=this.isFsUrl?new E(this,e,t):new R(this,e,t);return this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeRequestReaders.slice(0).forEach(function(t){t.cancel(e)})}},{key:"_progressiveDataLength",get:function(){return this._fullRequestReader?this._fullRequestReader._loaded:0}}]),e}();t.PDFNodeStream=k;var x=function(){function e(t){h(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;var r=t.source;this._contentLength=r.length,this._loaded=0,this._filename=null,this._disableRange=r.disableRange||!1,this._rangeChunkSize=r.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!r.disableStream,this._isRangeSupported=!r.disableRange,this._readableStream=null,this._readCapability=(0,g.createPromiseCapability)(),this._headersCapability=(0,g.createPromiseCapability)()}return f(e,[{key:"read",value:function(){function e(){return t.apply(this,arguments)}var t=c(m.default.mark(function e(){var t,r;return m.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:if(!this._done){e.next=4;break}return e.abrupt("return",{value:void 0,done:!0});case 4:if(!this._storedError){e.next=6;break}throw this._storedError;case 6:if(null!==(t=this._readableStream.read())){e.next=10;break}return this._readCapability=(0,g.createPromiseCapability)(),e.abrupt("return",this.read());case 10:return this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),r=new Uint8Array(t).buffer,e.abrupt("return",{value:r,done:!1});case 14:case"end":return e.stop()}},e,this)}));return e}()},{key:"cancel",value:function(e){if(!this._readableStream)return void this._error(e);this._readableStream.destroy(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",function(){t._readCapability.resolve()}),e.on("end",function(){e.destroy(),t._done=!0,t._readCapability.resolve()}),e.on("error",function(e){t._error(e)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new g.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}},{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}(),P=function(){function e(t){h(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=(0,g.createPromiseCapability)();var r=t.source;this._isStreamingSupported=!r.disableStream}return f(e,[{key:"read",value:function(){function e(){return t.apply(this,arguments)}var t=c(m.default.mark(function e(){var t,r;return m.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:if(!this._done){e.next=4;break}return e.abrupt("return",{value:void 0,done:!0});case 4:if(!this._storedError){e.next=6;break}throw this._storedError;case 6:if(null!==(t=this._readableStream.read())){e.next=10;break}return this._readCapability=(0,g.createPromiseCapability)(),e.abrupt("return",this.read());case 10:return this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded}),r=new Uint8Array(t).buffer,e.abrupt("return",{value:r,done:!1});case 14:case"end":return e.stop()}},e,this)}));return e}()},{key:"cancel",value:function(e){if(!this._readableStream)return void this._error(e);this._readableStream.destroy(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",function(){t._readCapability.resolve()}),e.on("end",function(){e.destroy(),t._done=!0,t._readCapability.resolve()}),e.on("error",function(e){t._error(e)}),this._storedError&&this._readableStream.destroy(this._storedError)}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}(),C=function(e){function t(e){var r;h(this,t),r=i(this,o(t).call(this,e));var n=function(t){if(404===t.statusCode){var n=new g.MissingPDFException('Missing PDF "'.concat(r._url,'".'));return r._storedError=n,void r._headersCapability.reject(n)}r._headersCapability.resolve(),r._setReadableStream(t);var i=function(e){return r._readableStream.headers[e.toLowerCase()]},a=(0,y.validateRangeRequestCapabilities)({getResponseHeader:i,isHttp:e.isHttp,rangeChunkSize:r._rangeChunkSize,disableRange:r._disableRange}),o=a.allowRangeRequests,s=a.suggestedLength;r._isRangeSupported=o,r._contentLength=s||r._contentLength,r._filename=(0,y.extractFilenameFromHeader)(i)};return r._request=null,"http:"===r._url.protocol?r._request=_.request(v(r._url,e.httpHeaders),n):r._request=A.request(v(r._url,e.httpHeaders),n),r._request.on("error",function(e){r._storedError=e,r._headersCapability.reject(e)}),r._request.end(),r}return s(t,e),t}(x),R=function(e){function t(e,r,n){var a;h(this,t),a=i(this,o(t).call(this,e)),a._httpHeaders={};for(var s in e.httpHeaders){var u=e.httpHeaders[s];void 0!==u&&(a._httpHeaders[s]=u)}a._httpHeaders.Range="bytes=".concat(r,"-").concat(n-1);var l=function(e){if(404===e.statusCode){var t=new g.MissingPDFException('Missing PDF "'.concat(a._url,'".'));return void(a._storedError=t)}a._setReadableStream(e)};return a._request=null,"http:"===a._url.protocol?a._request=_.request(v(a._url,a._httpHeaders),l):a._request=A.request(v(a._url,a._httpHeaders),l),a._request.on("error",function(e){a._storedError=e}),a._request.end(),a}return s(t,e),t}(P),T=function(e){function t(e){var r;h(this,t),r=i(this,o(t).call(this,e));var n=decodeURIComponent(r._url.path);return w.test(r._url.href)&&(n=n.replace(/^\//,"")),b.lstat(n,function(e,t){if(e)return"ENOENT"===e.code&&(e=new g.MissingPDFException('Missing PDF "'.concat(n,'".'))),r._storedError=e,void r._headersCapability.reject(e);r._contentLength=t.size,r._setReadableStream(b.createReadStream(n)),r._headersCapability.resolve()}),r}return s(t,e),t}(x),E=function(e){function t(e,r,n){var a;h(this,t),a=i(this,o(t).call(this,e));var s=decodeURIComponent(a._url.path);return w.test(a._url.href)&&(s=s.replace(/^\//,"")),a._setReadableStream(b.createReadStream(s,{start:r,end:n-1})),a}return s(t,e),t}(P)},function(e,t,r){"use strict";function n(e){var t=e.getResponseHeader,r=e.isHttp,n=e.rangeChunkSize,i=e.disableRange;(0,s.assert)(n>0,"Range chunk size must be larger than zero");var a={allowRangeRequests:!1,suggestedLength:void 0},o=parseInt(t("Content-Length"),10);return Number.isInteger(o)?(a.suggestedLength=o,o<=2*n?a:i||!r?a:"bytes"!==t("Accept-Ranges")?a:"identity"!==(t("Content-Encoding")||"identity")?a:(a.allowRangeRequests=!0,a)):a}function i(e){var t=e("Content-Disposition");if(t){var r=(0,u.getFilenameFromContentDispositionHeader)(t);if(/\.pdf$/i.test(r))return r}return null}function a(e,t){return 404===e||0===e&&/^file:/.test(t)?new s.MissingPDFException('Missing PDF "'+t+'".'):new s.UnexpectedResponseException("Unexpected server response ("+e+') while retrieving PDF "'+t+'".',e)}function o(e){return 200===e||206===e}Object.defineProperty(t,"__esModule",{value:!0}),t.createResponseStatusError=a,t.extractFilenameFromHeader=i,t.validateRangeRequestCapabilities=n,t.validateResponseStatus=o;var s=r(1),u=r(167)},function(e,t,r){"use strict";function n(e,t){return o(e)||a(e,t)||i()}function i(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}function a(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{
n||null==s.return||s.return()}finally{if(i)throw a}}return r}function o(e){if(Array.isArray(e))return e}function s(e){function t(e,t){return new RegExp("(?:^|;)\\s*"+e+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',t)}function r(e,t){if(e){if(!/^[\x00-\xFF]+$/.test(t))return t;try{var r=new TextDecoder(e,{fatal:!0}),n=Array.from(t,function(e){return 255&e.charCodeAt(0)});t=r.decode(new Uint8Array(n)),u=!1}catch(r){if(/^utf-?8$/i.test(e))try{t=decodeURIComponent(escape(t)),u=!1}catch(e){}}}return t}function i(e){return u&&/[\x80-\xff]/.test(e)&&(e=r("utf-8",e),u&&(e=r("iso-8859-1",e))),e}function a(e){if(e.startsWith('"')){for(var t=e.slice(1).split('\\"'),r=0;r<t.length;++r){var n=t[r].indexOf('"');-1!==n&&(t[r]=t[r].slice(0,n),t.length=r+1),t[r]=t[r].replace(/\\(.)/g,"$1")}e=t.join('"')}return e}function o(e){var t=e.indexOf("'");return-1===t?e:r(e.slice(0,t),e.slice(t+1).replace(/^[^']*'/,""))}function s(e){return!e.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(e)?e:e.replace(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(e,t,n,i){if("q"===n||"Q"===n)return i=i.replace(/_/g," "),i=i.replace(/=([0-9a-fA-F]{2})/g,function(e,t){return String.fromCharCode(parseInt(t,16))}),r(t,i);try{i=atob(i)}catch(e){}return r(t,i)})}var u=!0,l=t("filename\\*","i").exec(e);if(l){l=l[1];var c=a(l);return c=unescape(c),c=o(c),c=s(c),i(c)}if(l=function(e){for(var r,i=[],s=t("filename\\*((?!0\\d)\\d+)(\\*?)","ig");null!==(r=s.exec(e));){var u=r,l=n(u,4),c=l[1],h=l[2],d=l[3];if((c=parseInt(c,10))in i){if(0===c)break}else i[c]=[h,d]}for(var f=[],c=0;c<i.length&&c in i;++c){var p=n(i[c],2),h=p[0],d=p[1];d=a(d),h&&(d=unescape(d),0===c&&(d=o(d))),f.push(d)}return f.join("")}(e)){return i(s(l))}if(l=t("filename","i").exec(e)){l=l[1];var h=a(l);return h=s(h),i(h)}return""}Object.defineProperty(t,"__esModule",{value:!0}),t.getFilenameFromContentDispositionHeader=s},function(e,t,r){"use strict";function n(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function i(e){return function(){var t=this,r=arguments;return new Promise(function(i,a){function o(e){n(u,i,a,o,s,"next",e)}function s(e){n(u,i,a,o,s,"throw",e)}var u=e.apply(t,r);o(void 0)})}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}function u(e){var t=e.response;return"string"!=typeof t?t:(0,c.stringToBytes)(t).buffer}Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNetworkStream=void 0;var l=function(e){return e&&e.__esModule?e:{default:e}}(r(148)),c=r(1),h=r(166),d=function(){function e(t,r){a(this,e),this.url=t,r=r||{},this.isHttp=/^https?:/i.test(t),this.httpHeaders=this.isHttp&&r.httpHeaders||{},this.withCredentials=r.withCredentials||!1,this.getXhr=r.getXhr||function(){return new XMLHttpRequest},this.currXhrId=0,this.pendingRequests=Object.create(null)}return s(e,[{key:"requestRange",value:function(e,t,r){var n={begin:e,end:t};for(var i in r)n[i]=r[i];return this.request(n)}},{key:"requestFull",value:function(e){return this.request(e)}},{key:"request",value:function(e){var t=this.getXhr(),r=this.currXhrId++,n=this.pendingRequests[r]={xhr:t};t.open("GET",this.url),t.withCredentials=this.withCredentials;for(var i in this.httpHeaders){var a=this.httpHeaders[i];void 0!==a&&t.setRequestHeader(i,a)}return this.isHttp&&"begin"in e&&"end"in e?(t.setRequestHeader("Range","bytes=".concat(e.begin,"-").concat(e.end-1)),n.expectedStatus=206):n.expectedStatus=200,t.responseType="arraybuffer",e.onError&&(t.onerror=function(r){e.onError(t.status)}),t.onreadystatechange=this.onStateChange.bind(this,r),t.onprogress=this.onProgress.bind(this,r),n.onHeadersReceived=e.onHeadersReceived,n.onDone=e.onDone,n.onError=e.onError,n.onProgress=e.onProgress,t.send(null),r}},{key:"onProgress",value:function(e,t){var r=this.pendingRequests[e];r&&r.onProgress&&r.onProgress(t)}},{key:"onStateChange",value:function(e,t){var r=this.pendingRequests[e];if(r){var n=r.xhr;if(n.readyState>=2&&r.onHeadersReceived&&(r.onHeadersReceived(),delete r.onHeadersReceived),4===n.readyState&&e in this.pendingRequests){if(delete this.pendingRequests[e],0===n.status&&this.isHttp)return void(r.onError&&r.onError(n.status));var i=n.status||200;if(!(200===i&&206===r.expectedStatus)&&i!==r.expectedStatus)return void(r.onError&&r.onError(n.status));var a=u(n);if(206===i){var o=n.getResponseHeader("Content-Range"),s=/bytes (\d+)-(\d+)\/(\d+)/.exec(o);r.onDone({begin:parseInt(s[1],10),chunk:a})}else a?r.onDone({begin:0,chunk:a}):r.onError&&r.onError(n.status)}}}},{key:"hasPendingRequests",value:function(){for(var e in this.pendingRequests)return!0;return!1}},{key:"getRequestXhr",value:function(e){return this.pendingRequests[e].xhr}},{key:"isPendingRequest",value:function(e){return e in this.pendingRequests}},{key:"abortAllRequests",value:function(){for(var e in this.pendingRequests)this.abortRequest(0|e)}},{key:"abortRequest",value:function(e){var t=this.pendingRequests[e].xhr;delete this.pendingRequests[e],t.abort()}}]),e}(),f=function(){function e(t){a(this,e),this._source=t,this._manager=new d(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}return s(e,[{key:"_onRangeRequestReaderClosed",value:function(e){var t=this._rangeRequestReaders.indexOf(e);t>=0&&this._rangeRequestReaders.splice(t,1)}},{key:"getFullReader",value:function(){return(0,c.assert)(!this._fullRequestReader),this._fullRequestReader=new p(this._manager,this._source),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){var r=new v(this._manager,e,t);return r.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeRequestReaders.slice(0).forEach(function(t){t.cancel(e)})}}]),e}();t.PDFNetworkStream=f;var p=function(){function e(t,r){a(this,e),this._manager=t;var n={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=r.url,this._fullRequestId=t.requestFull(n),this._headersReceivedCapability=(0,c.createPromiseCapability)(),this._disableRange=r.disableRange||!1,this._contentLength=r.length,this._rangeChunkSize=r.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}return s(e,[{key:"_onHeadersReceived",value:function(){var e=this._fullRequestId,t=this._manager.getRequestXhr(e),r=function(e){return t.getResponseHeader(e)},n=(0,h.validateRangeRequestCapabilities)({getResponseHeader:r,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange}),i=n.allowRangeRequests,a=n.suggestedLength;i&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=(0,h.extractFilenameFromHeader)(r),this._isRangeSupported&&this._manager.abortRequest(e),this._headersReceivedCapability.resolve()}},{key:"_onDone",value:function(e){if(e)if(this._requests.length>0){var t=this._requests.shift();t.resolve({value:e.chunk,done:!1})}else this._cachedChunks.push(e.chunk);this._done=!0,this._cachedChunks.length>0||(this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[])}},{key:"_onError",value:function(e){var t=this._url,r=(0,h.createResponseStatusError)(e,t);this._storedError=r,this._headersReceivedCapability.reject(r),this._requests.forEach(function(e){e.reject(r)}),this._requests=[],this._cachedChunks=[]}},{key:"_onProgress",value:function(e){this.onProgress&&this.onProgress({loaded:e.loaded,total:e.lengthComputable?e.total:this._contentLength})}},{key:"read",value:function(){function e(){return t.apply(this,arguments)}var t=i(l.default.mark(function e(){var t,r;return l.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._storedError){e.next=2;break}throw this._storedError;case 2:if(!(this._cachedChunks.length>0)){e.next=5;break}return t=this._cachedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 5:if(!this._done){e.next=7;break}return e.abrupt("return",{value:void 0,done:!0});case 7:return r=(0,c.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 10:case"end":return e.stop()}},e,this)}));return e}()},{key:"cancel",value:function(e){this._done=!0,this._headersReceivedCapability.reject(e),this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[],this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}},{key:"filename",get:function(){return this._filename}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}},{key:"contentLength",get:function(){return this._contentLength}},{key:"headersReady",get:function(){return this._headersReceivedCapability.promise}}]),e}(),v=function(){function e(t,r,n){a(this,e),this._manager=t;var i={onDone:this._onDone.bind(this),onProgress:this._onProgress.bind(this)};this._requestId=t.requestRange(r,n,i),this._requests=[],this._queuedChunk=null,this._done=!1,this.onProgress=null,this.onClosed=null}return s(e,[{key:"_close",value:function(){this.onClosed&&this.onClosed(this)}},{key:"_onDone",value:function(e){var t=e.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunk=t;this._done=!0,this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[],this._close()}},{key:"_onProgress",value:function(e){!this.isStreamingSupported&&this.onProgress&&this.onProgress({loaded:e.loaded})}},{key:"read",value:function(){function e(){return t.apply(this,arguments)}var t=i(l.default.mark(function e(){var t,r;return l.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(null===this._queuedChunk){e.next=4;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 4:if(!this._done){e.next=6;break}return e.abrupt("return",{value:void 0,done:!0});case 6:return r=(0,c.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 9:case"end":return e.stop()}},e,this)}));return e}()},{key:"cancel",value:function(e){this._done=!0,this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[],this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}},{key:"isStreamingSupported",get:function(){return!1}}]),e}()},function(e,t,r){"use strict";function n(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function i(e){return function(){var t=this,r=arguments;return new Promise(function(i,a){function o(e){n(u,i,a,o,s,"next",e)}function s(e){n(u,i,a,o,s,"throw",e)}var u=e.apply(t,r);o(void 0)})}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}function u(e,t,r){return{method:"GET",headers:e,signal:r&&r.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFetchStream=void 0;var l=function(e){return e&&e.__esModule?e:{default:e}}(r(148)),c=r(1),h=r(166),d=function(){function e(t){a(this,e),this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}return s(e,[{key:"getFullReader",value:function(){return(0,c.assert)(!this._fullRequestReader),this._fullRequestReader=new f(this),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var r=new p(this,e,t);return this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeRequestReaders.slice(0).forEach(function(t){t.cancel(e)})}},{key:"_progressiveDataLength",get:function(){return this._fullRequestReader?this._fullRequestReader._loaded:0}}]),e}();t.PDFFetchStream=d;var f=function(){function e(t){var r=this;a(this,e),this._stream=t,this._reader=null,this._loaded=0,this._filename=null;var n=t.source;this._withCredentials=n.withCredentials||!1,this._contentLength=n.length,this._headersCapability=(0,c.createPromiseCapability)(),this._disableRange=n.disableRange||!1,this._rangeChunkSize=n.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),"undefined"!=typeof AbortController&&(this._abortController=new AbortController),this._isStreamingSupported=!n.disableStream,this._isRangeSupported=!n.disableRange,this._headers=new Headers;for(var i in this._stream.httpHeaders){var o=this._stream.httpHeaders[i];void 0!==o&&this._headers.append(i,o)}var s=n.url;fetch(s,u(this._headers,this._withCredentials,this._abortController)).then(function(e){if(!(0,h.validateResponseStatus)(e.status))throw(0,h.createResponseStatusError)(e.status,s);r._reader=e.body.getReader(),r._headersCapability.resolve();var t=function(t){return e.headers.get(t)},n=(0,h.validateRangeRequestCapabilities)({getResponseHeader:t,isHttp:r._stream.isHttp,rangeChunkSize:r._rangeChunkSize,disableRange:r._disableRange}),i=n.allowRangeRequests,a=n.suggestedLength;r._isRangeSupported=i,r._contentLength=a||r._contentLength,r._filename=(0,h.extractFilenameFromHeader)(t),!r._isStreamingSupported&&r._isRangeSupported&&r.cancel(new c.AbortException("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}return s(e,[{key:"read",value:function(){function e(){return t.apply(this,arguments)}var t=i(l.default.mark(function e(){var t,r,n,i;return l.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._headersCapability.promise;case 2:return e.next=4,this._reader.read();case 4:if(t=e.sent,r=t.value,!(n=t.done)){e.next=9;break}return e.abrupt("return",{value:r,done:n});case 9:return this._loaded+=r.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),i=new Uint8Array(r).buffer,e.abrupt("return",{value:i,done:!1});case 13:case"end":return e.stop()}},e,this)}));return e}()},{key:"cancel",value:function(e){this._reader&&this._reader.cancel(e),this._abortController&&this._abortController.abort()}},{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}(),p=function(){function e(t,r,n){var i=this;a(this,e),this._stream=t,this._reader=null,this._loaded=0;var o=t.source;this._withCredentials=o.withCredentials||!1,this._readCapability=(0,c.createPromiseCapability)(),this._isStreamingSupported=!o.disableStream,"undefined"!=typeof AbortController&&(this._abortController=new AbortController),this._headers=new Headers;for(var s in this._stream.httpHeaders){var l=this._stream.httpHeaders[s];void 0!==l&&this._headers.append(s,l)}this._headers.append("Range","bytes=".concat(r,"-").concat(n-1));var d=o.url;fetch(d,u(this._headers,this._withCredentials,this._abortController)).then(function(e){if(!(0,h.validateResponseStatus)(e.status))throw(0,h.createResponseStatusError)(e.status,d);i._readCapability.resolve(),i._reader=e.body.getReader()}),this.onProgress=null}return s(e,[{key:"read",value:function(){function e(){return t.apply(this,arguments)}var t=i(l.default.mark(function e(){var t,r,n,i;return l.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:return e.next=4,this._reader.read();case 4:if(t=e.sent,r=t.value,!(n=t.done)){e.next=9;break}return e.abrupt("return",{value:r,done:n});case 9:return this._loaded+=r.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded}),i=new Uint8Array(r).buffer,e.abrupt("return",{value:i,done:!1});case 13:case"end":return e.stop()}},e,this)}));return e}()},{key:"cancel",value:function(e){this._reader&&this._reader.cancel(e),this._abortController&&this._abortController.abort()}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}()}])});