function PdfItem(t,i,e,s){if(this.loaded=!1,this.uploaded=!1,this.sent=!1,this.scroll=pdf_scroll,this.hittest=PdfItemHitTest,this.bodyhittest=ImageItemBodyHitTest,this.highlight_dragbar=!1,this.scaleImage=ImageItemScale,this.clicked=pdfitem_clicked,this.file=t,this.imageConverted=!1,this.resized=!1,this.server_file_name="",this.complete_upload=function(t){this.server_file_name=t,this.uploaded=!0,next_image.loaded&&(cursor.assets.push(next_image),next_image=null)},this.img=new Image,this.size=new Location(i,e),this.type="pdfitem",this.deleted=!1,this.moved=!1,this.id=uuidv4().replace(/-/g,""),this.scale_index=3,this.assets_deleted=!1,this.assets=[],this.assets_dirty=!1,this.add_asset=PdfAddAsset,this.get_lowest_point=function(){return this.loc.y+this.size.y},FileReader&&t){var h=new FileReader;h.onload=function(){null!==next_image&&(next_image.img.src=h.result,next_image.loaded=!0,next_image.uploaded&&(cursor.assets.push(next_image),next_image=null),console.log("weszłotu"))},h.readAsDataURL(t)}this.server_file_name=s,this.img.src=s,this.img.onload=function(){},this.loaded=!0,this.offset=0,this.loc=new Location(i,e),this.filename=t?t.name:s,this.update=PdfItemUpdate,this.draw=PdfItemDraw,this.hittest=PdfitemHitTest,this.hittestresize=PdfItemHitTestResize,this.resize=PdfItemResize,this.correctionsEnabled=!0,this.moved?$("#catch-pdf").css({left:i,top:e}):$("#catch-pdf").css({left:"10%",top:100})}function PdfItemResize(t,i){var e=this.size.x,s=this.size.y,h=this.size.x+=t,o=e/s,l=h/o;h=l*o,this.size.x=h,this.size.y=l,this.correctionsEnabled=!1}function pdfitem_clicked(t,i,e){return i>=this.loc.y-20&&i<this.loc.y&&t>=this.loc.x+(.8*this.size.x-17.5)&&t<this.loc.x+.8*this.size.x&&(this.deleted=!0),i>=this.loc.y-20&&i<this.loc.y+.8*this.size.y&&t>=this.loc.x&&t<this.loc.x+.8*this.size.x}function pdf_scroll(){}function PdfitemHitTest(t,i){}function PdfItemDraw(t,i){var e=t.strokeStyle,s=t.fillStyle;if(this.loaded){var h={width:$(window).width(),height:$(window).height()};if(!this.imageConverted){var o=new Image;o.src=this.img.src,this.img=o,this.imageConverted=!0,this.size.x=.8*this.img.width,this.size.y=.8*this.img.height}if(0!=this.size.x&&0!=this.size.y||(this.size.x=this.img.width,this.size.y=this.img.height),this.correctionsEnabled&&this.size.x>h.width/2){var l=this.size.x/=2,n=this.size.x/this.size.y,a=l*n;this.size.x=l,this.size.y=a}var c=scale[this.scale_index],d=Math.round(c*this.size.x),r=Math.round(c*this.size.y);!1===this.highlight_dragbar?t.fillStyle="#222222":t.fillStyle="#444444";var m=document.createElement("canvas");m.getContext("2d");m.width=.8*this.size.x,m.height=.8*this.size.y,t.mozImageSmoothingEnabled=!0,t.imageSmoothingQuality="high",t.webkitImageSmoothingEnabled=!0,t.msImageSmoothingEnabled=!0,t.imageSmoothingEnabled=!0,t.drawImage(this.img,this.loc.x,this.loc.y-i,m.width,m.height),t.beginPath();var g=m.width;t.fillRect(this.loc.x,this.loc.y-20-i,g,20),t.stroke(),t.closePath(),t.font="14px verdana",t.fillStyle="white",t.fillText("X",this.loc.x+(g-17.5),this.loc.y-5-i,.4*this.size.x),t.linewidth=5,t.strokeStyle="#222222",d=m.width,r=m.height,t.beginPath(),t.moveTo(this.loc.x,this.loc.y-i),t.lineTo(this.loc.x+d,this.loc.y-i),t.lineTo(this.loc.x+d,this.loc.y+r-i),t.lineTo(this.loc.x,this.loc.y+r-i),t.lineTo(this.loc.x,this.loc.y-i),t.stroke(),t.closePath();for(var f=0;!cursor.moving&&f<this.assets.length;f++){this.assets[f].draw(t,i,this)}t.strokeStyle="#222222",t.beginPath(),t.moveTo(this.loc.x+d-20,this.loc.y+r-i-2),t.lineTo(this.loc.x+d-2,this.loc.y+r-i-20),t.moveTo(this.loc.x+d-15,this.loc.y+r-i-2),t.lineTo(this.loc.x+d-2,this.loc.y+r-i-15),t.moveTo(this.loc.x+d-10,this.loc.y+r-i-2),t.lineTo(this.loc.x+d-2,this.loc.y+r-i-10),t.moveTo(this.loc.x+d-5,this.loc.y+r-i-2),t.lineTo(this.loc.x+d-2,this.loc.y+r-i-5),t.stroke(),t.closePath()}t.strokeStyle=e,t.fillStyle=s}function PdfItemHitTestResize(t,i,e){return t>=this.loc.x+.8*this.size.x-20&&t<this.loc.x+.8*this.size.x&&i>=this.loc.y+.8*this.size.y-20&&i<this.loc.y+.8*this.size.y}function PdfItemUpdate(){}function PdfItemHitTest(t,i){}function PdfAddAsset(t){if("line"!=t.type)return void console.log("ERROR: We only handle lines for textboxes");for(var i=0;i<t.points.length;i++)t.points[i].x-=this.loc.x,t.points[i].y-=this.loc.y-this.offset;this.assets.push(t),this.assets_dirty=!0}var next_image=null;