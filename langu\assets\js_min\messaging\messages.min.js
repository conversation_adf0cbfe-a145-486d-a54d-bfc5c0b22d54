function nl2br(e,s){return(e+"").replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g,"$1"+(s||void 0===s?"<br />":"<br>")+"$2")}function br2nl(e){return e.replace(/<br>|<br \/><br\/>/g,"\r")}!function(e,s,t,a,r,i,n){({init:function(){this.classNames={outerContainer:".columns-wrapper",newMessageForm:".new-message-form",newFileMessageForm:".new-file-message-form",fileUploadButton:".new-file-message-upload-button",messagesMoreButton:".messages-list-more-button",newMessageTextarea:".new-message-box-input",threadsMoreButton:".threads-list-more-button",threadsList:".threads-list",threadSwitch:".message-info a",messagesList:".messages-list",messagePrototype:".prototype",thread:".threads-list-item",currentThread:".threads-list-item.current",stickyThread:".threads-list-item.sticky",sidebar:".sidebar",conversation:".conversation",timer:".timer-time",onlineStatus:".online-status"},linkify.options.defaults.defaultProtocol="https",linkify.options.defaults.className="message-link";var a={};a.markup={spinner:'<span class="glyphicon glyphicon-repeat spin"></span>',conversationSpinner:'<div class="activity-indicator"><div></div></div>',timeFormat:"H:i"},this.state={threadsCache:{},onlineStatusSubscriptions:{},threadLoadingRequest:null,hasThreads:!1},this.options=a;var r=e(t),i=r.find(this.classNames.outerContainer),n=i.find(this.classNames.sidebar),o={};o.global={outerContainer:i,sidebar:n},this.references=o,this.updateConversationReferences(),this.updateThreadsReferences(),this.linkifyConversation();var h=this.references.conversation.newFileMessageForm.find(this.classNames.fileUploadButton),l=parseInt(h.data("max-size")),d=h.data("allowed-mimetypes").split(",");this.options.fileUpload={maxSize:l,mimeTypes:d};var c=this.references.conversation.messagesList.children(this.classNames.messagePrototype).prop("outerHTML");this.options.markup.messagePrototype=c,this.socket=s.ws.socket,this.bind()},linkifyConversation:function(){this.references.conversation.messagesList.linkify()},updateConversationReferences:function(){var e=this.references.global.outerContainer,s=e.find(this.classNames.conversation),t=s.find(this.classNames.newMessageForm),a=s.find(this.classNames.messagesList),r=t.find(this.classNames.newMessageTextarea),i=s.find(this.classNames.timer),n=s.find(this.classNames.onlineStatus),o=s.find(this.classNames.newFileMessageForm),h={conversation:s,newMessageForm:t,newFileMessageForm:o,messagesList:a,newMessageInput:r,timerOutput:i,onlineStatus:n};this.references.conversation=h},updateThreadsReferences:function(){var e=this.references.global.sidebar,s=e.find(this.classNames.threadsList),t=s.children(this.classNames.stickyThread),a=s.children(this.classNames.currentThread);this.state.hasThreads=!!s.length,this.references.threads={threadsList:s,stickyThread:t,currentThread:a}},tickTimer:function(){if(void 0!==this.references.conversation&&this.references.conversation){var e=this.references.conversation.timerOutput;if(e.length){var s=new Date,t=s.getTime(),a=60*s.getTimezoneOffset()*1e3,r=1e3*parseInt(e.data("timezoneOffset")),i=t+a+r,n=new Date(i),o=n.getHours(),h=n.getMinutes(),l=o>9?o:"0"+o;l=l+":"+(h>9?h:"0"+h),e.text()!==l&&e.text(l)}}},appendNewThread:function(e,s,t){if(null!==(t=t||null)&&t.length>0)return void t.after(s);e.prepend(s)},appendNewMessage:function(e,s){s.linkify(),e.prepend(s)},onStatusUpdate:function(s){var t=s.user,a=s.online;if(a&&t){var r=this.references.threads.threadsList,i=r.find('[data-user="'+t+'"]').first();if(0!==i.length){var n=i.data("thread"),o=i.hasClass("current");if(n)if(!1===o){if(this.state.threadsCache.hasOwnProperty(n)){var h=e(this.state.threadsCache[n].data),l=h.find(this.classNames.onlineStatus);this.updateConversationStatus(l,a),this.state.threadsCache[n].data=e("<div/>").append(h).html()}}else{var l=this.references.conversation.onlineStatus;this.updateConversationStatus(l,a)}}}},updateConversationStatus:function(e,s){switch(s){case"online":e.removeClass("offline idle").addClass("online");break;case"idle":e.removeClass("offline online").addClass("idle");break;case"offline":e.removeClass("online idle").addClass("offline")}},onNewMessage:function(s){var t=s.thread_info.id,a=s.thread_info.is_new,r=this.references.threads.threadsList,i=this.references.threads.stickyThread,n=e(s.fragments.thread),o=r.find('[data-thread="'+t+'"]').first();if(a||0===o.length)return void this.appendNewThread(r,n,i);var h=o.hasClass("current"),l=e(s.fragments.message);if(!1===h){if(this.state.threadsCache.hasOwnProperty(t)){var d=e(this.state.threadsCache[t].data),c=d.find(this.classNames.messagesList);this.appendNewMessage(c,l),this.state.threadsCache[t].data=e("<div/>").append(d).html(),this.onlineStatusSubscribe(o)}o.replaceWith(n)}else{var c=this.references.conversation.messagesList;n.addClass("current"),o.replaceWith(n),this.appendNewMessage(c,l),this.onlineStatusSubscribe(n)}},bind:function(){var e=this.references.threads.threadsList,t=this.references.global.outerContainer;t.on("submit",this.classNames.newMessageForm,this.sendMessageHandler.bind(this)),t.on("submit",this.classNames.newFileMessageForm,this.sendFileMessageHandler.bind(this)),t.on("change",this.classNames.fileUploadButton,this.fileMessageStateChanged.bind(this)),t.on("keydown",this.classNames.newMessageTextarea,this.keyboardSubmit.bind(this)),t.on("click",this.classNames.messagesMoreButton,this.moreMessages.bind(this)),e.on("click",this.classNames.threadSwitch,this.switchThread.bind(this)),e.on("click",this.classNames.threadsMoreButton,this.moreThreads.bind(this)),s.setInterval(this.tickTimer.bind(this),999),this.socket.on("message:received",this.onNewMessage.bind(this)),this.socket.on("user:status:change",this.onStatusUpdate.bind(this));var a=this.references.threads.currentThread;this.onlineStatusSubscribe(a)},fileMessageStateChanged:function(e){e.target;e.target.files&&e.target.files.length&&this.references.conversation.newFileMessageForm.submit()},onlineStatusSubscribe:function(e){if(e){var s=e.data("user");s&&(this.state.onlineStatusSubscriptions.hasOwnProperty(s)||(this.socket.emit("user:status:sub",{users:[s]}),this.state.onlineStatusSubscriptions[s]=!0))}},moreThreads:function(s){s.preventDefault();var t=e(s.currentTarget),a=t.parent();t.html(this.options.markup.spinner);var r={url:t.attr("href"),method:"GET",global:!1},i=this,n=function(e,s,t){i.appendThreads(a,e)};this.ajaxCall(r,n,this.ajaxFailureCallback)},appendThreads:function(s,t){var a=this.references.threads.stickyThread,r=e(t);if(a.length>0){var i=praseInt(a.data("thread")),n=r.filter('[data-thread="'+i+'"]');n.length&&n.remove()}s.replaceWith(r)},moreMessages:function(s){s.preventDefault();var t=e(s.currentTarget),a=t.parent();t.html(this.options.markup.spinner);var r={url:t.attr("href"),method:"GET",global:!1},i=this.references.conversation.messagesList,n=this,o=function(s,t,r){n.appendMessages(a,s);var o=i.position().top+i.height();e("html, body").scrollTop(o,300)};this.ajaxCall(r,o,this.ajaxFailureCallback)},appendMessages:function(s,t){var a=e(t);s.replaceWith(a),s.linkify()},stripTags:function(e){return e.toString().replace(/<\/?[^>]+>/gi,"")},keyboardSubmit:function(e){if(13===e.which&&e.shiftKey){e.preventDefault();this.references.conversation.newMessageForm.submit()}},sendFileMessageHandler:function(s){s.preventDefault();var t=this.references.conversation.newFileMessageForm,a=new FormData(t[0]),r=t.find(this.classNames.fileUploadButton).get(0),n=r.files[0];if(n.size>this.options.fileUpload.maxSize)return t[0].reset(),void i.addFlash("error","messaging.new_file_message.file_too_big");if(""!==n.type&&-1===this.options.fileUpload.mimeTypes.indexOf(n.type))return t[0].reset(),void i.addFlash("error","messaging.new_file_message.file_type_not_allowed");a.append("file",n);var o=e(this.options.markup.messagePrototype);o.find('[data-property="message"]').addClass("content-file").html(nl2br(n.name)),o.removeClass("hidden"),this.references.conversation.messagesList.prepend(o);var h=n.name;h.length>50&&(h=h.substr(0,47)+"...");var l=o.find('[data-property="date"]'),d=this.references.threads.currentThread,c=d.find('[data-property="message"]'),u={url:t.attr("action"),method:"POST",data:a,dataType:"json",contentType:!1,processData:!1,cache:!1,global:!1},f=function(s,a,r){var i=e(s.payload.partial);o.find('[data-property="message"]').html(i),o.removeClass("sending");var n=s.payload.date;l.text(n),c.text(h),t[0].reset()},p=this,m=function(e,s,a){p.ajaxFailureCallback(e,s,a),o.remove(),t[0].reset()};this.ajaxCall(u,f,m)},sendMessageHandler:function(s){s.preventDefault();var t=this.references.conversation.newMessageInput,a=this.stripTags(t.val());if(0!==a.length){var r=this.references.conversation.newMessageForm,i=r.serialize();t.val(null);var n=e(this.options.markup.messagePrototype);n.find('[data-property="message"]').html(nl2br(a)),n.removeClass("hidden");this.references.conversation.messagesList.prepend(n);var o=a;o.length>50&&(o=o.substr(0,47)+"..."),n.linkify();var h=n.find('[data-property="date"]'),l=this.references.threads.currentThread,d=l.find('[data-property="message"]'),c=this.references.conversation.newMessageForm,u={url:c.attr("action"),method:"POST",data:i,dataType:"json",global:!1},f=function(e,s,t){n.removeClass("sending");var a=e.payload.date;h.text(a),d.text(o)},p=this,m=function(e,s,t){p.ajaxFailureCallback(e,s,t),n.remove()};this.ajaxCall(u,f,m)}},switchThread:function(s){s.preventDefault(),this.references.global.sidebar.removeClass("in");var t=e(s.currentTarget),r=this.references.threads.currentThread,i=t.closest(this.classNames.thread),n=r.data("thread"),o=i.data("thread"),h=null;if(n!==o){var l=this.references.conversation.conversation,d=l.html();if(this.state.threadsCache.hasOwnProperty(o)&&(h=this.state.threadsCache[o]),i.addClass("current"),r.removeClass("current"),null!==this.state.threadLoadingRequest?(this.state.threadLoadingRequest.abort(),this.state.threadLoadingRequest=null):this.state.threadsCache[n]={id:n,data:d},null!==h){l.html(h.data),this.updateConversationReferences(),this.updateThreadsReferences(),this.tickTimer();var c=this.references.conversation.newMessageInput;return a(c),void a.update(c)}var u={url:t.attr("href"),method:"GET",global:!1},f=this,p=function(s,t,r){f.state.threadLoadingRequest=null;var n=e(s);l.replaceWith(n),f.updateConversationReferences(),f.linkifyConversation(),f.tickTimer();var o=f.references.conversation.newMessageInput;a(o),a.update(o);var h=f.references.conversation.messagesList,d=br2nl(h.children(".messages-list-item").last().children(".content").html()).trim(),c=i.find('[data-property="message"]');d.length>50&&(d=d.substr(0,47)+"..."),c.text(d),f.updateConversationReferences(),f.updateThreadsReferences(),f.tickTimer(),f.onlineStatusSubscribe(i)};l.html(this.options.markup.conversationSpinner),f.state.threadLoadingRequest=this.ajaxCall(u,p,this.ajaxFailureCallback)}},ajaxCall:function(s,t,a){var r=e.ajax(s);return r.fail(a),r.done(t),r},ajaxFailureCallback:function(e,s,t){if(void 0!==e.responseJSON&&null!==e.responseJSON){var a=e.responseJSON.hasOwnProperty("payload")?e.responseJSON.payload.messageType||"error":"error",r=e.responseJSON.message;i.addFlash(a,r)}}}).init()}(jQuery,window,document,autosize,Translator,FLASHES);