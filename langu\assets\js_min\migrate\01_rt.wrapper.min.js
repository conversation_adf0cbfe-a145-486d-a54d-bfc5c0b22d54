!function(e,t){void 0===Array.isArray&&(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),void 0===Object.isObject&&(Object.isObject=function(e){return!Array.isArray(e)&&e===Object(e)}),void 0===String.isString&&(String.isString=function(e){return"[object String]"===Object.prototype.toString.call(e)}),e.rt={},rt.RealtimeWrapper=function(e,t){this.init(e,t)},rt.RealtimeWrapper.prototype={clientId:null,mimeType:"application/vnd.google-apps.drive-sdk",accessToken:null,init:function(e,t){if(void 0===e)throw new Error("No Client ID specified");if(!String.isString(e))throw new Error("Given Client ID is not a string");this.clientId=e,this.initOptions(t),this.bind()},start:function(t){var r=this;e.gapi.load("auth2,auth:client,drive-realtime,drive-share",{callback:function(){e.gapi.auth.setToken(r.accessToken),e.gapi.auth.token=r.accessToken,r.tokenRefresh(t)}})},initOptions:function(e){if(e){if(e.hasOwnProperty("accessToken")){if(!Object.isObject(e.accessToken))throw new Error("Access token expects to be an Associative Array (Object)");this.accessToken=e.accessToken}if(e.hasOwnProperty("mimeType")){if(!String.isString(e.mimeType))throw new Error("Mime type expects to be a string");this.mimeType=e.mimeType}if(!e.hasOwnProperty("userId"))throw new Error("Required option userId has not been set");if(!String.isString(e.userId))throw new Error("Option userId expects to be a string");if(this.userId=e.userId,!e.hasOwnProperty("scope"))throw new Error("Required option scope has not been set");if(String.isString(e.scope)){var t=e.scope.split(" ");this.scope=t}else{if(!Array.isArray(e.scope))throw new Error("Option scope must be an array or a string");this.scope=e.scope}}},bind:function(){this.onError=this.onError.bind(this)},getParam:function(t){var r=new RegExp(t+"=(.*?)($|&)","g"),i=e.location.search.match(r);return i&&i.length?(i=i[0],i.replace(t+"=","").replace("&","")):null},onError:function(t){var r=this;switch(console.log("RealtimeAPI error:"),!0){case t.type==e.gapi.drive.realtime.ErrorType.TOKEN_REFRESH_REQUIRED:this.tokenRefresh(function(){console.log("Error, auth refreshed");var e=r.lastCall;null!==e&&(this[e.method].apply(this,e.arguments),this.lastCall=null)});break;case t.type==e.gapi.drive.realtime.ErrorType.CLIENT_ERROR:console.log("An Error happened: "+t.message);break;case t.type==e.gapi.drive.realtime.ErrorType.NOT_FOUND:console.log("The file was not found. It does not exist or you do not have read access to the file.");break;case t.type==e.gapi.drive.realtime.ErrorType.FORBIDDEN:console.log("You do not have access to this file. Try having the owner shareit with you from Google Drive.");default:console.log(t)}},load:function(t,r,i,o,n){this.lastCall={method:"load",arguments:[r,i,o]},e.gapi.drive.realtime.load(r,function(e){i(t,e)},function(e){o(t,e)},function(){n(t)})},tokenRefresh:function(t){var r={authuser:-1,immediate:!0,client_id:this.clientId,user_id:this.userId,scope:this.scope};e.gapi.auth.authorize(r,function(e){t(e)})}}}(window);