!function(t){function e(){migrateData.lessons.forEach(function(t){i.load(t.id,t.fileId,n,a,r)})}function a(t,e){return o(t,""),!1}function n(t,e){try{var a=e.getModel().getRoot().get(s),n=a?a.getText():"";return o(t,n)}catch(e){return o(t,"")}}function r(t){return o(t,"")}function o(e,a){return t.ajax({async:!1,cache:!1,data:{text:a},type:"POST",url:"/lesson/"+e+"/classroom/save",dataType:"text",success:function(t){u.push(e)},error:function(t,a,n){u.push(e)}}),!1}var c={scope:migrateData.scope,userId:migrateData.googleEmail,accessToken:migrateData.accessToken},s="demo_string",i=new window.rt.RealtimeWrapper(migrateData.clientId,c),u=[];setInterval(function(){u.length===migrateData.lessons.length&&window.location.replace("/")},1e3),i.start(e)}(jQuery);