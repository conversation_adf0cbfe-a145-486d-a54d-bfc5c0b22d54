!function(e){var t=function(){var t=e(this),n=t.val(),i=e('.registration-form [data-field="student"]'),a=e('.registration-form [data-field="teacher"]');"2"===n?(a.removeClass("hidden"),i.addClass("hidden")):(a.addClass("hidden"),i.removeClass("hidden"))},n=function(t){e("#"+selectizeId)[0].selectize.setValue(t)};e('.registration-form [data-field="user_type"]').on("change",'input[type="radio"], input[type="checkbox"]',t),e(document).on("click",".timezone_country",function(){n(e(this).data("timezone")),o.html(""),o.append(d(e(this).data("timezone"),e(this).data("value")))});var i=function(){return(new Date).getTimezoneOffset()/60*-1}(),a=timezones[i],o=e("#timezones"),s=function(t,n){t.html(""),e.each(n,function(e,n){t.append(d(e,n))})},d=function(e,t){return'<div class="col-xs-12 col-ms-6 col-xl-4"><button type="button" class="timezone__btn timezone_country" data-timezone="'+e+'" data-value="'+t+'"><span>(GMT'+(+i>0?"+":"")+i+":00) </span>"+t+"</button></div>"};s(o,a),e("#resetTimezone").click(function(){s(o,a)})}(jQuery);