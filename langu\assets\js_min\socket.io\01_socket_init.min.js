!function(){window.ws=window.ws||{},window.ws.host=location.host+"/",window.ws.endpoint="/websocket",window.ws.secure=!0;var o=io.connect(window.ws.host,{path:window.ws.endpoint,secure:window.ws.secure,reconnectionAttempts:5});o.on("reconnect_failed",function(){console.log("The socket.io can not connect.")}),window.ws.socket=o;var n=function(o){var n=o.message,e=$("<div>"+n+"</div>"),s=e.find("audio");langu.modal.instance(e,"langu-modal-dialog langu-modal-dialog-short",{afterOpen:function(){s[0].play()}}).open()};o.on("lesson:call",n),o.on("user_joined_classroom",function(o){console.log(o)})}();