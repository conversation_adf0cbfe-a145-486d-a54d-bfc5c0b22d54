!function(e,s,n){var l=e(".lessons-list");if(l.length){var a=function(s){var n=s.lesson,l=s.url;e('.lessons-list-item[data-lesson="'+n+'"]').find(".lesson-enter").removeClass("disabled").attr("href",l).removeAttr("title")},i=s.ws.socket;i.on("lesson:init",a);for(var t={},o=l.find(".lessons-list-inner").children("li[data-user]"),r=0;r<o.length;r++){var d=e(o[r]),f=parseInt(d.data("user"));if(!f)return;t.hasOwnProperty(f)||(t[f]=e()),t[f]=t[f].add(d)}if(o.length){var u=Object.keys(t);i.emit("user:status:sub",{users:u}),i.on("user:status:change",function(e){console.log("Status change",e);var s=e.user,n=e.online;if(void 0!==s&&void 0!==n){var l=t[s];if(l.length){var a=l.find(".online-status");switch(n){case"online":a.removeClass("offline idle").addClass("online");break;case"idle":a.removeClass("offline online").addClass("idle");break;case"offline":a.removeClass("idle online").addClass("offline")}}}})}}}(jQuery,window);