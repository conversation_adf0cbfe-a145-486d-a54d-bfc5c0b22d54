function createImage(s,e,t,i){function a(){}function o(){}function c(){}function r(){}var n=document.createElement("div");n.setAttribute("id","image-classroom".concat(s)),document.getElementsByClassName("middle-inner-classroom")[0].appendChild(n),new Vue({el:"#image-classroom"+s,template:'  \n            <div v-if="activeImage" v-bind:id="this.getId" class="image-wrap-classroom active"">\n                <div :id="`desc-${this.assets.imageId}`" class="image-classroom-description">\n                    <span class="image-classroom-name">{{assets.imageName}}</span>\n                    <span @click="closeImage" class="image-classroom-cross">&times;</span>\n                </div>\n                <img class="image-classroom-item-icon"  :id="`image-classroom-icon-${this.assets.imageId}`" :src="assets.imageUrl" />\n            </div>\n        ',computed:{getId:function(){return"image-classroom-item-"+this._uid}},data:{activeImage:!0,assets:{id:this._uid,type:"vue_imageitem",imageId:s,imageUrl:e,imageName:t,update:a,draw:o,hittestresize:c,hittest:r,position:{top:"20%",left:"40%"},size:{width:"500",height:"350"},isResize:!1,isDrag:!1,isCreated:!1,isClose:!1,isClosed:!1}},mounted:function(){var t=this;this.assets.imageId=s,this.assets.imageUrl=e,this.assets.isCreated=i,window.cursor.assets.push(this.assets),this.$nextTick(function(){"teacher"===window.role?$(".image-wrap-classroom").addClass("teacher"):$(".image-wrap-classroom").addClass("student"),$("#"+t.getId).draggable({drag:function(s,e){t.assets.position.top="".concat(e.position.top,"px"),t.assets.position.left="".concat(e.position.left,"px"),t.assets.isDrag=!0,window.cursor.assets.push(t.assets)},stop:function(s,e){t.assets.position.top="".concat(e.position.top,"px"),t.assets.position.left="".concat(e.position.left,"px"),t.assets.isDrag=!0,window.cursor.assets.push(t.assets)}}),$("#"+t.getId).resizable({handles:"e, s, se",create:function(s,e){$(".image-wrap-classroom.teacher .ui-resizable-e").css("cursor","url('/images/classroom/cursor-teacher-right.svg') 30 0, auto"),$(".image-wrap-classroom.student .ui-resizable-e").css("cursor","url('/images/classroom/cursor-student-right.svg') 30 0, auto"),$(".image-wrap-classroom.teacher .ui-resizable-s").css("cursor","url('/images/classroom/cursor-teacher-down.svg') 0 30, auto"),$(".image-wrap-classroom.student .ui-resizable-s").css("cursor","url('/images/classroom/cursor-student-down.svg') 0 30, auto"),$(".image-wrap-classroom.teacher .ui-resizable-se").css("cursor"," url('/images/classroom/teacher-arrow.svg'), auto"),$(".image-wrap-classroom.student .ui-resizable-se").css("cursor"," url('/images/classroom/student-arrow.svg'), auto")},resize:function(s,e){t.assets.size.width=e.size.width,t.assets.size.height=e.size.height,t.assets.isResize=!0,window.cursor.assets.push(t.assets)},stop:function(s,e){t.assets.size.width=e.size.width,t.assets.size.height=e.size.height,t.assets.isResize=!0,window.cursor.assets.push(t.assets)}})})},methods:{closeImage:function(){this.assets.isClose=!0,this.activeImage=!1},closeImageAssets:function(){this.activeImage=!1}},watch:{"assets.position.top":function(s){document.getElementById(this.getId).style.top=s},"assets.position.left":function(s){document.getElementById(this.getId).style.left=s},"assets.size.width":function(s){document.getElementById(this.getId).style.width="".concat(s,"px")},"assets.size.height":function(s){document.getElementById(this.getId).style.height="".concat(s,"px")},"assets.isClosed":function(s){s&&this.closeImageAssets()}}})}