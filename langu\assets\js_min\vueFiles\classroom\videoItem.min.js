function createVideo(e){function s(){}function t(){}function i(){}var a=document.createElement("div");a.setAttribute("id","video".concat(e)),document.getElementsByClassName("middle-inner-classroom")[0].appendChild(a),new Vue({el:"#video"+e,template:'  \n            <div :id="this.getId" class="video-item" data-id="" v-bind:class="{active: activeVideo}">\n                <div :id="`desc-${this.videoId}`" class="video-item-description">\n                    <span class="video-item-name">{{player.config.title}}</span>\n                    <span @click="closeVideo" class="video-item-cross">&times;</span>\n                </div>\n                <div  :id="`player-${this.videoId}`" data-plyr-provider="youtube" :data-plyr-embed-id="videoId"></div>\n            </div>\n        ',computed:{getId:function(){return"video-item-"+this._uid}},data:{activeVideo:!0,videoId:null,player:{config:{}},canvas:null,mouseCanvasVideoClick:!1,start_loc:null,videoAssets:[],video_assets:null,assets:{type:"vue_videoitem",videoId:e,update:s,videoAssets:[],draw:t,hittest:i,position:{top:"20%",left:"40%"},size:{width:"500",height:"350"},currentTime:null,isResize:!1,isDrag:!1,isCreated:!1,isPlay:!1,isSeeking:!1,isMute:!1,volumeValue:null,speedValue:null,isClose:!1,addCanvasDraw:!1,canvasGetAssets:!1,videoEvent:!1}},mounted:function(){var s=this;this.videoId=e,this.assets.isCreated=!0,window.cursor.assets.push(this.assets),this.$nextTick(function(){"teacher"===window.role?$(".video-item").addClass("teacher"):$(".video-item").addClass("student"),s.player=new Plyr("#player-"+s.videoId,{controls:["play","progress","current-time","mute","volume","captions","settings","pip"],clickToPlay:!1,hideControls:!1,resetOnEnd:!0,speed:{selected:1,options:[.5,.75,1,1.25,1.5,1.75,2]}}),setTimeout(function(){s.player.speed=1},1e3),s.player.on("play",s.playVideo),s.player.on("pause",s.pauseVideo),s.player.on("seeking",s.seekingVideo),s.player.on("ratechange",s.changeSpeed),$("#"+s.getId).draggable({drag:function(e,t){s.assets.position.top="".concat(t.position.top,"px"),s.assets.position.left="".concat(t.position.left,"px"),s.assets.isDrag=!0,window.cursor.assets.push(s.assets)},stop:function(e,t){s.assets.position.top="".concat(t.position.top,"px"),s.assets.position.left="".concat(t.position.left,"px"),s.assets.isDrag=!0,window.cursor.assets.push(s.assets),setTimeout(function(){s.assets.isDrag=!1},60)}}),$("#"+s.getId).resizable({handles:"e, s, se",create:function(e,s){$(".video-item.teacher .ui-resizable-e").css("cursor","url('/images/classroom/cursor-teacher-right.svg') 30 0, auto"),$(".video-item.student .ui-resizable-e").css("cursor","url('/images/classroom/cursor-student-right.svg') 30 0, auto"),$(".video-item.teacher .ui-resizable-s").css("cursor","url('/images/classroom/cursor-teacher-down.svg') 0 30, auto"),$(".video-item.student .ui-resizable-s").css("cursor","url('/images/classroom/cursor-student-down.svg') 0 30, auto"),$(".video-item.teacher .ui-resizable-se").css("cursor"," url('/images/classroom/teacher-arrow.svg'), auto"),$(".video-item.student .ui-resizable-se").css("cursor"," url('/images/classroom/student-arrow.svg'), auto")},start:function(s,t){$("#canvas-video".concat(e)).hide(),$("a, button, input, label, span").css("cursor","none !important"),$(".cursor-pointer-teacher, .cursor-pointer-student").css("cursor","none !important")},resize:function(e,t){s.assets.videoEvent=!0,s.assets.size.width=t.size.width,s.assets.size.height=t.size.height,s.assets.isResize=!0;var i=null,a=$(e.toElement).hasClass("ui-resizable-se"),n=$(e.toElement).hasClass("ui-resizable-e"),o=$(e.toElement).hasClass("ui-resizable-s");a?i="resizable-se":n?i="resizable-e":o&&(i="resizable-s"),document.addEventListener("mousemove",function(e){cursorMouseMoveEvent(i,e)})},stop:function(t,i){s.assets.videoEvent=!0,s.assets.size.width=i.size.width,s.assets.size.height=i.size.height,s.assets.isResize=!0,setTimeout(function(){s.assets.isDrag=!1},60),$("#canvas-video".concat(e)).show(),role}}),s.createVideoCanvas(),s.assets.canvasGetAsssets=!1,s.drawAssetsCanvas()})},methods:{closeVideo:function(){this.assets.videoEvent=!0,this.activeVideo=!1,this.assets.isClose=!0},playVideo:function(s){this.clearCanvas(),!0!==this.assets.isPlay&&(this.assets.videoEvent=!0,this.assets.isPlay=!0,$("#canvas-video".concat(e)).hide())},pauseVideo:function(s){!1!==this.assets.isPlay&&(this.assets.videoEvent=!0,this.assets.isPlay=!1,$("#canvas-video".concat(e)).show(),this.drawAssetsCanvas())},seekingVideo:function(e){var s=this;setTimeout(function(){currentTime=e.detail.plyr.media.currentTime,Math.abs(currentTime-s.assets.currentTime)>5&&(s.assets.videoEvent=!0,s.assets.isSeeking=!0,s.assets.currentTime=currentTime,s.clearCanvas(),s.drawAssetsCanvas(),s.retrievePercentFromPlayerProgress(s.player))},1e3)},seekingVideoAssets:function(e){this.player.currentTime!==e&&(this.player.currentTime=e,this.clearCanvas(),this.drawAssetsCanvas(),this.retrievePercentFromPlayerProgress(this.player)),this.drawAssetsCanvas(),console.log("currentTime seeking",this.player.currentTime)},changeMute:function(e){this.assets.isMute!==e&&(this.assets.videoEvent=!0,this.assets.isMute=e)},changeMuteAssets:function(e){this.player.muted!==e&&(this.assets.videoEvent=!0,this.player.muted=e)},changeSpeed:function(e){this.assets.speedValue!==e.detail.plyr.config.speed.selected&&(this.assets.videoEvent=!0,this.assets.speedValue=e.detail.plyr.config.speed.selected)},changeSpeedAssets:function(e){this.player.speed=e},changeVolume:function(e){this.assets.volumeValue!==e&&(this.assets.videoEvent=!0,this.assets.volumeValue=e)},changeVolumeAssets:function(e){this.player.volume!==e&&(this.assets.videoEvent=!0,this.player.volume=e)},changeStatus:function(e){e?this.player.play():(this.player.pause(),this.drawAssetsCanvas(),this.assets.addCanvasDraw=!0)},createVideoCanvas:function(){var s=this,t=document.createElement("canvas");t.setAttribute("id","canvas-video".concat(e)),t.setAttribute("class","canvas-video");var i=document.querySelector("#video-item-"+this._uid+" .plyr");i&&i.appendChild(t),canvasV=document.getElementById("canvas-video".concat(e)),canvasV.style.position="absolute",canvasV.style.zIndex="100",canvasV.addEventListener("click",function(e){e.stopPropagation()}),canvasV.addEventListener("mousemove",function(e){e.stopPropagation()}),canvasV.addEventListener("mousedown",function(e){e.stopPropagation()}),canvasV.addEventListener("mouseup",function(e){e.stopPropagation()}),this.canvas=canvasV,this.canvas.addEventListener("mousedown",function(e){s.mouseCanvasVideoClick=!0;var t=s.getMousePos(s.canvas,e),i={type:void 0,component:null,percent:s.retrievePercentFromPlayerProgress(s.player)};if(-1!==window.cursor.selected.indexOf("SHAPE_")){var a=window.cursor.selected.replace("SHAPE_","");switch(a){case"SQUARE":i.component=new ShapeSquare(t.x,t.y);break;case"CIRCLE":i.component=new ShapeCircle(t.x,t.y);break;case"TRIANGLE":i.component=new ShapeTriangle(t.x,t.y);break;case"LINE":i.component=new ShapeLine(t.x,t.y);break;default:console.error("Undefined function called ["+a+"].")}i.type=i.component.type,s.start_loc=new Location(t.x,t.y),s.assets.videoAssets.push(i)}else s.assets.videoAssets.push({type:"line",component:new Line(t.x,t.y),percent:s.retrievePercentFromPlayerProgress(s.player)})}),this.canvas.addEventListener("mouseleave",function(){!0===s.mouseCanvasVideoClick&&s.assets.videoAssets.length&&(s.video_assets=s.assets.videoAssets[s.assets.videoAssets.length-1],appendAnnotationsBlock(s.video_assets.percent,s._uid)),s.mouseCanvasVideoClick=!1,cursor.disabled=!1}),this.canvas.addEventListener("mouseup",function(){!0===s.mouseCanvasVideoClick&&s.assets.videoAssets.length&&(s.video_assets=s.assets.videoAssets[s.assets.videoAssets.length-1],appendAnnotationsBlock(s.video_assets.percent,s._uid)),s.mouseCanvasVideoClick=!1,s.start_loc=null,s.drawOnCanvas(),s.assets.addCanvasDraw=!0}),this.canvas.addEventListener("mousemove",function(e){var t=s.getMousePos(s.canvas,e);if(1==s.mouseCanvasVideoClick)if(-1!==window.cursor.selected.indexOf("SHAPE_")){var i=t.x-s.start_loc.x,a=t.y-s.start_loc.y;s.assets.videoAssets[s.assets.videoAssets.length-1].component.resize(i,a),s.start_loc.x=t.x,s.start_loc.y=t.y}else s.assets.videoAssets[s.assets.videoAssets.length-1].component.add_point(t.x,t.y)})},getMousePos:function(e,s){var t=e.getBoundingClientRect();return{x:(s.clientX-t.left)/(t.right-t.left)*e.width,y:(s.clientY-t.top)/(t.bottom-t.top)*e.height}},retrievePercentFromPlayerProgress:function(e){var s=e.duration/100,t=this.assets.currentTime/s;return t||0},appendAnnotationsBlock:function(e){function s(s,t){return e.apply(this,arguments)}return s.toString=function(){return e.toString()},s}(function(e,s){if($("div#video-item-".concat(s," div.plyr__progress")).length){var t='<span class="video_annotations" id="video_annotations_'.concat(s,'" data-annotation-percent="annotation_percent_').concat(e,'"style="left: ').concat(e,'%;"></span>');$("div#video-item-".concat(s," div.plyr__progress")).append(t)}else setTimeout(function(){appendAnnotationsBlock(e,s)},1e3)}),drawOnCanvas:function(){var e=this.canvas.getContext("2d");this.assets.videoAssets[this.assets.videoAssets.length-1].component.draw(e,-7.5,void 0)},clearCanvas:function(){this.canvas.getContext("2d").clearRect(0,0,this.canvas.width,this.canvas.height)},drawAssetsCanvas:function(){if(this.canvas)for(var e=this.canvas.getContext("2d"),s=this.retrievePercentFromPlayerProgress(this.player),t=0;t<this.assets.videoAssets.length;t++)"shape_circle"===this.assets.videoAssets[t].component.type?this.assets.videoAssets[t].component.draw=ShapeCircleFunctionDraw:"shape_triangle"===this.assets.videoAssets[t].component.type?this.assets.videoAssets[t].component.draw=ShapeTriangleFunctionDraw:"shape_square"===this.assets.videoAssets[t].component.type?this.assets.videoAssets[t].component.draw=ShapeSquareFunctionDraw:"shape_line"===this.assets.videoAssets[t].component.type?this.assets.videoAssets[t].component.draw=ShapeLineFunctionDraw:"line"===this.assets.videoAssets[t].component.type&&(this.assets.videoAssets[t].component.draw=line_draw),Math.abs(s-this.assets.videoAssets[t].percent)<=1&&(console.log("percent diff",Math.abs(s-this.assets.videoAssets[t].percent)),this.assets.videoAssets[t].component.draw(e,-7.5,void 0));this.assets.canvasGetAssets=!1}},watch:{"player.config.mutedpercent":function(e){this.changeMute(e)},"assets.isMute":function(e){this.changeMuteAssets(e)},"assets.volumeValue":function(e){this.changeVolumeAssets(e)},"player.config.volume":function(e){this.changeVolume(e)},"assets.speedValue":function(e){this.changeSpeedAssets(e)},"assets.isPlay":function(e){this.changeStatus(e)},"assets.currentTime":function(e){this.seekingVideoAssets(e)},"assets.isClose":function(e){e&&this.closeVideo()},"assets.position.top":function(e){document.getElementById(this.getId).style.top=e},"assets.position.left":function(e){document.getElementById(this.getId).style.left=e},"assets.size.width":function(e){document.getElementById(this.getId).style.width="".concat(e,"px")},"assets.size.height":function(e){document.getElementById(this.getId).style.height="".concat(e,"px")},"assets.canvasGetAssets":function(e){console.log("watcher ",e),e&&(console.log("watcher true"),this.drawAssetsCanvas())}}})}