.availability-top-panel {
    .langu-panel-header {
        text-align: center;

        .current-day {
            font-size: 1.188em;
            text-transform: uppercase;
            position: absolute;
            left: 0;
            right: 0;
            margin: 0 auto;
        }
        
        .btn-action {
            .glyphicon {
                vertical-align: top;
            }
        }

        .prev-day {
            float: left;
        }

        .next-day {
            float: right;
        }
        
        @include clear-after();
    }

    .langu-panel-body {
        padding: 15px;
    }
}

.add-availability-form {
    .datetime-picker {
        background: none;
        border: none;
        border-bottom: 1px solid #ababab;
        margin-bottom: 1em;

        &-label {
            margin-bottom: 0.857em;
            font-size: 0.875em;
            font-weight: 700;
            text-transform: uppercase;
            margin-bottom: 0;
            display: block;
        }

        & > .select-wrapper {
            position: relative;
            width: 50%;
            float: left;
        }
    }

    &-legend {
        float: right;
        margin-right: 15px;

        &-item {
            font-size: 0.813em;
            display: inline-block;
            margin: 0.462em 0 0.462em 0.462em;

            &:not(:last-child) {
                margin-right: 0.462em;
            }

            .legend-label {
                height: 1em;
                width: 1em;
                margin-right: 0.5em;
                display: inline-block;
                vertical-align: text-bottom;
            }

            &.free {
                .legend-label {
                    background-color: $langu_green;
                }
            }

            &.occupied {
                .legend-label {
                    background-color: $langu_gold;
                }
            }
        }
    }

    .btn[type="submit"] {
        float: right;
        margin-top: 2em;
        margin-bottom: 0.5em;
    }
}

.slots-container {
    margin-top: 0.5em;

    .slots-slot {
        padding: 0.625em;
        margin: 0.376em 0.625em;
        display: inline-block;
        position: relative;

        &.removing {
            opacity: 0.75;
        }

        .close {
            position: absolute;
            cursor: pointer;
            right: -.5em;
            top: -.375em;
            display: none;
            font-size: 1em;
            font-weight: 700;
            line-height: 1;
            @include opacity(1);
            
            &:before {
                content: '';
                position: absolute;
                top: 10%;
                height: 80%;
                width: 80%;
                left: 10%;
                display: block;
                background-color: #ffffff;
                border-radius: 99%;
            }
            
            .glyphicon {
                &:not(:hover) {
                    @include opacity(0.65);
                }
            }
        }

        &.free {
            background-color: $langu_green;

            .close {
                display: block;
            }
        }

        &.occupied {
            background-color: $langu_gold;
        }
    }
}

.copy-slots-form {
    margin-top: 1.75em;

    &-helper {
        font-size: 1.17em;
        padding: 0.334rem;
        display: inline-block;
    }
    
    .copy-slots-container {
        margin-bottom: 1.75em;

        &-button {
            margin-bottom: 1.75em;
        }

        &:before {
            clear: both;
        }

        .copy-slots-item {
            display: block;

            &-checkbox {
                & + .copy-slots-item-label {
                    background-color: #ffffff;
                }

                &:checked + .copy-slots-item-label {
                    background-color: $langu_green;
                }
            }

            &-label {
                &:before, &:after {
                    content: none;
                }

                padding: 0.1em 0;
                font-size: 0.75em;
                font-weight: 700;
                display: block;
                background-color: #cfcfcf;
                border: 1px solid #cccccc;
                border-radius: 2px;
                @include transition(0.2s);
            }
        }
    }
}
