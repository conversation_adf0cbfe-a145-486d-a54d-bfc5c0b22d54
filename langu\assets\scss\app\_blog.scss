.blog {
    &-wrapper {
        display: block;
        padding: 2em 15px;
        max-width: 1200px;
        
        @media(min-width: 480px) {
            margin: 0 auto;
            width: 84%;
        }
    }
    
    &-content {
        margin-top: 2em;
        
        @media(min-width: 768px) {
            margin-top: 0;
        }
        
        .blog-panel {
            @media(min-width: 768px) {
                margin-right: 1.5em;
                padding: 1.5em 3em;
            }
             
            & + .blog-panel {
                margin-top: 2em;
            }
        }
        
        .langu-pagination {
            float: left;
            margin-top: 1em;
            
            li > a, .disabled > span {
                background-color: #ffffff;
                
                &:hover {
                    background-color: #ffffff;
                }
            }
            
            .disabled > span {
                &:hover {
                    background-color: #ffffff;
                }
            }
            
            .active > span {
                background-color: rgb(251,176,59);
            }
        }
    }
    
    &-sidebar {
        font-weight: 600;
        
        .blog-panel {
            @media(min-width: 768px) {
                margin-left: 1.75em;
            }
        }
        
        &-title {
            font-size: 1.875em;
            font-weight: inherit;
            margin: 0 0 1em;
        }
        
        &-list {
            margin-bottom: 1.5em;
            list-style: none;
            padding-left: 0;
            font-size: 1.25em;
            color: $langu_gold;
            
            &:last-child {
                margin-bottom: 0;
            }
            
            li {
                line-height: 1.15;
                
                > * {
                    display: block;
                }
            }
        }
        
        &-post {
            &-title {
                color: $langu_primary;
                font-size: 0.6em;
                border-bottom: 0.5px solid;
                display: block;
                padding: 0 0 0.35em;
                margin-bottom: 1em;
            }
        }
    }
    
    &-panel {
        padding: 1.5em;
        background: #ffffff;
    }
    
    &-post {
        &-container {
            position: relative;
            color: $langu_primary;
        }
        
        &-title {
            font-size: 2.5em;
            color: $langu_gold;
            line-height: 1;
            margin: 0 0 0.5em;
            font-weight: 600;
            
            > a {
                display: block;
            }
        }
        
        &-date {
            font-size: 0.9375em;
            line-height: 1;
            margin: 0.9em 0;
            
            > a {
                color: $langu_gold;
            }
        }
        
        &-main-img {
            display: block;
            max-width: 100%;
            margin: 1.25em 0;
        }
        
        &-content {
            display: block;
            line-height: 1.5;
            
            a {
                color: $langu_gold;
            }
            
            img {
                max-width: 100%;
            }
        }
        
        &-categories {
            display: inline-block;
            margin-top: 2em;
            font-weight: 600;
            
            a {
                color: $langu_gold;
            }
        }
    }
}
