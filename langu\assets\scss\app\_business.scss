.business-page-container {
  .heading {
    text-align: center;
    background-color: #262626;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    display: flex;
    height: 300px;
    flex-direction: column;
    justify-content: flex-end;
    .text {
      text-align: center;
      font-size: 1.75em;
      line-height: 1.1;
      color: #ffffff;
      padding: 0.75em 20%;
      margin: 0;
      background-color: rgba(0, 0, 0, 0.6);
      .em {
        color: #fbb03b;
      }
    }
  }
  .content {
    background-color: #ffffff;
    color: #262626;
    padding: 5rem 0;
    .button-container {
      margin: 0 0 6rem;
      text-align: center;
    }
    .intro {
      font-size: 1.75em;
      line-height: 1.25;
      padding: 0 0 4rem;
      text-align: center;
      margin: 0 1.5rem;
    }
    .boxes {
      margin: 4rem 0;
      text-align: center;
      .box {
        flex: 1 1 0;
        padding: 0.5rem 1.25rem;
        font-size: 1.2em;
        line-height: 1.15;
        .icon {
          height: 3em;
          max-width: 40%;
        }
        .headline {
          font-weight: bold;
          display: block;
          margin: 0;
          color: inherit;
          font-size: inherit;
        }
        .text {
          margin: 0;
        }
      }
    }
    .members {
      padding: 4.2rem 0 6rem;
      margin: 0 15px;
      .member {
        + .member {
          margin-top: 0.5rem;
        }
        &:after {
          display: block;
          clear: both;
          content: '';
        }
        .image {
          width: 50%;
          max-width: 250px;
          height: auto;
          float: left;
          margin-right: 19px;
          -webkit-box-shadow: 0px 3px 10px -2px rgba(0, 0, 0, 0.6);
          box-shadow: 0px 3px 10px -2px rgba(0, 0, 0, 0.6);
          margin-bottom: 15px;
        }
        .top {
          float: left;
          margin: 0;
          font-size: 1.2em;
          color: inherit;
          max-width: calc(50% - 19px);
          line-height: 1.15;
          > {
            .name {
              display: block;
              color: #fbb03b;
            }
            .title {
              display: block;
            }
          }
        }
        .bio {
          line-height: 1.15;
          background-color: #eeeeee;
          padding: 25px 40px 25px 25px;
          width: 100%;
          clear: both;
        }
        .bio-inner {
          font-size: 1.2em;
          margin: 0;
        }
      }
    }
    .section {
      margin: 0 1rem;
      text-align: center;
      .headline {
        margin: 0 auto;
        line-height: 1;
        font-size: 1em;
        display: inline-block;
        color: inherit;
        position: relative;
        text-transform: none;
        &:after {
          display: block;
          height: 4px;
          margin: 2px auto 0;
          content: '';
          background-color: #fbb03b;
          width: 100%;
        }
        .regular {
          font-size: 3.4em;
          padding: 0 0.12em;
          font-weight: bold;
        }
        .super {
          color: #fbb03b;
          text-transform: uppercase;
          position: absolute;
          top: 0;
          left: 1.6em;
          font-size: 1.105em;
          font-weight: bold;
        }
      }
      .text {
        font-size: 1.2em;
        line-height: 1.15;
        margin: 2rem 0 0;
        a {
          color: #fbb03b;
        }
      }
      .text-bold {
        font-weight: bold;
      }
    }
    .section-team {
      &.en .headline {
        .regular {
          padding-left: 0.75em;
        }
        .super {
          left: 0.3em;
        }
      }
      &.pl .headline {
        .regular {
          padding-left: 0.4em;
        }
        .super {
          left: 0.3em;
        }
      }
      &.es .headline {
        .super {
          left: -0.4em;
          top: -0.2em;
        }
      }
    }
    .section-teachers.pl .headline .super {
      left: 3.25em;
    }
    .section-teachers.es .headline .super {
      left: -0.4em;
      top: -0.2em;
    }
    .section-business.pl .headline {
      .regular {
        padding-left: 0.75em;
      }
      .super {
        left: 0.3em;
      }
    }
    .button-container {
      margin-bottom: 4rem;
    }
    .gif-box {
      text-align: center;
      margin: 0 0 6rem;
      img {
        max-width: 100%;
      }
    }
    .boxes {
      counter-reset: boxes;
      .box {
        position: relative;
        counter-increment: boxes;
        background-color: #eeeeee;
        padding: 1.5rem 1rem;
        margin: 1rem 2rem;
        &:before {
          content: counter(boxes) ".";
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          top: 0;
          left: 0;
          width: 3em;
          height: 3em;
          background-color: #fbb03b;
          color: #ffffff;
          font-weight: bold;
          border: 0.15em solid #ffffff;
          box-sizing: content-box;
          -webkit-border-radius: 99%;
          -moz-border-radius: 99%;
          border-radius: 99%;
          -moz-transform: translate(-25%, -25%);
          -o-transform: translate(-25%, -25%);
          -ms-transform: translate(-25%, -25%);
          -webkit-transform: translate(-25%, -25%);
          transform: translate(-25%, -25%);
        }
        .icon {
          margin-bottom: 1.5rem;
        }
        .headline {
          font-size: 1.3em;
          text-align: left;
        }
        .text {
          margin-top: 0.5em;
          text-align: left;
        }
      }
    }
    .section-how-it-works {
      &.en .headline {
        .regular {
          padding-right: 0.85em;
        }
        .super {
          left: -2.5em;
        }
      }
      &.pl .headline .super {
        left: 0;
        top: -0.5em;
      }
      &.es .headline .super {
        left: 1.9em;
        top: -0.8em;
      }
    }
    .section-philosophy {
      margin-bottom: 4rem;
      &.en .headline {
        .regular {
          padding-right: 0.5em;
        }
        .super {
          left: -1.8em;
          top: -0.7em;
        }
      }
      &.es .headline {
        .super {
          left: 3em;
          top: -1em;
        }
      }
    }
  }
  .btn {
    -webkit-border-radius: 1.5em;
    -moz-border-radius: 1.5em;
    border-radius: 1.5em;
    line-height: 1;
    padding: 1em 2.5em;
    display: inline-block;
    font-size: inherit;
    font-weight: inherit;
    color: #ffffff;
    &:hover, &:active, &:focus {
      color: #ffffff;
    }
  }
  .btn-orange {
    background-color: #fbb03b;
  }
  .btn-green {
    background-color: #7fb802;
  }
}

@media (min-width: 480px) {
  .business-page-container .heading {
    height: 500px;
  }
}

@media (min-width: 480px) {
  .business-page-container .content .intro {
    margin: 0 20%;
  }
}

@media (min-width: 480px) {
  .business-page-container .content .boxes {
    margin: 4em 10%;
    display: flex;
    justify-content: space-between;
  }
}

@media (min-width: 480px) {
  .business-page-container .content .boxes .box {
    padding: 0 1.25rem;
  }
}

@media (min-width: 480px) {
  .business-page-container .content .members {
    margin: 0 14%;
  }
}

@media (min-width: 768px) {
  .business-page-container .content .members .member .image {
    height: 250px;
    width: 250px;
    margin-bottom: 0;
  }
}

@media (min-width: 768px) {
  .business-page-container .content .members .member .top > .name {
    overflow: hidden;
    height: 35px;
    font-size: 28px;
  }
}

@media (min-width: 768px) {
  .business-page-container .content .members .member .top > .title {
    overflow: hidden;
    height: 25px;
    font-size: 19px;
  }
}

@media (min-width: 768px) {
  .business-page-container .content .members .member .top {
    max-width: calc(100% - 269px);
    line-height: 1;
    height: 60px;
  }
}

@media (min-width: 768px) {
  .business-page-container .content .members .member {
    .bio {
      float: left;
      clear: none;
      height: 190px;
      max-width: calc(100% - 269px);
      padding: 18px 40px 18px 10px;
    }
    .bio-inner {
      font-size: 1.2em;
      overflow: hidden;
      max-height: 100%;
    }
  }
}

@media (min-width: 480px) {
  .business-page-container .content .section {
    margin: 0 20%;
  }
}

@media (min-width: 768px) {
  .business-page-container .content .gif-box img {
    max-width: 55%;
  }
}

@media (min-width: 480px) {
  .business-page-container .content .boxes .box {
    margin: 0 0.75rem;
  }
}