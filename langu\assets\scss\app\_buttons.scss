.btn {
    font-size: 0.813em;
    
    &:focus {
        outline: none;
    }
    
    &.btn-action {
        border: none;
        background: none;
        text-transform: uppercase;
        color: #000000;
        
        &:hover, &:active {
            background: none;
        }
        
        .disabled {
            cursor: not-allowed;
            @include opacity(0.65);
        }
    }
    
    &.btn-tab {
        border: none;
        border-radius: 0;
        background: rgba(255, 255, 255, 0.5);
        padding: 0.813em;
        color: #787878;
        text-transform: uppercase;
        text-align: center;
        font-weight: 700;
        box-shadow: none;
        
        &.active {
            background: $langu_gold;
            color: #ffffff;
        }
    }

    &.btn-gold {
        background: $langu_gold;
        padding: 0.334em 0.667em;
        font-size: 1em;
        border-radius: 0;
        color: #ffffff;
        text-transform: uppercase;
        
        &.disabled {
            pointer-events: auto;
            background: #aaaaaa;
        }
    }
    
    &.btn-green {
        background: $langu_green;
        padding: 0.334em 0.667em;
        font-size: 1em;
        border-radius: 0;
        color: #232323;
        text-transform: uppercase;
    }

    &.btn-standard {
        padding: 0.334em 0.667em;
        border-radius: 0;
        background: #ffffff;
        border: none;
        color: #000000;
        text-transform: uppercase;
        font-size: 1em;
/*        display: inline-flex;
        align-items: center;*/ 
    }
    
    & > .glyphicon {
        line-height: 1.846;
        vertical-align: bottom;

        &.pre {
            margin-right: 0.2em;
        }

        &.post {
            margin-left: 0.2em;
        }
    }

    &.btn-big-ico {
        padding-bottom: 0.334em;
        padding-top: 0.166em;
        
        & > .glyphicon {
            line-height: 1;
            font-size: 2em;
        }  
    }

    .emp {
        font-weight: 900;
    }
    
    &.btn-rounded {
        border-radius: 0.125em;
    }
    
    &.btn-rect {
        border-radius: 0;
    }
    
    &.btn-small {
        padding: 0.188em 0.334em;
    }
    
    &.btn-right {
        float: right;
    }
}
