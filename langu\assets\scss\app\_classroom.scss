body {
    line-height: unset;
    font-size: unset;
}

.classroom-canvas-header {
    position: absolute;
    top: 20px;
    left: 70%;

    display: inline-block;
}

.classroom-canvas-header .canvas-toolbar-wrapper{
    display: none;
    background: #F6F6F6;
    display: inline-block;
    border: solid 3px #646464;
    padding: 0 5px;
    border: none;

    border-radius: 9px;
}

.canvas-toolbar {
    display: none;
    align-self: center;
}

.canvas-toolbar-button {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 5px;
    cursor: pointer;

}

.canvas-toolbar-button:first-child {
    padding-right: 0;
}

.canvas-toolbar-button-bold::after {
    content: '3';

    position: absolute;
    bottom: 0;
    right: 0;

    font-weight: bold;
    font-size: 18px;
}

.canvas-header-buttons {
}

.canvas-header-main-button {
    display: inline-block;
    margin-right: 10px;
    background-color: #3C87F8;
    border-radius: 66px;
    padding: 10px 10px;
    text-transform: uppercase;
    color: #fff;
    -webkit-box-shadow: 0px 11px 18px -2px rgba(0,0,0,0.4);
    -moz-box-shadow: 0px 11px 18px -2px rgba(0,0,0,0.4);
    box-shadow: 0px 11px 18px -2px rgba(0,0,0,0.4);
    cursor: pointer;

    &-finish {
        background-color: #AD0C5C;
    }
}

#canvas_div {
    height: 100%;
    background: white;
}

#catch-pdf {
    position: relative;
    border: #646464 2px solid;
    border-radius: 7px;

    &:hover {
        border-color: #3c87f8;
    }
}

#upload-button {
    width: 150px;
    display: block;
    margin: 20px auto;
}

#file-to-upload {
    display: none;
}

#pdf-main-container {
    width: 400px;
    margin: 20px auto;
    top: 100px;
    left: 70%;
    border-radius: 7px;
}

#pdf-loader {
    display: none;
    text-align: center;
    color: #999999;
    font-size: 13px;
    line-height: 100px;
    height: 100px;
}

#pdf-contents {
    display: none;
    border-radius: 7px;
}

#pdf-meta {
    overflow: hidden;
    margin: 0 0 20px 0;
}

#pdf-buttons {
    float: left;
    position: absolute;
    padding-left: 30%;
    top: 2px;
}

#pdf-prev, #pdf-next {
    background: #E5E5E5;
    color: #000;
    border: #646464 2px solid;
    border-radius: 7px;
    line-height: 19px;
}

.delete-pdf {
    font-size: 10px;
    outline: none;

    &-small {
        position:absolute;
        top:2.5px;
        right: 2.5px;
        background: #E5E5E5;
        color: #000;
        border: #646464 2px solid;
        border-radius: 7px;
        font-size: 17px;
        line-height: 19px;
    }

    &-big {
        height: 30px;
        background:#3C87F8;
        color: #fff;
        font-size: 16px;
        font-weight: bold;
        bottom: 100px;
        right: 10px;
        position: fixed;
        border: none;
        border-radius: 7px;
    }
}

#namePdf {
    background: #fff;
    margin: auto;
    height: 30px;
    padding-left: 10px;
    line-height: 30px;
}

#page-count-container {
    position: absolute;
    top: 4px;
    left: 55%;
    background-color: #fff;
}

#pdf-current-page {
    display: inline;
}

#pdf-total-pages {
    display: inline;
}

#pdf-canvas {
    border: 1px solid rgba(0,0,0,0.2);
    box-sizing: border-box;
    width: 100%;
    height: auto;
    clear: both;
}

#dropbox {
    height: 56px;
    width: 56px;
    position: fixed;
    right: 35px;
    bottom: 140px;
    background:#3C87F8;
    border-radius: 50%;
    color: #fff;
    font-size: 35px;
    z-index: 9999;
    text-align: center;
    cursor: pointer;
    line-height: 56px;
    font-weight: bold;
}

#dropboxInput {
    display:none;
    height: 56px;
    width: 56px;
    position: fixed;
    right: 25px;
    bottom: 100px;
    background:#3C87F8;
    border-radius: 50%;
    color: #fff;
    font-size: 35px;
    z-index: 9999;
    text-align: center;
    cursor: pointer;
    line-height: 56px;
    font-weight: bold;
}

#page-loader {
    height: 100px;
    line-height: 100px;
    text-align: center;
    display: none;
    color: #999999;
    font-size: 13px;
}

.classroom-header {
    @include flex-row-justified();
    width: 100%;

    .classroom-subtitle {
        flex: 5 1 auto;
    }

    .classroom-header-buttons {
        flex: 1 1 auto;
        display: flex;
        justify-content: flex-end;

        .btn {
            margin: 0 0.5em;

            &:first-child {
                margin-left: 0;
            }

            &:last-child {
                margin-right: 0;
            }

            &.disabled {
                pointer-events: auto;
                cursor: default;
            }
        }
    }
}

/*Shared whiteboard styles*/
.shared-whiteboard{
    margin-top: -15px;
    margin-left: -5px;
    width: 100vw;
    height: 100vh;
    // background-color: pink; // We need to see the text box underneath
}
#whiteboard_canvas{
    cursor: none;
}

// The element to host a youtube video
.whiteboard_video_el{
    position: absolute;
    width: 600px;
    height: 400px;
    background-color: pink;
    display: inline-block;
    z-index: 1;
}
.whiteboard_video_canvas{
    position: absolute;
    width: 600px;
    height: 355px;
    // background-color: pink;
    display: inline-block;
    z-index: 1;
}
.whiteboard_textarea{
    background-color: white;
    position:absolute;
    z-index: -1;
    overflow-y: scroll;
    padding-top: 45px;
    padding-left: 11px;  // To make the div match the summernote editor
}
#summernote_wrapper {
    top: 130px !important;
    left: 60px !important;
    height: 500px !important;
}

#text_summernote{
    position: absolute;
    width: 400px;
    height: 200px;
    z-index: 20;
    left: 100px;
    top: 100px;
}
.summernote-pb{
    position:absolute;
}

.classroom-content {
    margin-top: 1.5em;

    .note-editor {

        .note-editable {
            min-height: 50vh;
            /*max-height: 60vh;*/
            height: 60vh;

            p {
                margin: 0;
                line-height: 1.5;
            }
        }
    }
}

.classroom-sidebar {
    margin-top: -1.563em;
    margin-left: -1.563em;
    margin-right: -1.563em;
    background-color: #141414;

    .streams-container {
        position: fixed;
        top: $max_nav_height;
        right: 0;
        z-index: 3;
        width: 21.875em;
        max-width: 21.875em;
        background-color: #232323;

        .stream-controls {
            .control {
                overflow: hidden;

                @include opacity(0.6);
                @include transition(opacity 0.25s);

                &:hover {
                    @include opacity(1);
                    @include transition(opacity 0.25s);
                }

                &.fa-expand {
                    color: $langu_gold;
                    font-size: 1.5em;
                }
            }

            &.remote {
                position: absolute;
                right: 0.25em;
                top: 90%;
                cursor: pointer;
                display: block;
                z-index: 112;
                text-align: right;
            }

            &.screenshare {
                position: absolute;
                right: 0.25em;
                bottom: -3px;
                cursor: pointer;
                display: block;
                z-index: 112;
                text-align: right;
            }

            &.local {
                position: absolute;
                right: 0;
                left: 0;
                width: 100%;
                top: 90%;
                cursor: pointer;
                display: block;
                z-index: 111;
                text-align: center;

                .control {
                    padding: 0 0.125em;
                    min-width: 1.5em;
                }
            }
        }

        .remote-stream {
            // width: 100%;
            position: absolute;
            width: 300px;
            height: 300px;
            right: 5px;
            top: 60px;
        }

        .local-stream {
            max-width: 6.25em;
            min-width: 6.25em;
            position: absolute;
            right: 0;
            top: 0;
            z-index: 99;
        }

        .screenshare-stream {
            width: 100%;
            position: relative;

            .stream-container {
                position: relative;
                .OT_root {
                    width: 100% !important;
                }
            }
        }
    }
}

.sidebar-tabs-container {
    margin-top: 16.407em;

    &.screenshare-active {
        margin-top: 28.75em;
    }

    .langu-tabs {
        background-color: #141414;

        & > li {
            border: none;

            & > a {
                text-transform: uppercase;

                &:focus, &:hover {
                    box-shadow: none;
                }
            }

            &.active {
                background-color: #232323;

                & > a {
                    box-shadow: none;
                }
            }
        }
    }

    .langu-tab-content {
        background-color: #232323;
    }

    .objectives-list {
        margin: 0 2em;
        font-size: 0.875em;
        position: relative;

        &:before {
            content: '';
            display: block;
            width: 1px;
            height: 100%;
            background-color: $langu_gold;
            position: absolute;
            left: 0;
        }

        &-item {
            padding-left: 1.5em;
            position: relative;

            &:before {
                content: '';
                display: block;
                @include bullet(0.5em);
                background-color: $langu_gold;
                position: absolute;
                left: -3px;
                @include abs-vert-center();
            }
        }
    }

    .files-list {
        &-item {
            margin: 0 0.214em 1em;
            width: 6.25em;

            .download {
                right: 0.25em;
                color: $langu_primary;
            }
        }
    }
}

.langu-progress-bar {
    background-color: $langu_gold;
}

.progress-lg {
    height: 1em;
}

.progress-bar:only-child {
    @include border-radius(3px);
}
