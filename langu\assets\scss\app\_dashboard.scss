.page-subtitle {
    &.dashboard-subtitle {
        padding: 0.250em 0;
    }
}

.resources-picker-form {
    .form-control {
        &:focus {
            outline: none;
            box-shadow: none;
        }

        color: inherit;
        font-size: 0.875em;
        box-shadow: none;
        border-bottom: 1px solid #a1a1a1;
        padding: 0;
        @include border-radius(0);
    }

    .input-group {
        font-size: 0.875em;

        &-addon {
            font-size: inherit;
        }
    }

    .add-objective {
        margin-top: 1em;
    }
}

.purchases-list {
    margin-top: 3.125em;
    counter-reset: purchases-list;
    position: relative;

    @include clear-after();

    &:before {
        content: '';
        display: block;
        width: 4px;
        height: 100%;
        background-color: #232323;
        position: absolute;
        top: 0;
        left: 2em;
        @include transform(translateX(-50%));
    }
    
    &:after {
        width: 1em;
        height: 1em;
        background-color: #232323;
        position: absolute;
        bottom: 0;
        left: 2em;
        @include transform(translateX(-50%));
        @include border-radius(99%);
    }

    &-item {
        counter-increment: purchases-list;

        &:before {
            content: counter(purchases-list);
            width: 2em;
            height: 2em;
            background: #232323;
            border-radius: 99%;
            font-size: 2em;
            line-height: 2;
            text-align: center;
            color: #ffffff;
            font-weight: 700;
            position: relative;
            display: inline-block;
            float: left;
            margin-right: 0.5em;
        }

        @include clear-after();

        .purchases-panel {
            display: block;
            overflow: hidden;
            width: auto;
            
            &-header {
                .title {
                    .language-subtitle {
                        font-size: 1.188em;
                        line-height: 1.158em;
                        font-weight: 700;
                        text-transform: uppercase;
                    }

                    .user-subtitle {
                        display: block;
                        font-size: 0.875em;
                        color: $langu_gold;
                    }
                }
            }

            &-body {
                .info {
                    list-style: none;
                    margin: 6px 0 8px 0;
                    padding: 0;
                    font-size: 0.875em;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;

                    .item {
                        position: relative;
                        padding: 0 8px 0 7px;
                        margin: 3px 0;
                        float: left;
                        font-weight: 400;
                        line-height: 1.875em;

                        & > .glyphicon {
                            padding: .334em 0;
                            line-height: 1.6;
                        }

                        &:after {
                            content: '';
                            height: 100%;
                            background: #232323;
                            width: 1px;
                            position: absolute;
                            top: 0;
                            right: 0;
                        }

                        &:first-child {
                            padding-left: 0;
                        }

                        &:last-child {
                            &:after {
                                content: none;
                            }
                        }
                    }
                }
            }
        }
    }
}

.purchases-list, .lessons-list {
    margin-top: 3.125em;

    @include clear-after();

    &-day {
        .date {
            width: 4.917em;
            height: 4.917em;
            background: #232323;
            border-radius: 99%;
            font-size: 0.750em;
            vertical-align: middle;
            display: table-cell;
            line-height: 1;
            text-align: center;
            color: #ffffff;
            font-weight: 700;
            float: left;
            padding: 10px;
            position: relative;
            z-index: 1;

            .emp {
                font-size: 2em;
                display: block;
            }
        }
    }

    &-inner {
        margin: 0;
        margin-left: 5em;
        list-style: none;
        padding: 0;

        & > li {
            position: relative;

            &:before, &:after {
                content: '';
                width: 2px;
                position: absolute;
                background: #232323;
                left: -3.25em;
            }

            &:before {
                top: 0;
                height: calc(50% - 15px);
            }

            &:after {
                bottom: 0;
                height: calc(50% - 15px);
            }

            & > * {
                &:before {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    background: #232323;
                    top: 0;
                    left: -3.25em;
                    margin-left: -7px;
                    border-radius: 99%;                    
                    top: 40%;
                    margin-top: -8px;
                }

                &:after {
                    top: 50%;
                    margin-top: -0.25em;
                    content: attr(data-time);
                    left: -3.25em;
                    position: absolute;
                    @include transform(translateX(-50%));
                    font-size: 0.875em;
                    line-height: 1;
                    font-weight: 700;
                    margin-left: -5px;
                }
            }

            &:first-child {
                & > * {
                    &:before {
                        top: 60%;
                    }

                    &:after {
                        top: 70%;
                    }
                }

                &:before {
                    height: calc(70% - 15px);
                }

                &:after {
                    height: calc(30% - 15px);
                }
            }            

            &:last-child {
                &:after {
                    content: none;
                }
            }
        }
    }

    &-date {
        float: left;
        margin: 0;
        width: 3.688em;
        height: 3.688em;
        position: relative;
        display: block;
        background: #232323;
    }

    &-item {
        position: relative;
        padding-bottom: 10px;

        &-header {

            .title {
                .language-subtitle {
                    font-size: 1.188em;
                    line-height: 1.158em;
                    font-weight: 700;
                    text-transform: uppercase;
                    
                    .emp {
                        color: $langu_gold;
                    }
                }

                .user-subtitle {
                    display: block;
                    font-size: 0.875em;
                    color: $langu_gold;
                }
            }
        }

        &-body {
            .info {
                list-style: none;
                margin: 0;
                padding: 0;
                font-size: 0.875em;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                
                &:last-child {
                    margin-bottom: 0;
                }

                .item {
                    position: relative;
                    padding: 0 8px 0 7px;
                    margin: 3px 0;
                    float: left;
                    font-weight: 400;
                    line-height: 1.875em;

                    & > .glyphicon {
                        padding: .334em 0;
                        line-height: 1.6;
                    }

                    &:after {
                        content: '';
                        height: 100%;
                        background: #232323;
                        width: 1px;
                        position: absolute;
                        top: 0;
                        right: 0;
                    }

                    &:first-child {
                        padding-left: 0;
                    }

                    &:last-child {
                        &:after {
                            content: none;
                        }
                    }
                }
            }
        }
    }
}

.student-info {
    &__icon {
        margin-right: 1em;
        display: inline-block;
        &:before {
            content: '';
            background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIHg9IjBweCIgeT0iMHB4IiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTAwIDEwMDsiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxwYXRoIGQ9Ik01MCw0LjhDMjUsNC44LDQuOCwyNSw0LjgsNTBDNC44LDc1LDI1LDk1LjMsNTAsOTUuM0M3NSw5NS4zLDk1LjMsNzUsOTUuMyw1MEM5NS4zLDI1LDc1LDQuOCw1MCw0Ljh6IE01NC40LDcyLjUgIGMwLDIuNC0yLDQuNC00LjQsNC40cy00LjQtMi00LjQtNC40VjQxLjVjMC0yLjQsMi00LjQsNC40LTQuNHM0LjQsMiw0LjQsNC40VjcyLjV6IE01MCwzMmMtMi40LDAtNC40LTItNC40LTQuNCAgYzAtMi40LDItNC40LDQuNC00LjRzNC40LDIsNC40LDQuNEM1NC40LDMwLDUyLjQsMzIsNTAsMzJ6Ij48L3BhdGg+PC9zdmc+") no-repeat;
            height: 1em;
            width: 1em;
            position: absolute;
            top: 0.4em;
            left: .5em;
        }
    }
}