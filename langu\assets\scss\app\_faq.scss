.faq {
    margin-top: 25px;
    margin-bottom: 25px;

     h1 {
        font-size: 35px;
        font-weight: bold;
        padding:0;
        margin:0;
        color: #2d2d2d;
    }

    h3 {
        font-size: 25px;
        margin: 0 0 10px 0;
    }

    .whitewrapper {
        padding: 25px;
        background-color: #ffffff;
        border-radius: 10px;
        -webkit-box-shadow: 2px 2px 2px 0px rgba(204,204,204,1);
        -moz-box-shadow: 2px 2px 2px 0px rgba(204,204,204,1);
        box-shadow: 2px 2px 2px 0px rgba(204,204,204,1);   
        
        .faq-section {
            margin-left: 15px;
            margin-right: 15px;
            margin-top: 25px;
            padding-bottom: 25px;
            border-bottom: 1px solid #eee;

            h3 {
                color: $langu_gold;
            }

            li {
                .question {
                    padding-left: 35px;
                    position: relative;
                    @include transition(0.3s);

                    &:before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 0;
                        display: block;
                        width: 1.4em;
                        height: 2px;
                        background: $langu_gold;
                        @include transition(0.3s);
                        @include transform(translateY(-50%));
                    }

                    &:after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 1.5em;
                        display: block;
                        width: 2px;
                        height: 1.4em;
                        background: $langu_gold;
                        @include transform(translate(-0.8571em, -50%));
                        @include transition(0.3s);
                    }

                    &.active {
                        font-weight: bold;

                        &:before {
                            background: $langu_gold;
                        }
        
                        &:after {
                            background: $langu_gold;
                            height: 2px;
                        }
                    }                            
                }
            }

            .card {
                line-height: normal;
                position: relative;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-orient: vertical;
                -webkit-box-direction: normal;
                -ms-flex-direction: column;
                flex-direction: column;
                min-width: 0;
                word-wrap: break-word;
                
                a {
                    color: $langu_gold;
                }

                img {
                    width: 100%;
                }

                .video-container {
                    position:relative;
                    padding-bottom:56.25%;
                    padding-top:30px;
                    height:0;
                    overflow:hidden;
                }
                
                .video-container iframe, .video-container object, .video-container embed {
                    position:absolute;
                    top:0;
                    left:0;
                    width:100%;
                    height:100%;
                }
            }

            .card-body {
                -webkit-box-flex: 1;
                -ms-flex: 1 1 auto;
                flex: 1 1 auto;
                padding: 0.5rem 0 1.25rem 0;
            }
        }
    }

    .sidebar {
        h2 {
            font-size: 22px;
            color: #2d2d2d;
        }
        p {
            font-size: 15px;
            line-height: normal;
            img {
                float: left;
                width: 80px;
                padding-right: 10px;
            }
        }
        .intro {
            padding-bottom: 25px;
            border-bottom: 1px solid #cccccc;
            min-height: 130px;
            p {
                margin-bottom: 0;
            }
        }
        .contact {
            margin-top: 25px;
            h3 {
                color: #2d2d2d;
                font-size: 22px;
            }
            i {
                color: #2d2d2d;
                vertical-align: middle;
                padding-right: 15px;
            }
            li {
                color: #2d2d2d;                
                font-weight: bold;
            }
        }
    }
}