footer {
    background: rgba(0,0,0,0.84);
    padding: 40px 15px 20px;
    color: #FFF;

    h3 {
        font-size: 12px;
        line-height: 1;
        color: #FFF;
        font-weight: bold;
        padding-bottom: 10px;
        border-bottom: solid 2px #313131;
        margin: 0;
        margin-bottom: 6px;
    }

    ul {
        margin: 0 -0.75em;
        list-style: none;
        padding: 0;
        
        @media(min-width:768px) {
            &:not(.no-justify) {
                text-align: justify;
            }
        }
        
        &:after {
            content: "";
            display: inline-block;
            width: 100%;
        }

        &.social-links {
            margin: 0;
            
            @media (max-width: 767px) {
                text-align: left; 
                margin: 0 -0.2em;
            }
            
            li {
                display: inline-block;
                
                &.fb {
                    a {
                        background-image:url(../images/fb.png);
                    }
                }

                &.linkedin {
                    a {
                        background-image:url(../images/linkedin.png);
                    }
                }

                &.inst {
                    a {
                        background-image:url(../images/instagram.png);
                    }
                }

                &.twt {
                    a {
                        background-image:url(../images/twitter.png);
                    }
                }

                a {
                    display: block;
                    width: 30px;
                    height: 30px;
                    text-indent: -9999px;
                    background-position: center center;
                    background-repeat: no-repeat;
                    padding: 0;
                    
                    @media (max-width: 767px) {
                        padding: 0 0.25em;
                        box-sizing: content-box;
                    }
                }
            }
        }

        li {
            display: block;
            line-height: 1.5;

            @media (min-width: 768px) {
                display: inline-block;
                line-height: 1;
                
                a {
                    padding: 0 1em;
                }
            }
            
            a {
                cursor: pointer;
                color: rgba(255,255,255,0.4);
                text-decoration: none;
                font-size: 12px;
                display: block;
                padding: 0.2em 1em;
                
                &:hover, &:visited {
                    color: rgba(255,255,255,0.5);
                    text-decoration: none;
                }
            }
        }
    }

    #copyrights {
        font-size: 10px;
        color: #FFF;
        display: inline-block;
        
        .glyphicon-heart {
            color: #e03f2a;
        }
    }
}