@import "../common/inGoodCompany";

#search-icon-button {
    position: absolute;
    right: 0.250em;
    font-size: 5em;
    bottom: 0.313em;
    line-height: 1;
    cursor: pointer;
    color: inherit;
    z-index: 1;

    &:hover {
        color: $langu_gold;
    }
}

#how-it-works-scroll {
    cursor: pointer;
    display: block;
    z-index: 100;
    color: $langu_gold;
    font-size: 4em;
    margin: 0;
    position: absolute;
    left: 0;
    right: 0;
    text-align: center;

    &:hover, &:visited, &:active {
        color: $langu_gold;
    }
}

#home-search {

    &-language {
        @include opacity(0);
        top: 50%;
        @include transform(translateY(-50%));
        position: absolute;
    }

    .form-step {
        height: 0;
        overflow: hidden;

        .viewport-height {
            min-height: calc(100vh - 100px);
        }

        &.form-step-language {
            &.current {
                min-height: calc(100vh - 55px);
            }

            .label {
                display: inline-block;
                text-align: left;
            }
        }

        &.form-step-learning {
            .title {
                margin: 5px 15px;

                .btn {
                    font-size: 1em;
                    padding-top: 0.5em;
                }

                .label {
                    font-size: 1.563em;
                    font-weight: 700;
                    white-space: normal;
                    display: inline-block;
                    text-align: left;
                    margin-bottom: 0;

                    .emp {
                        color: $langu_gold;
                        font-weight: 700;
                    }
                }
            }
        }

        .label {
            font-size: 1.695em;
            line-height: 1.145;
            margin-bottom: 0.852em;
            font-weight: bold;
            color: #232323;
        }

        &.current {
            height: auto;
            width: 100%;

            .home-search-purpose {
                @include transform(translateY(0));
            }
        }

        .home-search-purpose {
            @include transform(translateY(100%));
            padding-bottom: 9999px;
            margin-bottom: -9999px;
            position: relative;
            transition-duration: 0.6s;
            display: flex;
            align-items: center;
            justify-content: center;

            &.life {
                background-color: #ffbe41;

                .image {
                    background-image: url('../images/life-icon-white.svg');
                }

                .text:after {
                    background: $langu_gold;
                }

                .text-minor {
                    top: 0;
                    margin-left: -80px;
                    position: absolute;
                }

                .text-major {
                    position: absolute;
                    margin-left: -30px;
                }

                &.lang-pl {
                    .text-minor {
                        top: 0;
                        margin-left: -110px;
                        position: absolute;
                    }

                    .text-major {
                        position: absolute;
                        margin-left: -70px;
                    }
                }
            }

            &.career  {
                background-color: #f4a636;
                transition-delay: 0.4s;

                .image {
                    background-image: url('../images/career-icon-white.svg');
                }

                .text:after {
                    background: #ffbe41;
                }

                .text-minor {
                    margin-left: -175px;
                }

                .text-major {
                    position: absolute;
                    left: 0;
                    right: 0;
                }

                &.lang-pl {
                    .text-minor {
                        margin-left: -160px;
                    }
                }
            }

            &.education {
                background-color: $langu_gold;
                transition-delay: 0.2s;

                .image {
                    background-image: url('../images/education-icon-white.svg');
                }

                .text:after {
                    background: #f4a636;
                }

                .text-minor {
                    margin-left: -135px;
                }

                .text-major {
                    position: absolute;
                    left: 0;
                    right: 0;
                }

                &.lang-pl {
                    .text-minor {
                        margin-left: -85px;
                    }
                }
            }

            .content {
                color: #FFFFFF;
                text-align: center;
                padding: 30px 0;
                width: 100%;
                min-height: calc(100vh - 100px);
                display: flex;
                flex-direction: column;
                justify-content: center;

                .image {
                    display: block;
                    margin: 0 auto 2em;
                    height: 5em;
                    background-size: contain;
                    background-position: center center;
                    background-repeat: no-repeat;
                    width: 100%;
                }

                .text {
                    line-height: 1;
                    position: relative;
                    height: 4.375em;
                    width: 100%;

                    &:after {
                        content: '';
                        position: absolute;
                        top: 5.000em;
                        left: 0;
                        right: 0;
                        width: 14.375em;
                        margin: 0 auto;
                        display: block;
                        height: 0.250em;
                        z-index: -1;
                    }

                    .text-major {
                        font-size: 5em;
                        font-weight: 900;
                    }

                    .text-minor {
                        text-transform: uppercase;
                        font-size: 1.4em;
                    }
                }

                .desc {
                    font-size: 1.125em;
                    margin: 1.666em auto;
                    max-width: 16.111em;
                    line-height: 1.333;
                }

                .label {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    z-index: 1;
                    display: block;
                    margin: -30px 0 0;
                    padding: 0;
                    cursor: pointer;
                }
            }
        }

        &.form-step-proficiency {
            .home-search-proficiency {
                @include transition(background 0.3s ease-in-out);

                &.a1 {
                    background: #313131;

                    .img {
                        background-position: 0 -257px;
                    }

                    .desc {
                        &:after {
                            background: #1e1e1e;
                        }
                    }
                }

                &.a2 {
                    background: #2c2c2c;

                    .img {
                        background-position: -62px -257px;
                    }

                    .desc {
                        &:after {
                            background: #1e1e1e;
                        }
                    }
                }

                &.b1 {
                    background: #252525;

                    .img {
                        background-position: -120px -257px;
                    }

                    .desc {
                        &:after {
                            background: #1e1e1e;
                        }
                    }
                }

                &.b2 {
                    background: #2c2c2c;

                    .img {
                        background-position: -178px -257px;
                    }

                    .desc {
                        &:after {
                            background: #313131;
                        }
                    }
                }

                &.c1 {
                    background: #252525;

                    .img {
                        background-position: -237px -257px;
                    }

                    .desc {
                        &:after {
                            background: #313131;
                        }
                    }
                }

                &.c2 {
                    background: #1e1e1e;

                    .img {
                        background-position: -293px -257px;
                    }
                }

                &:hover {
                    background: #1b1818;
                    .content {
                        .desc {
                            &:after {
                                background: #313131;
                            }
                        }
                    }
                }

                .content {
                    color: #FFFFFF;
                    text-align: center;
                    padding: 30px 0;
                    min-height: calc(50vh - 22.5px);

                    .label {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        display: block;
                        margin: -30px -15px 0;
                        padding: 0;
                        cursor: pointer;
                    }

                    .img {
                        width: 41px;
                        height: 34px;
                        display: block;
                        margin: 0 auto 2em;
                        background-image: url(../images/sprite.png);
                    }

                    .desc {
                        padding: 0 0.675em;
                        text-align: left;
                        font-size: 2.5em;
                        font-weight: 700;
                        margin: 0;
                        letter-spacing: -0.025em;
                        line-height: 0.77;

                        &:after {
                            content: '';
                            height: 4px;
                            width: 100%;
                            margin-top: 0.300em;
                            display: block;
                        }
                    }

                    .level {
                        font-size: 1.125em;
                        padding: 0 .75em;
                        margin: 0.75em 0;
                        color: #848484;
                    }
                }
            }
        }
    }
}

.quotes-box {
    background: rgba(0, 0, 0, 0.81);
    padding: 8em 0 0;
    position: relative;
    color: #ffffff;

    @media (min-width: 600px) {
        padding: 5em 0 0;    
    }

    &-top {
        position: absolute;
        left: 0;
        right: 0;
        background: #fbb03b;
        font-size: 2em;
        height: 5.5em;
        width: 5.5em;
        line-height: 1.15;
        text-align: center;
        display: table;
        margin: auto;
        top: 0;
        @include transform(translateY(-25%));
        @include border-radius(50%);
        background: $langu_gold;
        
        p {
            display: table-cell;
            vertical-align: middle;
        }
    }

    &-inner {
        width: 80%;
        margin: 0 auto;

        .quote {
            font-size: 1.25em;
            position: relative;
            width: 100%;
            padding: 1em 0;
            line-height: 1.15;

            @media (min-width: 600px) {
                width: 40%;
                padding: 0;

                &:first-child {
                    float: left;
                    margin-right: 60%;
                }

                &:last-child {
                    float: right;
                    margin-left: 60%;
                    @include transform(translateY(-2em));
                }
            }    

            &:before {
                content: '\201C';
                display: block;
                position: absolute;
                font-size: 5em;
                top: 0;
                left: 0;
                line-height: 0.5;
                @include transform(translateX(-100%));
            }

            .author {
                font-style: italic;
                font-size: 0.8em;
                display: block;
                width: 100%;
                text-align: right;
                margin-top: 0.5em;

                &:before {
                    content: '- ';
                    position: relative;
                }
            }
        }

        @include clear-after();
    }
}

.welcomeBox {
    padding: 5em 0;
    background: rgba(0, 0, 0, 0.81);

    &-inner {
        position: relative;
        padding: 0.4em 0;

        .separator {
            &:after {
                display: none;
            }
        }

        .langu-logo {
            display: block;
            margin: 0 auto 1em;
            max-height: 7em;
        }

        .welcome-text {
            color: #fff;
            line-height: 1.400;
            font-size: 1.563em;
            margin: 0;
            text-align: center;

            .emp {
                color: $langu_gold;
                font-weight: 600;
            }
        }

        @media (min-width: 768px) {
            display: flex;
            justify-content: center;
            align-items: center;

            .separator {       
                margin: 0 2em;

                &:after {
                    content: '';
                    position: absolute;
                    top: 0;
                    width: 3px;
                    height: 100%;
                    background-color: $langu_pink;
                    display: block;
                }
            }

            .welcome-text {
                text-align: left;
                align-self: flex-end;
            }

            .langu-logo {
                margin: auto;
                max-height: none;
            }
        }
    }    
}

.features-box {
    position: relative;

    @media (min-width: 768px) {
        display: flex;
        justify-content: space-between;
        align-items: stretch;

        &-item {
            float: left;
            width: 33.33333333%;

            .head {
                .icon {
                    min-height: 105px;
                    max-height: none;
                }
            }

            .text {
                font-size: 1em;

                .emp {
                    font-weight: normal;
                }
            }
        }
    }

    &-item {
        margin: 0;
        padding: 4em;
        min-height: 1px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        color: #ffffff;

        .head {
            margin-bottom: 2em;
            text-align: center;

            .icon {
                display: block;

                img {
                    max-height: 6.5625em;
                }
            }

            .title {
                margin-top: 0.375em;
                display: block;
                font-size: 1.5em;
                line-height: 1;

                .emp {
                    color: $langu_primary;
                    font-weight: 600;
                }
            }
        }

        .text {
            line-height: 1.15;
            font-size: 1.25em;
        }

        &.teacher {
            background-color: #ffbe41;
        }

        &.technology {
            background-color: $langu_gold;
        }

        &.matchmaking {
            background-color: #f4a636;
        }
    }
}

.work-box {
    padding: 3.25em .625em 2.5em;
    margin: 0 auto;

    & > .title {
        padding: 1.475em 0 0;
        background: url(../images/desktop-icon.png) center top no-repeat;
        background-size: 1.623em;
        position: relative;
        margin: 0 auto;
        text-align: right;
        width: 3.279em;
        color: #2d2d2d;
        font-size: 3.813em;
        line-height: .41;
        font-weight: 700;

        &:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -0.328em;
            width: 3.279em;
            height: 4px;
            background-color: $langu_gold;
        }

        .subtitle {
            display: block;
            color: $langu_gold;
            font-size: 0.344em;
            line-height: 1.143em;
            text-align: left;
            text-transform: uppercase;
        }
    }
    
    &.pl {
        & > .title {
            line-height: 0.9;
            
            &:after {
                bottom: -0.1em;
            }
        }
    }

    .content {
        .steps-list {
            margin-top: 4.063em;
            margin-bottom: 2.813em;
            counter-reset: list;
            list-style: none;
            padding: 0;

            &-item {
                position: relative;
                clear: both;
                counter-increment: list;
                padding: 2em 0;

                &.find-teacher {
                    .steps-list-item-icon {
                        img {
                            @include transform(translateX(29%));
                        }
                    }

                    &:before {
                        top: 3em;
                    }
                }

                &.schedule-lesson {
                    .steps-list-item-icon {
                        img {
                            @include transform(translateX(40%));
                        }
                    }
                }

                &.learn {
                    &:before {
                        height: 3em;
                    }
                }

                &:before {
                    content: '';
                    display: block;
                    height: 100%;
                    width: 4px;
                    left: calc(2.25em - 2px);
                    position: absolute;
                    background: $langu_gold;
                }

                @media(min-width: 768px) {
                    &:before {
                        left: calc(12.50em + 2px);
                    }
                }

                &-icon {
                    display: flex;
                    position: relative;
                    align-items: center;

                    img {
                        max-height: 5em;
                    }

                    &:before {
                        display: inline-block;
                        content: counter(list) '.';
                        overflow: hidden;
                        @include border-radius(50%);
                        width: 3em;
                        height: 3em;
                        line-height: 3;
                        text-align: center;
                        color: $langu_gold;
                        font-size: 1.5em;
                        background-color: #2d2d2d;
                        z-index: 1;
                        margin-right: 35%;
                            font-weight: 600;
                    }

                    @media(min-width: 768px) {
                        justify-content: space-between;
                        width: 15em;

                        &:before {
                            display: none;
                        }

                        img {
                            float: none;
                        }

                        &:after {
                            display: inline-block;
                            content: counter(list) '.';
                            overflow: hidden;
                            @include border-radius(50%);
                            width: 3em;
                            height: 3em;
                            line-height: 3;
                            text-align: center;
                            color: $langu_gold;
                            font-size: 1.5em;
                            background-color: #2d2d2d;
                            z-index: 1;
                            font-weight: 600;
                        }
                    }
                }

                &-inner {
                    display: block;
                    padding: 1em 0 0 5em;

                    @media (min-width: 768px) {
                        padding: 0 0 0 15em;
                    }

                    @include clear-after();

                    .details {
                        padding: 0.5em;
                        background: rgba(255, 255, 255, 0.5);
                        display: block;
                        line-height: 1.15;
                        font-size: 1.25em;

                        .title {
                            color: $langu_gold;
                            font-size: 2em;
                            font-weight: 600;
                            display: block;
                        }
                    }

                    .extended {
                        padding: 1em 0;

                        .column {
                            position: relative;
                            min-height: 1px;
                            line-height: 1.15;
                            padding: 1em 0 0;

                            .icon {
                                min-height: 3.75em;
                                display: block;
                                margin: 0 0 0.5em;

                                img {
                                    max-height: 5em;
                                }                               
                            }

                            .title {
                                color: $langu_gold;
                                font-weight: 600;
                                display: block;
                                font-size: 1.25em;
                            }

                            .text {
                                font-size: 1.25em;
                                color: $langu_primary;
                                margin: 0;
                            }
                        }

                        @media (min-width: 600px) {
                            display: flex;

                            .column {
                                width: 33.3333333333%;
                                float: left;
                                padding: 1em 1.5em 0 0;

                                &:before {
                                    content: '';
                                    width: 1px;
                                    height: 100%;
                                    position: absolute;
                                    display: block;
                                    top: 0;
                                    left: -1em;
                                    background: rgba(0, 0, 0, 0.5);
                                }

                                &:first-of-type {
                                    &:before {
                                        display: none;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.lang-homepage-search {
    &.selectize-control {
        &:after {
            content: '';
            display: block;
            position: relative;
            width: 100%;
            height: 0.750em;
            background: #232323;
            border-radius: 2px;
        }

        &.single {
            .selectize-input {
                background: transparent;
                border: none;
                box-shadow: none;

                &:after {
                    content: none;
                }

                &.input-active {
                    background: transparent;
                }

                &.dropdown-active {
                    &:before {
                        content: none;
                    }
                }
            }

            & + .tooltip {
                & > .tooltip-arrow {
                    border-color: #232323;
                }

                & > .tooltip-inner {
                    background: #232323;
                    border: none;
                    font-size: 1.25em;
                    max-width: 100%;
                }

                &.in {
                    @include opacity(1);
                }
            }
        }

        .selectize-input {
            color: $langu_gold;  
            font-size: 6.688em;
            line-height: 1.1;
            font-weight: 900;
            padding-right: 0.935em;

            input {
                @include input-placeholder {
                    color: $langu_gold;
                    opacity: 0.7;
                }
            }

            .item {
                white-space: nowrap;
                max-width: 100%;
            }

            * {
                color: $langu_gold;  
                font-size: 1em;
                line-height: 1.1;
                font-weight: 900;
            }
        }
    }

    &.selectize-dropdown {
        background: transparent;
        border: none;
        box-shadow: none;
        margin-top: 20px;
        z-index: 10000;

        &-content {
            overflow-y: hidden;
        }

        [data-selectable] {
            font-size: 1.875em;
            font-weight: 900;
            text-transform: lowercase;
            padding: 0 4px;
            line-height: 1.1;
        }

        .active {
            background-color: transparent;
            color: $langu_gold;
        }
    }
}
