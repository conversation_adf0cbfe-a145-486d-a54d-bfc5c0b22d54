body {
    &.pdf {
        width: 100%;
        padding: 0;
        margin: 0;
        max-width: 100%;
        background-image: none;
        background: #FFF;
    }
}

.invoice {
    max-width: 100%;
    overflow-x: hidden;
    font-size: 1.5em;

    &-header {
        background-color: $langu_primary;
        position: relative;
        overflow: hidden;
        padding: 0 0.5em;

        &-image {
            margin: 20px 0 20px 40px;
            max-height: 84px;
        }

        &-title {
            height: 100%;
            display: block;
            background-color: #ffffff;
            border-top-left-radius: 100px;
            margin-top: 10px;
            padding-bottom: 150px;
            margin-bottom: -150px;
            margin-right: -0.5em;
            text-align: center;
            position: relative;

            .text {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                font-size: 3em;
                margin: 0;
                font-weight: 700;
                text-transform: uppercase;
                padding-top: 0.5em;
            }
        }
    }

    &-body {
        padding: 0 0.5em 3em;
        background-color: #ffffff;

        &-time {
            float: right;
            @include opacity(0.8);

            .emp {
                font-weight: 700;
            }
        }

        &-invoice-no {
            text-align: center;
            margin: 0.5em 0;
            font-size: 2em;
            font-weight: 700;
        }

        &-info-label {
            text-transform: uppercase;
            color: $langu_light;
            padding-right: 0.5em;
        }
    }

    &-footer {
        background-color: $langu_primary;
        color: #ffffff;
        padding: 0.5em;
    }
}

.invoice-pdf {
    max-width: 100%;
    width: 100%;
    overflow: hidden;;
    font-size: 1.5em;

    &__page {
        page-break-after: always;
    }

    &__list {
        width: 60%;
        margin-left: 20%;
        padding-top: 5em;
    }

    &__list-row {
        p {
            margin: 0;
        }
    }

    &__list-item {
        width: 50%;
        float: left;

        &--name {
            font-weight: 900;
        }
    }

    &__header {
        //background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),url($langu_bg1) repeat;
        background: url($langu_bg1) repeat;
        background-size: 11em;
        height: 10em;
        line-height: 10em;
        color: white;
        background-position-y: -2em;
        padding-left: 7em;
        background-position-x: -1.5em;
        background-color: #0c0c0c;
        background-blend-mode: hard-light;
        p {
            font-size: 2em;
            font-weight: 400;
            text-transform: uppercase;

            &:after {
                content: '';
                background-image: url(../images/logo-white.png);
                background-size: 4em;
                position: absolute;
                width: 5em;
                background-repeat: no-repeat;
                height: 5em;
                background-position: center;
                right: 2em;
            }
        }
    }

    &__footer {
        background-color: #FFF;
        color: #000;
        width: 100%;
        line-height: .5em;
        text-align: center;
        bottom: 0;
    }
}