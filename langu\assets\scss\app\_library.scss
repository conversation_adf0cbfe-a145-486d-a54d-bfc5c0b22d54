.categories-list {
    &-item {
        margin: 0 -25px;
        padding: 1.25em 25px;
        position: relative;
        @include transition(background-color 0.6s);

        &:after {
            content: '';
            height: 1px;
            background-color: #1a1a1a;
            display: block;
            bottom: 0;
            position: absolute;
            width: calc(100% - 50px);
        }

        &.active {
            background-color: $langu_gold;

            &:after {
                display: none;
            }
        }

        .title {
            color: inherit;
            text-transform: uppercase;
            font-weight: 700;
            font-size: 0.875em;
            margin: 0;
        }

        .subtitle {
            color: inherit;
            margin: 1em 0 0 0;
            font-weight: 400;
            font-size: 0.75em;
            line-height: 1.25;
            @include opacity(0.8);
        }
    }
}

.category-panel {
    &-header {
        padding: 1.250em;

        .title {
            font-size: 1.188em;
            margin-left: 0;
            color: inherit;
            text-transform: uppercase;
            font-weight: 700;
        }
    }

    &-body {
        padding: 1.250em;
    }
}

.files-list {
    @include clear-after();

    &-item {
        position: relative;
        text-align: center;
        display: inline-block;
        float: left;
        width: 8.750em;
        margin: 0 1.875em 2.875em 0;

        &-check {
            display: block;
            width: 100%;
            height: 100%;
            position: absolute;

            input[type="checkbox"] {
                display: none;

                & + label {
                    @include transition(0.5s);
                    display: block;
                    width: 100%;
                    height: 100%;
                    background-color: transparent;
                    opacity: 1;

                    &:before, &:after {
                        content: none;
                    }       
                }

                &:checked + label {
                    background-color: $langu_gold;
                    opacity: 0.4;
                }
            }
        }

        .close {
            position: absolute;
            top: -0.5em;
            right: -0.5em;
            font-size: 1em;
            vertical-align: top;
        }

        .download {
            font-size: 1.5em;
            position: absolute;
            z-index: 1;
            right: 1em;
            top: 0.25em;
            line-height: 1;
            background: #ffffff;
            padding: 0 2px 0 0;
        }

        .thumb {
            margin: 0 auto;
            display: block;
            width: 6.250em;
            height: 6.250em;
        }

        .label {
            margin-top: 1em;
            width: 100%;
            font-weight: 400;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 0.875em;
            line-height: 1.286;
            padding: 0;
            color: inherit;
            display: block;
            overflow: hidden;
        }
    }
}

.student-library {
    &-page-subtitle {
        padding-bottom: 0;
    }
    
    &-page-description {
        margin: 0;
        padding: 0 0 1em 0;
        line-height: 1.15;
    }
}
