.login-sidebar {
    @include transition(all 0.3s linear);

    background: #f8f8f8;
    float: right;
    font-size: small;
    min-height: -webkit-calc(100vh);
    min-height: calc(100vh);
    overflow: hidden;
    padding: 55px 1em 1.563em 1.25em;
    position: fixed;
    right: -100%;
    top: 0;
    transition: all .3s linear;
    width: 100%;
    z-index: 12;

    @media (min-width: 768px) {
        width: 350px;
        right: -350px;
    }

    &.active {
        height: 100%;
        overflow-y: auto;
        right: 0;
    }

    &__block {
        margin-top: 3em;
        h2, h3 {
            margin: 0 auto;
            text-align: center;
            font-weight: bold;
        }

        h2 {
            font-size: 1.7em;
        }

        h3 {
            font-size: 1.25em;
            line-height: 1.25em;
            color: $langu_gold;
            padding-bottom: 0.7em;
        }
        p {
            font-size: 1.2em;
        }
    }

    &__btn {
        @include transition(0.3s);
        @include border-radius(2px);
        @include box-shadow(0 2px 4px 0 rgba(0, 0, 0, .15));

        background-color: $langu_gold;
        border: 1px solid $langu_gold;
        color: #ffffff !important;
        display: block;
        font-family: 'Roboto';
        font-weight: bold;
        height: 3em;
        line-height: 3em;
        margin: 0 auto 0.688em;
        text-align: center;
        width: 15em;
        &:hover {
            @include box-shadow(0 0 3px 3px rgba(251, 176, 59, .3));
        }
    }

    &__logo {
        @include border-radius(2px);

        background-color: #ffffff;
        background-position: center;
        background-repeat: no-repeat;
        float: left;
        font-size: 0.95em;
        padding: 1.5em;

        &--google {
            background-image: url(data:image/svg+xml;charset=utf-8;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjE4cHgiIGhlaWdodD0iMThweCIgdmlld0JveD0iMCAwIDQ4IDQ4IiBjbGFzcz0iYWJjUmlvQnV0dG9uU3ZnIj48Zz48cGF0aCBmaWxsPSIjRUE0MzM1IiBkPSJNMjQgOS41YzMuNTQgMCA2LjcxIDEuMjIgOS4yMSAzLjZsNi44NS02Ljg1QzM1LjkgMi4zOCAzMC40NyAwIDI0IDAgMTQuNjIgMCA2LjUxIDUuMzggMi41NiAxMy4yMmw3Ljk4IDYuMTlDMTIuNDMgMTMuNzIgMTcuNzQgOS41IDI0IDkuNXoiPjwvcGF0aD48cGF0aCBmaWxsPSIjNDI4NUY0IiBkPSJNNDYuOTggMjQuNTVjMC0xLjU3LS4xNS0zLjA5LS4zOC00LjU1SDI0djkuMDJoMTIuOTRjLS41OCAyLjk2LTIuMjYgNS40OC00Ljc4IDcuMThsNy43MyA2YzQuNTEtNC4xOCA3LjA5LTEwLjM2IDcuMDktMTcuNjV6Ij48L3BhdGg+PHBhdGggZmlsbD0iI0ZCQkMwNSIgZD0iTTEwLjUzIDI4LjU5Yy0uNDgtMS40NS0uNzYtMi45OS0uNzYtNC41OXMuMjctMy4xNC43Ni00LjU5bC03Ljk4LTYuMTlDLjkyIDE2LjQ2IDAgMjAuMTIgMCAyNGMwIDMuODguOTIgNy41NCAyLjU2IDEwLjc4bDcuOTctNi4xOXoiPjwvcGF0aD48cGF0aCBmaWxsPSIjMzRBODUzIiBkPSJNMjQgNDhjNi40OCAwIDExLjkzLTIuMTMgMTUuODktNS44MWwtNy43My02Yy0yLjE1IDEuNDUtNC45MiAyLjMtOC4xNiAyLjMtNi4yNiAwLTExLjU3LTQuMjItMTMuNDctOS45MWwtNy45OCA2LjE5QzYuNTEgNDIuNjIgMTQuNjIgNDggMjQgNDh6Ij48L3BhdGg+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGg0OHY0OEgweiI+PC9wYXRoPjwvZz48L3N2Zz4=);
            background-size: 1.6em;
        }

        &--email {
            background-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSIjRjlBRjQ4IiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOmNjPSJodHRwOi8vY3JlYXRpdmVjb21tb25zLm9yZy9ucyMiIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyIgeG1sbnM6c3ZnPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczpzb2RpcG9kaT0iaHR0cDovL3NvZGlwb2RpLnNvdXJjZWZvcmdlLm5ldC9EVEQvc29kaXBvZGktMC5kdGQiIHhtbG5zOmlua3NjYXBlPSJodHRwOi8vd3d3Lmlua3NjYXBlLm9yZy9uYW1lc3BhY2VzL2lua3NjYXBlIiB2ZXJzaW9uPSIxLjEiIHg9IjBweCIgeT0iMHB4IiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCwtOTUyLjM2MjE4KSI+PHBhdGggc3R5bGU9InRleHQtaW5kZW50OjA7dGV4dC10cmFuc2Zvcm06bm9uZTtkaXJlY3Rpb246bHRyO2Jsb2NrLXByb2dyZXNzaW9uOnRiO2Jhc2VsaW5lLXNoaWZ0OmJhc2VsaW5lO2NvbG9yOiMwMDAwMDA7ZW5hYmxlLWJhY2tncm91bmQ6YWNjdW11bGF0ZTsiIGQ9Im0gOC45OTk5OTgsOTcyLjM2MjE5IGMgLTIuMTY0MiwwIC00LDEuODM1ODUgLTQsNCBsIDAsNTIuMDAwMDEgYyAwLDIuMTY0MSAxLjgzNTgsNCA0LDQgbCA4Mi4wMDAwMDQsMCBjIDIuMTY0MiwwIDQsLTEuODM1OSA0LC00IGwgMCwtNTIuMDAwMDEgYyAwLC0yLjE2NDE1IC0xLjgzNTgsLTQgLTQsLTQgbCAtODIuMDAwMDA0LDAgeiBtIDUuOTM3NSw2IDcwLjEyNSwwIC0zNS4wNjI1LDMwLjA2MjUxIC0zNS4wNjI1LC0zMC4wNjI1MSB6IG0gLTMuOTM3NSw0LjQ2ODc1IDIxLjA5MzgsMTguMDkzNzYgLTIxLjA5MzgsMjEuMDkzNyAwLC0zOS4xODc0NiB6IG0gNzgsMCAwLDM5LjE4NzQ2IC0yMS4wOTM4LC0yMS4wOTM3IDIxLjA5MzgsLTE4LjA5Mzc2IHogbSAtNTIuMzQzOCwyMi4wMzEyNiAxMS40MDYzLDkuNzUgYSAzLjAwMDMsMy4wMDAzIDAgMCAwIDMuODc1LDAgbCAxMS40MDYzLC05Ljc1IDIxLjUsMjEuNSAtNjkuNjg3NiwwIDIxLjUsLTIxLjUgeiIgZmlsbD0iI0Y5QUY0OCIgZmlsbC1vcGFjaXR5PSIxIiBzdHJva2U9Im5vbmUiIG1hcmtlcj0ibm9uZSIgdmlzaWJpbGl0eT0idmlzaWJsZSIgZGlzcGxheT0iaW5saW5lIiBvdmVyZmxvdz0idmlzaWJsZSI+PC9wYXRoPjwvZz48L3N2Zz4=);
            background-size: 2.2em;
        }
    }

    &__form {
        margin: auto;
        width: 15em;
    }

    &__form-input {
        position: relative;
        width: 15em;

        input {
            background-color: inherit;
            border: 1px solid #a7a798;
            margin-bottom: 0.2em;
            padding-left: .5em;
            height: 2.4em;
            @include placeholder {
                color: #c8c0b3;
            }
        }
        &:after {
            background-position: center !important;
            background-repeat: no-repeat !important;;
            bottom: 0;
            content: "";
            position: absolute;
            right: 0;
            top: 0.6em;
            width: 2em;
        }

        &--email {
            &:after {
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' stroke='#a7a798' width='100%' height='100%' viewBox='0 0 40 40' fill-rule='evenodd'%3E%3Cpath d='m 22.5 4 h -21 c -0.83 0 -1.5 0.67 -1.5 1.51 v 12.99 c 0 0.83 0.67 1.5 1.5 1.5 h 20.99 a 1.5 1.5 0 0 0 1.51 -1.51 v -12.98 c 0 -0.84 -0.67 -1.51 -1.5 -1.51 Z m 0.5 14.2 l -6.14 -7.91 l 6.14 -4.66 v 12.58 Z m -0.83 -13.2 l -9.69 7.36 c -0.26 0.2 -0.72 0.2 -0.98 0 l -9.67 -7.36 h 20.35 Z m -21.17 0.63 l 6.14 4.67 l -6.14 7.88 Z m 0.63 13.37 l 6.3 -8.1 l 2.97 2.26 c 0.62 0.47 1.57 0.47 2.19 0 l 2.97 -2.26 l 6.29 8.1 Z'%3E%3C/path%3E%3C/svg%3E");
            }
        }

        &--password {
            &:after {
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' stroke='#a7a798' width='100%' height='100%' viewBox='0 0 40 40' fill-rule='evenodd'%3E%3Cpath d='m19.5 9h-.5v-2a7 7 0 1 0 -14 0v2h-.5c-.78 0-1.5.72-1.5 1.5v12c0 .78.72 1.5 1.5 1.5h15c .78 0 1.5-.72 1.5-1.5v-12c0-.78-.72-1.5-1.5-1.5zm.5 13.5c0 .22-.28.5-.5.5h-15c-.22 0-.5-.28-.5-.5v-12c0-.22.28-.5.5-.5h1a .5.5 0 0 0 .5-.5v-2.5a6 6 0 1 1 12 0v2.5a.5.5 0 0 0 .5.5h1c .22 0 .5.28.5.5zm-8-10.5a3 3 0 0 0 -3 3c0 .83.36 1.59.94 2.15l-.9 2.16a.5.5 0 0 0 .46.69h5a .5.5 0 0 0 .46-.69l-.87-2.19c.56-.55.91-1.31.91-2.13a3 3 0 0 0 -3-3zm1.04 5.19.72 1.81h-3.51l.74-1.79a.5.5 0 0 0 -.17-.6 2 2 0 1 1 3.18-1.61c0 .64-.31 1.24-.8 1.6a.5.5 0 0 0 -.17.59zm-1.04-14.19a4 4 0 0 0 -4 4v2.5a.5.5 0 0 0 .5.5h7a .5.5 0 0 0 .5-.5v-2.5a4 4 0 0 0 -4-4zm3 6h-6v-2a3 3 0 1 1 6 0z'%3E%3C/path%3E%3C/svg%3E");
            }
        }
    }

    &__footer {
        padding-top: 2em;
        h3 {
            font-size: 1.25em;
            font-weight: bold;
            line-height: 1.25em;
            margin: 0 auto;
            padding-bottom: 0.7em;
            text-align: center;
            &:after {
                background: #f4a636;
                content: '';
                display: block;
                height: .25rem;
                margin: .25rem auto 0;
                position: relative;
                width: 10.625rem;
            }
        }

        p {
            font-size: 1.2em;
            margin-top: .5em;
        }

        ul {
            list-style: none;
            padding-left: 0;
            text-align: center;

            li {
                a {
                    color: $langu_gold;
                    @include transition(0.3s);

                    &:hover {
                        color: $langu_primary;

                        &:before {
                            border-left-color: $langu_primary;
                        }
                    }

                    &:before {
                        @include transition(0.3s);

                        border-bottom: solid 0.313em transparent;
                        border-left: solid 0.313em $langu_gold;
                        border-top: solid 0.313em transparent;
                        content: "";
                        display: inline-block;
                        height: 0;
                        margin-right: 0.625em;
                        width: 0;
                    }
                }
            }
            &:before {
                background: #f4a636;
                content: '';
                display: block;
                height: .25rem;
                margin: .25rem auto 0;
                position: relative;
                width: 10.625rem;
            }
        }
    }

    &__forgot {
        margin-top: .5em;
        display: block;
    }

    &__or {
        opacity: 0.5;
        overflow: hidden;
        padding-bottom: 1.2em;
        padding-top: 0.5em;

        &:before, &:after {
            background: #000000;
            box-sizing: border-box;
            content: '';
            display: inline-block;
            height: 1px;
            vertical-align: middle;
            width: 8em;

        }
        &:before {

            margin-left: -100%;
        }
        &:after {
            margin-right: -100%;
        }
    }
}

.forgot-password {
    position: absolute;
    left: 50%;
    top: 20%;
    z-index: 999;

    &__content {
        position: relative;
        left: -50%;
        background-color: $langu_primary;
        padding: 1.563rem;
        color: #ffffff;
        text-align: center;
        width: 90%;

        @media (min-width: 480px) {
            width: 27.5em;
        }

        h2 {
            margin-top: 0;
            text-transform: uppercase;
            font-size: 1.688em;
            font-weight: 700;
            color: inherit;
        }

        .info {
            font-style: italic;
            font-size: .75em;
            line-height: 1.15;
            padding: 0;
            margin: 0;
            text-align: left;
            display: block;
            width: 100%;
            font-weight: 400;
        }

        .summary-row {
            margin-bottom: 1.5em;
            line-height: 1.15;
        }

        .center-inner {
            display: inline-block;
            position: relative;
            left: 50%;
            @include transform(translateX(-50%));
        }
    }

    &__form {
        input {
            padding-left: .5em;
            background-color: #a6a6a6;
            width: 100% !important;
        }
        button {
            margin-top: .5em;
            cursor: pointer;
        }
    }

    &__close {
        position: absolute;
        right: 0.25em;
        top: 0;
        font-size: 2rem;
        line-height: 1;
        color: $langu_primary;
    }
}

.confirm-modal {
    width: 25em;
    background-color: white;
    padding: 2em;
    margin-top: 40%;

    &__title {
        padding-left: 2em;
        padding-right: 2em;
        margin-top: 1em;
        margin-bottom: 3em;
        h5 {
            font-weight: 600;
            color: $langu_gold;
        }
    }

    &__form {
        margin-top: 1em;
        a {
            color: #000000 !important;
        }
        label {
            @extend .login-sidebar__form-input;
            @extend .login-sidebar__form-input--password;
            width: 80%;
        }
    }
}

.login-modal {
    width: 40em;
    background-color: white;
    padding: 2em;
    margin-top: 40%;

    &__title {
        margin-top: 1em;
        margin-bottom: 3em;
        font-size: x-large;
        text-align: left;
        h5 {
            &:after {
                background: #f4a636;
                content: '';
                display: block;
                height: .25rem;
                margin: .25rem auto 0;
                position: relative;
            }
        }
    }

    &__text {
        text-align: left;
        margin-bottom: 2em;
    }

    &__form {
        margin-top: 1em;
        a {
            color: #000000 !important;
        }
        label {
            @extend .login-sidebar__form-input;
            @extend .login-sidebar__form-input--password;
            width: 80%;
        }
    }
}