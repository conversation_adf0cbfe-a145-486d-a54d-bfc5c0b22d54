header {
    position: relative;
    z-index: 13;
    background: #ffffff;
    @include box-shadow(0 1px 2px rgba(0,0,0,.3));

    &:after {
        content: '';
        display: table;
        clear: both;
    }

    #main-nav {
        @media(min-width: $nav_collapse) {
            max-height: $max_nav_height;
        }

        min-height: auto;
        border-radius: 0;
        z-index: 99;
        margin-bottom: 0;

        .visible-expand {
            display: none;
        }

        .visible-collapse {
            display: block;
        }

        @media(min-width: $nav_collapse) {
            .menu-right {
                float: right;
            }

            .visible-expand {
                display: block;
            }

            .visible-collapse {
                display: none;
            }
        }

        .navbar-header {
            .navbar-brand {
                height: 55px;
                line-height: 23px;
                padding: 2px 15px;

                @media(min-width: $nav_collapse) {
                    margin-left: -15px;
                }

                img {
                    max-height: 100%;
                }
            }

            .navbar-toggle {
                font-size: 1rem;
                line-height: 1rem;
            }

            .sidebar-toggle {
                margin-top: 9px;
                margin-right: 0;
                margin-bottom: 0;
            }

            .mobile-toggle {
                margin-top: 10px;
                margin-bottom: 0;

                .icon-bar {
                    background: #232323;
                }
            }
        }

        .main-navbar {
            li {
                a {
                    color: #232323;
                    
                    &:hover, &:focus, &:active, &:visited {
                        background: none;
                    }

                    & > .flag {
                        @include box-shadow(0px 0 1px 0px #aaaaaa);
                        font-size: 1.5em;
                        vertical-align: sub;
                        margin-right: 0.125em;
                    }
                }
            }

            & > li {
                @media (max-width: $nav_collapse_min) {
                    margin-top: 7px;
                    margin-bottom: 7px;
                }

                &.dropdown {
                    @media (max-width: $nav_collapse_min) {
                        .dropdown-menu {
                            margin-top: -7px;
                            margin-bottom: 3px;

                            display: block;
                            float: left;
                            width: 100%;
                            position: relative;
                            padding: 0;
                            box-shadow: none;

                            & > li {
                                margin-top: 7px;
                                margin-bottom: 7px;

                                &.divider {
                                    display: none;
                                }

                                & > a {
                                    color: inherit;
                                    font-weight: inherit;
                                    text-transform: uppercase;
                                    font-size: 14px;
                                    line-height: 19px;
                                    padding: 3px 12px;
                                }
                            }
                        }
                    }
                }

                @media (min-width: $nav_collapse) {
                    &:nth-last-child(n+2) {
                        border-right: 1px solid #232323;
                    }

                    margin-top: 13px;

                    &.dropdown {

                        &:hover {
                            .dropdown-menu {
                                display: block;
                                text-align: right;
                                
                                &.align-left {
                                    text-align: left;
                                }
                                    
                                & > li {
                                    & > a {
                                        font-weight: 600;
                                        
                                        &:hover {
                                            color: $langu_gold;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                &.profile-pic {
                    @media (min-width: $nav_collapse) {
                        margin-top: 9px;
                    }

                    img {
                        margin-right: 5px;
                        vertical-align: top;
                        max-width: 31px;
                        max-height: 31px;
                        font-size: 0.5em;
                    }

                    & > a > span {
                        display: inline-block;
                        padding-top: 5px;
                    }
                }

                & > a {
                    text-transform: uppercase;
                    color: #232323;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 19px;
                    padding: 3px 12px;

                    @media(min-width: 1200px) {
                        padding: 3px 20px;
                    }

                    &:hover {
                        font-weight: 600;
                    }
                }
            } 
        }
    }
}