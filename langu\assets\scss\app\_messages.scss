.messages {
    margin: 0 -1.563em;

    .threads-list {        
        &-more {
            @include clear-before();

            text-align: center;
            margin: 0.75em;
        }

        &-item {

            &.current {
                background: #141414;
                color: $langu_gold;
                
                .threads-list-item-inner {
                    .top-bar {
                        .date {
                            color: $langu_gold;
                        }
                    }
                }
            }

            &.sticky {
                color: $langu_gold;
            }

            &:not(:last-child) {
                .threads-list-item-inner {
                    &:after {
                        content: '';
                        display: block;
                        border-bottom: 1px solid #141414;
                        top: 2em;
                        position: relative;
                    }
                }
            }

            &-inner {
                position: relative;
                padding: 2em;

                .top-bar {
                    float: left;
                    font-size: 0.75em;
                    line-height: 0.938em;
                    font-weight: 600;
                    color: $langu_gold;
                    display: block;
                    width: 100%;
                    
                    .date {
                        float: right;
                        line-height: 1;
                        text-align: right;
                        color: #ffffff;
                        padding-left: 1em;
                    }
                }

                .message-info {
                    margin: 1em 0;

                    &:before {
                        content: '';
                        clear: both;
                        display: block;
                    }

                    .user-image {
                        float: left;
                        margin-right: 0.5em;
                        font-size: 0.75em;
                    }

                    .last-message {
                        font-size: 0.875em;
                        overflow: hidden;
                        padding: 0 0.250em;
                        line-height: 1.15;
                        display: block;
                        width: auto;
                        overflow: hidden;
                    }
                }
            }
        }
    }
}

.conversation {

    .loader {
        font-size: 2em;
        display: block;
        text-align: center;
        text-transform: uppercase;
    }

    .langu-panel {
        &-header {
            .title {
                font-size: 1.188em;
                text-transform: uppercase;
                margin: 0.531em 0 0;
                font-weight: 700;

                .emp {
                    color: $langu_gold;
                }
            }
            
            .helper {
                font-size: 0.750em;
                margin-bottom: 0;
                line-height: 1.15;
            }
        }

        &-body {
            @include clear-after();

            .messages-list {
                margin: 0 0 1em;
                padding: 0;

                @include clear-after();

                &-more {
                    @include clear-before();

                    text-align: center;
                    margin: 0.75em;
                }

                &-item {
                    width: 55%;
                    border-radius: 0.5em;
                    background: #ffffff;
                    padding: 0.938em 1.75em;
                    margin: 0.5em 1em;
                    float: right;

                    &.sending {
                        @include transition(opacity 0.5s);
                        @include opacity(0.6s);
                    }

                    &.mine {
                        float: left;
                    }

                    .info {
                        @include clear-after();

                        &-date {
                            font-size: 0.75em;
                            font-weight: 700;
                            color: $langu_gold;
                        }

                        &-picture {
                            float: right;                
                            @include transform(translateX(0.812em));
                        }
                    }

                    .content {
                        padding: 13px 0 0;
                        font-size: 0.875em;
                        line-height: 1.15;
                        
                        .message-link {
                            color: $langu_gold;
                            text-decoration: underline;
                        }
                        
                        &-file {
                            .content-file-icon {
                                font-size: 2em;
                                margin-right: 0.25em;
                                vertical-align: middle;
                            }
                        }
                    }
                }
            }

            .new-message-box {
                padding: 0.875em;
                overflow: hidden;
                display: table;
                table-layout: fixed;

                &-input-wrapper {
                    display: table-cell;
                    padding-right: 40px;
                    width: 100%;
                }

                &-input {
                    max-height: 250px;
                    overflow-y: auto;
                    background: none;
                    border-bottom: 1px solid #ababab;
                    display: block;
                    box-shadow: none;
                    line-height: 1.5em;
                    width: 100%;
                    resize: none;
                }

                &-send-button-wrapper {
                    width: 22px;
                    height: 22px;
                    vertical-align: bottom;
                    display: table-cell;
                }

                &-send-button {
                    background: url(../images/send_msg.png) no-repeat;
                    height: 22px;
                    width: 22px;
                    appearance: none;
                    -webkit-appearance: none;
                    -moz-appearance: none;
                    border: none;
                }
            }
        }
    }
}
