.bootbox.modal {
    z-index: 1050;

    .modal-dialog {
        max-width: 80%;

        @media (min-width: 768px) {
            max-width: 60%;
        }

        .modal-body {
            padding: 1em 3em;
        }
    }

    .modal-footer {
        .btn-primary {
            background-color: $langu_gold;
        }
    }
}

.langu-modal {
    background: rgba(0, 0, 0, .3);
    display: none;
    position: fixed;
    z-index: 1050;
    -webkit-overflow-scrolling: touch;
    outline: 0;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;

    .modal-open & {
        overflow-x: hidden;
        overflow-y: auto;
    }

    & &-dialog {
        position: relative;
        margin: 30px auto;
        @include border-radius(6px);
        @include box-shadow(0 3px 9px rgba(0, 0, 0, .5));
        background: #ffffff;
        padding: 2em;
        width: 80%;

        &.langu-modal-dialog-short {
            width: 50%;
        }

        &.langu-modal-dialog-fluid {
            width: auto;
            display: table;
        }

        &-transparent {
            background: none;
            position: relative;
            margin: 30px auto;
            padding: 2em;
            width: auto;
            display: table;
            text-align: center;

            @include clear-after();
        }

        &-content {
            max-height: calc(100vh - 65px);
            overflow-x: hidden;

            a:not(.btn) {
                color: $langu_gold;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        @include clear-after();

        &-close {
            position: absolute;
            right: 0.25em;
            top: 0;
            font-size: 2rem;
            line-height: 1;
            color: $langu_primary;
        }
    }

    &--active {
        display: block;
    }
}

.modal-backdrop {
    z-index: 900;
}

.modal-black {
    @include clear-after();
    background-color: $langu_primary;
    padding: 2em;
    color: #ffffff;
    text-align: center;
    max-width: 80%;
    margin: auto;

    &__title {
        h2 {
            color: $langu_gold;
            margin: auto;
            font-size: 2.5em;
            padding-bottom: .8em;
        }
        &--white {
            h2 {
                color: white;
                margin: auto;
                font-size: 2.5em;
                padding-bottom: .8em;
            }
        }
    }

    &__text {
        margin: 2em;
    }

    &__button {
        padding-bottom: 0.8em;
    }

    &__close {
        position: absolute;
        color: white;
        margin-top: -1.2em;
        right: 15%;
        font-size: 2.5em;
        a {
            text-decoration: none !important;
        }
    }

    &__list {
        @include clear-after();
        margin-bottom: 1.5em;
        font-size: .875em;
        text-align: left;

        dt {
            color: $langu_gold;
            float: left;
            clear: right;
            padding: 0 0.25em;
            line-height: 1.15;
            &:after {
                content: ':';
            }
        }

        dd {
            float: right;
            clear: right;
            padding: 0 0.25em;
            line-height: 1.15;
        }
    }

    &--student-info {
        width: 40%;
    }
}