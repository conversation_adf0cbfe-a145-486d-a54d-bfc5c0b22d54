.pagination-nav {
    text-align: center;
    
    .langu-pagination {
        display: inline-block;
        padding-left: 0;
        margin: 20px 0;
        border-radius: 4px;
        font-size: 0.875em;

        li {
            display: inline;

            & > a, & > span {
                position: relative;
                float: left;
                padding: 6px 12px;
                margin-left: -1px;
                line-height: 1.42857143;
                color: $langu_gold;
                text-decoration: none;
                background-color: #fff;
                border: 1px solid #ddd;
            }

            &:first-child {
                & > a {
                    margin-left: 0;
                    border-top-left-radius: 4px;
                    border-bottom-left-radius: 4px;
                }
            }
        }

        .disabled {
            & > a, & > span {
                color: #777;
                cursor: not-allowed;
                background-color: #fff;
                border-color: #ddd;

                &:focus, &:hover {
                    color: #777;
                    cursor: not-allowed;
                    background-color: #fff;
                    border-color: #ddd;
                }
            }
        }

        .active {
            & > a, & > span {
                z-index: 3;
                color: #fff;
                cursor: default;
                background-color: $langu_gold;
                border-color: $langu_gold;

                &:focus, &:hover {
                    z-index: 3;
                    color: #fff;
                    cursor: default;
                    background-color: $langu_gold;
                    border-color: $langu_gold;
                }
            }
        }
    }
}