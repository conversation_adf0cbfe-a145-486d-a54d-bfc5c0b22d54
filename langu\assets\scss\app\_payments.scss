.payments-sidebar {
    &-section {
        border-bottom: 1px solid #141414;
        padding: 0 0 0.675em;
        margin-bottom: 1.250em;

        &-header {
            text-transform: uppercase;
            color: inherit;
            font-size: 0.875em;
            font-weight: 900;
        }

        &-content {
            margin-top: 2em;
            margin-bottom: 1em;

            @include clear-after();

            .revenue {                
                font-size: 1.875em;
                line-height: 1;
                margin: 0;
                text-align: center;
                color: $langu_gold;
                vertical-align: text-bottom;
                font-weight: 400;

                .amount {
                    font-size: 2em;
                    color: #ffffff;
                }
            }

            .withdraw-btn {
                color: inherit;
                margin-top: 1.5em;
                display: block;
                font-weight: 700;
                @include box-shadow(none);

                &:not(.disabled):hover {
                    color: $langu_gold;
                }
            }

            .revenue-piece {
                display: flex;
                flex-direction: column;

                &:after {
                    display: block;
                    text-align: center;
                    position: relative;
                    margin: 0 auto;  
                    padding: 0.2em 0;                     
                    font-size: 1.875em;
                    line-height: 0.5;
                    font-weight: 400;
                }

                &.plus {
                    &:after {
                        content: '+';
                    }
                }

                &.minus {
                    &:after {
                        content: '-';
                    }
                }

                &-info {
                    padding: .2em 0;
                    display: flex;
                    align-items: center;

                    .amount {
                        text-align: right;
                        font-weight: 400;
                        font-size: 1.875em;
                        line-height: 1;
                    }

                    .description {
                        text-align: left;
                        color: $langu_gold;
                        font-weight: 600;
                        line-height: 1.2;
                        font-size: 0.875em;
                    }

                    &.emp {
                        background-color: $langu_gold;
                        
                        .description {
                            color: $langu_primary;
                        }
                    }
                }
            }
        }
    }
}

.payments-panel {
    margin-top: 2em;

    &-header {
        font-size: 1.188em;
        margin-left: 0;
        color: inherit;
        text-transform: uppercase;
        font-weight: 700;
    }

    &-body {
        background: transparent;
        padding: 0;
    }
}

.payments-list {
    margin: 0;
    padding: 0;
    list-style: none;

    & > * {
        margin: 0.25em 0;
        padding: 0.5em 0.5em;
        background: rgba(244,244,244,.5);
    }

    &-item {
        position: relative;
        display: flex;
        font-size: 0.875em;
        line-height: 1.143;
        margin: 0.5em;

        & > * {
            padding: 0 0.286em;
            height: 100%;
            line-height:  2.143;

            /*            &:after {
                            content: '';
                            position: absolute;
                            right: 0;
                            top: 0;
                            height: 100%;
                            width: 1px;
                            background-color: $langu_primary;
                        }*/

            &:last-child {
                &:after {
                    content: none;
                }
            }
        }

        &-info {
            .invoice-no {
                font-weight: 900;
                text-transform: uppercase;
                line-height: 1.25;
                display: block;
            }

            .date {
                font-size: 0.857em;
                line-height: 1.167;
                color: $langu_gold;
                display: block;
            }
        }

        &-student {
            padding-left: 50px;
            background: url('../images/student_name.png') left center no-repeat;
        }

        &-amount {
            padding-left: 38px;
            background: url('../images/wallet.png') left center no-repeat;
        }

        &-status {

        }
    }
}

.withdraw-panel {
    &-header {
        .title {
            font-size: 1.5em;
            text-transform: uppercase;
            font-weight: 700;
            margin: 0.5em;
            color: inherit;
        }

        background-color: $langu_primary;
        color: #ffffff;
        padding: 10px;
        margin-bottom: 1.5em;
        @include border-radius(5px);
    }

    &-body {
        &-item {
            background-color: #ffffff;
            color: $langu_primary;
            text-align: center;
            padding: 0.75em;
            @include transition(0.3s);

            a {            
                &:hover {
                    color: $langu_gold;
                }
            }
        }
    }
}

.payments {
    &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
    }

    &-title {
        font-size: 1.5rem;
        margin: 0;
    }

    &-tabs {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        
        .tab {
            padding: 0.5rem 2rem;
            border-radius: 20px;
            background: #f5f5f5;
            
            &.active {
                background: #e8f5e9;
                color: #2e7d32;
            }
        }
    }
}

.lesson-entry, .payout-entry {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 8px;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.date-box {
    background: #e8f5e9;
    padding: 0.5rem;
    border-radius: 8px;
    text-align: center;
    min-width: 80px;
    
    .time {
        font-size: 0.8rem;
        color: #666;
    }
}

.lesson-details, .payout-details {
    flex: 1;
    
    .student-name {
        margin: 0 0 0.5rem;
        font-size: 1.2rem;
    }
    
    .lesson-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.5rem;
    }
}

.payment-summary-card {
    background: #333;
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    
    .amount-available {
        margin-bottom: 1.5rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        
        h3 {
            font-size: 2rem;
            margin: 0;
        }
    }
    
    .scheduled-value {
        h3 {
            font-size: 1.5rem;
            margin: 0;
        }
    }
}
