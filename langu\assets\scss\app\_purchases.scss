.summary-block {
    .summary-list {
        @include clear-after();
        margin-bottom: 1.5em;
        font-size: .875em;

        .entry {
            &-label {
                color: $langu_gold;
                float: left;
                clear: right;
                padding: 0 0.25em;
                width: 40%;
                line-height: 1.15;
                margin-top: 0.5em;

                & + .entry-value {
                    margin-top: 0.5em;
                }

                &:after {
                    content: ':';
                }
            }

            &-value {
                float: right;
                clear: right;
                padding: 0 0.25em;
                width: 60%;
                line-height: 1.15;
            }
        }
    }

    .summary-row {
        margin-bottom: 1.5em;
        line-height: 1.15;
        
        &-important {
            font-size: .875em;
            font-weight: 600;
        }
    }
}

.purchase-summary-box {
    @include clear-after();

    @media (min-width: 480px) {
        width: 27.5em;
    }

    background-color: $langu_primary;
    padding: 1.563rem;
    color: #ffffff;
    text-align: initial;
    width: 90%;

    h2 {
        margin-top: 0;
        text-transform: uppercase;
        font-size: 1.688em;
        font-weight: 700;
        color: inherit;
    }
    
    .info {
        font-style: italic;
        font-size: .75em;
        line-height: 1.15;
        padding: 0;
        margin: 0;
        text-align: left;
        display: block;
        width: 100%;
        font-weight: 400;
    }   
    
    .summary-row {
        margin-bottom: 1.5em;
        line-height: 1.15;
    }
    
    .center-inner {
        display: inline-block;
        position: relative;
        left: 50%;
        @include transform(translateX(-50%));
    }
}
