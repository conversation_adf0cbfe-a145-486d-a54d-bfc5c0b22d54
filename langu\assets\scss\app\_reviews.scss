.reviews-list {
    &-item {
        margin: 0.75em 0;
        
        .langu-panel-header {
            padding-bottom: 15px;
            
            .title {
                font-weight: 700;
                margin-left: 0;
            }
        }
        
        .langu-panel-body {           
            line-height: 1.15;
            padding-top: 20px;
        }
    }
}

.reviews-sidebar {
    &-section {
        border-bottom: 1px solid #141414;
        padding: 0 0 0.675em;
        margin-bottom: 1.250em;

        .overall {
            text-align: center;
            
            &-number {
                font-size: 3.75em;
                line-height: 1;
                margin: 0;
                text-align: center;
                vertical-align: text-bottom;
                font-weight: 300;
            }

            &-stars {
                font-size: 1.5em;
                color: $langu_gold;
            }
        }
        
        .number {
            color: $langu_gold;
            font-size: 2em;
            font-weight: 300;
            margin: 0;
            line-height: 1;
        }
        
        .detail {
            .star-rating {
                color: $langu_gold;
            }
            
            &-number {
                margin-left: 1em;
            }
        }

        &-header {
            text-transform: uppercase;
            color: #ffffff;
            font-size: 0.875em;
            font-weight: 600;
        }

        &-content {
            margin-top: 2em;
            margin-bottom: 1em;

            @include clear-after();
        }
    }
}