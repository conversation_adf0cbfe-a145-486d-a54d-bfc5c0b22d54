.columns-wrapper {
    overflow: hidden;

    .sidebar {
        .in-button {
            display: none;
        }
        
        @media (max-width: 767px) {
            @include transition(0.2s ease-in-out 0.2s);
            
            & > * {
                @include transition(0.2s ease-in-out 0.4s);
            }
            
            & + .middle {
                @include transition(0.2s ease-in-out 0.4s);
            }
            
            &.in + .middle {
                pointer-events: none;
                @include opacity(0);
                @include transition(0.2s ease-in-out);
            }
            
            &:not(.in) {
                width: 2.188rem;
                
                & > *:not(.in-button) {
                    @include opacity(0);
                    @include transition(0.2s ease-in-out);
                }
            }
            
            .in-button {
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                margin: 0 auto;
                display: flex;
                height: calc(100vh - 55px);
                justify-content: flex-start;
                align-items: center;
                width: 2.188rem;
                z-index: 899;
                
                &-text {
                    text-transform: uppercase;
                    font-weight: bold;
                    font-size: 1em;
                    @include transform(translateX(-50%) rotate(-90deg) translateY(100%));
                    line-height: 1;
                    white-space: nowrap;
                    
                    &.expanded {
                        display: none;
                    }
                    
                    &.collapsed {
                        display: initial;
                    }
                }
            }
            
            &.in {
                .in-button {
                    &-text {
                        &.expanded {
                            display: initial;
                        }
                        
                        &.collapsed {
                            display: none;
                        }
                    }
                }
            }
        }
        
        width: 21.875em;
        background: #232323;
        color: #ffffff;
        position: relative;
        margin-bottom: -9999px;
        padding-bottom: 9999px;

        .sidebar-inner {
            padding: 1.563rem;

            .info {
                font-style: italic;
                font-size: .75em;
                line-height:1.15;
                padding: 0;
                margin: 0;
                text-align: left;
                display: block;
                width: 100%;
                font-weight: 400;
            }
            
            .overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0,0,0,0.6);
                z-index: 2;
                
                &-inner {
                    position: fixed;
                    top: 50%;   
                    right: 10.937rem; 
                    @include transform(translate(50%, -50%));
                    margin: 0 auto;
                    background: #2d2d2d;
                    z-index: 1;
                    padding: 25px 25px 25px 20px;
                    width: 17.5rem;
                    text-align: center;
                    line-height: 1.15;
                }
            }
        }
    }

    .middle {
        float: none;
        overflow: hidden;
        width: auto;
        min-height: calc(100vh - #{$max_nav_height});
        position: relative;

        .middle-inner {
            padding: 0 1.25em;
            margin-top: -5px;
        }
        
        .middle-inner-classroom{
            padding-top: 0;
            margin-top: -4px;}
    }
}

