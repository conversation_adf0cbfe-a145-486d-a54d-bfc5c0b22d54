.empty-result {
    position: relative;
    text-align: center;
    left: 50%;
    color: $langu_primary;
    @include transform(translateX(-50%));
    width: 60%;
    line-height: 1.25;
    font-size: 1.1em;

    &-image {
        max-width: 20%;
        margin: 0 auto;
        display: block;
    }

    &-link {
        color: $langu_gold;
        font-weight: bold;
    }
}

.top-container {
    display: block;
    margin: 0 -1.25em .938em;
}

.steps {
    display: flex;
    counter-reset: steps;
    padding: 0;
    align-items: stretch;
    margin: 0;
    
    @media (max-width: 479px) {
        display: block;
    }

    .step {
        flex: 1 1 0;
        counter-increment: steps;
        color: #ffffff;
        font-weight: 600;
        display: flex;
        padding: 0.5em;
        line-height: 1.15;
        align-items: center;

        &:before {
            content: counter(steps) '.';
            display: block;
            @include border-radius(99%);
            background-color: $langu_gold;
            height: 2em;
            width: 2em;
            margin-right: 1.5em;
            text-align: center;
            flex: 0 0 auto;
            line-height: 2;
        }

        &:first-of-type {
            background-color: #5f5f5f;
        }

        &:nth-of-type(2) {
            background-color: #4c4c4c;
        }

        &:nth-of-type(3) {
            background-color: #2d2d2d;
        }
    }
}

.top-banner {
    background: $langu_pink;
    background: -moz-linear-gradient(45deg, $langu_pink 0%, $langu_gold 52%);
    background: -webkit-linear-gradient(45deg, $langu_pink 0%,$langu_gold 52%);
    background: linear-gradient(45deg, $langu_pink 0%,$langu_gold 52%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#{$langu_pink}', endColorstr='#{$langu_gold}',GradientType=1 ); 
    color: #ffffff;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 1em 1.5em;
    
    @media (min-width: 480px) {
        flex-wrap: nowrap;
        padding: 0 1em 0 2.5em;
    }
    
    .title {
        line-height: 1.15;
        font-size: 1em;
        margin: 0;
        color: inherit;
        flex: 0 1 0;
        
        @media(min-width: 480px) {
            flex: 0 0 0;
            padding: 1em 0;
        }
        
        .small {
            display: block;
            font-size: 1em;
            color: inherit;
            white-space: nowrap;
        }
        
        .large {
            display: block;
            font-size: 2em;
            font-weight: 600;
            color: inherit;
            text-transform: uppercase;
        }
    }
    
    .icon {
        overflow: hidden;    
        padding: 0 1em;
        vertical-align: middle;
        
        img {
            height: 5em;
        }
    }
    
    .description {
        line-height: 1.15;
        margin: 0;
        
        @media (min-width: 480px) {
            flex: 1 1 0;
            padding: 1em 0;
        }
    }
}

.teacher-item {
    background: none;

    .teacher-heading {
        background-color: #ffffff;

        .teacher-heading-left {
            overflow: hidden;
            float: none;
            width: auto;
            padding-left: 5px;

            .teacher-name {
                margin: 0.675em 0 0 0;
                font-size: 1.25em;
                font-weight: 900;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                color: inherit;
            }

            & > .rating {
                font-size: 0.75em;
                line-height: 1.5;
                font-weight: 700;

                .emp {
                    color: $langu_pink;
                }

                .teacher-badge {
                    height: 1.3em;
                    vertical-align: text-top;
                }

                .star-rating {
                    .star {
                        padding: 0 1px;
                    }
                }
            }
        }

        .teacher-heading-right {
            float: right;
            display: flex;
            text-align: right;

            .teacher-picture {
                float: right;
                z-index: 1;
                position: relative;
                font-size: 0.5em;
                z-index: 11;
                max-width: 56px;
                max-height: 56px;

                @media (min-width: 768px) {
                    max-width: 61px;
                    max-height: 61px;
                }

                @media (min-width: 1200px) {
                    max-width: 78px;
                    max-height: 78px;
                }
            }

            .teacher-flags {
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-right: -0.375em;

                .flag-icon {
                    height: 2em;
                    width: 2.6666em;
                    display: block;
                    @include border-radius(0.5em);
                    z-index: 1;
                    margin: 0.1em 0;
                }
            }
        }        
    }

    &:hover {
        .teacher-hover-card {
            @include opacity(1);
        }
    }

    .teacher-hover-card {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #ffffff;
        @include opacity(0);
        @include transition(opacity 0.5s);
        border-bottom-left-radius: 2px;
        border-bottom-right-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;

        p {
            font-size: 1.5em;
            color: $langu_gold;
            font-weight: 900;
            text-transform: uppercase;
        }
    }

    .teacher-content {
        background: rgba(244,244,244,0.5);
        position: relative;
        min-height: 13.5625em;

        @media (min-width: 992px) {
            min-height: 187px;
        }

        @media (min-width: 1200px) {
            min-height: 217px;
        }

        &-inner {
            display: flex;
            flex-wrap: wrap;

            @media (min-width: 992px) {
                flex-wrap: initial;
            }
        }

        .teacher-trial {
            border-radius: 0.43em;
            color: #fff;
            font-weight: 600;
            font-size: 0.875em;
            display: inline-block;
            padding: 0.43em;
            min-width: 7.857em;
            text-align: center;
            line-height: 25.844;

            &.teacher-trial-free {
                background-color: $langu_green;            
            }

            &.teacher-trial-paid {
                background-color: $langu_gold;
            }
            
            &.teacher-trial-full-schedule {
                background-color: #5f5f5f;
            }

            &.teacher-trial-none {
                visibility: hidden;
            }

            .emp {
                text-transform: uppercase;
            }
        }

        .teacher-price {
            line-height: 33.228px;
            font-size: 1.125em;
            font-weight: 300;
            padding: 4px 0 2px;
            float: right;

            @media (min-width: 992px) {
                display: block;
                float: none;
            }

            .emp {
                font-size: smaller;
            }
        }

        &-left {
            width: 100%;
            position: relative;
            padding-left: 5px;
            padding: 0 5px;
            margin-bottom: 0.5em;

            @media (min-width: 992px) {
                width: 58.33333333%;
                margin-right: 10px;                
                padding-right: 25px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 0;

                &:after {
                    content: '';
                    position: absolute;
                    width: 2px;
                    background: rgba(35, 35, 35, 0.325);
                    height: 100%;
                    top: 0;
                    right: 0;
                }
            }

            .teacher-summary {
                color: #000000;
                line-height: 1.15;
                font-weight: 400;
                margin: 0.4em 0;

                @media (min-width: 480px) {
                    margin: 0;
                    min-height: 5.75em;
                }

                @media (min-width: 992px) {
                    min-height: 6.9em;
                }
            }
        }

        &-right {
            width: 100%;
            float: right;
            line-height: 1.15;
            font-weight: 400;
            padding: 0 5px;

            @media (min-width: 992px) {
                width: 41.66666667%;
                margin-top: 1em;
                padding: 0;
            }

            .teacher-specialities {
                .title {
                    color: #fbb03b;
                    margin-bottom: 0.5em;
                    display: inline-block;
                    font-weight: 600;
                    text-transform: uppercase;
                }

                .items {
                    display: flex;

                    @media (min-width: 992px) {
                        margin-bottom: 0;
                    }

                    @media (min-width: 480px) {
                        display: block;
                    }

                    .item {
                        display: flex;
                        align-items: center;
                        margin: 3px;

                        @media (min-width: 480px) {
                            margin: 3px 0;
                        }

                        &-icon {
                            max-width: 2.3em;
                            max-height: 2.3em;
                            margin-right: 0.25em;
                        }
                    }
                }
            }
        }

    }
}
