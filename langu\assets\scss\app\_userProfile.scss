@include keyframes(overlay-fade-in) {
    from { opacity: 0; }
    to { opacity: 1; }
};

@include keyframes(activity-indicator-spin) {
    from { 
        opacity: .5;	
        @include transform(scale(0.75)); 
    }
    50% {   
        opacity: 1;	
        @include transform(scale(1));
    }
    to	 { 
        opacity: .5;	
        @include transform(scale(0.75)); 
    }
};

.image-lightbox {
    &-full {
        position: fixed;
        z-index: 99999;

        -ms-touch-action: none;
        touch-action: none;
        @include box-shadow(0 0 3.125em rgba( 0, 0, 0, .3));
    }

    &-overlay {
        background-color: rgba( 255, 255, 255, .6);
        position: fixed;
        z-index: 99908;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;

        @include animation(overlay-fade-in .25s linear);
    }

    &-close-button {
        width: 2.5em;
        height: 2.5em;
        text-align: left;
        background-color: $langu_gold;
        @include border-radius(50%);
        position: fixed;
        z-index: 100002;
        top: 2.5em;
        right: 2.5em;
        border: none;
        @include transition(color 0.3s ease);

        &:before, &:after {
            width: 2px;
            background-color: #fff;
            content: '';
            position: absolute;
            top: 20%;
            bottom: 20%;
            left: 50%;
            margin-left: -1px;
        }

        &:before {
            @include transform(rotate( 45deg ));
        }

        &:after {
            @include transform(rotate( -45deg ));
        }
    }

    &-activity-indicator {
        width: 2.5em;
        height: 2.5em;
        background-color: #444;
        background-color: rgba( 0, 0, 0, .5 );
        position: fixed;
        z-index: 100003;
        top: 50%;
        left: 50%;
        padding: 0.625em;
        margin: -1.25em 0 0 -1.25em;
        @include border-radius(50%);
        @include box-shadow(0 0 2.5em rgba(0, 0, 0, 0.75));

        div {
            width: 1.25em;
            height: 1.25em;
            background-color: $langu_gold;
            @include border-radius(50%);        
            @include animation(activity-indicator-spin .5s ease infinite);
        }
    }
}

.trial-booking {
    position: absolute;
    left: 0;
    top: 10px;
    width: 6.385em;
    height: 6.385em;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    text-align: center;
    border-radius: 99px;
    font-size: .813em;
    line-height: 1.2;
    z-index: 898;
    @include transform(translateX(-80%));
    @include no-select();

    span {
        text-transform: uppercase;
        max-width: 100%;
        display: inline-block;
        position: relative;
        padding: 0 3px;
        cursor: default;

        .emp {
            font-weight: 900;
        }
    }

    &:after {
        content: '';
        position: absolute;
        display: block;
        width: 100%;
        height: 105px;
        top: 6.385em;
        left: 8px;
        background-repeat: no-repeat;
        background-size: contain;
    }

    &.free {
        background: $langu_green;

        &:after {        
            background-image: url('../images/arrow_green.png');
        }
    }

    &.paid {
        background: $langu_gold;

        &:after {        
            background-image: url('../images/arrow_orange.png');
        }
    }
}

.teacher-item {
    .teacher-heading {

        &.profile-heading {
            padding-bottom: 10px;
        }

        &-profile-right {
            display: flex;
            float: right;
            text-align: right;

            .teacher-picture {
                position: relative;
                font-size: 0.5em;
                z-index: 11;
                
                max-width: 67px;
                max-height: 67px;
                
                @media (min-width: 768px) {
                    max-width: 72px;
                    max-height: 72px;
                }
                
                @media (min-width: 992px) {
                    max-width: 90px;
                    max-height: 90px;
                }
                
                background-color: rgba(243,246,248,.94);
                border: 4px solid #fff;
                @include box-shadow(inset 0 1.5px 3px 0 rgba(0,0,0,.15), 0 1.5px 3px 0 rgba(0,0,0,.15));
            }

            .teacher-flags {
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-right: -0.375em;

                .flag-icon {
                    height: 2.5em;
                    width: 3.3333em;
                    display: block;
                    /*margin-bottom: 0.188em;*/
                    @include border-radius(0.526em);
                    z-index: 1;

/*                    &:only-child {
                        margin-top: 1.125em;
                    }                */
                }
            }
        }
        
        &-profile-left {
            min-height: 67px;
                
            @media (min-width: 768px) {
                min-height: 72px;
            }

            @media (min-width: 992px) {
                min-height: 90px;
            }
        }

        &-left {
            &-row {
                display: flex;
                align-items: baseline;
                padding: 0.75em 0;
                flex-wrap: wrap;
            }
            
            .profile-teacher-name {
                display: inline-block;
                flex-shrink: 0;
                font-size: 1.563em;
                margin: 0 .25em 0 0;
                text-transform: uppercase;
                font-weight: 900;
                overflow: hidden;
                color: #232323;
            }

            .profile-teacher-rating {
                display: inline-block;
                flex-shrink: 0;
                padding: 0 0.5em 0 0;
                color: $langu_pink;
                
                .emp {
                    font-weight: bolder;
                }
                
                .reviews {
                    color: $langu_primary;
                }
                
                & > .teacher-badge {
                    height: 1.3em;
                    vertical-align: text-bottom;
                }

                & > .star-rating {
                    font-size: 0.75em;
                    vertical-align: text-top;

                    .star {
                        padding: 0 1px;
                    }
                }
            }

            .profile-new-message-button {
                margin-bottom: 0.5em;
                font-size: 0.875em;
            }
        }

        .profile-video {
            @include clear-before();
            @include clear-after();

            margin: 0 -15px;
        }

        .profile-short-summary {
            margin: 1em;
            text-align: center;
            font-weight: 300;
            line-height: 1.15;
            font-size: 1.125em;
        }
        
        .profile-specialities {
            &-title {
                color: $langu_gold;
                font-weight: 600;
                font-size: 1em;
                margin: 0.5em 15px;
                display: block;
            }
            
            &-speciality {
                display: flex;
                align-items: center;
                line-height: 1;
                min-height: 2.5em;
                
                &-icon {
                    max-width: 2em;
                    max-height: 2em;
                    margin-right: 0.5em;
                }
            }
        }
    }

    .teacher-body {
        @include clear-after();
        padding: 10px 0 25px;

        .info {
            .white-panel {
                background: rgba(255, 255, 255, 0.4);
                padding: 10px;
                margin: calc(1.286em - 10px) 0 0 -15px;
                
                & + .white-panel {
                    margin-top: 10px;
                }
                
                .title:first-child {
                    margin-top: 0;
                }
            }

            .title {
                font-weight: 700;
                text-transform: uppercase;
                margin: 1.286em 0 0.786em;
                line-height: 1.15;
            }

            .description {
                line-height: 1.125;

                &.emp {
                    font-weight: 700;
                    color: $langu_gold;
                }
                
                ul {
                    margin: 0.25em 0;
                    padding-left: 1.5em;
                    
                    li {
                        list-style-type:none;
                        position:relative;

                        &:before {
                            content: "\2022";
                            line-height: 0;
                            position: absolute;
                            margin-top: 0.525em;
                            color: $langu_gold;
                            margin-left: -1rem;
                        }
                    }
                }
                
                &.qualification {
                    line-height: 1;
                    margin: 0.5em 0;
                    padding-left: 1.1em;
                    
                    & > .glyphicon {
                        color: $langu_gold;
                        font-size: 0.8em;
                        margin-left: -1.3em;
                    }
                }
            }
        }
    }
}

.side-panel {
    &-header {
        text-align: left;

        .title {
            text-transform: uppercase;
            font-weight: 700;
            margin-left: 0;
            font-size: 0.875em;
            color: #232323;
        }
    }

    & + .side-panel {
        margin-top: 1.5em;
    }
}

.unique-facts-panel {
    &-body {
        font-size: 0.875em;

        ul {
            padding: 0 0 0 0.5em;
            list-style: none;
            
            li {
                position: relative;
                line-height: 1.25;
                padding-left: 1em;
                
                &:before {
                    content: "\2022";
                    line-height: 0;
                    position: absolute;
                    margin-top: 0.35em;
                    color: $langu_gold;
                    font-size: 1.5em;
                    margin-left: -0.9rem;
                }
                
                &:not(:last-child) {
                    margin-bottom: 0.5em;                
                }
            }
        }
    }
}

.prices-panel {
    &-body {
        font-size: 0.875em;
        
        .trial-info {
            font-weight: 600;
            margin: 0 0 0.313em 0;

            .free-trial {
                text-transform: uppercase;
                color: olivedrab;
            }
        }
        
        .length-selector {
            border-top: 2px solid #ddd;
            font-weight: 600;
            color: $langu_primary;
            line-height: 1.15;
            padding: 0.5em 0 0.25em;
            display: flex;
            align-items: center;
            width: 90%;
            
            label {
                font-weight: inherit;
                margin: 0;
                padding-right: 0.5em;
                flex-grow: 0;
            }
            
            .selectize-control {
                flex-grow: 1;
                
                .selectize-input {
                    background-color: transparent;
                    background-image: none;
                    @include box-shadow(none);
                    @include border-radius(0);
                    border: 2px solid #ddd;
                    padding: 0.1em calc(25px + 0.25em) 0.1em 0.25em;
                    line-height: inherit;
                    color: inherit;
                }
                
                &.single {
                    .selectize-input {
                        &.dropdown-active {
                            &::before {
                                content: '';
                                position: absolute;
                                right: 20px;
                                left: auto;
                                top: 0;
                                height: 100%;
                                width: 2px;
                                background-color: #ddd;
                            }
                        }
                        
                        &::before {
                            content: '';
                            position: absolute;
                            right: 20px;
                            top: 0;
                            height: 100%;
                            width: 2px;
                            background-color: #ddd;
                        }
                        
                        &::after {
                            right: 5px;
                        }
                    }
                }
                
                .selectize-dropdown {
                    background-color: #ffffff;
                    background-image: none;
                    @include box-shadow(none);
                    @include border-radius(0);
                    border: 2px solid #ddd;
                    margin-top: -2px;
                    line-height: inherit;
                    color: inherit;
                    
                    .option {
                        padding: 0.1em 0.25em;
                        color: inherit;
                        background: none;
                        
                        &.active {
                            color: $langu_gold;
                        }
                    }
                }
            }
        }

        .table {
            margin-bottom: 0;
            width: 90%;

            caption {
                caption-side: bottom;
                color: inherit;
                font-style: italic;
            }
            
            thead {
                & > tr {
                    & > th {
                        border-bottom: none;
                        text-align: center;
                        padding: 0.1em 0.25em;
                        text-transform: uppercase;
                    }
                }
            }
            
            tbody { 
                tr {
                    display: none;

                    &.visible {
                        display: table-row;
                    }
                    
                    & > th {
                        border: none;
                        text-align: center;
                        padding: 0.1em 0.25em;
                    }
                    
                    & > td {
                        border: none;
                        text-align: center;
                        padding: 0.1em 0.25em;
                    }
                }
            }
        }
    }
}

.page-subtitle {
    &.reviews-subtitle {
        padding-bottom: 0;
        margin-top: 1.5em;
    }
}

.reviews-list {
    &-item {
        & &-profile {
            &-content {
               font-size: 0.875em;
               font-style: italic;
            }
        }
    }
}

.reviews-empty {
    line-height: 1.15;
}

.booking-form-overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    z-index: 99;

    &-inner {
        position: relative;
        margin: auto;
        display: flex;
        flex-direction: column;
        padding: 0 5em;
        justify-content: center;
        align-items: center;
        position: fixed;
    }
}
