.settings-panel {
    overflow: hidden;
    color: $langu_primary;

    @media (min-width: 768px) {
        display: flex;
    }

    &-column {
        padding-left: 0;
        padding-right: 0;
        padding-bottom: 9999px;
        margin-bottom: -9999px;

        &-inner {
            padding: 2.875em 2em 2em;
            height: 100%;

            .pricing-form {
                button[type="submit"] {
                    font-size: 0.875em;
                    margin-bottom: 2.5em;
                }
            }
        }
    }

    .qualification-panel {
        &-table {
            table-layout: fixed;
            width: 100%;
            margin-right: -1em;

            thead {
                th {
                    line-height: 1;
                    vertical-align: bottom;
                    padding-bottom: 1em;
                }
            }
        }

        &-action {
            @include transition(color 0.3s);

            .glyphicon {
                font-weight: 100;
            }

            &:hover {
                color: $langu_gold;
            }
        }
    }

    .userinfo {
        background-color: #f1f1f1;

        .settings-panel-column-inner
        {
            padding-left: 5em;
            padding-right: 5em;
            min-height: calc(100vh - 55px);

            &.student {
                padding-bottom: 15em;
            }
        }

        &-picture {
            display: block;
            margin: 0 auto;
            cursor: pointer;
        }

        &-picture-tip {
            font-size: 0.875em;
            line-height: 1.286;
        }

        &-username {
            display: block;
            font-weight: 900;
            font-size: 3.875em;
            line-height: 1.048;
            margin-top: 0.290em;
            overflow: hidden;
            max-width: 100%;
            color: inherit;

            &:after {
                content: '';
                background-color: $langu_gold;
                height: 4px;
                position: relative;
                width: 100%;
                margin-top: 0.258em;
                display: block;
            }

            .sub {
                font-size: 0.339em;
                text-transform: uppercase;
                color: $langu_gold;
                margin-left: 2em;
            }
        }

        &-details {
            font-size: 0.875em;
            line-height: 1.286;

            .input-group {
                &-addon {
                    font-size: inherit;
                    color: inherit;
                    padding: 0 0.25em 0 0.5em;
                    border: none;
                }

                .form-control {
                    font-size: inherit;
                    height: auto;
                    color: inherit;
                    line-height: inherit;
                    display: inline-block;
                    padding: 0 0.25em 0 0;
                    min-width: 2em;
                    width: auto;
                    background-color: #ffffff;
                    @include box-shadow(none);

                    &[disabled] {
                        border-bottom: none;
                        @include opacity(0.7);
                        background-color: transparent;
                    }
                }
            }

            &-title {
                text-transform: uppercase;
                font-weight: 900;
                margin: 1.286em 0 0.857em;
                line-height: 1.5;
                
                & > .sub {
                    display: block;
                    font-weight: 400;
                    text-transform: none;
                    line-height: 1.15;
                }
            }

            &-content {
                line-height: inherit;

                p, span {
                    line-height: inherit;
                }
                
                .trial-selection {
                    .input-group {
                        .form-control {
                            width: 2.3em;
                        }
                    }
                }

                .indent {
                    margin-left: 1.5em;
                }
            }

            .pricing-panel, .qualification-panel {
                &-table {
                    width: 100%;
                    margin-right: -1em;

                    thead {
                        th {
                            line-height: 1;
                            vertical-align: bottom;
                            padding-bottom: 1em;
                        }
                    }
                }

                &-action {
                    @include transition(color 0.3s);

                    .glyphicon {
                        font-weight: 100;
                    }

                    &:hover {
                        color: $langu_gold;
                    }
                }
            }

            .qualification-panel {
                &-table {
                    table-layout: fixed;
                }
            }

            .pricing-panel {
                &-table {
                    thead {
                        & > tr {
                            & > th {
                                border-bottom: 2px solid #ddd;
                            }
                        }
                    }

                    tbody {
                        & > tr {
                            & > td {
                                border-bottom: 1px solid #ddd;
                            }

                            & > th {
                                border-bottom: 1px solid #ddd;
                                padding-right: 0.5em;                            
                            }
                        }
                    }
                }

                .input-group {
                    margin: 0.25em;

                    &-addon {
                        padding: 0 0.25em 0;
                    }

                    .form-control {
                        background-color: #ffffff;
                        border: 1px solid #dddddd;
                        min-width: auto;
                        width: 100%;
                        display: block;
                        line-height: inherit;
                        @include box-shadow(none);
                        
                        &.error {
                            border-color: $langu_pink;
                        }
                    }
                }
            }
        }
    }

    .options {        
        background: #FFFFFF;

        &:before {
            content: '';
            display: block;
            width: 50%;
            background-color: #f7f7f7;
            position: absolute;
            height: 100%;
            top: 0;
            left: 0;
        }

        &-form {
            position: relative;
            height: 100%;
            @include clear-after();

            button[type="submit"] {
                margin: 1em 0;
            }
        }

        &-section {
            &-header {
                text-transform: uppercase;
                font-size: 0.875em;
                font-weight: 900;
                line-height: 1;
                margin: 0 0 0 -2.286em;
                padding: 1em 6em 0;
                width: calc(50% + 2.286em);
                color: inherit;
                cursor: pointer;

                &:after {
                    content: '';
                    display: block;
                    width: 100%;
                    margin: calc(1em - 1px) auto 0;
                    position: relative;
                    height: 1px;
                    background-color: #a5a5a5;
                }

                &.active {
                    background-color: #ffffff;
                    padding-bottom: 1em;

                    &:after {
                        content: none;
                    }

                    & + .options-section-content {
                        display: block;
                        max-height: 100%;
                        overflow-y: scroll;
                        @include clear-after();
                    }
                }
            }

            &-content {
                @include transition(0.5s);
                display: none;
                position: absolute;
                right: 0;    
                top: 0.857em;
                width: 50%;
                padding-left: 5em;
                padding-right: 5em;
                font-size: 0.875em;

                .title {
                    text-transform: uppercase;
                    color: inherit;
                    line-height: 1.214;
                    font-weight: 900;
                    margin: 0 0 1.286em 0;
                    border: none;
                    font-size: 1em;
                }

                & > :not(.title) {                    
                    margin-left: 1.5em;
                }

                .form-group {
                    margin-bottom: 1em;

                    .form-label {
                        text-transform: uppercase;
                        color: inherit;
                        line-height: 1.214;
                        font-weight: 700;
                        margin: 0 0 1.286em;
                        border: none;
                        font-size: 1em;
                    }

                    .selectize-input {
                        font-size: inherit;

                        input {
                            font-size: 1em;
                        }
                    }

                    .selectize-dropdown {
                        font-size: inherit;
                    }

                    input[type="checkbox"] + label, input[type="radio"] + label {
                        line-height: 1.15;

                        &:before {
                            background-color: #e8e8e8;
                        }
                    }

                    .form-helper {
                        @include opacity(0.7);
                        color: inherit;
                        font-size: 0.857em;
                        padding: 0.286em;
                        line-height: 1.15;

                        &.bold {
                            font-weight: bolder;
                        }

                        .emp {
                            font-weight: bolder;
                        }
                    }

                    textarea.form-control {
                        border: 1px solid #a1a1a1;
                        padding: 0.313em;
                    }

                    tex
                    .form-control {
                        color: inherit;
                        font-size: inherit;
                        box-shadow: none;
                        border-bottom: 1px solid #a1a1a1;
                        padding: 0;
                        @include border-radius(0);

                        &.autosize {
                            line-height: 1.2;
                            resize: none;
                        }

                        &.disabled {
                            cursor: not-allowed;
                        }

                        @include input-placeholder {
                            color: inherit;
                            @include opacity(0.7);
                        }

                        + .form-control {
                            margin-top: 2em;
                        }
                    }

                    .selectize {
                        &.form-control {
                            margin-top: 0;
                        }

                        &.no-dropdown {
                            &.selectize-control {
                                .selectize-input {
                                    &:after {
                                        content: none;
                                    }
                                }

                                .selectize-dropdown {
                                    display: none;
                                }
                            }
                        }

                        &.selectize-control {
                            line-height: 1;
                            color: inherit;

                            .selectize-input {
                                border-radius: 0;    
                                border: none;
                                /*border-bottom: 1px solid #a1a1a1;*/
                                box-shadow: none;
                                background: none;
                                color: inherit;
                                padding: 0.675em 0;

                                &.dropdown-active {
                                    &:before {
                                        content: none;
                                    }

                                    &:after {
                                        border-color: transparent transparent #a1a1a1;
                                    }
                                }

                                &:after {
                                    border-color: inherit transparent #a1a1a1;
                                }

                                input {
                                    color: inherit;
                                }
                            }

                            .selectize-dropdown {
                                &.form-control {                            
                                    height: auto;
                                }

                                position: absolute;
                                border: none;
                                border-radius: 0;
                                box-shadow: none;
                                color: inherit;
                                background: #ffffff;

                                .option {
                                    background: #ffffff;
                                }

                                .option.active {
                                    color: $langu_gold;
                                }
                            }

                            &.multi {
                                .selectize-input {
                                    .item {
                                        border: 1px solid #CAD8F3;
                                        background: #DEE7F8;
                                        box-shadow: none;
                                        @include border-radius(3px);
                                        text-shadow: none;
                                        color: inherit;
                                    }
                                }
                            }

                            &.single {
                                line-height: 1;
                                color: inherit;

                            }
                        }
                    }
                }
            }

            &-button-container {
                width: 50%;
                display: block;
                margin-top: 2em;
                text-align: center;
            }
        }
    }
}

.specialities {
    &-title {
        font-size: 1em;
        line-height: 1.15;
        color: $langu_light;
    }

    &-container {
        display: flex;
        flex-direction: row;
        align-content: stretch;
        margin-bottom: 1em;
        float: left;
        width: 100%;

        & > div {
            display: flex;
            flex-direction: column;
            align-items: stretch;

            & > div, form {
                display: flex;
                flex-direction: column;
                align-items: stretch;
                flex: 1;
            }
        }
    }

    &-list {
        min-height: 1.5em;
        border: 1px solid $langu_primary;
        padding: 0.5em;
        line-height: 1.4;
        flex: 1;

        &-selected {
            .speciality-item {
                &:nth-child(-n+3) {
                    color: $langu_gold;
                }
            }
        }

        .speciality-item {
            cursor: move;
            @include no-select();
        }
    }
}
