.content {
    background-color: #ffffff;
    color: $langu_primary;
    padding: 5rem 0;
    
    .button-container {
        margin: 0 0 6rem;
        text-align: center;
    }

    .intro {
        font-size: 1.75em;
        line-height: 1.25;
        padding: 0 0 4rem;
        text-align: center;
        margin: 0 1.5rem;
        
        @media (min-width: 480px) {
            margin: 0 20%;
        }
    }
    
    .boxes {
        margin: 4rem 0;
        text-align: center;
        
        @media (min-width: 480px) {
            margin: 4em 10%;
            display: flex;
            justify-content: space-between;
        }
        
        .box {
            flex: 1 1 0;
            padding: 0.5rem 1.25rem;            
            font-size: 1.2em;
            line-height: 1.15;
            
            @media (min-width: 480px) {
                padding: 0 1.25rem;
            }
            
            .icon {
                height: 3em;
                max-width: 40%;
            }
            
            .headline {
                font-weight: bold;
                display: block;
                margin: 0;
                color: inherit;
                font-size: inherit;
            }
            
            .text {
                margin: 0;
            }
        }
    }
    
    .members {
        padding: 4.2rem 0 6rem;
        margin: 0 15px;
        
        @media (min-width: 480px) {
            margin: 0 14%;
        }
        
        .member {            
            & + .member {
                margin-top: 0.5rem;
            }
            
            @include clear-after();
            
            .image {
                width: 50%;
                max-width: 250px;
                height: auto;
                float: left;
                margin-right: 19px;
                @include box-shadow(0px 3px 10px -2px rgba(0,0,0,0.6));
                margin-bottom: 15px;
                
                @media (min-width: 768px) {
                    height: 250px;
                    width: 250px;
                    margin-bottom: 0;
                }                
            }
            
            .top {
                float: left;
                margin: 0;
                font-size: 1.2em;
                color: inherit;
                max-width: calc(50% - 19px);
                line-height: 1.15;
                
                & > .name {
                    display: block;
                    color: $langu_gold;
                    
                    @media (min-width: 768px) {
                        overflow: hidden;
                        height: 35px;
                        font-size: 28px;
                    }
                }
                
                & > .title {
                    display: block;
                    
                    @media (min-width: 768px) {
                        overflow: hidden;
                        height: 25px;                    
                        font-size: 19px;
                    }
                }
                
                @media (min-width: 768px) {
                    max-width: calc(100% - 269px);
                    line-height: 1;
                    height: 60px;
                }                
            }
                        
            .bio {
                line-height: 1.15;
                background-color: #eeeeee;
                padding: 25px 40px 25px 25px;
                width: 100%;
                clear: both;
                
                &-inner {
                    font-size: 1.2em;
                    margin: 0;
                }

                @media (min-width: 768px) {
                    float: left;
                    clear: none;
                    height: 190px;
                    max-width: calc(100% - 269px);
                    padding: 18px 40px 18px 10px;
                    
                    &-inner {
                        font-size: 1.2em;
                        overflow: hidden;
                        max-height: 100%;
                    }
                }                
            }
        }
    }
    
    .section {
        margin: 0 1rem;
        text-align: center;

        @media (min-width: 480px) {
            margin: 0 20%;
        }
        
        .headline {
            margin: 0 auto;
            line-height: 1;            
            font-size: 1em;
            display: inline-block;
            color: inherit;
            position: relative;
            text-transform: none;
                
            
            &:after {
                display: block;
                height: 4px;
                margin: 2px auto 0;
                content: '';
                background-color: $langu_gold;
                width: 100%;
            }
                            
            .regular {
                font-size: 3.4em;
                padding: 0 0.12em;
                font-weight: bold;
            }

            .super {
                color: $langu_gold;
                text-transform: uppercase;
                position: absolute;
                top: 0;
                left: 1.6em;
                font-size: 1.105em;
                font-weight: bold;
            }
        }
        
        .text {
            font-size: 1.2em;
            line-height: 1.15;
            margin: 2rem 0 0;
            
            a {
                color: $langu_gold;
            }
            
            &-bold {
                font-weight: bold;
            }
        }

        &-team {
            &.en {
                .headline {
                    .regular {
                        padding-left: 0.75em;
                    }

                    .super {
                        left: 0.3em;
                    }
                }
            }

            &.pl {
                .headline {
                    .regular {
                        padding-left: 0.4em;
                    }

                    .super {
                        left: 0.3em;
                    }
                }
            }
        }

        &-teachers {
            &.pl {
                .headline {
                    .super {
                        left: 3.25em;
                    }
                }
            }
        }

        &-methodology {
            &.pl {
                .headline {
                    .regular {
                        padding-left: 0.75em;
                    }

                    .super {
                        left: 0.3em;
                    }
                }
            }
            &.es {
                .headline {
                    .super {
                        left: 0em;
                        top: -0.2em;
                    }
                }
            }
        }
    }
}
