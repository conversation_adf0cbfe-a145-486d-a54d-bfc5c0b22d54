.heading {
    text-align: center;
    background-color: $langu_primary;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    display: flex;
    height: 300px;
    flex-direction: column;
    justify-content: flex-end;
    
    @media(min-width: 480px) {
        height: 500px;
    }
    
    .text {    
        text-align: center;
        font-size: 1.75em;
        line-height: 1.1;
        color: #ffffff;
        padding: 0.75em 20%;
        margin: 0;
        background-color: rgba(0, 0, 0, 0.6);
        
        .em {
            color: $langu_gold;
        }
    }
}
