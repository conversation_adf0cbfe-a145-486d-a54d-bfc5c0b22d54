@charset "UTF-8";

/*@import "fa/variables";
@import "fa/path";*/

@import "../common/variables";
@import "../common/fonts";
@import "../common/mixins";

html {
    font-size: 0.6em;
    min-height: 100%;

    @media (min-width: 768px) {
        font-size: 0.7em;
    }

    @media (min-width: 992px) {
        font-size: 0.8em;
    }

    @media (min-width: 1200px) {
        font-size: 1em;
    }
}

body {
    font-family: 'Lato',sans-serif;
    font-size: 100%;
    color: #232323;
    padding: 0;
    margin: 0;
    background: url($langu_bg) repeat;
    overflow-y: auto;
    overflow-x: hidden;
    
    @media (min-width: 768px) {
        &.no-overflow {
            overflow-y: auto;
        }
    }

    /*    @media (max-width: 991px) {
            font-size: 0.8em;
        }
    
        @media (max-width: 767px) {
            font-size: 0.7em;
        }
    
        @media (max-width: 359px) {
            font-size: 0.6em;
        }*/
}

a {
    cursor: pointer;
}


@font-face {
    font-family: BBB;
    src: url(../fonts/bbb-icons.woff);
}


.langu-badge {
    background-color: $langu_primary;
    color: $langu_gold;
    font-weight: 900;
}

.lower {
    text-transform: lowercase;
}

.flash-container {
    position: fixed;
    max-width: 50vw;
    left: 50%;
    top: 50%;
    z-index: 999999;
    @include transform(translate(-50%, -50%));
    @include transition(opacity 0.6s ease-in);
    @include opacity(0);

    &:empty {
        display: none;
    }

    .alert {
        &.alert-grey {
            background: $langu_primary;
            color: #ffffff;

            .title {
                color: $langu_gold;
            }
        }

        li {
            line-height: 1.15;
        }

        .close {
            font-size: 1.5em;
            position: absolute;
            top: 0;
            right: 0.2em;
            line-height: 1;
            @include opacity(1);
        }

        .ok-button {
            display: block;
            margin: 1em auto 0;
            min-width: 25%;
            @include transform(translateX(12.5px));
        }

        a {
            text-decoration: underline;
        }
    }
}

.media_resource .media_resource-name {
    text-align: center;
    word-break: break-all;
    padding: 10px;
}

#library-dropzone {
    position: relative;
    width: 100%;
    border: 1px solid #08468a;
    padding: 40px 0;
    text-align: center;
    cursor: pointer;
}

#library-dropzone #library-dropzone-label {
    display: inline-block;
    @include no-select();
}

.offer-list-item {
    margin: 10px 0;
}

.no-overflow {
    overflow: hidden;
}

.block-msg {
    border: none;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0.125em;
    color: $langu_primary;
    padding: 0.125em 0.188em;
    cursor: wait;
    text-align: center;
    z-index: 1101;
    font-size: 2em;
}

.page-subtitle {
    text-transform: uppercase;
    margin: 0;
    font-size: 1.688em;
    line-height: 1.148em;
    font-weight: 700;
    padding: 26px 0;
    color: inherit;
}

.no-margin {
    margin: 0;
}

.form-feedback {
    margin: 1em 0 2em;

    .title {
        text-transform: uppercase;
        font-size: 2em;
        margin-top: 0.5em;
    }

    label {
        font-weight: 700;
    }

    .select-wrapper {
        border-bottom: 1px solid $langu_primary;
    }

    .autosize {
        resize: none;
        max-height: 12em;
        width: 100%;
        box-shadow: none;
        -webkit-box-shadow: none;
        background: none;
        margin: 0.5em 0;
        border-bottom: 1px solid $langu_primary;
        line-height: 1.5;
    }
}

#content {
    width: 100%;
    min-height: calc(100vh - 279px);
}

.gu-mirror {
    position: fixed !important;
    margin: 0 !important;
    z-index: 9999 !important;
    opacity: 0.8;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
    filter: alpha(opacity=80);
}
.gu-hide {
    display: none !important;
}
.gu-unselectable {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
}
.gu-transit {
    opacity: 0.2;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
    filter: alpha(opacity=20);
}

.activity-indicator {
    width: 2.5em;
    height: 2.5em;
    background-color: #444;
    background-color: rgba( 0, 0, 0, .5 );
    position: absolute;
    z-index: 100003;
    top: 50%;
    left: 50%;
    padding: 0.625em;
    margin: -1.25em 0 0 -1.25em;
    @include border-radius(50%);
    @include box-shadow(0 0 2.5em rgba(0, 0, 0, 0.75));

    div {
        width: 1.25em;
        height: 1.25em;
        background-color: $langu_gold;
        @include border-radius(50%);        
        @include animation(activity-indicator-spin .5s ease infinite);
    }
}

.buzz {
    text-align: center;
    padding: 0.5em 25%;

    &-title {
        font-size: 1.75em;
        color: $langu_gold;
        font-weight: 700;
        margin: 0;
    }

    &-message {
        line-height: 1.15;
        font-size: 1.25em;
        margin: 0 0 0.5em;
    }

    &-picture {
        max-width: 12em;
        max-height: 12em;
        margin: 0.75em auto;
        background-color: rgba(243,246,248,.94);
        border: 4px solid #fff;
        @include box-shadow(inset 0 1.5px 3px 0 rgba(0,0,0,.15), 0 1.5px 3px 0 rgba(0,0,0,.15));
    }
}

.text-bold {
    font-weight: bold;
}

.online-status {
    & > .status-indicator {
        width: 0.8em;
        height: 0.8em;
        margin-right: 0.1em;
        @include border-radius(99%);
        display: inline-block;
    }
    
    & > .status-text {
        display: none;
        color: #000000;
    }
    
    &.online { 
        & > .status-indicator {
            background-color: $langu_green;
        }
        
        & > .status-text.online {
            display: initial;
        }
    }
    
    &.offline { 
        & > .status-indicator {
            background-color: #909090;
        }
        
        & > .status-text.offline {
            display: initial;
        }
    }
    
    &.idle { 
        & > .status-indicator {
            background-color: $langu_gold;
        }
        
        & > .status-text.idle {
            display: initial;
        }
    }
    
    &.disabled {
        display: none;
    }
}

@import "common/common";
@import "starRating";
@import "menu";
@import "footer";
@import "home";
@import "login";
@import "buttons";
@import "links";
@import "sidebar";
@import "teacherListing";
@import "dashboard";
@import "availability";
@import "messages";
@import "userProfile";
@import "userSettings";
@import "library";  
@import "classroom";
@import "payments";
@import "purchases";
@import "reviews";
@import "invoice";
@import "terms";
@import "faq";
@import "pagination";
@import "blog";

@import "modal";
@import "forms/forms";
@import "widgets/widgets";
@import "animations/animations";
@import "errors";
@import "overlays/overlays";
@import "seo";
@import "aboutUs";
@import "methodology";
@import "timezone";
@import "business";