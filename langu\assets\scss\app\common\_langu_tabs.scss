.langu-tabs {
    & > li {
        & > a, a:hover, a:focus {
            color: inherit;
            @include box-shadow(inset 0 -1px 0 $langu_primary);
            outline: none;
        }
        
        & > a:focus:hover {
            color: $langu_gold;
        }

        &.active {

            & > a, a:hover, a:focus, a:focus:hover {
                color: $langu_gold;
                @include box-shadow(inset 0 -2px 0 $langu_gold); 
                outline: none;               
            }
        }
    }
}

.langu-tab-content {
    .tab-pane {
        padding: 1em 0;
    }
}