input, input[type="text"], input[type="password"], input[type="email"], textarea, select {
    outline: none;
    box-shadow: none;
        
    &:focus, &:active, &:hover {    
        outline: none;
        box-shadow: none;
    }
}

fieldset {
    legend {
        margin-bottom: 5px;
        border: none;
        box-shadow: none;
        font-size: 1em;
    }
}

.star-rating-input {
    position: relative;
    display: inline-block;
    color: $langu_gold;
    white-space: nowrap;
    unicode-bidi: bidi-override;
    direction: rtl;
    
    .star {
        font-family: FontAwesome;
        line-height: 1;
        font-size: inherit;
        font-weight: normal;
        padding: 0 2px;
        cursor:pointer;
        @include no-select();
    }
    
    & > .star {
        &:before {
            content: '\f006';
        }
        
        &:hover, &.selected {
            & ~ .star {
                &:before {
                   content: '\f005';
                }   
            }
            
            &:before {
                content: '\f005';
            }
        }
    }
}

input[type="text"], input[type="password"], input[type="email"] {
    width: 100%;
}

input[type="checkbox"], input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 1px;
    height: 1px;
    margin-top: 17px;
    margin-left: 4px;

    & + label {
        position: relative;
        padding-left: 1.3em;
        
        &.disabled {
            cursor: not-allowed;
            @include opacity(0.6);
            pointer-events: none;
        }
        
        &.light {
            &:before {
                background-color: #e8e8e8;
            }
        }
        
        &:before, &:after {
            content: '';    
            height: 0.8em;
            width: 0.8em;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 50%;
            @include transform(translateY(-50%));
            @include transition(transform 0.5s);
        }

        &:before {
            background-color: #383838;
        }

        &:after {
            background-color: $langu_gold;
            @include transform(translateY(-50%) scale(0));
        }
        
        &.checkbox-top {
            &:before, &:after {
                top: 0;
            }
            
            &:before {
                @include transform(translateY(25%));
            }
            
            &:after {
                @include transform(translateY(25%) scale(0));
            }
        }
        
        &.no-indicator {
            padding-left: 0;
            
            &:before, &:after {
                display: none;
            }
        }
    }

    &:checked {
        & + label {
            &:after {   
                @include transform(translateY(-50%) scale(1));
            }
            
            &.checkbox-top {
                &:after {
                    @include transform(translateY(25%) scale(1));
                }
            }
        }
    }
}

input[type="radio"] {
    & + label {
        &:before, &:after {
            border-radius: 99%;
        }
    }
}

.select-wrapper {
    position: relative;
    
    &:after {
        content: '';
        position: absolute;
        top: 50%;
        @include transform(translateY(-50%));
        right: .5em;
        box-sizing: border-box;
        display: block;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 4px;
        border-color: transparent;
        border-top-color: inherit;
        border-bottom-width: 0;
    }

    & > select {
        background: none;
        border: none;
        box-shadow: none;
        width: 100%;
        padding: 0.188em 0;
    }
}

label {
    &.no-wrap {
        white-space: nowrap;
        overflow-x: visible;
    }
}

.form-control {
    height: 2.846em;
}

.selectize-input {
    font-size: 1em;
    
    input {
        font-size: 1em;
    }
}

.selectize-dropdown {
    font-size: 1em;
}

.autosize {
    resize: none;
    display: block;
}

.char-counter {
    &-display {
        margin: 0 0 0.5em 1.5em;
        float: right;
        display: block;
        font-size: 0.875em;
    }
    
    &-hint {
        float: left;
        margin: 0 0.5em 0.5em 0;
        font-size: 0.875em;
    }
}
