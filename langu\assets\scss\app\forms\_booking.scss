.booking-form {
    @include clear-after();
    counter-reset: booking-heading;
    
    h2 {
        margin-top: 0;
        text-transform: uppercase;
    }
    
    .mangopay-payment-form, .web-payment-method-form, .payment-method-form {
        counter-increment: booking-heading 4;
    }
    
    .mangopay-trial-finish-form, .trial-confirm-form {
        counter-increment: booking-heading 3;
    }
    
    .help-block {
        @include clear-before();
        color: $langu_gold;
        line-height: 1.2;
        
        ul {
            list-style: none;
            padding-left: 0;
        }
    }
    
    .label-alike {
        font-size: 0.875em;
        line-height: 1;
        font-weight: 700;
    }
    
    .amount-to-pay {
        text-transform: uppercase;
        margin-bottom: 0;
    }
    
    .form-label-helper {
        font-weight: 600;
    }
    
    .form-container {
        font-weight: 700;
                
        .form-container-label {        
            font-weight: 700;
            counter-increment: booking-heading;
            
            &:before {
                color: $langu_gold;
                content: counter(booking-heading) '. ';
            }
        }
        
        .form-container-fields {
            * {
                font-weight: 700;
            }
            
            .char-counter {
                &-hint {
                    font-weight: 400;
                    font-style: italic;
                }
            }

            .char-count {
                font-size: 0.875em;
                float: right;
                font-weight: 400;
                margin: 0 .5em .5em 0;
            }
            
            @include clear-after();
            
            border-bottom: 1px solid #141414;
            margin-bottom: 0.938em;
            
            &-label {
                font-weight: 300;
                font-size: 0.875em;
                margin-top: 1.125em;
                margin-bottom: 0;
                padding: 0;
            }
            
            input[type="radio"], input[type="checkbox"] {
                & + label {
                    font-size: 0.875em;
                }
            }
            
            &.booking-selection-form-services {
                input[type="radio"] {
                    & + label {
                        &:before, &:after {
                            top: 0.5em;
                        }
                        
                        .main {
                            text-transform: uppercase;
                            display: block;
                        }
                        
                        .helper {
                            margin: 0.5em 0;
                            font-weight: 400;
                            display: inline-block;
                        }
                    }
                }
            }
        }
        
        .form-container-input {
            background: #444444;
            border-bottom: #6c6c6c solid 1px;
            font-size: 0.875em;
            font-weight: 400;
            line-height: 1.15;
            min-height: 80px;
            width: 100%;
            box-shadow: none;
        }
        
        .select-wrapper {
            float: left;
            width: 100%;
            margin-bottom: 0.250em;
            border-bottom: 1px solid #6c6c6c;
            
            @media (min-width: 768px) {
                width: 50%;
                margin-bottom: 0;
            }
            
            & > select {
                font-weight: 400;
                text-align: right;
                padding-right: 1.500em;
                font-size: 0.875em;
            }
        }
    }
    
    .back-button {
        color: white;
        text-transform: lowercase;
        display: block;
        text-align: right;
        clear: both;
        font-size: 0.813em;
        padding-top: 0.5em;
    }
    
    .img-mp {
        margin: 0.625em 0;
    }
}
