.full-search-form {
    .form-container-label {
        cursor: pointer;
    }
}

.full-search-form, .booking-form {
    color: #ffffff;

    h2 {
        font-size: 1.688em;
        font-weight: 700;
        color: inherit;
    }

    .form-container-label {
        padding-top: 0.938em;
        padding-bottom: 0.938em;
        text-transform: uppercase;
        line-height: 1;
        color: inherit;
        font-size: 0.875em;
        font-weight: 900;
        width: 100%;
        display: block;
        margin: 0;
    }

    .form-label-helper {
        font-size: 0.875em;
        color: inherit;
        font-weight: 400;
        line-height: 1.15;
    }
    
    .form-important {
        font-size: 0.875em;
        color: inherit;
        font-weight: 600;
        line-height: 1.15;
        text-align: right;
        float: right;
        width: 75%;
        margin: 1em 0;
        
        & > .emp {
            color: $langu_gold;
            text-transform: uppercase;
        }
    }
    
    .form-container-fields {
        width: 100%;
        clear: both;
        padding-bottom: 0.938em;

        input[type="checkbox"], input[type="radio"] {
            & + label {
                line-height: 1;
            }
        }
    }
}
