.standalone-form {
    &-wrapper {
        background: #f8f8f8;
        box-shadow: 0 2px 4px 1px #ccc;
        padding: 0.938em;
        border-radius: 3px;
        margin: 2em auto;
    }

    &-title {
        font-size: 1.125em;
        line-height: 1.111;
        margin: 0 0 0.556em;
        font-weight: bold;
        padding-bottom: 0.556em;
        border-bottom: solid 4px $langu_gold;
        color: inherit;
    }

    &-form {
        @include clear-after();

        .selectize-standalone {
            &.selectize-control, &.selectize-control.single {
                .selectize-input {
                    border: none;
                    border-bottom: 1px solid #aaa;
                    background: none;
                    @include border-radius(0);
                    @include box-shadow(none);
                }

                .selectize-dropdown {
                    border-left: none;
                    border-right: none;

                    &-content {
                        .option {
                            &:hover, &.active {
                                background: inherit;
                                color: $langu_gold;
                            }
                        }
                    }
                }
            }
        }
        
        .submit-button-container {
            margin-top: 1em;
            text-align: right;
        }

        .field-container {
            &-label {
                font-weight: 700;
                
                &.extra-top {
                    margin-top: 12px;
                }
            }

            &-hint {
                font-size: 0.75em;
                line-height: 1.15;
                color: #aaa;
            }

            input[type="text"], input[type="email"], input[type="password"], textarea {
                padding: 0.25em 0.5em;
                background: none;
                border: none;
                border-bottom: 1px solid #aaa;
            }
            
            textarea {
                width: 100%;
                display: block;
                
                &.autosize {
                    resize: none;
                }
            }

            input[type="checkbox"], input[type="radio"] {
                & + label {
                    font-size: 0.875em;

                    &:before {
                        background-color: #ccc;
                    }
                }
            }            
        }

        .field-inline-list {
            display: flex;
            width: 100%;
            text-align: justify;
            margin: 0;

            &-item {
                flex: 1;

                & + .field-inline-list-item {
                    margin-left: 0.25em;
                }
            }
        }

        .field-inline-wrapper {
            display: inline-block;

            & + .field-inline-wrapper {
                margin-left: 0.25em;
            }
        }
    }
}
