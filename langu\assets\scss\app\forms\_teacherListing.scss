.full-search-form {

    h2 {
        margin: 0 0.25rem 2rem;
    }

    .form-container {

        border-top: 1px solid #ffffff;
        padding: 0.85em 0.85em 0.85em 0.25em;

        &:last-of-type {
            border-bottom: 1px solid #ffffff;
        }

        &-label {
            position: relative;
            @include transition(0.3s);

            &:before {
                content: '';
                position: absolute;
                top: 50%;
                right: 0;
                display: block;
                width: 1.7143em;
                height: 2px;
                background: #ffffff;
                @include transition(0.3s);
                @include transform(translateY(-50%));
            }

            &:after {
                content: '';
                position: absolute;
                top: 50%;
                right: -1px;
                display: block;
                width: 2px;
                height: 1.7143em;
                background: #ffffff;
                @include transform(translate(-0.8571em, -50%));
                @include transition(0.3s);
            }
        }

        .form-container-fields {
            display: none;
            padding-bottom: 0;

            select, input[type="text"] {
                color: #ffffff;
            }

            .or-text {
                font-size: 0.875em;
                text-transform: uppercase;
            }

            input[type="checkbox"], input[type="radio"] {
                display: none;

                & + label {
                    &:hover {
                        text-decoration: underline;
                    }
                }

                &:checked {
                    & + label {
                        color: $langu_gold;
                    }
                }
            }

            .selectize {
                &.no-dropdown {
                    &.selectize-control {
                        .selectize-input {
                            &:after {
                                content: none;
                            }
                        }

                        .selectize-dropdown {
                            display: none;

                        }
                    }
                }

                &.selectize-control {
                    line-height: 1.875em;
                    color: #ffffff;

                    .selectize-input {
                        border-radius: 0;
                        border: none;
                        box-shadow: none;
                        background: none;
                        color: inherit;

                        &.dropdown-active {                            
                            &:before {
                                display: none;
                            }

                            &:after {
                                border-color: transparent transparent #ffffff;
                            }
                        }

                        &:after {
                            border-color: #ffffff transparent transparent;
                        }

                        input {
                            color: inherit;
                        }
                    }

                    .selectize-dropdown {
                        position: static;
                        border: none;
                        border-radius: 0;
                        box-shadow: none;
                        color: inherit;
                        background: none;

                        .option.active {
                            color: $langu_gold;
                            background: none;
                        }
                    }

                    &.multi {
                        .selectize-input {
                            .item {
                                border: none;
                                background: none;
                                box-shadow: none;
                                border-radius: 0;
                            }
                        }
                    }

                    &.single {
                        line-height: 1.875em;
                        color: #ffffff;
                    }
                }
            }
            
            .speciality-radio-buttons {
                display: flex;
                flex-wrap: wrap;
                
                .radio-button-container {
                    @media (min-width: 768px) {
                        width: 50%;
                        display: block;
                        line-height: 1;
                        margin: 0.075em 0;
                    }
                    
                    label {
                        margin: 0.1em 0;
                    }
                }
            }

            .motivation-radio-buttons {
                display: flex;
                justify-content: space-between;

/*                :first-child {
                    input[type="checkbox"], input[type="radio"] {
                        &:checked + label {
                            svg {
                                path {
                                    fill: transparent;
                                    stroke: $langu_gold;
                                }
                            }
                        }
                    }
                }*/
                
                input[type="checkbox"], input[type="radio"] {
                    &:checked + label {
                        svg {
                            path {
                                fill: $langu_gold;
                            }
                        }
                    }
                }

                label {
                    margin-bottom: 0;
                    line-height: 1;

                    svg {
                        max-height: 1em;
                        max-width: 1em;
                        margin-right: 0.1em;
                        vertical-align: bottom;
                    }
                }
            }
        }

        &.active {
            .form-container-fields {
                display: block;
            }

            .form-container-label {
                color: $langu_gold;

                &:before {
                    background: $langu_gold;
                }

                &:after {
                    background: $langu_gold;
                    height: 2px;
                }
            }
        }
    }

    .submit {
        margin-top: 2.188rem;
        margin-bottom: 2.188rem;
        margin-left: 0.625rem;
        padding-left: 2.188rem;
        padding-right: 2.188rem;
    }
}