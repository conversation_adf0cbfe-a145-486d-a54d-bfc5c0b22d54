.content {
    .button-container {
        margin-bottom: 4rem;
    }
    
    .gif-box {
        text-align: center;
        margin: 0 0 6rem;
        
        img {
            max-width: 100%;
            
            @media (min-width: 768px) {
                max-width: 55%;
            }
        }
    }
    
    .boxes {
        counter-reset: boxes;
        
        .box {
            position: relative;
            counter-increment: boxes;
            background-color: #eeeeee;
            padding: 1.5rem 1rem;
            margin: 1rem 2rem;
            
            @media (min-width: 480px) {
                margin: 0 0.75rem;
            }
            
            &:before {
                content: counter(boxes) '.';
                display: flex;
                align-items: center;
                justify-content: center;
                position: absolute;
                top: 0;
                left: 0;
                width: 3em;
                height: 3em;
                background-color: $langu_gold;
                color: #ffffff;
                font-weight: bold;
                border: 0.15em solid #ffffff;
                box-sizing: content-box;
                
                @include border-radius(99%);
                @include transform(translate(-25%, -25%));
            }
            
            .icon {
                margin-bottom: 1.5rem;
            }
            
            .headline {
                font-size: 1.3em;
                text-align: left;
            }
            
            .text {
                margin-top: 0.5em;
                text-align: left;
            }
        }
    }
        
    .section {
        
        &-how-it-works {
            &.en {
                .headline {
                    .regular {
                        padding-right: 0.85em;
                    }
                    
                    .super {
                        left: -2.5em;
                    }
                }
            }
            
            &.pl {
                .headline {
                    .super {
                        left: 0;
                        top: -0.5em;
                    }
                }
            }
        }
        
        &-philosophy {
            margin-bottom: 4rem;
            
            &.en {
                .headline {
                    .regular {
                        padding-right: 0.5em;
                    }
                    
                    .super {
                        left: -1.8em;
                        top: -0.7em;
                    }
                }
            }
        }
    }
}
