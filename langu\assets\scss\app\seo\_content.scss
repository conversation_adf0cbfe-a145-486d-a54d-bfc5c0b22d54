.content {
    background-color: #ffffff;
    padding: 3em 0;

    .button-container {
        margin: 0;
        text-align: center;
    }

    .how-it-works {
        &-top {
            position: relative;
            margin: 1.2em auto;
            text-align: right;
            width: 3.279em;
            color: #2d2d2d;
            font-size: 3.813em;
            line-height: .41;
            font-weight: 700;

            &:after {
                content: '';
                position: absolute;
                left: 0;
                bottom: -0.328em;
                width: 3.279em;
                height: 4px;
                background-color: $langu_gold;
            }

            .subtitle {
                display: block;
                color: $langu_gold;
                font-size: 0.344em;
                line-height: 1.143em;
                text-align: left;
                text-transform: uppercase;
            }

            /*            
                        line-height: 1.846;
                        color: $langu_primary;
                        padding: 1em 0;
                        font-size: 2em;
                        position: relative;
                        text-align: center;
                        width: 60%;
                        margin: 0 auto;
            
                        &:after {
                            content: '';
                            position: relative;
                            display: block;
                            width: 100%;
                            height: 1px;
                            background: $langu_primary;
                        }*/
        }

        .steps {
            line-height: 1.15;

            @media (max-width: 599px) {
                margin: 3em 10em 5em;
            }

            @media (max-width: 479px) {
                margin: 3em 3em 5em 4em;
            }
        }
    }

    .features {
        padding: 0 2em;

        @media (min-width: 480px) {
            padding: 0 8em;
        }

        @media (min-width: 768px) {
            padding: 0 17.5em;
        }

        & > .feature {
            display: flex;
            margin-bottom: 3em;

            .icon {
                flex: 0 0 auto;
                width: 4em;
                margin-right: 1em;

                img {
                    width: 100%;
                }
            }

            .text {
                flex: 1 1 auto;
                color: $langu_primary;

                .title {
                    color: inherit;
                    font-size: 1.75em;
                    margin: 0 0 0.5em;
                }

                .desc {
                    font-size: 1.2em;
                    color: inherit;
                    margin: 0;
                    line-height: 1.15;

                    @import "content/markdown";
                }
            }
        }
    }    
}
