p {
    margin: 0;
}

ul, ol {
    padding: 0;
    margin: 0;
    line-height: 1.15;
    list-style: none;
}

$bullet-width: 0.75em;

ul {
    li {
        padding: 0 0 0 $bullet-width;
        
        & + li {
            padding: 0.5em 0 0 $bullet-width;
        }
        
        &:before {
            content: '\2022';
                width: $bullet-width;
                display: inline-block;
                margin-left: -$bullet-width;
        }
    } 
}

$ordered-list-gutter: 0.5em;

ol {
    counter-reset: markdown-ol;
    
    li {
        counter-increment: markdown-ol;
        
        & + li {
            padding: 0.5em 0 0 0;
        }
        
        &:first-line {
            font-weight: bold;
        }
        
        &:before {
            content: counter(markdown-ol) '. ';
            font-weight: bold;
        }        
    }
    
    @media (min-width: 992px) {
        display: flex;
        margin: 0 (-$ordered-list-gutter);
        
        li, li + li {
            flex: 1 1 0;
            padding: 0 $ordered-list-gutter;
        }
    }

}
