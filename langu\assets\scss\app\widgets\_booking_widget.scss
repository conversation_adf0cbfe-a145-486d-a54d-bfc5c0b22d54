.langu-modal {
    .slots-picker {
        width: calc(14.875em + 60px);
    }
}

.slots-picker {
    .title {
        color: inherit;  
        font-size: inherit;
        padding: 0.5em 0;
        margin: 0;
        line-height: 1.15;
    }
    
    & &-page {
        &:not(.active) {
            display: none;
        }  
    }

    .flex-row {
        display: flex;
        flex-wrap: nowrap;

        &-column {
            flex: 1 1 40px;
        }
    }

    .header-row {
        align-items: flex-end;
        line-height: 1.15;
        margin-bottom: 1em;

        &-column {
            @include no-select();

            font-size: 0.750em;
            line-height: 1;
            text-align: center;
            text-transform: uppercase;
            font-weight: 400;
            display: inline-block;
            
            .emp {
                font-weight: 700;
                display: block;
            }
        }

        .pagination {
            position: relative;
            display: flex;
            width: 100%;
            margin: 0 -1px;
            align-items: flex-end;
            
            .calendar-page-switcher {
                padding: 0 1px;
                margin: 5px 0 0;
                background: none;
                border: none;

                flex: 1.75;
                
                &[data-dir="prev"] {
                    flex: 1;
                    margin-bottom: 1px;
                }
                
                img {
                    max-width: 100%;
                }
            }
            
/*            li {
                float: left;
                height: 100%;
                display: inline;
                
                a {
                    padding: 0;
                    margin: 0;
                    background: none;
                    height: 100%;
                    border: none;
                    display: inline;
                    width: 66%;
                    
                    img {
                        max-width: 100%;
                    }
                    
                    &[data-dir="prev"] {
                        width: 33%;
                    }
                }
            }*/
        }
    }

    .arrow-row {
        font-size: 2.5em;
        line-height: 1;
        text-align: center;

        a {
            color: #ffffff;
            display: inline-block;
            @include no-select();
        }
    }

    .calendar-row {
        color: #fff;    
        max-height: calc(45em + 43px);
        overflow: hidden;

        &-inner {
            margin-top: calc(-21em - 21px);
            @include transition(margin-top 0.1s ease-in-out);
            
            & > * {
                & > * {       
                    background: #020202;
                    margin-right: 1px;
                }
            }

            &-column {
                &:last-child {
                    margin-right: 0;
                }

                & > * {
                    margin-bottom: 1px;
                }
            }

            &-slots-column {
                flex: 0 0 calc(14.875em + 7px);
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;
                max-height: calc(72em + 70px);

                & > * {
                    height: 1.5em;
                    width: 2.125em;
                    padding: 0;

                    &:nth-child(even) {
                        margin-bottom: 2px;
                    }

                    &:nth-child(48n) {
                        margin-bottom: 0;
                    }

                    .slot-label {
                        display: block;
                        width: 100%;
                        height: 100%;
                        padding: 0;
                        margin: 0;
                        background:  #999999;

                        &:before, &:after {
                            content: none;
                        }

                        &.occupied {
                            background-color: #636363;    
                        }

                        &.free {
                            background:  $langu_green;

                            &.active {
                                background: #fff repeating-linear-gradient(
                                    45deg,
                                    rgba($langu_gold, 0.6),
                                    rgba($langu_gold, 0.6) 5px,
                                    $langu_gold 5px,
                                    $langu_gold 10px
                                    );
                            }
                        }
                    }

                    .slot-picker-checkbox:checked + .slot-label {
                        background: $langu_gold;
                    }
                }
            }

            &-time-column {
                & > * {
                    height: calc(3em + 1px);
                    @include no-select();
                    text-align: right;
                    padding: 0 5px;
                    vertical-align: top;

                    &:not(:last-child) {
                        margin-bottom: 2px;                
                    }

                    span {        
                        font-size: .875em;
                    }
                }
            }
        }
    }
}
