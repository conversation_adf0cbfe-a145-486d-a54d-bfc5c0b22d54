.calendar-month {
    border: none;
    table-layout: fixed;
    float: right;
    display: inline-block;
    padding-top: 1em;
    padding-bottom: 1em;
    border-bottom: #141414 solid 1px;
    
    &:first-of-type {
        padding-top: 0;
    }
    
    &:only-of-type {
        border-bottom: none;
    }
    
    tr, th, td {
        text-align: center;
    }
    
    .calendar-day {
        border-radius: 99%;
        width: 2.500em;
        height: 2.500em;
        margin: 2px;
        line-height: 1.875em;
        padding: 0.313em;
        display: block;
        background: $langu_primary;
        color: #fefefe;
        font-weight: 700;
        @include no-select();
        cursor: default;
     
        @at-root #{selector-unify(&, a)} {
            cursor: pointer;
        }
        
        &.free {
            color: $langu_green;
        }
        
        &.occupied {
            color: $langu_gold;
        }
        
        &.past {
            color: $langu_past;
        }
        
        &.today:not(.current) {
            background: white;
            color: $langu_primary;
        }
        
        &.current {
            color: $langu_primary;
            background: $langu_gold;
        }
    }
}
