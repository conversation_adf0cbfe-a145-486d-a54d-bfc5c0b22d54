@mixin clear-after() {
    &:after {
        display: block;
        clear: both;
        content: '';
    }
}

@mixin clear-before() {
    &:before {
        display: block;
        clear: both;
        content: '';
    }
}

@mixin text-shadow($shadow)
{
    text-shadow: $shadow;
}

@mixin bullet($size) {
    width: $size;
    height: $size;
    @include border-radius(99%);
}

@mixin abs-vert-center() {
    top: 50%;
    @include transform(translateY(-50%));
}

@mixin flex-row-justified() {
    display: -webkit-flex; /* Safari */
    -webkit-align-items: center; /* Safari 7.0+ */
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@mixin box-shadow($args...) {
    -webkit-box-shadow: $args;
    box-shadow: $args;
}

@mixin border-radius($radius) {
    -webkit-border-radius: $radius;
    -moz-border-radius: $radius;
    border-radius: $radius;
}

@mixin transition($args...) {
  -webkit-transition: $args;
  -moz-transition: $args;
  -ms-transition: $args;
  -o-transition: $args;
  transition: $args;
}

@mixin opacity($opacity) {
  opacity: $opacity;
  $opacity-ie: $opacity * 100;
  filter: alpha(opacity=$opacity-ie); //IE8
}

@mixin input-placeholder {
    &.placeholder { @content; }
    &:-moz-placeholder { @content; }
    &::-moz-placeholder { @content; }
    &:-ms-input-placeholder { @content; }
    &::-webkit-input-placeholder { @content; }
}

@mixin transform($transforms) {
    -moz-transform: $transforms;
    -o-transform: $transforms;
    -ms-transform: $transforms;
    -webkit-transform: $transforms;
    transform: $transforms;
}

@mixin keyframes($animation-name) {
    @-webkit-keyframes #{$animation-name} {
        @content;
    }
    @-moz-keyframes #{$animation-name} {
        @content;
    }  
    @-ms-keyframes #{$animation-name} {
        @content;
    }
    @-o-keyframes #{$animation-name} {
        @content;
    }  
    @keyframes #{$animation-name} {
        @content;
    }
}

@mixin animation($str) {
  -webkit-animation: #{$str};
  -moz-animation: #{$str};
  -ms-animation: #{$str};
  -o-animation: #{$str};
  animation: #{$str};      
}

@mixin no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

@mixin clip-path($clipPath) {
    -webkit-clip-path: $clipPath;
    clip-path: $clipPath;
}

@mixin placeholder {
    &::-webkit-input-placeholder { @content; }
    &:-moz-placeholder           { @content; }
    &::-moz-placeholder          { @content; }
    &:-ms-input-placeholder      { @content; }
}