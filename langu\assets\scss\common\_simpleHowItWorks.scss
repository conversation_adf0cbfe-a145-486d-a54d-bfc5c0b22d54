.how-it-works {
    background-color: #ffffff;
    
    .steps {
        position: relative;
        counter-reset: steps;
        list-style: none;
        margin: 3em 20% 5em;
        padding: 0;
        display: block;

        @media (min-width: 768px) {
            display: flex;
            justify-content: space-between;
            padding: 0 3em;
            margin: 3em 10% 5em;
        }

        &-item {
            margin-bottom: 0.75em;
            padding: 0;
            counter-increment: steps;
            display: block;

            @media (min-width: 768px) {
                width: 30%;
            }

            & > * {
                margin: 0;
            }

            &:before {
                content: counter(steps) '.';
                font-size: 1.25em;
                font-weight: 700;
                @include transform(translateX(-150%));
                display: inline-block;
                position: relative;
            }

            $icon-height: 4em;

            &--icon {
                min-height: $icon-height;
                vertical-align: bottom;
                margin-bottom: 1.5em;

                & > img {
                    vertical-align: bottom;
                    height: $icon-height;
                }
            }

            &--title {
                font-size: 1.25em;
                font-weight: 700;
                margin: 0.3em 0;
                color: inherit;
            }

            &--text {
                line-height: 1.15;
                color: inherit;
            }
        }
    }
}
