@charset "UTF-8";

/*@import "fa/variables";
@import "fa/path";*/

@import "../common/variables";
@import "../common/fonts";
@import "../common/mixins";

html {
    font-size: 0.6em;
    min-height: 100%;

    @media (min-width: 768px) {
        font-size: 0.7em;
    }

    @media (min-width: 992px) {
        font-size: 0.8em;
    }

    @media (min-width: 1200px) {
        font-size: 1em;
    }
}

body {
    font-family: 'Lato',sans-serif;
    font-size: 100%;
    padding: 0;
    margin: 0;
    background: #ffffff;    
    overflow-y: auto;
    line-height: 1.15;
    color: #000000;
}

h1, h2, h3 {
    color: inherit;
}

a {
    cursor: pointer;
    color: inherit;
}

.header {

    display: flex;
    align-items: center;
    padding: 3em 0 2em;
    
    @media(max-width: 767px) {
        margin-left: 10%;
    }
    
    @media (max-width: 1023px) {
        display: block;
    }
    
    &-logo {
        @media (min-width: 1024px) {
            margin-top: 0.25em;
        }
    }

    &-left {
        display: flex;
        align-items: center;
        margin-left: 3.3125em;
        
        @media (max-width: 767px) {
            display: block;
            text-align: center;
            margin-left: 0;
        }
    }
    
    &-logo {
    }

    &-text {
        @media (max-width: 767px) {
            width: 100%;
            margin: 2em 0 0 0;
        }

        flex-grow: 1;
        margin: 0 4em 0 3.3125em;

        &--title {
            font-weight: 600;
            font-size: 2em;
            color: $langu_gold;
            margin-top: 0;
            margin-bottom: 0.15em;
            
            @media (min-width: 1024px) {
                margin-bottom: 0.3em;
                line-height: 1;
            }
        }

        &--description {
            font-weight: 300;
            font-size: 2em;
            margin: 0;
        }
    }

    &-button {
        display: block;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
        font-size: 1em;
        font-weight: 700;
        color: #ffffff;
            
        @media (max-width: 767px) {
            margin: 1.5em 0;
        }
        
        @media (min-width: 768px) and (max-width: 1023px) {
            margin: 1.5em 0 1.5em 10%;
        }
    }
}

.left-aligned {
    margin-left: 10%;
}

.right-aligned {
    margin-right: 10%;
}

.text-gold {
    color: $langu_gold;
}

.boxes-container {
    width: 100%;
    height: auto;
    background-size: 100% auto;
    background-repeat: no-repeat;
    position: relative;

    @media (min-width: 1024px) {
        height: 0;
        padding-top: 43.375%;
    }

    
    &.ielts {
        @media (max-width: 480px) {
            background-image: url(../images/landing/ielts/ielts_student_480.jpg);
        }

        @media (max-width: 800px) {
            background-image: url(../images/landing/ielts/ielts_student_800.jpg);
        }

        @media (max-width: 1200px) {
            background-image: url(../images/landing/ielts/ielts_student_1200.jpg);
        }

        @media (max-width: 1600px) {
            background-image: url(../images/landing/ielts/ielts_student_1600.jpg);
        }

        @media (min-width: 1601px) {
            background-image: url(../images/landing/ielts/ielts_student.jpg);
        }
    }
    
    &.conversation {
        @media (max-width: 480px) {
            background-image: url(../images/landing/conversations/conversations1.jpg);
        }

        @media (max-width: 800px) {
            background-image: url(../images/landing/conversations/conversations1_800.jpg);
        }

        @media (max-width: 1200px) {
            background-image: url(../images/landing/conversations/conversations1_1200.jpg);
        }

        @media (max-width: 1600px) {
            background-image: url(../images/landing/conversations/conversations1_1600.jpg);
        }

        @media (min-width: 1601px) {
            background-image: url(../images/landing/conversations/conversations1.jpg);
        }
    }

    &--inner {
        position: relative;
        padding: 2em 0 4em;
        width: 18em;
        margin: 0 auto;
        z-index: 4;    

        @media (min-width: 1024px) {
            position: absolute;
            right: 10%;
            top: 2em;
            width: 18em;
            z-index: 4;    
            margin: 0 auto;
            padding: 0;
        }

        .boxes {
            position: relative;
            margin: 0;
            padding: 0;
            list-style: none;

            &-item {
                margin-bottom: 0.75em;
                padding: 1.5em;
                background-color: #e3e3e3;
                background-color: rgba(235, 235, 235, 0.83);
                @include border-radius(6px);

                & > * {
                    margin: 0;
                }

                $icon-height: 4em;

                &--icon {
                    min-height: $icon-height;
                    vertical-align: bottom;

                    & > img {
                        vertical-align: bottom;
                        max-width: 45%;
                        height: $icon-height;
                    }
                }

                &--title {
                    font-size: 1.25em;
                    font-weight: 700;
                    margin: 0.3em 0;
                }

                &--text {
                    line-height: 1.15;
                }
            }
        }
    }
}

.bottom-widget {
    position: relative;
    padding: 0 0 1em;
    background-color: #ffffff;

    @media (max-width: 1023px) {
        margin: 0 10%;
    }

    @media (min-width: 1024px) {
        padding: 4em 21% 2em 11%;
        margin-top: -5.5em;
    }

    &--title {
        font-size: 2em;
        font-weight: 600;
        line-height: 1.15;
        margin: 0;

        @media (min-width: 1024px) {
            width: 78%;
        }
    }

    &--columns {
        @media (min-width: 1200px) {
            display: flex;
            justify-content: space-between;
        }

        margin: 2em 0;
    }

    &--button {
        margin: 3em 0 5em;
        text-align: center;
        font-size: 1em;
        font-weight: 700;
        color: #ffffff;
    }
}

.column-3 {
    @media (max-width: 1999px) {
        width: 100%;
    }

    width: 31%;
}

.badge-panel {
    &--icon {
        min-height: 5em;
        vertical-align: bottom;

        & > img {
            vertical-align: bottom;
            max-width: 100%;
            height: 5em;
        }
    }

    &--title {
        line-height: 1;
        font-size: 2em;
        margin: 0.75em 0;
    }

    &--description {
        line-height: 1.15;
    }
}

@import "../common/simpleHowItWorks";
@import "../common/inGoodCompany";
@import "../common/ctaButtons";

@import "footer";
