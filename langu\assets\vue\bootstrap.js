const {createLogger, format, transports, config} = require('winston');

// @TODO: Remove this in the near future.
window.logger = global.logger = createLogger({
    levels: Object.assign({
        label: -1,
        meta: -2,
    }, config.npm.levels),
    transports: [
        new transports.Console({
            level: 'silly',
            format: format.combine(
                format.timestamp({
                    format: 'MM-DD-YYYY HH:mm (ZZ)',
                }),
                format.label(),
                format.metadata({fillExcept: ['message', 'level', 'timestamp', 'label']}),
                format.splat(),
                format.printf((info) => {
                    console.log('INFO:', info);
                    return 'String';
                }),
                // format.printf(({level, message, timestamp, metadata: {label, meta}}) => {
                //
                //
                //     let [, datetime, timezone] = /([\d-:\s]+)\s(\(.*\))/.exec(timestamp);
                //     console.log(datetime, timezone);
                //
                //     let format = util.format('[%s %c(%s)] %c%s <> %c%s %c(meta=%o)',
                //         // Timestamp:
                //         datetime, 'color: #999999', timezone,
                //         // Module name
                //         label ? 'font-weight: bold; background: #2f2d2d; padding: 0 5px; color: #fff;' : 'display: none', label,
                //         // Message
                //         'font-weight: bold; background: #5394EC; padding: 0 5px; color: #fff;', message,
                //         // Meta
                //         '', meta
                //     );
                //
                //     console.log(format);
                //     return format;
                // }),
            ),
        }),
    ],
});

/**
 * Log debug-level message
 *
 * @param label {String} prefix for logging output
 * @param message {String} log message
 * @param meta {Object} Extra data to include in logging output
 */
global.console.debug = (message, label = 'global', meta = {}) => {
    console.log('%c[%s]%c %c%s%c %c%s%c %o',
        // Timestamp:
        'color: #999999;', +new Date(), '',
        // Message
        'font-weight: bold; background: #5394EC; padding: 0 5px; color: #fff;', message, '',
        // Module name
        label ? 'font-weight: bold; background: #2f2d2d; padding: 0 5px; color: #fff;' : 'display: none', label, '',
        // Meta
        meta,
    );
};
