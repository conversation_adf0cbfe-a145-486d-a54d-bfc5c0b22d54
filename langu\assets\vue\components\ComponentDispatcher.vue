<template>
  <div>
    <keep-alive>
      <component
          v-for="component in activeComponents"
          v-bind:key="component.ref"
          v-bind:is="`component-${component.ref}`"
          v-bind:ref="component.ref"
          v-bind="component.properties"></component>
    </keep-alive>
  </div>
</template>

<script>
export default {
  data() {
    return {
      profiler: window.logger.startTimer(),
    }
  },
  mounted() {
    this.profiler.done({message: 'Vue dispatcher initialized.'});
  },
  computed: {
    activeComponents() {
      console.log(this.$store.getters.activeComponents)
      return this.$store.getters.activeComponents;
    }
  }
}
</script>

<style scoped>
</style>
