<template>
  <div id='summernote_wrapper'
       :style="{top: `${this.position.top}px`, left: `${this.position.left}px`, position: 'absolute'}">
    <textarea id='text_summernote' ref="textarea"></textarea>
  </div>
</template>

<script>
import {UPDATE_COMPONENT_DATA} from "../import/mutation-types";
import {PERMANENT_UPDATE} from "../import/update-types";

const diff = require('fast-diff');
const rangy = require('rangy');

export default {
  props: {
    text: {
      type: String,
      required: false,
      default: '<p>Here you can write your message.</p>',
    },
    position: {
      type: Object,
      default: () => {
        return {
          left: {
            type: Number,
            required: false,
            default: 100,
          },
          top: {
            type: Number,
            required: false,
            default: 100,
          },
        }
      },
    },
    size: {
      type: Object,
      default: () => {
        return {
          height: {
            type: Number,
            required: false,
            default: 400,
          },
          width: {
            type: Number,
            required: false,
            default: 450,
          },
        }
      },
    },
  },
  data() {
    return {
      // Internal vars
      _summernote: null,
      _noteEditor: null,
      _toolbarWrapperHeight: null,
      _noteEditorMarginBottom: null,
      _noteEditable: null,
      _selection: null,

      // Throttle protectors
      throttledResizeNoteEditor: null,
      throttledPositionNoteEditor: null,

      // State handlers
      isHovered: false,
      isFocused: false,
      range: null,

      // Internal methods
      _saveSelection: () => {},
      _restoreSelection: () => {},
    }
  },
  created() {
    this.throttledResizeNoteEditor = _.throttle(this.resizeNoteEditor, 300);
    this.throttledPositionNoteEditor = _.throttle(this.positionNoteEditor, 300);
  },
  mounted() {
    this._summernote = $(this.$refs.textarea);
    this.initSummernote();
    this.postInit();
    // this.enableRangy();
  },
  methods: {
    initSummernote() {
      this._summernote.summernote({
        height: this.size.height,
        width: this.size.width,
        focus: true,
        airMode: false,
        disableDragAndDrop: true,
        toolbar: [
          ['style', ['bold', 'underline', 'strikethrough', 'color']],
          ['insert', ['link', 'hr', 'picture', 'table']],
          ['para', ['ul', 'ol', 'paragraph']]
        ],
        popover: {
          air: [
            ['style', ['bold', 'underline', 'strikethrough', 'color']],
            ['insert', ['link', 'hr', 'picture', 'table']],
            ['para', ['ul', 'ol', 'paragraph']]
          ],
          image: [
            ['float', ['floatLeft', 'floatRight', 'floatNone']],
            ['remove', ['removeMedia']]
          ],
          link: [
            ['link', ['linkDialogShow', 'unlink']]
          ]
        },
        dialogsFade: true,
        dialogsInBody: true,
        disableResizeEditor: true,
        callbacks: {
          onInit: () => {
            this._noteEditor = $('.note-editor');
            this._toolbarWrapperHeight = $('.note-toolbar-wrapper').height();
            this._noteEditorMarginBottom = parseInt(this._noteEditor.css('marginBottom'));
            this._noteEditable = $('.note-editable');
          }
        },
      });
    },
    postInit() {
      this._noteEditable.html(this.text);

      this._noteEditor.on('mouseover', () => this.isHovered = true);
      this._noteEditor.on('mouseleave', () => this.isHovered = false);
      this._noteEditor.resizable({
        handles: 'e, s, se',
        create: () => {
          if (window.langu_role && (window.langu_role).toLowerCase() === 'teacher') {
            $(".note-editor .ui-resizable-e").css("cursor", "url('/images/classroom/cursor-teacher-right.svg') 30 0, auto");
            $(".note-editor .ui-resizable-s").css("cursor", "url('/images/classroom/cursor-teacher-down.svg') 0 30, auto");
            $(".note-editor .ui-resizable-se").css("cursor", " url('/images/classroom/teacher-arrow.svg'), auto");
          } else {
            $(".note-editor .ui-resizable-e").css("cursor", "url('/images/classroom/cursor-student-right.svg') 30 0, auto");
            $(".note-editor .ui-resizable-s").css("cursor", "url('/images/classroom/cursor-student-down.svg') 0 30, auto");
            $(".note-editor .ui-resizable-se").css("cursor", " url('/images/classroom/student-arrow.svg'), auto");
          }
        },
        aspectRatio: false,
        minHeight: 100,
        minWidth: 450,
        resize: (event, ui) => {
          this.throttledResizeNoteEditor(ui.size.height, ui.size.width);
        },
        stop: (event, ui) => {
          this.throttledResizeNoteEditor(ui.size.height, ui.size.width, PERMANENT_UPDATE);
        }
      });
      this._noteEditable.on('input', this.textChanged);
      this._noteEditable.on('click focus', () => {
        this.isFocused = true;
        // this.range = rangy.saveSelection(this._noteEditable.get(0)); // @TODO: What's next?
        this._summernote.summernote('saveRange');
      });
      this._noteEditable.on('blur', () => {
        this.isFocused = false;
        // this.range = null;
      });
      $('#summernote_wrapper').draggable({
        handle: 'div.note-toolbar-wrapper',
        drag: (event, ui) => {
          this.throttledPositionNoteEditor(ui.position.left, ui.position.top);
        },
        stop: (event, ui) => {
          this.throttledPositionNoteEditor(ui.position.left, ui.position.top);
        }
      });
    },
    /**
     * Resize note editor
     *
     * @param height
     * @param width
     * @param type {null|String} if set - will be dispatched emit to sockets
     */
    resizeNoteEditor(height, width, type = null) {
      dispatch(UPDATE_COMPONENT_DATA, type, this.$vnode.data.ref, 'properties.size', {
        height: height - this._toolbarWrapperHeight - this._noteEditorMarginBottom,
        width: width,
      });
    },
    /**
     * Set position of component and emit to sockets
     *
     * @param left
     * @param top
     */
    positionNoteEditor(left, top) {
      dispatch(UPDATE_COMPONENT_DATA, PERMANENT_UPDATE, this.$vnode.data.ref, 'properties.position', {
        left: left,
        top: top,
      });
    },
    /**
     * Handle text updates and dispatch them to sockets
     */
    textChanged() {
      this._summernote.summernote('saveRange');

      dispatch(
          UPDATE_COMPONENT_DATA,
          PERMANENT_UPDATE,
          this.$vnode.data.ref,
          'properties.text',
          this._noteEditable.html(),
      );
    },
    /**
     * Enables rangy and sets private setter and getter functions
     */
    enableRangy() {
      console.debug('Enabling multiple selection for IE to save cursor location.', 'Summernote');
      try {
        document.execCommand('MultipleSelection', null, true);
      } catch(exception) {
        logger.debug('Failed to enable multiple selection for IE', exception);
      }

      rangy.init();

      if (rangy.supported) {
        logger.debug('Rangy is supported, creating setter and getter for selections');

        /**
         * Save selection to variable
         *
         * @private
         */
        this._saveSelection = () => {
          this._selection = rangy.saveSelection();
          logger.debug('Selection has been saved.', {selection: this._selection});
        };

        logger.debug('Setter is ready');

        /**
         * Restore selection and (optional) set focus on element
         *
         * @param setFocus
         * @private
         */
        this._restoreSelection = (setFocus = false) => {
          if (this._selection) {
            rangy.restoreSelection(this._selection, true);
            logger.debug('Selection has been restored.', {selection: this._selection});
            this._selection = null;

            if (setFocus && !this.isFocused) {
              this._summernote.focus();
              logger.debug('Focus on element has been set.', {element: this._summernote})
            }
          }
        };
        logger.debug('Getter is ready');
      }
    },
  },
  watch: {
    isHovered: function (state) {
      this._noteEditor.css({outline: state ? `2px solid ${getHoverColor()}` : `none`});

      (new UpdateBuilder()).type(UpdateBuilder.TEMPORARY).name(
          state ? UpdateBuilder.TEXTBOX_MOUSEOVER : UpdateBuilder.TEXTBOX_MOUSELEAVE
      ).send();
    },
    'size.height': function (height) {
      this._noteEditor.height(height + this._toolbarWrapperHeight + this._noteEditorMarginBottom);
      this._noteEditable.height(height);
    },
    'size.width': function (width) {
      this._noteEditor.width(width);
      this._noteEditable.width(width);
    },
    'text': function (replacement, local) {
      if (this.isFocused) {
        this._summernote.summernote('restoreRange');
      }

      let changes = diff(local, replacement);



      this._noteEditable.html((index, html) => {
        return html.replace(local, replacement);
      });
    }
  }
}
</script>

<style scoped>
</style>