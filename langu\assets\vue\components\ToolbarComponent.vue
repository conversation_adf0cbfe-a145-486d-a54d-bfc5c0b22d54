<template>
  <div v-drag="{handle: '.toolbar-button-wrapper-replace'}" class="toolbar-buttons-wrapper"
       :class="{teacher: this.$store.state.role === 'teacher', student: this.$store.state.role === 'student', [this.$style[`${this.$store.state.role}_toolbar_hover`]]: isHovered}">
    <ul class="toolbar-buttons" id="toolbar-buttons" ref="toolbar_buttons">
      <ul ref="toolbar-horizontal"
          class="toolbar-buttons toolbar-buttons-horizontal"
          :class="{[this.$style[`${this.$store.state.role}_horizontal_hover`]]: isHovered}"
          v-bind:style="{top: (this.$store.state.role === 'student' ? '25.5%' : '16%')}">
        <li class="toolbar-button-wrapper-horizontal">
          <button class="toolbar-button-item toolbar-button-item-draw-line toolbar-button-item-horizontal"
                  @click="cursor.type = 'SHAPE_LINE'; cursor.svgId = 'draw-line';">
            <svg class="toolbar-button-icon ">
              <use xlink:href="/images/classroom/toolbar.svg#draw-line"></use>
            </svg>
          </button>
          <div class="hover-horizontal-button">Draw a line</div>
        </li>
        <li class="toolbar-button-wrapper-horizontal">
          <button class="toolbar-button-item toolbar-button-item-horizontal"
                  @click="cursor.type = 'SHAPE_CIRCLE'; cursor.svgId = 'draw-circle';">
            <svg class="toolbar-button-icon">
              <use xlink:href="/images/classroom/toolbar.svg#draw-circle"></use>
            </svg>
          </button>
          <div class="hover-horizontal-button">Draw a circle</div>
        </li>
        <li class="toolbar-button-wrapper-horizontal">
          <button class="toolbar-button-item toolbar-button-item-horizontal"
                  @click="cursor.type = 'SHAPE_TRIANGLE'; cursor.svgId = 'draw-triangle';">
            <svg class="toolbar-button-icon">
              <use xlink:href="/images/classroom/toolbar.svg#draw-triangle"></use>
            </svg>
          </button>
          <div class="hover-horizontal-button">Draw a triangle</div>
        </li>
        <li class="toolbar-button-wrapper-horizontal">
          <button class="toolbar-button-item toolbar-button-item-horizontal"
                  @click="cursor.type = 'SHAPE_SQUARE'; cursor.svgId = 'draw-square';">
            <svg class="toolbar-button-icon">
              <use xlink:href="/images/classroom/toolbar.svg#draw-square"></use>
            </svg>
          </button>
          <div class="hover-horizontal-button">Draw a square</div>
        </li>
        <li class="toolbar-button-wrapper-horizontal">
          <button class="toolbar-button-item toolbar-button-item-horizontal"
                  @click="cursor.type = 'LINE'; cursor.svgId = 'pencil';">
            <svg class="toolbar-button-icon">
              <use xlink:href="/images/classroom/toolbar.svg#pencil"></use>
            </svg>
          </button>
          <div class="hover-horizontal-button">Enable drawing tool</div>
        </li>
        <li class="toolbar-button-wrapper-horizontal">
          <button class="toolbar-button-item toolbar-button-item-hand toolbar-button-item-horizontal"
                  @click="cursor.type = 'POINTER_HAND'; cursor.svgId = 'hand';">
            <svg class="toolbar-button-icon">
              <use xlink:href="/images/classroom/toolbar.svg#hand"></use>
            </svg>
          </button>
          <div class="hover-horizontal-button">Draw a line</div>
        </li>
      </ul>
      <ul ref="toolbar-horizontal-file"
          class="toolbar-buttons toolbar-buttons-horizontal toolbar-buttons-horizontal-file"
          :class="{[this.$style[`${this.$store.state.role}_horizontal_hover`]]: isHovered}"
          v-bind:style="{top: (this.$store.state.role === 'student' ? '76.5%' : '72%')}">
        <li class="toolbar-button-wrapper-horizontal">
          <button id="load-files-library" class="toolbar-button-item toolbar-button-item-horizontal">
            <svg class="toolbar-button-icon">
              <use xlink:href="/images/classroom/toolbar.svg#books"></use>
            </svg>
          </button>
          <div class="hover-horizontal-button">Select from library</div>
        </li>
        <li class="toolbar-button-wrapper-horizontal">
          <button class="toolbar-button-item toolbar-button-item-horizontal">
            <label class="popup-load-files-label-upload popup-load-files-label-upload-laptop">
              <svg class="toolbar-button-icon">
                <use xlink:href="/images/classroom/toolbar.svg#laptop"></use>
              </svg>
              <input type="file" id="upload-library-files-laptop" multiple
                     class="popup-load-files-btn-upload"/>
            </label>
          </button>
          <div class="hover-horizontal-button">Upload from computer</div>
        </li>
      </ul>
      
      <li class="toolbar-button-wrapper toolbar-button-wrapper-replace">
        <button class="toolbar-button-item toolbar-button-replace">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#squareline"></use>
          </svg>
        </button>
      </li>
      <li class="toolbar-button-wrapper">
        <button id="toolbar-switch" class="toolbar-button-item toolbar-button-pointer"
                @click="cursor.type = 'POINTER'; cursor.svgId = 'hand';">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#pointer"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Default cursor</div>
      </li>
      <li class="toolbar-button-wrapper">
        <button ref="toolbar-button-hand" class="toolbar-button-item toolbar-button-hand"
                @click="showHorizontalMenu('toolbar-horizontal', 'toolbar-button-hand')">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#hand"></use>
          </svg>
        </button>
      </li>
      <li class="toolbar-button-wrapper">
        <button class="toolbar-button-item" @click="cursor.type = 'ERASER';">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#lastic"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Enable erasing tool</div>
      </li>
      <li class="toolbar-button-wrapper">
        <button id="toolbar-button-video" class="toolbar-button-item">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#play"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Add video</div>
      </li>
      <li class="toolbar-button-wrapper">
        <button class="toolbar-button-item" @click="clearLines()">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#trash"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Remove all lines</div>
      </li>
      <li class="toolbar-button-wrapper toolbar-button-wrapper-undo">
        <button class="toolbar-button-item toolbar-button-undo">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#undo"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Undo</div>
      </li>
      <li v-if="this.$store.state.role === 'teacher'" class="toolbar-button-wrapper">
        <button ref="buzz" v-bind:data-user="this.studentId"
                v-bind:href="this.buzzUrl" class="toolbar-button-item"
                @click.prevent="buzz()">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#ring"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Buzz student</div>
      </li>
      <li class="toolbar-button-wrapper">
        <button ref="toolbar-button-file" class="toolbar-button-item toolbar-button-file"
                @click="showHorizontalMenu('toolbar-horizontal-file', 'toolbar-button-file')">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#file"></use>
          </svg>
        </button>
      </li>
      <li class="toolbar-button-wrapper">
        <button class="toolbar-button-item" onclick="onExit()">
          <svg class="toolbar-button-icon">
            <use xlink:href="/images/classroom/toolbar.svg#exit"></use>
          </svg>
        </button>
        <div class="hover-btn-info">Exit class</div>
      </li>
      <li v-if="this.$store.state.role === 'teacher'" class="toolbar-button-wrapper">
        <form id="lesson-finish-form" v-bind:action="this.finishLessonUrl" method="POST"
              style="display: inline-block;">
          <input type="hidden" name="lessonId" v-bind:value="this.$store.state.lesson_id"/>
          <button class="toolbar-button-item" type="submit">
            <svg class="toolbar-button-icon">
              <use xlink:href="/images/classroom/toolbar.svg#tick"></use>
            </svg>
          </button>
          <div class="hover-btn-info">Finish lesson</div>
        </form>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    buzzUrl: {
      type: String,
      required: true,
    },
    finishLessonUrl: {
      type: String,
      required: true,
    },
    studentId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      cursor: {
        type: 'POINTER',
        svgId: 'hand',
      },

      _toolbarButtons: null,

      isHovered: false,
    }
  },
  mounted() {
    this._toolbarButtons = $(this.$refs.toolbar_buttons);

    this._toolbarButtons.on('mouseover', () => this.isHovered = true);
    this._toolbarButtons.on('mouseleave', () => this.isHovered = false);
  },
  methods: {
    buzz() {
      console.time('Buzz student');
      let buzzButton = $(this.$refs.buzz);

      if (buzzButton.hasClass('disabled')) {
        return;
      }

      let disabledDescription = buzzButton.data('disabled-title'),
          enabledDescription = buzzButton.data('enabled-title');

      buzzButton.addClass('disabled cooldown');
      buzzButton.attr('title', disabledDescription);
      setTimeout(() => {
        let classes = 'cooldown';

        if (!buzzButton.hasClass('offline')) {
          classes += ' disabled';
          buzzButton.attr('title', enabledDescription);
        }

        buzzButton.removeClass(classes);
      }, 30 * 1000);

      $.ajax(buzzButton.attr('href'), {
        cache: false
      });
      console.timeEnd('Buzz student');
    },
    clearLines() {
      console.time('Clearing lines');

      for (let i = 0; i < window.cursor.assets.length; i++) {
        if (window.cursor.assets[i].type && ((window.cursor.assets[i].parent && window.cursor.assets[i].parent === 'shape') || (window.cursor.assets[i].type).toLowerCase() == 'line')) {
          window.cursor.assets[i].update(true);
        }
      }

      console.timeEnd('Clearing lines');
    },
    showHorizontalMenu(menu, button) {
      console.time('Showing horizontal menu');

      $(this.$refs[menu]).toggleClass('toolbar-show');
      $(this.$refs[button]).toggleClass('selected');

      console.timeEnd('Showing horizontal menu');
    },
  },
  watch: {
    isHovered: function (state) {
      (new UpdateBuilder()).type(UpdateBuilder.TEMPORARY).name(
          state ? UpdateBuilder.TOOLBAR_MOUSEOVER : UpdateBuilder.TOOLBAR_MOUSELEAVE
      ).send();
    },
    'cursor.type': function (type) {
      console.time('Cursor type change');

      if (!window.toolbar || !window.cursor) {
        console.error('Cursor or toolbar is not defined');
        return;
      }

      window.toolbar.cur_selected = type;
      window.cursor.selected = type;

      console.timeEnd('Cursor type change');
    },
    'cursor.svgId': function (svgId) {
      console.time('Cursor svg change');

      $('body').css('cursor', `url('/images/classroom/cursor_${svgId}_${this.$store.state.role}.svg') 10 8, auto !important;`);
      $('#toolbar-button-hand use').attr('xlink:href', `/images/classroom/toolbar.svg#${svgId}`);

      console.timeEnd('Cursor svg change');
    },
  }
}
</script>

<style scoped>
.toolbar-buttons-wrapper {
  transform: none;
  position: absolute;
  top: 25%;
  left: 90%;
  z-index: 1000;
  box-shadow: 0 0 10px -2px rgba(0, 0, 0, .25);
  background: white;
}

.toolbar-buttons {
  padding-left: 0;
  margin-bottom: 0;
}

.toolbar-buttons-horizontal.toolbar-show, .toolbar-buttons-horizontal-file.toolbar-show {
  display: flex !important;
}

.toolbar-buttons li {
  list-style: none;
  z-index: 2000;
}

.toolbar-button-wrapper {
  max-width: 47px;
  display: flex;
  justify-content: center;
  position: relative;
}
.toolbar-button-wrapper-horizontal {
  width: 41px;
  height: 46px;
  max-width: 47px;
  display: flex;
  justify-content: center;
  position: relative;
}

.toolbar-button-wrapper-replace, .toolbar-button-wrapper-undo {
  padding: 0;
}

.toolbar-button-item {
  width: 100%;
  padding: 6px 0;
  position: relative;
  outline: none;
  border: none;
  background: #fff;
}

.toolbar-button-replace {
  width: 100%;
  margin-bottom: 8px;
  padding: 6px 15px 8px 15px;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
}

.toolbar-button-pointer {
  padding-top: 10px;
  padding-bottom: 0;
}

.toolbar-button-undo {
  padding: 8px 0;
  margin-bottom: 8px;
  width: 100%;
  border-radius: 6px;
  box-shadow: 0 4px 2px -2px rgba(0, 0, 0, 0.15);
}

.toolbar-button-hand::before, .toolbar-button-file::before {
  content: '';
  position: absolute;
  left: 3px;
  bottom: 0;
  border: 3px solid transparent;
  border-bottom: 3px solid black;
  border-left: 3px solid black;
}

.toolbar-button-file::before {
  left: 3px;
  bottom: 3px;
}

.toolbar-button-icon {
  max-height: 27px;
  max-width: 25px;
}

.toolbar-buttons-horizontal {
  display: none;
  position: absolute;
  top: 21%;
  left: -246px;
  border-radius: 6px;
}

.toolbar-buttons-horizontal-file {
  display: none;
  top: 77%;
  left: -82px;
}

.toolbar-button-item-horizontal {
  padding: 8px;
}

.toolbar-button-item-draw-line {
  padding-right: 0;
}

.toolbar-button-item-hand {
  padding-left: 0;
}

.toolbar-button-item-horizontal .toolbar-button-icon {
  height: auto;
  width: auto;
  margin: auto;
  display: block;
}

.toolbar-buttons-horizontal .toolbar-button-wrapper:first-child .toolbar-button-item-horizontal {
  border-bottom-left-radius: 4px !important;
  border-top-left-radius: 4px !important;
}

#toolbar-switch {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.toolbar-button-wrapper:last-child .toolbar-button-item {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border-bottom: none !important;
  width: 41px;
  height: 46px;
}

div.teacher .toolbar-button-wrapper:hover svg {
  color: #80B723 !important;
}
div.teacher .toolbar-button-wrapper-horizontal:hover svg {
  color: #80B723 !important;
}

div.student .toolbar-button-wrapper:hover svg {
  color: #3C87F8 !important;
}
div.student .toolbar-button-wrapper-horizontal:hover svg {
  color: #3C87F8 !important;
}

.toolbar-button-item svg {
  color: #2D2D2D;
}

.hover-btn-info {
  position: absolute;
  display: none;
  width: auto;
  top: 50%;
  transform: translateY(-50%);
  right: 60px;
  background: #444444;
  border-radius: 6px;
  color: #fff;
  padding: 5px 10px;
  white-space: nowrap;
  font-size: 13px;
}

.hover-horizontal-button {
  visibility: hidden;
  height: auto;
  width: auto;
  background-color: black;
  color: #fff;
  position: absolute;
  bottom: 55px;
  padding: 5px 10px;
  border-radius: 6px;
  white-space: nowrap;
  font-size: 13px;
  z-index: 1001;
  left: calc(50% - 7px);
  transform: translateX(-50%);
}

.hover-horizontal-button::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  border: 5px solid transparent;
  border-top: 5px solid #444444;
}

.toolbar-button-item:hover + .hover-horizontal-button {
  visibility: visible;
}

.hover-btn-info::after {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 100%;
  border: 5px solid transparent;
  border-left: 5px solid #444444;
}

.hover-btn-info-horizontal {
  top: -20px;
  right: -60%;
}

.hover-btn-info-horizontal.hover-btn-info::after {
  border-top: 5px solid #444444;
  border-left: 5px solid transparent;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.toolbar-button-item.toolbar-button-replace {
  max-height: 27px;
}

.toolbar-button-replace + .hover-btn-info {
  top: 40%;
}

.selected {
  border-bottom: none;
}

.selected.button-student svg {
  color: #3C87F8;
}

.selected.button-teacher svg {
  color: #80B723;
}
</style>

<style module>
  .teacher_horizontal_hover {
    box-shadow: rgb(127, 184, 2) 0 2px 0 0, rgb(127, 184, 2) -2px -2px 0 0, rgb(127, 184, 2) -2px 2px 0 0, rgb(127, 184, 2) 2px -2px 0 0;
  }

  .teacher_toolbar_hover {
    box-shadow: 0 0 2px 2px rgb(127, 184, 2) !important;
  }

  .student_horizontal_hover {
    box-shadow: rgb(60, 135, 248) 0 2px 0 0, rgb(60, 135, 248) -2px -2px 0 0, rgb(60, 135, 248) -2px 2px 0 0, rgb(60, 135, 248) 2px -2px 0 0;
  }

  .student_toolbar_hover {
    box-shadow: 0 0 2px 2px rgb(60, 135, 248) !important;
  }
</style>