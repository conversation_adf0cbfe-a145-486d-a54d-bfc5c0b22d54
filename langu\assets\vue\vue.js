import Vue from 'vue';
import Vuex from 'vuex';
import {FORCE_PUSH_COMPONENTS, ADD_COMPONENT, UPDATE_COMPONENT_DATA, SEND_SOCKET_UPDATE} from "./import/mutation-types";
import set from 'lodash/set';
import VueSocketIO from 'vue-socket.io';
import drag from 'v-drag';

// CORE COMPONENTS
Vue.use(Vuex);
Vue.use(new VueSocketIO({
    debug: true,
    connection: `${location.host}/`,
    // @TODO: Integrate vuex on Vue side
    // vuex: {
    //     store,
    //     actionPrefix: 'SOCKET_',
    //     mutationPrefix: 'SOCKET_'
    // },
    options: {
        path: '/websocket',
        secure: true,
        reconnectionAttempts: 5,
    }
}));
Vue.use(drag);
Vue.component('component-dispatcher', require('./components/ComponentDispatcher').default);
Vue.component('component-toolbar', require('./components/ToolbarComponent').default);
Vue.component('component-other-cursor', require('./components/OtherCursorComponent').default);

// Synced components (via sockets)
Vue.component('component-summernote', require('./components/SummernoteComponent').default);
Vue.component('component-video', require('./components/VideoComponent').default);

// Configurations
require('./bootstrap');

let profiler = window.logger.startTimer();

global.store = new Vuex.Store({
    state: {
        user_id: window.student_id,
        role: window.langu_role,
        other_user_role: window.langu_role === 'teacher' ? 'student' : 'teacher',
        lesson_id: window.lesson_id,
        components: [],
        videoId: 'heywattafucc'
    },
    mutations: {
        [ADD_COMPONENT](state, component) {
            state.components.push(component);
        },
        [FORCE_PUSH_COMPONENTS](state, components) {
            state.components = components;
        },
        [UPDATE_COMPONENT_DATA](state, {index, path, data}) {
            Vue.set(state.components, index, set(state.components[index], path, data));
        }
    },
    getters: {
        activeComponents: state => {
            return state.components.filter(component => component.is_active);
        },
        getIndexByRef: state => ref => {
            return state.components.findIndex(component => component.ref === ref);
        }
    },
    actions: {
        // @TODO: handle async via setTimeout and delegate them from another thread
        // @TODO: Optimize UPDATE_COMPONENT_DATA method
        async [UPDATE_COMPONENT_DATA]({commit, getters, dispatch}, {type, ref, path, data}) {
            if (type === null) {
                return commit(UPDATE_COMPONENT_DATA, {
                    path: path,
                    data: data,
                    index: getters.getIndexByRef(ref),
                });
            } else {
                return dispatch(SEND_SOCKET_UPDATE, {
                    type: type,
                    ref: ref,
                    path: path,
                    data: data,
                }).then(() => {
                    commit(UPDATE_COMPONENT_DATA, {
                        path: path,
                        data: data,
                        index: getters.getIndexByRef(ref),
                    });
                });
            }
        },
        async [SEND_SOCKET_UPDATE]({commit}, {type, ref, path, data}) {
            return new Promise((resolve, reject) => {
                (new UpdateBuilder()).type(type).name(UpdateBuilder.VUE_COMPONENT_UPDATE)
                    .componentId(ref)
                    .updateData({
                        path: path,
                        target: data,
                    })
                    .send();

                resolve();
            });
        }
    }
});

global.vue = new Vue({
    el: '#canvas_div',
    store: store,
    mounted() {
        console.log('MOUNTED!!!');
    },
});

profiler.done({message: 'Vue initialization complete.'});
