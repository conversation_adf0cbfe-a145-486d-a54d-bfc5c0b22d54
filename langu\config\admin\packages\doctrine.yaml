# Doctrine Configuration
doctrine:
    dbal:
        default_connection: default
        connections:
            default:
                driver:   pdo_mysql
                host:     "%database_host%"
                port:     "%database_port%"
                dbname:   "%database_name%"
                user:     "%database_user%"
                password: "%database_password%"
                charset:  utf8mb4

            admin_users:
                driver: pdo_mysql
                host: "%admin_database_host%"
                port: "%admin_database_port%"
                dbname: "%admin_database_name%"
                user: "%admin_database_user%"
                password: "%admin_database_password%"
                charset: UTF8
        types:
                json_array: Doctrine\DBAL\Types\JsonType
