doctrine:
    orm:
        default_entity_manager: default
        auto_generate_proxy_classes: "%kernel.debug%"
        entity_managers:
            default:
                connection: default
                naming_strategy: doctrine.orm.naming_strategy.underscore
                metadata_cache_driver: ~
                query_cache_driver: ~
                mappings:
                    AdminBundle: ~
                    AppBundle: ~
                    MoneyBundle: ~
                    MessageBundle: ~
                    UserBundle: ~
                    TeachingBundle: ~
                    FaqBundle: ~
                    IntlBundle: ~
                    MangopayBundle: ~
                    MediaBundle: ~
                    AvailabilityBundle: ~
                    LessonBundle: ~
                    BlogBundle: ~
                    PaymentBundle: ~
                    GoogleApiBundle: ~
                    DoctrineBundle: ~
                    DoctrineMigrationsBundle: ~
                    SonataDoctrineORMAdminBundle: ~
                    StofDoctrineExtensionsBundle: ~
                    AdminAnalyticsBundle:
                        is_bundle: false
                        type: annotation
                        dir: '%kernel.project_dir%/src/AdminAnalyticsBundle/Model'
                        prefix: 'App\\AdminAnalyticsBundle\\Model'
                        alias: AdminAnalytics

            admin_users:
                connection: admin_users
                naming_strategy: doctrine.orm.naming_strategy.underscore
                metadata_cache_driver: ~
                query_cache_driver: ~
                mappings:
                    FOSUserBundle: ~
                    SonataUserBundle: ~
                    ApplicationSonataUserBundle: ~
                    AdminAnalyticsBundle:
                        is_bundle: false
                        type: annotation
                        dir: '%kernel.project_dir%/src/AdminAnalyticsBundle/Model'
                        prefix: 'App\\AdminAnalyticsBundle\\Model'
                        alias: AdminAnalytics

services:
    doctrine.result_cache_provider:
        class: Symfony\Component\Cache\DoctrineProvider
        public: false
        arguments:
            - '@doctrine.result_cache_pool'
    doctrine.system_cache_provider:
        class: Symfony\Component\Cache\DoctrineProvider
        public: false
        arguments:
            - '@doctrine.system_cache_pool'

framework:
    cache:
        pools:
            doctrine.result_cache_pool:
                adapter: cache.adapter.apcu
            doctrine.system_cache_pool:
                adapter: cache.adapter.apcu