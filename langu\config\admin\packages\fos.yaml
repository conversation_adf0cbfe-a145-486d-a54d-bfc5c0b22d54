fos_user:
  db_driver:      orm
  firewall_name:  main
  user_class:     App\Application\Sonata\UserBundle\Entity\User
  model_manager_name: admin_users
  from_email:
    address: "<EMAIL>"
    sender_name: "Demo String"
  service:
    mailer: 'fos_user.mailer.noop'
#    user_manager: sonata.user.orm.user_manager
    # mailer: 'fos_user.mailer.twig_swift' # Works as well

#  group:
#    group_class:   App\Application\Sonata\UserBundle\Entity\Group
#    group_manager: sonata.user.orm.group_manager

