# config/packages/fos_ck_editor.yaml
fos_ck_editor:
    default_config: pages
    configs:
        pages:
            toolbar: standard
        blog_post:
            toolbar:
                -
                    name: clipboard
                    items: [Cut, Co<PERSON>, Paste, PasteText, PasteFromWord, '-', Undo, Redo]
                -
                    name: editing
                    items: [Scayt]
                -
                    name: links
                    items: [Link, Unlink, Anchor]
                -
                    name: insert
                    items: [Image, Table, HorizontalRule, SpecialChar, Iframe]
                -
                    name: tools
                    items: [Maximize]
                -
                    name: document
                    items: [Source]
                -
                    name: basicstyles
                    items: [Bold, Italic, Underline, '-', RemoveFormat]
                -
                    name: paragraph
                    items: [NumberedList, BulletedList, '-', Outdent, Indent, '-', Blockquote]
                -
                    name: styles
                    items: [Styles, Format]
                -
                    name: about
                    items: [About]
            filebrowserBrowseRoute: elfinder
            contentsCss:
                - admin/css/blog.css
            bodyClass: blog-outer
            disallowedContent: 'img{width,height}[width,height]'
            extraAllowedContent: 'iframe[*]'
            image2_altRequired: true
            extraPlugins: "image2,iframe"
    plugins:
        image2:
            path: "/admin/js/ckeditor-plugins/image2/"
            filename: "plugin.js"
        widget:
            path: "/admin/js/ckeditor-plugins/widget/"
            filename: "plugin.js"
        widgetselection:
            path: "/admin/js/ckeditor-plugins/widgetselection/"
            filename: "plugin.js"
        lineutils:
            path: "/admin/js/ckeditor-plugins/lineutils/"
            filename: "plugin.js"
        iframe:
            path: "/admin/js/ckeditor-plugins/iframe/"
            filename: "plugin.js"
        dialog:
            path: "/admin/js/ckeditor-plugins/dialog/"
            filename: "plugin.js"
        fakeobjects:
            path: "/admin/js/ckeditor-plugins/fakeobjects/"
            filename: "plugin.js"
