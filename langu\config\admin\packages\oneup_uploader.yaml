oneup_uploader:
    mappings:
        profile_images:
            frontend: dropzone
            storage:
                directory: "%kernel.project_dir%/public/uploads/profiles/pictures"
            max_size: 8388608
            allowed_mimetypes:
                image/jpg: []
                image/jpeg: []
                image/png: []
                image/gif: []
                image/svg+xml: []
        teacher_cv:
            frontend: dropzone
            namer: user.uploader.user_cv_namer
            storage:
                directory: "%kernel.project_dir%/public/uploads/profiles/cv"
            max_size: 2097152
            allowed_mimetypes:
                application/pdf: ['pdf']
        teacher_resources:
            frontend: dropzone
            namer: media.uploader.name.resource_namer
            storage:
                directory: "%kernel.project_dir%/public/uploads/resources"
            max_size: 10485760000
            allowed_mimetypes:
                text/plain: []
                text/richtext: []
                application/rtf: []
                text/rtf: []
                font/woff: []
                image/jpg: []
                image/jpeg: []
                image/png: []
                image/svg+xml: []
                image/gif: []
                image/tiff: []
                image/x-tiff: []
                application/pdf: []
                application/zip: []
                application/ppt: []
                application/msword: []
                application/vnd.ms-powerpoint: []
                video/msvideo: []
                video/mpeg: []
                video/H264: []
                video/ogg: []
                video/mp4: []
                video/x-ms-asf: []
                audio/mp3: []
                audio/wma: []
                audio/mp4: []
                audio/x-ms-wma: []
                audio/mpeg: []
                audio/ogg: []
                audio/wav: []
                audio/aac: []
                audio/avi: []
                video/quicktime: []
                video/x-quicktime: []
                image/mov: []
                audio/aiff: []
                audio/x-midi: []
                audio/x-wav: []
                video/avi: []
                application/mspowerpoint: []
                application/xls: []
                application/powerpoint: []
                application/x-mspowerpoint: []
                application/vnd.openxmlformats-officedocument.presentationml.presentation: []
                application/vnd:
                application/openxmlformats-officedocument.spreadsheetml.shee: []
                application/excel: []
                application/vnd.ms-excel: []
                text/vnd.ms-excel: []
                application/x-excel: []
                application/x-msexcel: []
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet: []
                text/mspowerpoint: []
                text/powerpoint: []
                text/x-mspowerpoint: []
                text/vnd.openxmlformats-officedocument.presentationml.presentation: []
                text/excel: []
                text/x-excel: []
                text/x-msexcel: []
                text/vnd.openxmlformats-officedocument.spreadsheetml.sheet: []
