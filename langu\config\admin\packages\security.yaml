# To get started with security, check out the documentation:
# http://symfony.com/doc/current/book/security.html
parameters:
    google_state_key: google_oauth_state

security:

    role_hierarchy:
        ROLE_BLOG_EDITOR:
            - ROLE_USER
            - ROLE_APP_ADMIN_BLOG_POST_CREATE
            - ROLE_APP_ADMIN_BLOG_POST_EDIT
            - ROLE_APP_ADMIN_BLOG_POST_VIEW
            - ROLE_APP_ADMIN_BLOG_POST_LIST
            - ROLE_SONATA_ADMIN
        ROLE_ADMIN: [ ROLE_USER, ROLE_BLOG_EDITOR, ROLE_APP_ADMIN_BLOG_POST_PUBLISH ]
        ROLE_SUPER_ADMIN: [ ROLE_ADMIN, ROLE_ALLOWED_TO_SWITCH ]

    password_hashers:
        Sonata\UserBundle\Model\UserInterface: auto
        App\UserBundle\Entity\AbstractUser: bcrypt

#        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
#            algorithm: 'bcrypt'
#            cost: 15
        
    providers:
        sonata_user_bundle:
            id: sonata.user.security.user_provider
            
#        google_oauth:
#            id: google_auth.security.user.provider

    firewalls:
        # Disabling the security for the web debug toolbar, the profiler and Assetic.
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        admin:
            lazy: true
            pattern: ^/(admin(.*)|_uploader/profile_images/upload)
            provider: sonata_user_bundle
            context: user
            form_login:
                login_path: sonata_user_admin_security_login
                check_path: sonata_user_admin_security_check
                default_target_path: sonata_admin_dashboard
            logout:
                path: sonata_user_admin_security_logout
                target: sonata_user_admin_security_login
            remember_me:
                secret: '%env(APP_SECRET)%'
                lifetime: 2629746
                path: /admin

    access_control:
        - { path: ^/login$, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/register, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/resetting, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/efconnect, role: ROLE_USER }
        - { path: ^/elfinder, role: ROLE_USER }

        - { path: ^/admin/login$, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin/logout$, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin/login_check$, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin/resetting, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin/app/blogpost, role: ROLE_BLOG_EDITOR }
        - { path: ^/admin/dashboard, role: ROLE_USER }
        - { path: ^/admin/, role: [ROLE_SONATA_ADMIN] }
        - { path: ^/.*, role: IS_AUTHENTICATED_ANONYMOUSLY }

