sonata_intl:
    timezone:
        default: UTC

#sonata_translation:
#    locales: ['pl', 'en', 'es']
#    default_locale: 'en'
#    knplabs:
#        enabled: true

a2lix_translation_form:
        locales: ['en', 'pl', 'es']

sonata_user:
    security_acl: false
    manager_type: orm
    class:
        user: App\Application\Sonata\UserBundle\Entity\User
#        group: App\Application\Sonata\UserBundle\Entity\Group
    resetting:
        email:
            address: sonata@localhost
            sender_name: Sonata Admin

    admin:                  # Admin Classes
        user:
            class:          App\Application\Sonata\UserBundle\Admin\UserAdmin
            controller:     sonata_user.admin.user.controller
            translation:    SonataUserBundle
            
sonata_formatter:
    default_formatter: markdown
    formatters:
        markdown:
#            service: sonata.formatter.text.markdown
            service: sonata.formatter.text.twig
            extensions:
                - sonata.formatter.twig.control_flow
                - sonata.formatter.twig.gist

sonata_admin:
    title: Langu Dashboard
    show_mosaic_button:   false

    security:
        handler: sonata.admin.security.handler.role
        
    dashboard:      
        groups:
            user:
                label: User
                items: ~

            lesson:
                label: Lessons
                items: ~

            settings:
                label: Settings
                items: ~

            other:
                label: Misc
                items: ~
                
            analytics:
                label: Analytics
                items: ~

            mangopay:
                label: Mangopay
                items: ~
                
            blog:
                label: Blog
                items: ~
                
            sonata_user:
                label: Administration
                items: ~

            faq:
                label: FAQ
                items: ~

            payments:
                label: Payments
                items: ~

            voucher:
                label: Voucher
                items: ~

            company_clients:
                label: Company Clients
                items: ~
                
        blocks:
        - 
            position: left
            type: sonata.admin.block.admin_list
            settings:
                groups: [user, analytics, mangopay, faq, payments, company_clients]
        - 
            position: right
            type: sonata.admin.block.admin_list
            settings:
                groups: [lesson, settings, blog, other, voucher]
        -
            position: bottom
            class: col-xs-offset-2 col-xs-8
            type: sonata.admin.block.admin_list
            settings:
                groups: [sonata_user]
                
    options:
        sort_admins: true

    templates:
        layout: 'admin/standard_layout.html.twig'
        
    assets:
        stylesheets:
            - bundles/sonataadmin/app.css
            - bundles/sonatacore/vendor/bootstrap/dist/css/bootstrap.min.css
            - bundles/sonatacore/vendor/components-font-awesome/css/font-awesome.min.css
            - bundles/sonatacore/vendor/ionicons/css/ionicons.min.css
            - bundles/sonataadmin/vendor/admin-lte/dist/css/AdminLTE.min.css
            - bundles/sonataadmin/vendor/admin-lte/dist/css/skins/skin-blue-light.min.css
            - bundles/sonataadmin/vendor/iCheck/skins/square/blue.css
            - bundles/sonatacore/vendor/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css
            - bundles/sonataadmin/vendor/jqueryui/themes/base/jquery-ui.css
            - bundles/sonatacore/vendor/select2/select2.css
            - bundles/sonatacore/vendor/select2-bootstrap-css/select2-bootstrap.min.css
            - bundles/sonataadmin/vendor/x-editable/dist/bootstrap3-editable/css/bootstrap-editable.css
            - bundles/sonataadmin/css/styles.css
            - bundles/sonataadmin/css/layout.css
            - bundles/sonataadmin/css/tree.css
            - bundles/sonatatranslation/css/sonata-translation.css
            - admin/css/langu.css
