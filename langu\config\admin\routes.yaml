admin_area:
  resource: "@SonataAdminBundle/Resources/config/routing/sonata_admin.xml"
  prefix: /admin

_sonata_admin:
  resource: .
  type: sonata_admin
  prefix: /admin

revolut_check_token:
  path: /revolut-check-token
  defaults: { _controller: App\AdminBundle\Controller\RevolutTokenController::checkToken }

sonata_user_admin_security:
  resource: '@SonataUserBundle/Resources/config/routing/admin_security.xml'
  prefix: /admin

sonata_user_admin_resetting:
  resource: '@SonataUserBundle/Resources/config/routing/admin_resetting.xml'
  prefix: /admin/resetting

elfinder:
  resource: "@FMElfinderBundle/Resources/config/routing.yaml"

payments:
  resource: "@PaymentBundle/Resources/config/routing.yml"
  prefix: /

catch_main:
  path: /
  defaults:
    _controller: FrameworkBundle:Redirect:redirect
    route: sonata_user_admin_security_login
    permanent: true

catch_all:
  path: /{path}
  defaults:
    _controller: FrameworkBundle:Redirect:redirect
    route: sonata_user_admin_security_login
    permanent: true

user:
  resource: "@UserBundle/Resources/config/routing.yml"
  prefix:   /user

google_api:
  resource: "@GoogleApiBundle/Resources/config/routing.yml"
  prefix:   /google

langu:
  resource: "@AppBundle/Resources/config/routing.yml"
  prefix:   /

oneup_uploader:
  resource: .
  type: uploader

_liip_imagine:
  resource: "@LiipImagineBundle/Resources/config/routing.yaml"
