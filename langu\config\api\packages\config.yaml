#imports:
#  - resource: parameters.yaml
#  - resource: security.yaml
#  - resource: services.yaml
#  - resource: stof.yaml
#  - resource: mangopay.yaml
#  - resource: uploader.yaml
#  - resource: imagine.yaml
#  - resource: doctrine.yaml
#  - resource: doctrine_orm.yaml
#  - resource: doctrine_migrations.yaml
#  - resource: translations.yaml
#  - resource: '@UserBundle/Resources/config/services.yml'
#  - resource: '@AppBundle/Resources/config/services.yml'
#  - resource: '@GoogleApiBundle/Resources/config/services.yml'
#  - resource: '@MoneyBundle/Resources/config/services.yml'
#  - resource: '@IntlBundle/Resources/config/services.yml'
#  - resource: '@AvailabilityBundle/Resources/config/services.yml'
#  - resource: '@MessageBundle/Resources/config/services.yml'
#  - resource: '@MediaBundle/Resources/config/services.yml'
#  - resource: '@TeachingBundle/Resources/config/services.yml'
#  - resource: '@MangopayBundle/Resources/config/services.yml'
#  - resource: '@LessonBundle/Resources/config/services.yml'
#  - resource: '@BlogBundle/Resources/config/services.yml'
#  - resource: '@PaymentBundle/Resources/config/services.yml'
#  - resource: snappy.yml
#  - resource: redis.yml
#  - resource: '@LandingBundle/Resources/config/services.yml'
#  - resource: redirects.yml
#parameters:
#  locale: en
#framework:
#  secret: '%secret%'
#  router:
#    resource: '%kernel.root_dir%/config/routing.yml'
#    strict_requirements: null
#  form: null
#  csrf_protection: null
#  validation:
#    enabled: true
#    enable_annotations: true
#  templating:
#    engines:
#      - twig
#  default_locale: '%locale%'
#  trusted_hosts: null
#  session:
#    handler_id: snc_redis.session.handler
#    cookie_domain: .%main_domain%
#    name: L2SESSID
#    save_path: null
#  fragments: null
#  http_method_override: true
#  translator:
#    enabled: true
#    fallbacks:
#      - en
#  assets:
#    version: '*************'
#    version_format: '%%s?v=%%s'
#    base_path: null
#twig:
#  debug: '%kernel.debug%'
#  strict_variables: '%kernel.debug%'
#  form_themes:
#    - bootstrap_3_layout.html.twig
#    - 'UserBundle:Form:fields.html.twig'
#  globals:
#    google_client_id: '%google_client_id%'
#    google_scopes: '%google_app_scopes%'
#    tokbox_api_key: '%tokbox_api_key%'
#    snippets: '%snippets%'
#    main_domain: '%main_domain%'
#    crisp_chat_website_id: '%crisp_website_id%'
#    domain_to_app: '%domain_to_redirect%'
#    lib_upload_settings:
#      size: '%max_size_lib%'
#      allowed_mimetypes: '%allowed_mimetypes_lib%'
