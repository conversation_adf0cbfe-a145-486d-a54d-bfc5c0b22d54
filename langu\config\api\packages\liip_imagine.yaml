liip_imagine:
    resolvers:
        default:
            web_path:
                web_root: "%kernel.project_dir%/public"
                cache_prefix: "images/cache"
    loaders:
        default:
            filesystem:
                data_root: "%liip_imagine_data_path%"

    driver:               "gd"
    cache:                default
    data_loader:          default
    default_image:        null
    controller:
        filter_action:         liip_imagine.controller:filterAction
        filter_runtime_action: liip_imagine.controller:filterRuntimeAction

    filter_sets:
        cache: ~

        seo_page_1600:
            quality: 75
            filters:
                thumbnail: { size: [1600, null], mode: outbound }

        seo_page_768:
            quality: 75
            filters:
                thumbnail: { size: [768, null], mode: outbound }

        landing_logo_2x:
            quality: 80
            filters:
                thumbnail: { size: [null, 156], mode: outbound }

        landing_logo_1x:
            quality: 80
            filters:
                thumbnail: { size: [null, 78], mode: outbound }

        library_thumb_listing:
            quality: 70
            filters:
                thumbnail: { size: [100, 100], mode: outbound }


        library_thumb_listing_pdf:
            quality: 70
            data_loader: pdf_file_system_loader
            format: jpg
            filters:
                thumbnail: { size: [100, 100], mode: outbound }

        user_thumb_menu:
            quality: 70
            filters:
                thumbnail: { size: [31, 31], mode: outbound }

        user_thumb_settings:
            quality: 80
            filters:
                thumbnail: {size: [100, 100], mode: outbound }

        user_thumb_profile:
            quality: 80
            filters:
                thumbnail: {size: [100, 100], mode: outbound }

        user_thumb_profile_big:
            quality: 80
            filters:
                thumbnail: {size: [90, 90], mode: outbound }

        user_thumb_90x90:
            quality: 80
            filters:
                thumbnail: {size: [90, 90], mode: outbound }

        user_thumb_180x180:
            quality: 80
            filters:
                thumbnail: {size: [180, 180], mode: outbound }

        user_thumb_teacher_listing:
            quality: 75
            filters:
                thumbnail: {size: [62, 62], mode: outbound }

        user_thumb_52x52:
            quality: 75
            filters:
                thumbnail: { size: [ 52, 52 ], mode: outbound }

        user_thumb_78x78:
            quality: 75
            filters:
                thumbnail: {size: [78, 78], mode: outbound }

        user_thumb_87x87:
            quality: 75
            filters:
                thumbnail: { size: [ 87, 87 ], mode: outbound }

        user_thumb_96x96:
            quality: 75
            filters:
                thumbnail: { size: [ 96, 96 ], mode: outbound }

        user_thumb_104x104:
            quality: 80
            filters:
                thumbnail: { size: [ 104, 104 ], mode: outbound }

        user_thumb_110x110:
            quality: 80
            filters:
                thumbnail: { size: [ 110, 110 ], mode: outbound }

        user_thumb_124x124:
            quality: 80
            filters:
                thumbnail: { size: [ 124, 124 ], mode: outbound }

        user_thumb_118x118:
            quality: 80
            filters:
                thumbnail: { size: [ 118, 118 ], mode: outbound }

        user_thumb_220x220:
            quality: 80
            filters:
                thumbnail: { size: [ 220, 220 ], mode: outbound }

        user_thumb_248x248:
            quality: 80
            filters:
                thumbnail: { size: [ 248, 248 ], mode: outbound }

        user_thumb_140x140:
            quality: 75
            filters:
                thumbnail: { size: [ 140, 140 ], mode: outbound }

        user_thumb_280x280:
            quality: 75
            filters:
                thumbnail: { size: [ 280, 280 ], mode: outbound }

        user_thumb_156x156:
            quality: 75
            filters:
                thumbnail: {size: [156, 156], mode: outbound }

        user_thumb_163x163:
            quality: 75
            filters:
                thumbnail: { size: [ 163, 163 ], mode: outbound }

        user_thumb_174x174:
            quality: 75
            filters:
                thumbnail: { size: [ 174, 174 ], mode: outbound }

        user_thumb_192x192:
            quality: 75
            filters:
                thumbnail: { size: [ 192, 192 ], mode: outbound }

        user_thumb_236x236:
            quality: 80
            filters:
                thumbnail: { size: [ 236, 236 ], mode: outbound }

        user_thumb_360x360:
            quality: 75
            filters:
                thumbnail: {size: [360, 360], mode: outbound }

        user_thumb_362x362:
            quality: 75
            filters:
                thumbnail: { size: [ 362, 362 ], mode: outbound }

        user_thumb_messages_listing:
            quality: 75
            filters:
                thumbnail: { size: [41, 41], mode: outbound }

        user_thumb_lesson_listing:
            quality: 75
            filters:
                thumbnail: { size: [62, 62], mode: outbound }

        user_profile_picture_upload:
            quality: 85
            filters:
                strip: ~
                thumbnail: {size: [1024, 768], mode: inset }

        blog_listing_image:
            quality: 85
            filters:
                strip: ~
                thumbnail: { size: [null, 240], mode: outbound }
