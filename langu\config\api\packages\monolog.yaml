monolog:
    channels:
        - deprecation # Deprecations are logged in the dedicated "deprecation" channel when it exists

when@dev:
    monolog:
        handlers:
            main:
                type: rotating_file
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: error
                channels: ["!event"]
                buffer_size: 30 # How many messages should be saved? Prevent memory leaks
#                formatter: monolog.formatter.custom_datetime

when@test:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                action_level: error
                handler: nested
                channels: ["!event"]
                buffer_size: 30 # How many messages should be saved? Prevent memory leaks
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: error

when@prod:
    monolog:
        handlers:
            main:
                type: rotating_file
                level: error
                handler: nested
                buffer_size: 30 # How many messages should be saved? Prevent memory leaks
            nested:
                type: stream
                path: php://stderr
                level: error
                formatter: monolog.formatter.json
                buffer_size: 30 # How many messages should be saved? Prevent memory leaks
            deprecation:
                type: stream
                channels: [deprecation]
                path: php://stderr
        channels: ['!event', '!info', '!debug']

