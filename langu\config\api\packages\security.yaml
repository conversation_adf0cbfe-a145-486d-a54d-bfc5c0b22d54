# To get started with security, check out the documentation:
# http://symfony.com/doc/current/book/security.html
parameters:
    google_state_key: google_oauth_state

security:

    role_hierarchy:
        ROLE_TEACHER: [ROLE_USER]
        ROLE_STUDENT: [ROL<PERSON>_USER]
        ROLE_ADMIN: [ROLE_USER]
        ROLE_SUPER_ADMIN: [ROLE_USER, ROLE_ALLOWED_TO_SWITCH]

    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
            algorithm: bcrypt
            cost: 15
        
    providers:
        in_memory:
            memory: ~
            
        google_oauth:
            id: google_auth.security.user.provider

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|fonts|audio|media|uploads|js)/
            security: false
         
        main:
#            logout_on_user_change: true
            pattern: ^/
            provider: google_oauth
            google_auth:
                login_path: user.login
                check_path: google.api_redirect
                register_path: user.register
                use_referer: false
                auth_state_key: "%google_state_key%"
                google_client_factory: App\GoogleApiBundle\Factory\GoogleClientFactory
                google_service_factory: App\GoogleApiBundle\Factory\GoogleServiceFactory
                failure_handler: google_auth.authentication.failure_handler
                success_handler: google_auth.authentication.success_handler
            logout:
                path: /api/users/logout
                target: /api/users/logout/success
            anonymous: ~

    access_control:
        - { path: ^/user/login, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/user/register, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/user/forgot, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/user/password, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/user/profile$, roles: [ROLE_TEACHER] }
        - { path: ^/user/profile/, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/user/availability, roles: [ROLE_TEACHER] }
        - { path: ^/user/reviews, roles: [ROLE_TEACHER] }
        - { path: ^/user/payments/receipt, roles: [ROLE_STUDENT] }
        - { path: ^/user/payments/receipts, roles: [ROLE_STUDENT] }
        - { path: ^/user/payments, roles: [ROLE_TEACHER] }
        - { path: ^/user/offers, roles: [ROLE_TEACHER] }
        - { path: ^/user/qualifications, roles: [ROLE_TEACHER] }
        - { path: ^/user/library, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/user/resources, roles: [ROLE_STUDENT] }
        - { path: ^/user/resource, roles: [ROLE_TEACHER] }
        - { path: ^/feedback, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/services, roles: [ROLE_TEACHER] }
        - { path: ^/resource, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/slot, roles: [ROLE_TEACHER] }
        - { path: ^/slots, roles: [ROLE_TEACHER] }
        - { path: ^/booking, roles: [ROLE_STUDENT] }
        - { path: ^/services, roles: [ROLE_TEACHER] }
        - { path: ^/user, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/mangopay, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/messages, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/lesson, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/api/classroom, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/api/students/settings, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/api/teacher/settings, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/api/users/settings, roles: [ROLE_TEACHER, ROLE_STUDENT] }
        - { path: ^/$, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/api/users/payments/earnings, roles: [ ROLE_TEACHER ] }
        - { path: ^/api/users/payments/earnings-balance, roles: [ ROLE_TEACHER ] }

