#sonata_block:
#    default_contexts: [sonata_page_bundle]
#    blocks:
#        sonata.admin.block.admin_list:
#        sonata.user.block.menu:
#        sonata.user.block.account:
        
sonata_intl:
    timezone:
        default: UTC

#sonata_translation:
#    locales: ['pl', 'en', 'es']
#    default_locale: 'en'
#    knplabs:
#        enabled: true

a2lix_translation_form:
        locales: ['en', 'pl', 'es']

sonata_user:
    security_acl: false
    manager_type: orm
    class:
        user: Application\Sonata\UserBundle\Entity\User
#        group: Application\Sonata\UserBundle\Entity\Group
    resetting:
        email:
            address: sonata@localhost
            sender_name: Sonata Admin

    admin:                  # Admin Classes
        user:
            class:          App\Application\Sonata\UserBundle\Admin\UserAdmin
            controller:     SonataAdminBundle:CRUD
            translation:    SonataUserBundle
            
sonata_formatter:
    default_formatter: markdown
    formatters:
        markdown:
#            service: sonata.formatter.text.markdown
            service: sonata.formatter.text.twig
            extensions:
                - sonata.formatter.twig.control_flow
                - sonata.formatter.twig.gist

sonata_admin:
    title: Langu Dashboard
    show_mosaic_button:   false

    security:
        handler: sonata.admin.security.handler.role
        
    dashboard:      
        groups:
            user:
                label: User
                items: ~

            lesson:
                label: Lessons
                items: ~

            settings:
                label: Settings
                items: ~

            other:
                label: Misc
                items: ~
                
            analytics:
                label: Analytics
                items: ~

            mangopay:
                label: Mangopay
                items: ~
                
            blog:
                label: Blog
                items: ~
                
            sonata_user:
                label: Administration
                items: ~

            faq:
                label: FAQ
                items: ~

            payments:
                label: Payments
                items: ~

            voucher:
                label: Voucher
                items: ~

            company_clients:
                label: Company Clients
                items: ~
                
        blocks:
        - 
            position: left
            type: sonata.admin.block.admin_list
            settings:
                groups: [user, analytics, mangopay, faq, payments, company_clients]
        - 
            position: right
            type: sonata.admin.block.admin_list
            settings:
                groups: [lesson, settings, blog, other, voucher]
        -
            position: bottom
            class: col-xs-offset-2 col-xs-8
            type: sonata.admin.block.admin_list
            settings:
                groups: [sonata_user]
                
    options:
        sort_admins: true

    templates:
        layout: '::standard_layout.html.twig'
        
    assets:
        stylesheets:
            - bundles/sonatacore/vendor/bootstrap/dist/css/bootstrap.min.css
            - bundles/sonatacore/vendor/components-font-awesome/css/font-awesome.min.css
            - bundles/sonatacore/vendor/ionicons/css/ionicons.min.css
            - bundles/sonataadmin/vendor/admin-lte/dist/css/AdminLTE.min.css
            - bundles/sonataadmin/vendor/admin-lte/dist/css/skins/skin-blue-light.min.css
            - bundles/sonataadmin/vendor/iCheck/skins/square/blue.css
            - bundles/sonatacore/vendor/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css
            - bundles/sonataadmin/vendor/jqueryui/themes/base/jquery-ui.css
            - bundles/sonatacore/vendor/select2/select2.css
            - bundles/sonatacore/vendor/select2-bootstrap-css/select2-bootstrap.min.css
            - bundles/sonataadmin/vendor/x-editable/dist/bootstrap3-editable/css/bootstrap-editable.css
            - bundles/sonataadmin/css/styles.css
            - bundles/sonataadmin/css/layout.css
            - bundles/sonataadmin/css/tree.css
            - bundles/sonatatranslation/css/sonata-translation.css
            - admin/css/langu.css
               
#ivory_ck_editor:
#    default_config: pages
#    configs:
#        pages:
#            toolbar: standard
#        blog_post:
#            toolbar:
#                -
#                    name: clipboard
#                    items: [Cut, Copy, Paste, PasteText, PasteFromWord, '-', Undo, Redo]
#                -
#                    name: editing
#                    items: [Scayt]
#                -
#                    name: links
#                    items: [Link, Unlink, Anchor]
#                -
#                    name: insert
#                    items: [Image, Table, HorizontalRule, SpecialChar, Iframe]
#                -
#                    name: tools
#                    items: [Maximize]
#                -
#                    name: document
#                    items: [Source]
#                - /
#                -
#                    name: basicstyles
#                    items: [Bold, Italic, Underline, '-', RemoveFormat]
#                -
#                    name: paragraph
#                    items: [NumberedList, BulletedList, '-', Outdent, Indent, '-', Blockquote]
#                -
#                    name: styles
#                    items: [Styles, Format]
#                -
#                    name: about
#                    items: [About]
#            filebrowserBrowseRoute: elfinder
#            filebrowserBrowseRouteParameters: []
#            contentsCss:
#                - admin/css/blog.css
#            bodyClass: blog-outer
#            disallowedContent: 'img{width,height}[width,height]'
#            extraAllowedContent: 'iframe[*]'
#            image2_altRequired: true
#            extraPlugins: "image2,iframe"
#    plugins:
#        image2:
#            path: "/admin/js/ckeditor-plugins/image2/"
#            filename: "plugin.js"
#        widget:
#            path: "/admin/js/ckeditor-plugins/widget/"
#            filename: "plugin.js"
#        widgetselection:
#            path: "/admin/js/ckeditor-plugins/widgetselection/"
#            filename: "plugin.js"
#        lineutils:
#            path: "/admin/js/ckeditor-plugins/lineutils/"
#            filename: "plugin.js"
#        iframe:
#            path: "/admin/js/ckeditor-plugins/iframe/"
#            filename: "plugin.js"
#        dialog:
#            path: "/admin/js/ckeditor-plugins/dialog/"
#            filename: "plugin.js"
#        fakeobjects:
#            path: "/admin/js/ckeditor-plugins/fakeobjects/"
#            filename: "plugin.js"
#fm_elfinder:
#    instances:
#        # should be named "icons"
#        seo_pages:
#            locale: '%locale%'
#            editor: form
#            path_prefix: ''
#            include_assets: true
#            visible_mime_types: ['image/svg+xml']
#            connector:
#                debug: true
#                roots:
#                    blog_posts:
#                        driver: LocalFileSystem
#                        path: '%kernel.root_dir%/../web/uploads/seo/icons'
#                        url: 'https://%main_domain%/uploads/seo/icons'
#                        upload_allow: ['image/svg+xml']
#                        upload_deny: ['all']
#                        upload_max_size: 512K
#                        alias: 'icons'
#                        attributes:
#                            - { pattern: '/(.*?)/', read: true, write: true, locked: true }
#
#        pages:
#            locale: '%locale%'
#            editor: form
#            path_prefix: ''
#            include_assets: true
#            visible_mime_types: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif']
#            connector:
#                debug: true
#                roots:
#                    blog_posts:
#                        driver: LocalFileSystem
#                        path: '%kernel.root_dir%/../web/uploads/pages/photos'
#                        url: 'https://%main_domain%/uploads/pages/photos'
#                        upload_allow:  ['image/png', 'image/jpg', 'image/jpeg', 'image/gif']
#                        upload_deny: ['all']
#                        upload_max_size: 2M
#                        alias: 'images'
#
#        blog_user:
#            locale: '%locale%'
#            editor: ckeditor
#            path_prefix: ''
#            include_assets: true
#            visible_mime_types: ['image/png', 'image/jpg', 'image/jpeg']
#            connector:
#                debug: true
#                roots:
#                    blog_posts:
#                        driver: LocalFileSystem
#                        path: '%kernel.root_dir%/../web/uploads/blog/resources'
#                        url: 'https://%main_domain%/uploads/blog/resources'
#                        upload_allow: ['image/png', 'image/jpg', 'image/jpeg']
#                        upload_deny: ['all']
#                        upload_max_size: 2M
#                        alias: 'blog resources'
#                        attributes:
#                            - { pattern: '/(.*?)/', read: true, write: true, locked: true }
#
#        blog_admin:
#            locale: '%locale%'
#            editor: ckeditor
#            path_prefix: ''
#            include_assets: true
#            visible_mime_types: ['image/png', 'image/jpg', 'image/jpeg']
#            connector:
#                debug: true
#                roots:
#                    blog_posts:
#                        driver: LocalFileSystem
#                        path: '%kernel.root_dir%/../web/uploads/blog/resources'
#                        url: 'https://%main_domain%/uploads/blog/resources'
#                        upload_allow: ['image/png', 'image/jpg', 'image/jpeg']
#                        upload_deny: ['all']
#                        upload_max_size: 10M
#                        alias: 'blog resources'
