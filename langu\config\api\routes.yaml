web:
  resource: "../../src/ApiBundle/Resources/config/routing.yml"
  prefix:   /api

api-user-registration:
  resource: "../../src/ApiBundle/Resources/config/UserRegistration/routing.yml"
  prefix: /api

api-user-login:
  resource: "../../src/ApiBundle/Resources/config/UserLogin/routing.yml"
  prefix: /api

api-teacher-settings:
  resource: "../../src/ApiBundle/Resources/config/UserSettings/teacher.yml"
  prefix:   /api

api-user-settings:
  resource: "../../src/ApiBundle/Resources/config/UserSettings/user.yml"
  prefix:   /api

api-student-settings:
  resource: "../../src/ApiBundle/Resources/config/UserSettings/student.yml"
  prefix:   /api

teaching:
  resource: "../../src/TeachingBundle/Resources/config/routing.yml"
  prefix:   /api

payments:
  resource: "../../src/PaymentBundle/Resources/config/routing.yml"
  prefix: /api
#
#sitemap:
#  path: /sitemap
#  controller: App\AppBundle\Controller\SitemapController::generateAction
#  methods: GET
#
#language_switch:
#  path: /language/{new_language}
#  defaults: { _controller: AppBundle:Language:switch }
#
#terms.student:
#  path: /terms/student
#  defaults: { _controller: AppBundle:Page:StaticPage, pageName: 'studentTerms' }
#
#terms.teacher:
#  path: /terms/teacher
#  defaults: { _controller: AppBundle:Page:StaticPage, pageName: 'teacherTerms' }
#
#about_us:
#  path: /about-us
#  defaults: { _controller: AppBundle:Page:AboutUsPage }
#
#methodology:
#  path: /how-it-works
#  defaults: { _controller: AppBundle:Page:MethodologyPage }
#
#business_page:
#  path: /business
#  defaults: { _controller: AppBundle:BusinessPage:BusinessPage }
#
#faq:
#  path: /faq
#  defaults: { _controller:  FaqBundle:Faq:FaqPage }
#
#christmas.page:
#  path: /christmas
#  defaults: { _controller: AppBundle:ChristmasPage:ChristmasPage }
#
#christmas.page_christmas-thankyou:
#  path: /merry-christmas
#  defaults: { _controller: AppBundle:ChristmasPage:ChristmasPage }
#
#christmas.page.set.cookie:
#  path: /christmas-set-cookie
#  defaults: { _controller: UserBundle\Controller\ChristmasVoucherBannerController::setChristmasVoucherBannerHide }
#
#education_page:
#  path: /education
#  defaults: { _controller: AppBundle:EducationPage:EducationPage }
#
#_bazinga_jstranslation:
#  resource: "@BazingaJsTranslationBundle/Resources/config/routing/routing.yml"
#
#lesson:
#  resource: "@LessonBundle/Resources/config/routing.yml"
#  prefix:   /
#
#langu:
#  resource: "@AppBundle/Resources/config/routing.yml"
#  prefix:   /
#
#mangopay:
#  resource: "@MangopayBundle/Controller/"
#  type:     annotation
#  prefix:   /mangopay
#
#teaching:
#  resource: "@TeachingBundle/Resources/config/routing.yml"
#  prefix:   /
#
#media:
#  resource: "@MediaBundle/Resources/config/routing.yml"
#  prefix:   /
#
#message:
#  resource: "@MessageBundle/Resources/config/routing.yml"
#  prefix:   /
#
#availability:
#  resource: "@AvailabilityBundle/Resources/config/routing.yml"
#  prefix:   /
#
#intl:
#  resource: "@IntlBundle/Resources/config/routing.yml"
#  prefix:   /
#
#money:
#  resource: "@MoneyBundle/Resources/config/routing.yml"
#  prefix:   /
#
#google_api:
#  resource: "@GoogleApiBundle/Resources/config/routing.yml"
#  prefix:   /google
#
#user:
#  resource: "@UserBundle/Resources/config/routing.yml"
#  prefix:   /user
#
#app:
#  resource: "@AppBundle/Controller/"
#  type:     annotation
#
#payments:
#  resource: "@PaymentBundle/Resources/config/routing.yml"
#  prefix: /
#
#_liip_imagine:
#  resource: "@LiipImagineBundle/Resources/config/routing.yaml"
#
#oneup_uploader:
#  resource: .
#  type: uploader
#
#landing:
#  resource: "@LandingBundle/Resources/config/routing.yml"
#  prefix:   /
#
#seo_page:
#  path: /{path}
#  defaults: { _controller: AppBundle:SeoPage:showPage }
#
#new.teacher.profile.view:
#  path:     /teacher/{username}
#  defaults: { _controller: UserBundle:Teacher:teacherProfile }