<?php


return [
   Symfony\Bundle\FrameworkBundle\FrameworkBundle::class => ['all' => true],
   <PERSON>sio\Bundle\FrameworkExtraBundle\SensioFrameworkExtraBundle::class => ['all' => true],
   Symfony\Bundle\TwigBundle\TwigBundle::class => ['all' => true],
   Symfony\Bundle\WebProfilerBundle\WebProfilerBundle::class => ['dev' => true, 'test' => true],
   Symfony\Bundle\MonologBundle\MonologBundle::class => ['all' => true],
   Symfony\Bundle\DebugBundle\DebugBundle::class => ['dev' => true, 'test' => true],
   Doctrine\Bundle\DoctrineBundle\DoctrineBundle::class => ['all' => true],
   Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle::class => ['all' => true],
   Symfony\Bundle\SecurityBundle\SecurityBundle::class => ['all' => true],
   Knp\Bundle\SnappyBundle\KnpSnappyBundle::class => ['all' => true],
   Snc\RedisBundle\SncRedisBundle::class => ['all' => true],
   A2lix\AutoFormBundle\A2lixAutoFormBundle::class => ['all' => true],
   A2lix\TranslationFormBundle\A2lixTranslationFormBundle::class => ['all' => true],
   Debesha\DoctrineProfileExtraBundle\DebeshaDoctrineProfileExtraBundle::class => ['all' => true],
   Knp\Bundle\MarkdownBundle\KnpMarkdownBundle::class => ['all' => true],
   Sonata\IntlBundle\SonataIntlBundle::class => ['all' => true],
   Symfony\Bundle\SwiftmailerBundle\SwiftmailerBundle::class => ['all' => true],
   Bazinga\Bundle\JsTranslationBundle\BazingaJsTranslationBundle::class => ['all' => true],
   Florianv\SwapBundle\FlorianvSwapBundle::class => ['all' => true],
   Stof\DoctrineExtensionsBundle\StofDoctrineExtensionsBundle::class => ['all' => true],
   Oneup\UploaderBundle\OneupUploaderBundle::class => ['all' => true],
   Vich\UploaderBundle\VichUploaderBundle::class => ['all' => true],
   Sonata\Twig\Bridge\Symfony\SonataTwigBundle::class => ['all' => true],
   Sonata\Exporter\Bridge\Symfony\SonataExporterBundle::class => ['all' => true],
   Knp\Bundle\MenuBundle\KnpMenuBundle::class => ['all' => true],
   Sonata\AdminBundle\SonataAdminBundle::class => ['all' => true],
   Sonata\DoctrineORMAdminBundle\SonataDoctrineORMAdminBundle::class => ['all' => true],
   FOS\CKEditorBundle\FOSCKEditorBundle::class => ['all' => true],
   Sonata\FormatterBundle\SonataFormatterBundle::class => ['all' => true],
   Sonata\TranslationBundle\SonataTranslationBundle::class => ['all' => true],
   Sonata\UserBundle\SonataUserBundle::class => ['all' => true],
   FM\ElfinderBundle\FMElfinderBundle::class => ['all' => true],
   Sonata\BlockBundle\SonataBlockBundle::class => ['all' => true],
   Knp\DoctrineBehaviors\DoctrineBehaviorsBundle::class => ['all' => true],
   Twig\Extra\TwigExtraBundle\TwigExtraBundle::class => ['all' => true],
   Sonata\Form\Bridge\Symfony\SonataFormBundle::class => ['all' => true],
   Sonata\Doctrine\Bridge\Symfony\SonataDoctrineBundle::class => ['all' => true],
   Sonata\MediaBundle\SonataMediaBundle::class => ['all' => true],
   Liip\ImagineBundle\LiipImagineBundle::class => ['all' => true],
   JMS\SerializerBundle\JMSSerializerBundle::class => ['all' => true],
   SimpleBus\SymfonyBridge\SimpleBusCommandBusBundle::class => ['all' => true],
   SimpleBus\SymfonyBridge\SimpleBusEventBusBundle::class => ['all' => true],
   App\LessonBundle\LessonBundle::class => ['all' => true],
   App\AppBundle\AppBundle::class => ['all' => true],
   App\UserBundle\UserBundle::class => ['all' => true],
   App\MessageBundle\MessageBundle::class => ['all' => true],
   App\PaymentBundle\PaymentBundle::class => ['all' => true],
   App\TeachingBundle\TeachingBundle::class => ['all' => true],
   App\MediaBundle\MediaBundle::class => ['all' => true],
   App\AvailabilityBundle\AvailabilityBundle::class => ['all' => true],
   App\IntlBundle\IntlBundle::class => ['all' => true],
   App\MoneyBundle\MoneyBundle::class => ['all' => true],
   App\GoogleApiBundle\GoogleApiBundle::class => ['all' => true],
   App\LandingBundle\LandingBundle::class => ['all' => true],
   App\FaqBundle\FaqBundle::class => ['all' => true],
   App\MangopayBundle\MangopayBundle::class => ['all' => true],
   App\BlogBundle\BlogBundle::class => ['all' => true],
];
