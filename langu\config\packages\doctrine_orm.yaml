doctrine:
    orm:
        auto_generate_proxy_classes: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: true
        mappings:
            LessonBundle:
                is_bundle: true
                prefix: 'App\LessonBundle\Entity'
                alias: LessonBundle

            AppBundle:
                is_bundle: true
                prefix: 'App\AppBundle\Entity'
                alias: AppBundle

            UserBundle:
                is_bundle: true
                prefix: 'App\UserBundle\Entity'
                alias: UserBundle

            MessageBundle:
                is_bundle: true
                prefix: 'App\MessageBundle\Entity'
                alias: MessageBundle

            PaymentBundle:
                is_bundle: true
                prefix: 'App\PaymentBundle\Entity'
                alias: PaymentBundle

            TeachingBundle:
                is_bundle: true
                prefix: 'App\TeachingBundle\Entity'
                alias: TeachingBundle

            BlogBundle:
                is_bundle: true
                prefix: 'App\BlogBundle\Entity'
                alias: BlogBundle
        metadata_cache_driver:
            type: service
            id: doctrine.system_cache_provider
        query_cache_driver:
            type: service
            id: doctrine.system_cache_provider
        result_cache_driver:
            type: service
            id: doctrine.result_cache_provider

services:
    doctrine.result_cache_provider:
        class: Symfony\Component\Cache\DoctrineProvider
        public: false
        arguments:
            - '@doctrine.result_cache_pool'
    doctrine.system_cache_provider:
        class: Symfony\Component\Cache\DoctrineProvider
        public: false
        arguments:
            - '@doctrine.system_cache_pool'

framework:
    cache:
        pools:
            doctrine.result_cache_pool:
                adapter: cache.adapter.apcu
            doctrine.system_cache_pool:
                adapter: cache.adapter.apcu