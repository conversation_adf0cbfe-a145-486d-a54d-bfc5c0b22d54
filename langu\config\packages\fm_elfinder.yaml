fm_elfinder:
    instances:
        # should be named "icons"
        seo_pages:
            locale: '%locale%'
            editor: form
            path_prefix: ''
#            include_assets: true
            visible_mime_types: ['image/svg+xml']
            connector:
                debug: true
                roots:
                    blog_posts:
                        driver: LocalFileSystem
                        path: '%kernel.project_dir%/public/uploads/seo/icons'
                        url: 'https://%main_domain%/uploads/seo/icons'
                        upload_allow: ['image/svg+xml']
                        upload_deny: ['all']
                        upload_max_size: 512K
                        alias: 'icons'
                        attributes:
                            - { pattern: '/(.*?)/', read: true, write: true, locked: true }

        pages:
            locale: '%locale%'
            editor: form
            path_prefix: ''
#            include_assets: true
            visible_mime_types: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif']
            connector:
                debug: true
                roots:
                    blog_posts:
                        driver: LocalFileSystem
                        path: '%kernel.project_dir%/public/uploads/pages/photos'
                        url: 'https://%main_domain%/uploads/pages/photos'
                        upload_allow:  ['image/png', 'image/jpg', 'image/jpeg', 'image/gif']
                        upload_deny: ['all']
                        upload_max_size: 2M
                        alias: 'images'

        blog_user:
            locale: '%locale%'
            editor: ckeditor
            path_prefix: ''
#            include_assets: true
            visible_mime_types: ['image/png', 'image/jpg', 'image/jpeg']
            connector:
                debug: true
                roots:
                    blog_posts:
                        driver: LocalFileSystem
                        path: '%kernel.project_dir%/public/uploads/blog/resources'
                        url: 'https://%main_domain%/uploads/blog/resources'
                        upload_allow: ['image/png', 'image/jpg', 'image/jpeg']
                        upload_deny: ['all']
                        upload_max_size: 2M
                        alias: 'blog resources'
                        attributes:
                            - { pattern: '/(.*?)/', read: true, write: true, locked: true }

        blog_admin:
            locale: '%locale%'
            editor: ckeditor
            path_prefix: ''
#            include_assets: true
            visible_mime_types: ['image/png', 'image/jpg', 'image/jpeg']
            connector:
                debug: true
                roots:
                    blog_posts:
                        driver: LocalFileSystem
                        path: '%kernel.project_dir%/public/uploads/blog/resources'
                        url: 'https://%main_domain%/uploads/blog/resources'
                        upload_allow: ['image/png', 'image/jpg', 'image/jpeg']
                        upload_deny: ['all']
                        upload_max_size: 10M
                        alias: 'blog resources'