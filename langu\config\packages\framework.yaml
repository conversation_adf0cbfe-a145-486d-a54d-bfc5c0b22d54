framework:
  secret: '%env(APP_SECRET)%'
#  router:
#    resource: '%kernel.project_dir%/config/routes.yaml'
#    strict_requirements: null
  form: null
  csrf_protection: null
  validation:
    enabled: true
    enable_annotations: true
  default_locale: '%locale%'
  trusted_hosts: null
  session:
    handler_id: snc_redis.session.handler
    cookie_domain: '.%main_domain%'
    name: L2SESSID
    save_path: null
  fragments: null
  http_method_override: true
  translator:
    enabled: true
    fallbacks:
      - en
    paths:
      - '%kernel.project_dir%/translations'
  assets:
    base_path: ''

  profiler: { enabled: true }
