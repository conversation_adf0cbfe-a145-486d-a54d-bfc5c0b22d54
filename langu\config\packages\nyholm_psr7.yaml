services:
    # Register nyholm/psr7 services for autowiring with PSR-17 (HTTP factories)
    Psr\Http\Message\RequestFactoryInterface: '@nyholm.psr7.psr17_factory'
    Psr\Http\Message\ResponseFactoryInterface: '@nyholm.psr7.psr17_factory'
    Psr\Http\Message\ServerRequestFactoryInterface: '@nyholm.psr7.psr17_factory'
    Psr\Http\Message\StreamFactoryInterface: '@nyholm.psr7.psr17_factory'
    Psr\Http\Message\UploadedFileFactoryInterface: '@nyholm.psr7.psr17_factory'
    Psr\Http\Message\UriFactoryInterface: '@nyholm.psr7.psr17_factory'

    # Register nyholm/psr7 services for autowiring with HTTPlug factories
    Http\Message\MessageFactory: '@nyholm.psr7.httplug_factory'
    Http\Message\RequestFactory: '@nyholm.psr7.httplug_factory'
    Http\Message\ResponseFactory: '@nyholm.psr7.httplug_factory'
    Http\Message\StreamFactory: '@nyholm.psr7.httplug_factory'
    Http\Message\UriFactory: '@nyholm.psr7.httplug_factory'

    nyholm.psr7.psr17_factory:
        class: Nyholm\Psr7\Factory\Psr17Factory

    nyholm.psr7.httplug_factory:
        class: Nyholm\Psr7\Factory\HttplugFactory
