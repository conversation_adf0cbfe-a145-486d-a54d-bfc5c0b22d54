# This file is auto-generated during the composer install
parameters:
 database_host: mysql
 database_port: null
 database_name: '%env(DATABASE_NAME)%'
 database_user: user
 database_password: pass
 admin_database_host: mysql
 admin_database_port: null
 admin_database_name: admin
 admin_database_user: user
 admin_database_password: pass
 mailer_transport: smtp
 mailer_host: smtp.gmail.com
 mailer_user: lan22<PERSON>@heylangu.com
 mailer_password: La22ngu!1999
 mailer_override_address: { }
 secret: zB9O1TwR7057OVfmrYAIbd69o286gQQp
 google_client_id: 1078588399151-8bsqun7dcat5bvd3gksd8u4nd9kfk8j0.apps.googleusercontent.com
 google_client_id_send_lesson: 1078588399151-8bsqun7dcat5bvd3gksd8u4nd9kfk8j0.apps.googleusercontent.com
 google_client_secret: 1qEx5tfZxy959EAglQe3x1Rq
 google_client_secret_send_lesson: 1qEx5tfZxy959EAglQe3x1Rq
 google_client_secret_calendar: OFcZofnHHZG_4_JZ639B6XRH
 google_client_secret_calendar_send_lessons: OFcZofnHHZG_4_JZ639B6XRH
 google_app_name: Langu
 google_app_scopes:
   - openid
   - email
   - profile
   - 'https://www.googleapis.com/auth/drive.file'
 tokbox_api_key: '45598702'
 tokbox_api_secret: 413564490366b0112122d235dd25b835b903a6bb
 tokbox_session_routed_mode: true
 mangopay_client_id: ''
 mangopay_client_password: ''
 mangopay_base_url: ''
 mangopay_debug_mode: true
 fixer_key: ********************************
 langu_exchange_rate_fee: 0.01
 multiplier_for_currencies: 1.02
 langu_wallets:
   EUR:
     wallet: 17988934
     user: 17988933
   GBP:
     wallet: 17988936
     user: 17988933
   USD:
     wallet: 17988935
     user: 17988933
   PLN:
     wallet: 17988937
     user: 17988933
 refund_fee: 0.15
 refund_compensation: 0.15
 refund_deductions_limit: 1.0
 first_email_business_page: <EMAIL>
 second_email_business_page: <EMAIL>
 third_email_business_page: <EMAIL>
 snippets:
   google_analytics: UA-81657453-1
   google_tag_manager: GTM-WB9P3RB
   google_remarketing: '876635808'
   zopim: heylangu.zendesk.com
   go_squared: GSN-194064-I
   hotjar: '341510'
   facebook: '1796598343952485'
   autopilot: 238ac849afb44d9d88ea2a73f96f56712e1ff8efcf7d4192952c39c9ee7f85f2
 router.request_context.host: heylangu.com
 router.request_context.scheme: https
 redis.host: redis
 redis.password: 12345
 redis.port: 6379
 liip_imagine_data_path:
   - '%kernel.project_dir%/public/'
   - '%kernel.project_dir%/public/'
   - '%kernel.project_dir%/public/uploads/'
 hosts_languages: '%env(json:HOSTS_LANGUAGES)%'
 #  hosts_languages:
 #    'https://langu.loc:4061': en
 #    'https://es.langu.loc:4061': es
 #    'https://pl.langu.loc:4061': pl
 main_domain: '%env(MAIN_DOMAIN)%'
 allowed_mimetypes_lib:
   - text/plain
   - text/richtext
   - application/rtf
   - image/jpg
   - image/jpeg
   - image/gif
   - image/png
   - application/pdf
   - /avi
   - video/msvideo
   - video/mpeg
   - video/H264
   - video/ogg
   - video/mp4
   - audio/mp3
   - audio/x-ms-wma
   - audio/wma
   - audio/mp4
   - audio/mpeg
   - audio/mpeg3
   - audio/x-mpeg-3
   - audio/ogg
   - application/vnd.ms-excel
   - application/msword
   - application/vnd.ms-excel
   - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
   - application/vnd.oasis.opendocument.text
   - application/vnd.oasis.opendocument.spreadsheet
   - application/vnd.oasis.opendocument.presentation
   - application/vnd.openxmlformats-officedocument.wordprocessingml.document
   - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
   - application/vnd.ms-powerpoint
   - application/vnd.openxmlformats-officedocument.presentationml.presentation
   - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
   - application/excel
   - application/x-excel
   - application/x-msexcel
   - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
   - text/plain
   - text/richtext
   - application/rtf
   - text/rtf
   - font/woff
   - image/jpg
   - image/jpeg
   - image/png
   - image/svg+xml
   - image/gif
   - image/tiff
   - image/x-tiff
   - application/pdf
   - application/zip
   - application/ppt
   - application/msword
   - application/vnd.ms-powerpoint
   - video/msvideo
   - video/mpeg
   - video/H264
   - video/ogg
   - video/mp4
   - audio/mp3
   - audio/mp4
   - audio/mpeg
   - audio/ogg
   - audio/wav
   - audio/wma
   - audio/aac
   - audio/avi
   - video/quicktime
   - video/x-quicktime
   - image/mov
   - audio/aiff
   - audio/x-midi
   - audio/x-wav
   - video/avi
   - application/mspowerpoint
   - application/xls
   - application/powerpoint
   - application/x-mspowerpoint
   - application/vnd.openxmlformats-officedocument.presentationml.presentation
   - application/vnd
   - application/openxmlformats-officedocument.spreadsheetml.shee
   - application/excel
   - application/vnd.ms-excel
   - text/vnd.ms-excel
   - application/x-excel
   - application/x-msexcel
   - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
   - text/mspowerpoint
   - text/powerpoint
   - text/x-mspowerpoint
   - text/vnd.openxmlformats-officedocument.presentationml.presentation
   - text/excel
   - text/x-excel
   - text/x-msexcel
   - text/vnd.openxmlformats-officedocument.spreadsheetml.sheet
 max_size_lib: 10485760
 google_p12_path: ../app/auth_langu_loc.json
 google_p12_path_for_command: app/auth_for_calendar_langu_loc.json.json
 google_p12_path_calendar: '%kernel.project_dir%/credentials.json'
 google_p12_path_calendar_send_lessons: '%kernel.project_dir%/credentials-send-lesson.json'
 google_p12_email: <EMAIL>
 google_userinfo_scopes:
   - 'https://www.googleapis.com/auth/userinfo.email'
   - 'https://www.googleapis.com/auth/userinfo.profile'
   - 'https://www.googleapis.com/auth/calendar'
 google_calendar_scopes:
   - 'https://www.googleapis.com/auth/calendar'
   - 'https://www.googleapis.com/auth/calendar.readonly'
 revolut_client_id: '%env(REVOLUT_CLIENT_ID)%'
 revolut_url_redirect: '%env(REVOLUT_URL_REDIRECT)%'
 revolut_type_account: '%env(REVOLUT_TYPE_ACCOUNT)%'
 revolut_is_test_account: '%env(bool:REVOLUT_IS_TEST_ACCOUNT)%'
 revolut_api_link: 'https://sandbox-b2b.revolut.com/api/1.0/payment-drafts'
 stripe_public_key: pk_test_MQIviMptDCcknTZ25dojL7Z400OHkru9Qw
 stripe_secret_key: sk_test_g6vOqeqLbTa7xN1cx9WocgI000sn4zLSBW
 p24_shop_id: 104797
 p24_key_crc: b2e3d4bd52d878e6
 p24_key_api: d130840fe50229e5ec3578151c7c0ed2
 p24_url_external: 'https://sandbox.przelewy24.pl/external/104797.wsdl'
 p24_url_register: 'https://sandbox.przelewy24.pl/trnRequest/'
 p24_url_check: 'https://langu.io/p24-check-status-transaction/'
 p24_url_payin: 'https://langu.io/checkout/p24-payin/'
 p24_url_payin_christmas_voucher: 'https://langu.io/checkout/p24-payin/christmas'
 voxeet_consumer_key: ZnM0OXNtMzIzbXBqMw==
 voxeet_consumer_secret_key: Mm00N3Vzc3V0MmRvOHFlcXRiMGU3cHQ0dGs=
 revolut_secret_key: '%kernel.project_dir%/%env(REVOLUT_SECRET_KEY)%'
 revolut_wallet_transfer_wise: '%env(REVOLUT_WALLET_TRANSFER_WISE)%'
 crisp_chat_id: 55bb224e-c6a1-4823-afa9-1f2de219d648
 crisp_chat_key: '71494ce4cdf5dcd92a9c3d90b0a1dc5cd0ede58bc2629ed246957994ca1044ad'
 crisp_website_id: 92670abc-f360-4e63-8298-4fbb034654cc
 old_classroom_date: '2020-05-05'
 max_file_size_user_settings: 6291456
 allowed_mimetypes_avatar:
   - image/jpg
   - image/jpeg
   - image/png
 allowed_mimetypes_user_settings_file:
   - image/jpg
   - image/jpeg
   - image/png
   - application/pdf
 domain_to_redirect: 'https://langu.io'
 mailchimp_transactional_api_key: TUBZk1xw9Fl5tAhRw5fOqg
 mailchimp_marketing_api_key: ************************************
 mailchimp_email: <EMAIL>
 mailchimp_prefix_server: us6
 mailchimp_list_id_langu_user: e9b361c101
 twilio_account_sid: **********************************
 twilio_api_key: **********************************
 twilio_api_secret_key: jvWMJcqvGx3XeeSMVnH3aRIvok99tHkc
