#web:
#    resource: "../src/Resources/config/routing.yaml"
#    prefix:   /api/

sitemap:
    path: /sitemap
    controller: App\AppBundle\Controller\SitemapController::generateAction
    methods: GET

language_switch:
    path: /language/{new_language}
    defaults: { _controller: AppBundle:Language:switch }

terms.student:
    path: /terms/student
    defaults: { _controller: App\AppBundle\Controller\PageController::staticPageAction, pageName: 'studentTerms' }

terms.teacher:
    path: /terms/teacher
    defaults: { _controller: App\AppBundle\Controller\PageController::staticPageAction, pageName: 'teacherTerms' }

about_us:
    path: /about-us
    defaults: { _controller: App\AppBundle\Controller\PageController::aboutUsPageAction }

methodology:
    path: /how-it-works
    defaults: { _controller: App\AppBundle\Controller\PageController::methodologyPageAction }

business_page:
    path: /business
    controller: App\AppBundle\Controller\BusinessPageController::businessPageAction
    methods: GET

faq:
    path: /faq
    controller: App\FaqBundle\Controller\FaqController::faqPageAction
    methods: GET

christmas.page:
    path: /christmas
    defaults: { _controller: AppBundle:ChristmasPage:ChristmasPage }

christmas.page_christmas-thankyou:
    path: /merry-christmas
    defaults: { _controller: AppBundle:ChristmasPage:ChristmasPage }

christmas.page.set.cookie:
    path: /christmas-set-cookie
    defaults: { _controller: UserBundle\Controller\ChristmasVoucherBannerController::setChristmasVoucherBannerHide }

education_page:
    path: /education
    controller: App\AppBundle\Controller\EducationPageController::educationPageAction
    methods: GET

_bazinga_jstranslation:
    resource: "@BazingaJsTranslationBundle/Resources/config/routing/routing.yml"

lesson:
    resource: "@LessonBundle/Resources/config/routing.yml"
    prefix:   /

langu:
    resource: "@AppBundle/Resources/config/routing.yml"
    prefix:   /

#mangopay:
#    resource: "@MangopayBundle/Controller/"
#    type:     annotation
#    prefix:   /mangopay

teaching:
    resource: "@TeachingBundle/Resources/config/routing.yml"
    prefix:   /

media:
    resource: "@MediaBundle/Resources/config/routing.yml"
    prefix:   /

message:
    resource: "@MessageBundle/Resources/config/routing.yml"
    prefix:   /

availability:
    resource: "@AvailabilityBundle/Resources/config/routing.yml"
    prefix:   /

intl:
    resource: "@IntlBundle/Resources/config/routing.yml"
    prefix:   /

money:
    resource: "@MoneyBundle/Resources/config/routing.yml"
    prefix:   /

google_api:
    resource: "@GoogleApiBundle/Resources/config/routing.yml"
    prefix:   /google

user:
    resource: "@UserBundle/Resources/config/routing.yml"
    prefix:   /user

app:
    resource: "@AppBundle/Controller/"
    type:     annotation

payments:
    resource: "@PaymentBundle/Resources/config/routing.yml"
    prefix: /

_liip_imagine:
    resource: "@LiipImagineBundle/Resources/config/routing.yaml"

oneup_uploader:
    resource: .
    type: uploader

landing:
    resource: "@LandingBundle/Resources/config/routing.yml"
    prefix:   /

#seo_page:
#    path: /{path}
#    defaults: { _controller: App\AppBundle\Controller\SeoPageController::showPageAction }

new.teacher.profile.view:
    path:     /teacher/{username}
    defaults: { _controller: UserBundle:Teacher:teacherProfile }

user.logout:
    path:     /user/logout
    defaults: { _controller: App\UserBundle\Controller\SecurityController::logoutAction }

user.login.google:
    path:     /user/login/google
    defaults: { _controller: App\UserBundle\Controller\SecurityController::googleSignInAction }

user.login.google.calendar:
    path:     /user/login/google-calendar
    defaults: { _controller: App\UserBundle\Controller\SecurityController::googleCalendarSignInAction }

user.login:
    path:     /user/login
    defaults: { _controller: App\UserBundle\Controller\SecurityController::loginAction }