# Learn more about services, parameters and containers at
# http://symfony.com/doc/current/book/service_container.html
imports:
    - resource: 'packages/pusher_parameters.yml'
    - { resource: '../src/UserBundle/Resources/config/services.yaml' }
    - { resource: '../src/AppBundle/Resources/config/services.yml' }
    - { resource: '../src/GoogleApiBundle/Resources/config/services.yml' }
    - { resource: '../src/MoneyBundle/Resources/config/services.yml' }
    - { resource: '../src/IntlBundle/Resources/config/services.yml' }
    - { resource: '../src/AvailabilityBundle/Resources/config/services.yml' }
    - { resource: '../src/MessageBundle/Resources/config/services.yml' }
    - { resource: '../src/MediaBundle/Resources/config/services.yml' }
    - { resource: '../src/TeachingBundle/Resources/config/services.yml' }
    - { resource: '../src/LessonBundle/Resources/config/services.yml' }
    - { resource: '../src/BlogBundle/Resources/config/services.yml' }
    - { resource: '../src/PaymentBundle/Resources/config/services.yml' }
#    - { resource: '../src/ApiBundle/Resources/config/services.yml' }
    - { resource: '../src/AdminBundle/Resources/config/services.yml' }

parameters:
    locale: 'en'
    web_dir: '%kernel.project_dir%/public'


services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Kernel.php'
            - '../src/Tests/'
            - '../src/Repository/'
            - '../src/UserBundle/Model'
            - '../src/UserBundle/Utils'
            - '../src/UserBundle/Twig'
            - '../src/AppBundle/CommandBus'
            - '../src/GoogleApiBundle/Factory'
            - '../src/ApiBundle/Resources'
            - '../src/ApiBundle/Listener/'
            - '../src/UserBundle/Listener/'
            - '../src/AdminBundle/Listener/'
            - '../src/UserBundle/Uploader/'
            - '../src/AvailabilityBundle/Listener/'

    monolog.formatter.custom_datetime:
        class: Monolog\Formatter\LineFormatter
        arguments:
            - "[%%datetime%%]\t%%extra.remote_addr%%\t%%level_name%%\t%%message%%\t%%extra.request_uri%%\n"
            - "Y-m-d H:i:s"
        calls:
            - [ includeStacktraces ]
        public: true

    App\AppBundle\Provider\MailchimpTransactionalClientProvider:
        class: App\AppBundle\Provider\MailchimpTransactionalClientProvider
        arguments:
          [ '%mailchimp_transactional_api_key%' ]

    App\AppBundle\Builder\Mailchimp\UserChangePasswordBuilder:
        arguments:
          [ '%mailchimp_email%' ]

    App\AppBundle\Builder\Mailchimp\OnBoardingBuilder:
        arguments:
          [ '%mailchimp_email%' ]

    App\AppBundle\Builder\Mailchimp\NewMessageBuilder:
        arguments:
          [ '%mailchimp_email%' ]

    App\AppBundle\Builder\Mailchimp\OneHourReminderEmailBuilder:
        arguments:
          [ '%mailchimp_email%' ]

    App\AppBundle\Builder\Mailchimp\CanceledLessonBuilder:
        arguments:
          [ '%mailchimp_email%' ]

    App\AppBundle\Builder\Mailchimp\UpdateAvailabilityBuilder:
        arguments:
          [ '%mailchimp_email%' ]

    App\AppBundle\Builder\Mailchimp\UserBookingLessonBuilder:
        arguments:
          [ '%mailchimp_email%' ]

    App\PaymentBundle\Service\AddTransactionService:
        autowire: true
        public: true

    App\Admin\Manager\PurchaseManager:
        class: App\Admin\Manager\PurchaseManager
        arguments:
            - "@doctrine.orm.default_entity_manager"
            - "@app.wallet_manager"
            - "%refund_fee%"
            - "%refund_compensation%"
            - '@App\PaymentBundle\Service\AddTransactionService'
            - '@App\LessonBundle\Repository\LessonRepository'
            - '@money.money_manager'
            - '@App\PaymentBundle\Provider\RefundTransactionProvider'
            - '@App\PaymentBundle\Revolut\Handler\TransferBetweenRevolutWalletsHandler'
            - '@monolog.logger.security'

    App\PaymentBundle\Revolut\Controller\RevolutTokenController:
        autowire: true

    App\PaymentBundle\Handler\CreatePayInVoucherHandler:
        class: App\PaymentBundle\Handler\CreatePayInVoucherHandler
        autowire: true
        tags:
            - { name: 'payment.payin.handler', type: 'christmasVoucher' }

    App\AppBundle\Handler\Command\DataForMailchimpCommandHandler:
        arguments:
            - '%mailchimp_list_id_langu_user%'
            - '@App\AppBundle\Provider\MailchimpMarketingClientProvider'
            - '@App\UserBundle\Repository\AbstractUserRepository'
            - '@App\AppBundle\Builder\Mailchimp\UserDataBuilder'
            - '@monolog.logger'
            - '@doctrine.orm.default_entity_manager'

    App\AppBundle\Provider\MailchimpMarketingClientProvider:
        class: App\AppBundle\Provider\MailchimpMarketingClientProvider
        arguments:
          [ '%mailchimp_marketing_api_key%', '%mailchimp_prefix_server%' ]

    App\UserBundle\Handler\CrispChatUpdateDataHandler:
        arguments:
            - '%crisp_website_id%'
            - '@App\UserBundle\Provider\CrispChatClientProvider'
            - '@App\UserBundle\Provider\UserProvider'
            - '@App\UserBundle\Builder\CrispChatUserProfileDataBuilder'
            - '@monolog.logger.security'
            - '@snc_redis.default'

    App\AdminBundle\Controller\RevolutTokenController:
        class: App\AdminBundle\Controller\RevolutTokenController
        arguments:
          [ '%revolut_is_test_account%', '@App\PaymentBundle\Revolut\Provider\RevolutProvider' ]

    App\UserBundle\Provider\CrispChatClientProvider:
        arguments:
            - '%crisp_chat_key%'
            - '%crisp_chat_id%'
            - '@monolog.logger.security'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\GoogleApiBundle\Provider\GoogleCalendarClientConfigurationProvider:
        arguments:
            - '%google_calendar_scopes%'
            - '%google_p12_path_calendar%'
            - '%google_app_name%'
            - '%google_client_secret_calendar%'

    App\GoogleApiBundle\Provider\GoogleCalendarClientConfigurationForSendLessonsProvider:
        arguments:
            - '%google_calendar_scopes%'
            - '%google_p12_path_calendar_send_lessons%'
            - '%google_app_name%'
            - '%google_client_secret_calendar_send_lessons%'

    App\AppBundle\Manager\PageManager:
        class: App\AppBundle\Manager\PageManager
        autowire: true

    App\PaymentBundle\Revolut\Provider\RevolutClientProvider:
        class: App\PaymentBundle\Revolut\Provider\RevolutClientProvider
        arguments:
          [
              '@App\PaymentBundle\Provider\RevolutTokenProvider',
              '%revolut_type_account%' ]

    App\AvailabilityBundle\Model\ReservationManager:
        class: App\AvailabilityBundle\Model\ReservationManager
        arguments:
          [
              '@doctrine',
              '@event_dispatcher',
              '@?',
              '@App\AvailabilityBundle\Model\AvailabilityManager',
              '@session'
          ]

    command_bus:
        alias: App\AppBundle\Interfaces\CommandBusInterface
        public: true

    App\AppBundle\Interfaces\CommandBusInterface:
        class: App\AppBundle\CommandBus\CommandBus
        public: true
        tags:
            - { name: command_bus, type: command }

    App\AppBundle\Handler\Query\TeacherListQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\AppBundle\Message\Query\TeacherListQueryMessage }

    App\AppBundle\Handler\Query\BusinessPageQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\AppBundle\Message\Query\BusinessPageQueryMessage }

    gedmo.listener.translatable:
        class: Gedmo\Translatable\TranslatableListener
        tags:
            - { name: doctrine.event_subscriber, connection: default }
        calls:
            - [ setAnnotationReader, [ '@annotation_reader' ] ]
        public: true

    app.base_manager:
        abstract: true
        arguments:
            - '@doctrine'
            - '@event_dispatcher'
            - null

    App\AvailabilityBundle\Model\AvailabilityManager:
        parent: app.base_manager
        class: App\AvailabilityBundle\Model\AvailabilityManager
        autowire: true
        public: true


    App\LessonBundle\Utils\OpenTokHelper:
        class: App\LessonBundle\Utils\OpenTokHelper
        arguments:
            - '%tokbox_api_key%'
            - '%tokbox_api_secret%'
            - '%tokbox_session_routed_mode%'

    App\AppBundle\Form\ContactFormType:
        class: App\AppBundle\Form\ContactFormType
        autowire: true
        tags:
            - { name: form.type }

    App\IntlBundle\Form\UiLanguageType:
        class: App\IntlBundle\Form\UiLanguageType
        arguments:
            - '@translator'
            - '%hosts_languages%'
        tags:
            - { name: form.type }

    App\AppBundle\Services\FileDownloader:
        class: App\AppBundle\Services\FileDownloader
        arguments:
            - '%kernel.project_dir%/public/'

    App\AppBundle\Utils\DomainResolver:
        class: App\AppBundle\Utils\DomainResolver
        arguments:
            - '%hosts_languages%'

    App\AppBundle\Twig\Extension\InlineSvgExtension:
        class: App\AppBundle\Twig\Extension\InlineSvgExtension
        arguments:
            - '%kernel.project_dir%'
        tags:
            - { name: twig.extension }

    App\MoneyBundle\Model\MoneyManager:
        class: App\MoneyBundle\Model\MoneyManager
        arguments:
          [ '@security.token_storage', '@App\MoneyBundle\Model\ExchangeRateManager','%multiplier_for_currencies%' ]

    form.purchase.refund:
        class: App\AdminBundle\Form\PurchaseRefundFormType
        arguments:
            - "%refund_fee%"
            - "%refund_compensation%"
            - "%refund_deductions_limit%"
        tags:
            - { name: form.type }

    App\LessonBundle\Handler\Classroom\LessonDataHandler:
        arguments:
            - '%pusher_key%'
            - '%tokbox_api_key%'
            - '%voxeet_consumer_key%'
            - '%voxeet_consumer_secret_key%'
            - '@App\LessonBundle\Provider\LessonProvider'
            - '@App\LessonBundle\Factory\Classroom\LessonDataFactory'
            - '@App\LessonBundle\Handler\OpenTokHandler'
            - '@App\LessonBundle\Provider\VideoClassroomProvider'
            - '@App\AppBundle\Handler\Twilio\TwilioAccessTokenHandler'
            - '@event_dispatcher'

    App\PaymentBundle\Stripe\Provider\AccessToApiStripeProvider:
        class: App\PaymentBundle\Stripe\Provider\AccessToApiStripeProvider
        arguments:
          [ '%stripe_secret_key%' ]

    App\MessageBundle\Utils\MessageFile\Helper:
        class: App\MessageBundle\Utils\MessageFile\Helper
        arguments:
          [ '@router', '%kernel.project_dir%' ]

    Redis: '@snc_redis.default'

    App\AppBundle\Handler\LiipImagineHandler:
        arguments:
            - '@liip_imagine.service.filter'
            - '%kernel.project_dir%'

    App\UserBundle\Handler\GoogleUrlHandler:
        arguments:
          [ '@google.client.factory', '%google_state_key%', '@session', '@App\GoogleApiBundle\Provider\GoogleCalendarClientConfigurationProvider' ]

    App\AppBundle\Handler\Twilio\TwilioAccessTokenHandler:
        arguments:
          [ '%twilio_account_sid%', '%twilio_api_key%', '%twilio_api_secret_key%', '@App\AppBundle\Generator\UUIDGenerator' ]

    App\UserBundle\Validator\UserSettings\BasicInfoValidator:
        arguments:
            - '@App\UserBundle\Provider\TaxCountriesProvider'
            - '%max_file_size_user_settings%'
            - '%allowed_mimetypes_avatar%'

    App\UserBundle\Validator\UserSettings\UserSettingsFileValidator:
        arguments:
            - '%max_file_size_user_settings%'
            - '%allowed_mimetypes_user_settings_file%'

    App\UserBundle\Validator\UserSettings\TeacherQualificationValidator:
        arguments:
            - '%max_file_size_user_settings%'
            - '%allowed_mimetypes_user_settings_file%'
            - '@App\UserBundle\Repository\TeachingQualificationRepository'

    Liip\ImagineBundle\Service\FilterService:
        alias: 'liip_imagine.service.filter'

    App\MessageBundle\Command\:
        resource: '../src/MessageBundle/Command'

    App\LessonBundle\Command\:
        resource: '../src/LessonBundle/Command'

    App\MoneyBundle\Command\:
        resource: '../src/MoneyBundle/Command'

    App\AppBundle\Command\:
        resource: '../src/AppBundle/Command'

    App\AppBundle\Controller\:
        resource: '../src/AppBundle/Controller'
        tags: ['controller.service_arguments']

    App\MediaBundle\Controller\:
        resource: '../src/MediaBundle/Controller'
        tags: ['controller.service_arguments']

    App\AvailabilityBundle\Controller\:
        resource: '../src/AvailabilityBundle/Controller'
        tags: ['controller.service_arguments']

    App\GoogleApiBundle\Controller\:
        resource: '../src/GoogleApiBundle/Controller'
        tags: [ 'controller.service_arguments' ]

    App\AppBundle\Utils\SearchHelper\:
        resource: '../src/AppBundle/Utils/SearchHelper.php'
        tags: ['utils.service_arguments']

    App\TeachingBundle\Controller\:
        resource: '../src/TeachingBundle/Controller'
        tags: ['controller.service_arguments']

    App\TeachingBundle\Repository\:
        resource: '../src/TeachingBundle/Repository'

    App\BlogBundle\Controller\:
        resource: '../src/BlogBundle/Controller'
        tags: ['controller.service_arguments']

    App\TeachingBundle\Widgets\BookingWidget\BookingWidget\:
        resource: '../src/TeachingBundle/Widgets/BookingWidget/BookingWidget.php'
        tags: ['widgets.service_arguments']

    App\LessonBundle\Listener\:
        resource: '../src/LessonBundle/Listener'
        tags: ['listener.service_arguments']

    App\LessonBundle\Controller\:
        resource: '../src/LessonBundle/Controller'
        tags: ['controller.service_arguments']

    App\LessonBundle\Repository\:
        resource: '../src/LessonBundle/Repository'

    App\LessonBundle\Model\FileLessonManager\:
        resource: '../src/LessonBundle/Model/FileLessonManager.php'
        tags: ['model.service_arguments']

    App\MessageBundle\Twig\Extension\:
        resource: '../src/MessageBundle/Twig/Extension/MessagesExtension.php'
        tags: ['twig.extension']

    App\MessageBundle\Model\:
        resource: '../src/MessageBundle/Model'
        tags: ['model.service_arguments']

    App\MessageBundle\Controller\:
        resource: '../src/MessageBundle/Controller'
        tags: ['controller.service_arguments']

    App\MessageBundle\Model\MessageManager\:
        resource: '../src/MessageBundle/Model/MessageManager.php'
        tags: ['message_manager.service_arguments']

    App\AvailabilityBundle\Widgets\AvailabilityCalendar\:
        resource: '../src/AvailabilityBundle/Widgets/AvailabilityCalendar'
        tags: ['widgets.service_arguments']

    App\AvailabilityBundle\Repository\:
        resource: '../src/AvailabilityBundle/Repository'

    App\UserBundle\Controller\:
        resource: '../src/UserBundle/Controller'
        tags: ['controller.service_arguments']

    App\UserBundle\Repository\:
        resource: '../src/UserBundle/Repository'

    App\UserBundle\Service\:
        resource: '../src/UserBundle/Service/'
        tags: ['service.service_arguments']

    App\UserBundle\Formatter\UserInformationFormatter\:
        resource: '../src/UserBundle/Formatter/UserInformationFormatter.php'
        tags: ['formatter.service_arguments']

    App\IntlBundle\Repository\:
        resource: '../src/IntlBundle/Repository'

    App\PaymentBundle\Controller\:
        resource: '../src/PaymentBundle/Controller'
        tags: ['controller.service_arguments']

    App\PaymentBundle\Repository\:
        resource: '../src/PaymentBundle/Repository'

    App\PaymentBundle\Provider\:
        resource: '../src/PaymentBundle/Provider'

    App\PaymentBundle\Service\:
        resource: '../src/PaymentBundle/Service'

    App\PaymentBundle\Stripe\Service\:
        resource: '../src/PaymentBundle/Stripe/Service'

    App\PaymentBundle\Stripe\Controller\:
        resource: '../src/PaymentBundle/Stripe/Controller'
        tags: ['controller.service_arguments']

    App\PaymentBundle\P24\Controller\:
        resource: '../src/PaymentBundle/P24/Controller'
        tags: ['controller.service_arguments']

    App\PaymentBundle\Calculator\:
        resource: '../src/PaymentBundle/Calculator'

    App\PaymentBundle\Command\:
        resource: '../src/PaymentBundle/Command'

    App\PaymentBundle\P24\Service\:
        resource: '../src/PaymentBundle/P24/Service'

    App\PaymentBundle\P24\Calculator\:
        resource: '../src/PaymentBundle/P24/Calculator'

    App\PaymentBundle\P24\Factory\:
        resource: '../src/PaymentBundle/P24/Factory'

    App\PaymentBundle\Handler\:
        resource: '../src/PaymentBundle/Handler'

    App\PaymentBundle\Builder\:
        resource: '../src/PaymentBundle/Builder'

    App\PaymentBundle\Persister\:
        resource: '../src/PaymentBundle/Persister'

    App\PaymentBundle\Helper\:
        resource: '../src/PaymentBundle/Helper'

    App\PaymentBundle\UserCreditSystem\Controller\:
        resource: '../src/PaymentBundle/UserCreditSystem/Controller'
        tags: ['controller.service_arguments']

    App\UserBundle\Command\:
        resource: '../src/UserBundle/Command'

    App\UserBundle\Factory\:
        resource: '../src/UserBundle/Factory'

    gedmo.listener.softdeleteable:
        public: true
        class: Gedmo\SoftDeleteable\SoftDeleteableListener
        tags:
            - { name: doctrine.event_subscriber, connection: default }
        calls:
            - [ setAnnotationReader, [ '@annotation_reader' ] ]

    Gedmo\Translatable\TranslatableListener:
        public: true
        class: Gedmo\Translatable\TranslatableListener
        tags:
            - { name: doctrine.event_subscriber, connection: default }
        calls:
            - [ setAnnotationReader, [ '@annotation_reader' ] ]

    twig.extension.intl:
        public: true
        class: Twig_Extensions_Extension_Intl
        tags:
            - { name: twig.extension }

    Pusher\Pusher:
        class: Pusher\Pusher
        arguments:
            $auth_key: '%pusher_key%'
            $secret: '%pusher_secret%'
            $app_id: '%pusher_app_id%'
            $options: ["%pusher_options%"]

    App\MessageBundle\Handler\Query\ThreadListViewQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\MessageBundle\Message\ThreadListViewQueryMessage }

    App\MessageBundle\Handler\Query\ThreadViewQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\MessageBundle\Message\ThreadViewQueryMessage }

    App\MessageBundle\Handler\Command\MessageSendCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\MessageBundle\Message\MessageSendCommandMessage }

    App\MessageBundle\Handler\Command\MessageDeleteCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\MessageBundle\Message\MessageDeleteCommandMessage }

    App\MessageBundle\Handler\Command\MessageFileCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\MessageBundle\Message\MessageFileCommandMessage }

    App\MessageBundle\Handler\Command\ThreadViewCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\MessageBundle\Message\ThreadViewCommandMessage }

    App\MessageBundle\Handler\Command\ThreadCreateCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\MessageBundle\Message\ThreadCreateCommandMessage }

    App\LessonBundle\Handler\Classroom\Query\AssetQueryViewHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\Query\AssetQueryViewMessage }

    App\LessonBundle\Handler\Classroom\Command\AssetCreateCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\Command\AssetCreateCommandMessage }

    App\LessonBundle\Handler\Classroom\Command\AssetUpdateCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\Command\AssetUpdateCommandMessage }

    App\LessonBundle\Handler\Classroom\Command\AssetDeleteCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\Command\AssetDeleteCommandMessage }

    App\MessageBundle\Handler\NewThreadHandler:
        autowire: true

    App\UserBundle\Handler\UserRegistration\Command\ConfirmRegistrationUserCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\UserBundle\Message\ConfirmRegistrationUserMessage }

    App\LessonBundle\Handler\Query\UpcomingLessonViewQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\UpcomingLessonViewQueryMessage }

    App\LessonBundle\Handler\Query\PastLessonViewQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\PastLessonViewQueryMessage }

    App\LessonBundle\Handler\Query\UnscheduledLessonViewQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\UnscheduledLessonViewQueryMessage }

    App\LessonBundle\Handler\Command\BookUnscheduledLessonCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\BookUnscheduledLessonCommandMessage }

    App\LessonBundle\Handler\Query\CalendarMyLessonQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\CalendarMyLessonQueryMessage }

    App\LessonBundle\Handler\Query\MyLessonByDateQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\MyLessonByDateQueryMessage }

    App\LessonBundle\Handler\Query\StudentInfoQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\StudentInfoQueryMessage }

    App\LessonBundle\Handler\Command\CancelLessonCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\CancelLessonCommandMessage }

    App\LessonBundle\Handler\Command\FinishLessonCommandHandler:
        class: App\LessonBundle\Handler\Command\FinishLessonCommandHandler
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\FinishLessonCommandMessage }
        autowire: true

    App\AppBundle\Handler\Query\PdfQueryMessageHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\AppBundle\Message\PdfQueryMessage }

    App\AppBundle\Handler\PDF\WhiteBoardTemplateHandler:
        tags:
            - { name: 'pdf-template', type: 'white-board' }
        autowire: true

    App\LessonBundle\Handler\Command\NewFeedbackCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\Command\CreateFeedbackCommandMessage }

    App\LessonBundle\Handler\FeedbackHandler:
        autowire: true

    App\PaymentBundle\Provider\UrlFailedProvider:
        class: App\PaymentBundle\Provider\UrlFailedProvider
        arguments:
          [ '@App\UserBundle\Provider\UserProvider', '@router.default', '%domain_to_redirect%' ]

    App\LessonBundle\Handler\Query\DataForFeedbackFormQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\LessonBundle\Message\Query\DataForFeedbackFormQueryMessage }

    App\UserBundle\Handler\TeacherSortingFeedbackTagDataHandler:
        autowire: true

    App\AppBundle\Handler\Query\BusinessPageTeachersQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\AppBundle\Message\Query\BusinessPageTeachersQueryMessage }

    App\AppBundle\Handler\Query\BusinessPageRatingsQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\AppBundle\Message\Query\BusinessPageRatingsQueryMessage }

    App\AppBundle\Handler\Command\BusinessPageFormCommandHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\AppBundle\Message\Command\BusinessPageFormCommandMessage }

    App\AppBundle\Handler\Query\EducationPageQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\AppBundle\Message\Query\EducationPageQueryMessage }

    App\AppBundle\Handler\Query\EducationPageTeachersQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\AppBundle\Message\Query\EducationPageTeachersQueryMessage }

    App\AppBundle\Handler\Query\EducationPageRatingsQueryHandler:
        autowire: true
        tags:
            - { name: command_handler, handles: App\AppBundle\Message\Query\EducationPageRatingsQueryMessage }

    App\AppBundle\Services\ContactFormSendEmailService:
        arguments:
          [
              '@twig',
              '@swiftmailer.mailer.default',
              '%first_email_business_page%',
              '%second_email_business_page%',
              '%third_email_business_page%'
          ]

    App\MessageBundle\Validator\MessageFileCommandValidator:
        arguments:
            - '%max_size_lib%'
            - '%allowed_mimetypes_lib%'
            - '@App\MessageBundle\Repository\MessageORMRepository'

    user.security.utils.google_auth.token_manager:
        class: App\UserBundle\Security\Utils\GoogleAuthTokenManager

    google_auth.authentication.failure_handler:
        class: App\UserBundle\Security\Authentication\Handler\GoogleAuthFailureHandler
        arguments:
            - '@router'
            - '@App\UserBundle\Handler\UserLogin\UserLoginHandler'
            - '@App\UserBundle\Security\Authentication\Provider\GoogleUrlToRedirectProvider'
            - '@session'

    google_auth.authentication.success_handler:
        class: App\UserBundle\Security\Authentication\Handler\GoogleAuthSuccessHandler
        arguments: [ '@router', '@App\UserBundle\Security\Authentication\Provider\GoogleUrlToRedirectProvider' ]

    google_auth.security.user.provider:
        class: App\UserBundle\Security\User\Provider\GoogleUserProvider
        arguments: [ '@App\UserBundle\Model\UserManager', '@security.token_storage' ]

    google_auth.security.authentication.listener:
        class: App\UserBundle\Security\Firewall\GoogleAuthListener
        public: false
        abstract: true
        parent: security.authentication.listener.abstract

    google_auth.security.authentication.provider:
        class: App\UserBundle\Security\Authentication\Provider\GoogleAuthProvider
        arguments: [ '@google_auth.security.user.provider' ]

    google_auth.security.authentication.entry_point:
        class: App\UserBundle\Security\Authentication\EntryPoint\GoogleAuthEntryPoint
        public: false
        abstract: true
        arguments: [ '@http_kernel', '@security.http_utils' ]

    App\UserBundle\Security\Authentication\Provider\GoogleUrlToRedirectProvider:
        arguments:
            - '%domain_to_redirect%'
            - '@App\UserBundle\Provider\StudentInformationProvider'
            - '@router'

    App\GoogleApiBundle\Factory\GoogleClientFactory:
        class: App\GoogleApiBundle\Factory\GoogleClientFactory
        arguments:
            - '@monolog.logger.security'
            - '@router'
            - '%google_client_id%'
            - '%google_client_secret%'
            - '%google_app_name%'
            - '%google_app_scopes%'

    App\GoogleApiBundle\Factory\GoogleServiceFactory:
        class: App\GoogleApiBundle\Factory\GoogleServiceFactory

    App\GoogleApiBundle\Factory\GoogleCalendarFactory:
        class: App\GoogleApiBundle\Factory\GoogleCalendarFactory
        autowire: true

    google.user_info.factory:
        class: App\GoogleApiBundle\Factory\GoogleUserInfoFactory
        arguments:
            - '@router'
            - '%google_client_id%'
            - '%google_client_secret%'
            - '%google_userinfo_scopes%'
            - '%google_p12_path%'
            - '%google_app_name%'
            - '%google_p12_email%'

    App\GoogleApiBundle\Persister\GoogleCalendarPersister:
        class: App\GoogleApiBundle\Persister\GoogleCalendarPersister
        autowire: true


    App\GoogleApiBundle\Builder\GoogleCalendarNotificationBuilder:
        autowire: true

    App\GoogleApiBundle\Handler\GoogleCalendarHandler:
        class: App\GoogleApiBundle\Handler\GoogleCalendarHandler
        autowire: true

    App\GoogleApiBundle\Controller\GoogleCalendarController:
        class: App\GoogleApiBundle\Controller\GoogleCalendarController
        autowire: true

    App\GoogleApiBundle\Provider\GoogleCalendarProvider:
        autowire: true

    App\GoogleApiBundle\Controller\CallbackController:
        autowire: true
        tags: [ 'controller.service_arguments' ]

    App\GoogleApiBundle\Handler\GoogleCalendarTokenHandler:
        autowire: true

    App\GoogleApiBundle\Command\GoogleTokenCalendarCommand:
        arguments:
            - '@App\GoogleApiBundle\Provider\GoogleCalendarClientConfigurationForSendLessonsProvider'
            - '%google_p12_path_calendar_send_lessons%'
        tags:
            - { name: console.command }

    user.listener.user_request:
        class: App\UserBundle\Listener\UserRequestListener
        autowire: true
        tags:
            - { name: kernel.event_listener, event: kernel.request, priority: 1 }

    user.listener.user_login:
        class: App\UserBundle\Listener\UserLoginListener
        tags:
            - { name: kernel.event_listener, event: security.interactive_login, priority: 7 }

    App\UserBundle\Subscriber\ChristmasVoucherSubscriber:
        class: App\UserBundle\Subscriber\ChristmasVoucherSubscriber
        calls:
            - [ 'setContainer', [ '@service_container' ] ]
        tags:
            - { name: kernel.event_subscriber }
        autowire: true

    user.uploader.listener.abstract_upload:
        abstract: true
        arguments:
            - '@App\UserBundle\Model\UserManager'
            - '@security.token_storage'
            - '@session'

    user.uploader.listener.user_cv_upload:
        parent: user.uploader.listener.abstract_upload
        class: App\UserBundle\Uploader\Listener\UserCVUploadListener
        tags:
            - { name: kernel.event_subscriber }

    user.uploader.listener.user_resources_upload:
        parent: user.uploader.listener.abstract_upload
        class: App\UserBundle\Uploader\Listener\UserResourceUploadListener
        tags:
            - { name: kernel.event_subscriber }
        autowire: true

    user.uploader.user_cv_namer:
        class: App\UserBundle\Uploader\Namer\UserCVNamer
        arguments:
            - '@security.token_storage'

    media.uploader.name.resource_namer:
        class: App\MediaBundle\Uploader\Namer\ResourceNamer
        arguments:
            - '@security.token_storage'
        public: true

    App\MediaBundle\Model\ResourceManager:
        class: App\MediaBundle\Model\ResourceManager
        parent: app.base_manager

    App\MediaBundle\Handler\ResourceHandler:
        autowire: true

    App\PaymentBundle\Builder\PayoutBuilder:
        class: App\PaymentBundle\Builder\PayoutBuilder
        tags:
            - { name: 'payment.payout', type: 'rev' }
        autowire: true

    App\PaymentBundle\TransferWise\Builder\PayOutTransferWiseBuilder:
        class: App\PaymentBundle\TransferWise\Builder\PayOutTransferWiseBuilder
        tags:
            - { name: 'payment.payout', type: 'tw' }
        autowire: true

    App\PaymentBundle\TransferWise\Handler\PayOutHandler:
        class: App\PaymentBundle\TransferWise\Handler\PayOutHandler
        autowire: true

    App\AppBundle\Calculator\TotalAvailableUnbookedSlotsCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\LastLoginTeacherCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\TrialConversionRateCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\LessonCanceledRateCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\NumberOfStudentsBookedLessonsCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\NumberOf5StarReviewsCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\PercentWrittenReviewsCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\LessonsTaughtCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\EarningsPerMonthCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\NumberOfRefundedPurchasesCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\NewestTeachersCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\AvgNumberLessonsPerStudentCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Calculator\NumberOfCoursesCalculator:
        tags:
            - { name: 'recommended.sorting' }
        autowire: true

    App\AppBundle\Handler\Email\UserChangePasswordHandler:
        class: App\AppBundle\Handler\Email\UserChangePasswordHandler
        arguments:
            - '@translator.default'
            - '@App\AppBundle\Provider\MailchimpTransactionalClientProvider'
            - '@monolog.logger.security'
            - '@Gedmo\Translatable\TranslatableListener'
            - '@App\AppBundle\Builder\Mailchimp\UserChangePasswordBuilder'
            - '@router.default'
            - '%domain_to_redirect%'

    App\GoogleApiBundle\Factory\GoogleCalendarChannelFactory:
        class: App\GoogleApiBundle\Factory\GoogleCalendarChannelFactory
        arguments:
            - '%main_domain%'

    App\LessonBundle\Handler\Command\FinishLesson\FinishLessonRedirectHandler:
        class: App\LessonBundle\Handler\Command\FinishLesson\FinishLessonRedirectHandler
        arguments:
            - '@router'
            - '%domain_to_redirect%'

    App\PaymentBundle\Provider\UrlProvider:
        arguments:
            - '%domain_to_redirect%'
            - '@router.default'