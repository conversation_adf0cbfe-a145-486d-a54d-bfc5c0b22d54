<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20190812142138 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE fos_user_user 
        ADD created_at DATETIME NOT NULL,
        ADD updated_at DATETIME NOT NULL,
        ADD date_of_birth DATETIME DEFAULT NULL, 
        ADD firstname VARCHAR(64) DEFAULT NULL,
        ADD lastname VARCHAR(64) DEFAULT NULL,
        ADD website VARCHAR(64) DEFAULT NULL, 
        ADD biography VARCHAR(1000) DEFAULT NULL,
        ADD gender VARCHAR(1) DEFAULT NULL,
        ADD locale VARCHAR(8) DEFAULT NULL,
         ADD timezone VARCHAR(64) DEFAULT NULL,
         ADD phone VARCHAR(64) DEFAULT NULL,
         ADD facebook_uid VARCHAR(255) DEFAULT NULL, 
         ADD facebook_name VARCHAR(255) DEFAULT NULL,
         ADD facebook_data LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:json)\', 
         ADD twitter_uid VARCHAR(255) DEFAULT NULL,
         ADD twitter_name VARCHAR(255) DEFAULT NULL, 
         ADD twitter_data LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:json)\',
         ADD gplus_uid VARCHAR(255) DEFAULT NULL, 
         ADD gplus_name VARCHAR(255) DEFAULT NULL,
         ADD gplus_data LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:json)\', 
         ADD token VARCHAR(255) DEFAULT NULL,
         ADD two_step_code VARCHAR(255) DEFAULT NULL, 
         CHANGE username username VARCHAR(180) NOT NULL, 
         CHANGE username_canonical username_canonical VARCHAR(180) NOT NULL,
         CHANGE email email VARCHAR(180) NOT NULL,
         CHANGE email_canonical email_canonical VARCHAR(180) NOT NULL, 
         CHANGE salt salt VARCHAR(255) DEFAULT NULL,
         CHANGE confirmation_token confirmation_token VARCHAR(180) DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_C560D761C05FB297 ON fos_user_user (confirmation_token)');
        $this->addSql('ALTER TABLE fos_user_group CHANGE name name VARCHAR(180) NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE fos_user_group CHANGE name name VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci');
        $this->addSql('DROP INDEX UNIQ_C560D761C05FB297 ON fos_user_user');
        $this->addSql('ALTER TABLE fos_user_user
    DROP created_at, DROP updated_at,
    DROP firstname,
    DROP lastname,
    DROP website,
    DROP biography,
    DROP gender,
    DROP locale,
    DROP timezone,
    DROP phone,
    DROP facebook_uid,
    DROP facebook_name,
    DROP facebook_data,
    DROP twitter_uid,
    DROP twitter_name,
    DROP twitter_data,
    DROP gplus_uid,
    DROP gplus_name,
    DROP gplus_data,
    DROP token,
    DROP two_step_code,
    CHANGE username username VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci,
    CHANGE username_canonical username_canonical VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci,
    CHANGE email email VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci,
    CHANGE email_canonical email_canonical VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci,
    CHANGE salt salt VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci,
    CHANGE confirmation_token confirmation_token VARCHAR(255) DEFAULT NULL COLLATE utf8_unicode_ci,
    CHANGE date_of_birth expires_at DATETIME DEFAULT NULL');
    }
}
