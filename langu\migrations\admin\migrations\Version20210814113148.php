<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * This migration adds a new user_tag table and adds ManyToMany user__tags relationships between
 * fos_user_user and user_tag tables. It also deletes feature table.
 */
final class Version20210814113148 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE user__tags (user_id INT NOT NULL, tag_id INT NOT NULL, INDEX IDX_D05B1F65A76ED395 (user_id), INDEX IDX_D05B1F65BAD26311 (tag_id), PRIMARY KEY(user_id, tag_id)) DEFAULT CHARACTER SET UTF8 COLLATE UTF8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_tag (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) DEFAULT NULL, createdAt DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET UTF8 COLLATE UTF8_unicode_ci ENGINE = InnoDB');
        $this->addSql('ALTER TABLE user__tags ADD CONSTRAINT FK_D05B1F65A76ED395 FOREIGN KEY (user_id) REFERENCES fos_user_user (id)');
        $this->addSql('ALTER TABLE user__tags ADD CONSTRAINT FK_D05B1F65BAD26311 FOREIGN KEY (tag_id) REFERENCES user_tag (id)');
        $this->addSql('DROP TABLE feature');
    }

    public function down(Schema $schema) : void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE user__tags DROP FOREIGN KEY FK_D05B1F65BAD26311');
        $this->addSql('CREATE TABLE feature (id INT AUTO_INCREMENT NOT NULL, seo_page_id INT DEFAULT NULL, heading VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci, icon VARCHAR(2083) NOT NULL COLLATE utf8_unicode_ci, content LONGTEXT NOT NULL COLLATE utf8_unicode_ci, INDEX IDX_1FD77566A57F59AF (seo_page_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('DROP TABLE user__tags');
        $this->addSql('DROP TABLE user_tag');
    }
}
