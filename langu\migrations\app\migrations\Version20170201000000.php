<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20170201000000 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE transaction (id INT AUTO_INCREMENT NOT NULL, authorId INT NOT NULL, creditedUserId INT NOT NULL, creditedWalletId INT NOT NULL, debitedFunds INT NOT NULL, creditedFunds INT NOT NULL, fees INT NOT NULL, status VARCHAR(255) NOT NULL, resultCode INT NOT NULL, resultMessage VARCHAR(255) NOT NULL, executionDate DATETIME NOT NULL, type VARCHAR(255) NOT NULL, nature VARCHAR(255) NOT NULL, cardType VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE langu_feedback (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, area SMALLINT NOT NULL, comment VARCHAR(2000) NOT NULL, created_at DATETIME NOT NULL, INDEX IDX_16FA03FFA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE page (id INT AUTO_INCREMENT NOT NULL, template_name VARCHAR(255) NOT NULL, page_name VARCHAR(20) NOT NULL, content LONGTEXT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, UNIQUE INDEX UNIQ_140AB620D9E762DC (page_name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mangopay_wallet (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, currency_id INT NOT NULL, wallet_id VARCHAR(40) NOT NULL, balance NUMERIC(15, 4) NOT NULL, main TINYINT(1) NOT NULL, walletType INT NOT NULL, UNIQUE INDEX UNIQ_5BB5BE6712520F3 (wallet_id), INDEX IDX_5BB5BE6A76ED395 (user_id), INDEX IDX_5BB5BE638248176 (currency_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE langu_user (id INT AUTO_INCREMENT NOT NULL, currency_id INT NOT NULL, language_to_learn_id INT DEFAULT NULL, username VARCHAR(255) NOT NULL, password CHAR(60) NOT NULL, first_name VARCHAR(255) NOT NULL, last_name VARCHAR(255) NOT NULL, email VARCHAR(255) DEFAULT NULL, google_email VARCHAR(255) NOT NULL, locale VARCHAR(12) NOT NULL, timezone VARCHAR(36) NOT NULL, mp_user_id VARCHAR(255) DEFAULT NULL, picture VARCHAR(2083) NOT NULL, google_id VARCHAR(255) NOT NULL, google_refresh_token VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, last_login_at DATETIME DEFAULT NULL, last_notification_at DATETIME DEFAULT NULL, userType INT NOT NULL, proficiency SMALLINT DEFAULT NULL, learning_for_career TINYINT(1) DEFAULT NULL, learning_for_education TINYINT(1) DEFAULT NULL, learning_for_life TINYINT(1) DEFAULT NULL, teacher_preference SMALLINT DEFAULT NULL, mangopay_type SMALLINT DEFAULT NULL, proficiency_preferred SMALLINT DEFAULT NULL, teaching_for_career TINYINT(1) DEFAULT NULL, teaching_for_education TINYINT(1) DEFAULT NULL, teaching_for_life TINYINT(1) DEFAULT NULL, commission SMALLINT DEFAULT NULL, free_trial TINYINT(1) DEFAULT NULL, cv VARCHAR(255) DEFAULT NULL, linked_in_url VARCHAR(255) DEFAULT NULL, facts_about LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:array)\', what_to_expect TEXT DEFAULT NULL, youtube_video_id VARCHAR(16) DEFAULT NULL, twitter_username VARCHAR(20) DEFAULT NULL, short_summary VARCHAR(100) DEFAULT NULL, long_summary VARCHAR(400) DEFAULT NULL, teaching_background TEXT DEFAULT NULL, general_background TEXT DEFAULT NULL, confirmed TINYINT(1) DEFAULT NULL, availability_reminder TINYINT(1) DEFAULT NULL, UNIQUE INDEX UNIQ_A54214B0F85E0677 (username), UNIQUE INDEX UNIQ_A54214B07E7F757B (google_email), UNIQUE INDEX UNIQ_A54214B08134C9E2 (mp_user_id), UNIQUE INDEX UNIQ_A54214B076F5C865 (google_id), UNIQUE INDEX UNIQ_A54214B0FD3B3BD7 (google_refresh_token), INDEX IDX_A54214B038248176 (currency_id), INDEX IDX_A54214B097B41181 (language_to_learn_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE langu_user__native_languages (native_language_user_id INT NOT NULL, native_language_id INT NOT NULL, INDEX IDX_10831A9EA52735B9 (native_language_user_id), INDEX IDX_10831A9EF44D7B10 (native_language_id), PRIMARY KEY(native_language_user_id, native_language_id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE langu_user__languages_spoken (spoken_language_user_id INT NOT NULL, spoken_language_id INT NOT NULL, INDEX IDX_236EB59C1319B42D (spoken_language_user_id), INDEX IDX_236EB59C50D2B87B (spoken_language_id), PRIMARY KEY(spoken_language_user_id, spoken_language_id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE langu_user__languages_taught (taught_language_user_id INT NOT NULL, taught_language_id INT NOT NULL, INDEX IDX_1D99F2BE89962CA6 (taught_language_user_id), INDEX IDX_1D99F2BE4FC87DD0 (taught_language_id), PRIMARY KEY(taught_language_user_id, taught_language_id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE teaching_qualification (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, name VARCHAR(255) NOT NULL, proof VARCHAR(2083) DEFAULT NULL, verified TINYINT(1) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_98D1AC1AA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mp_bank_account (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, account_id VARCHAR(40) NOT NULL, account_number VARCHAR(40) NOT NULL, UNIQUE INDEX UNIQ_85019D6C9B6B5FBA (account_id), INDEX IDX_85019D6CA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exchange_rate (id INT AUTO_INCREMENT NOT NULL, source_currency_id INT DEFAULT NULL, target_currency_id INT DEFAULT NULL, date DATETIME NOT NULL, rate NUMERIC(17, 6) NOT NULL, original_rate NUMERIC(17, 6) NOT NULL, INDEX IDX_E9521FAB45BD1D6 (source_currency_id), INDEX IDX_E9521FABBF1ECE7C (target_currency_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE currency (id INT AUTO_INCREMENT NOT NULL, active TINYINT(1) NOT NULL, unicode_symbol CHAR(4) NOT NULL, iso_code CHAR(3) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE language (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, isoCode CHAR(2) NOT NULL, uiAvailable TINYINT(1) NOT NULL, UNIQUE INDEX UNIQ_D4DB71B55E237E06 (name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE slot_reservation (id INT AUTO_INCREMENT NOT NULL, student_id INT DEFAULT NULL, slot_id INT DEFAULT NULL, created_at DATETIME NOT NULL, INDEX IDX_DA7AB0B0CB944F1A (student_id), UNIQUE INDEX UNIQ_DA7AB0B059E5119C (slot_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE slot (id INT AUTO_INCREMENT NOT NULL, teacher_id INT DEFAULT NULL, date DATETIME NOT NULL, status SMALLINT NOT NULL, INDEX IDX_AC0E206741807E1D (teacher_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE message (id INT AUTO_INCREMENT NOT NULL, thread_id INT DEFAULT NULL, author_id INT DEFAULT NULL, recipient_id INT DEFAULT NULL, date DATETIME NOT NULL, read_date DATETIME DEFAULT NULL, notification_date DATETIME DEFAULT NULL, message VARCHAR(1000) NOT NULL, isSystem TINYINT(1) NOT NULL, INDEX IDX_B6BD307FE2904019 (thread_id), INDEX IDX_B6BD307FF675F31B (author_id), INDEX IDX_B6BD307FE92F8F78 (recipient_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE directory (id INT AUTO_INCREMENT NOT NULL, teacher_id INT DEFAULT NULL, parent_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, createdAt DATETIME NOT NULL, deletedAt DATETIME DEFAULT NULL, INDEX IDX_467844DA41807E1D (teacher_id), INDEX IDX_467844DA727ACA70 (parent_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE resource (id INT AUTO_INCREMENT NOT NULL, teacher_id INT DEFAULT NULL, directory_id INT DEFAULT NULL, display_name VARCHAR(1024) NOT NULL, name VARCHAR(1024) NOT NULL, path VARCHAR(2083) NOT NULL, access SMALLINT NOT NULL, type SMALLINT NOT NULL, mime VARCHAR(255) NOT NULL, createdAt DATETIME NOT NULL, deletedAt DATETIME DEFAULT NULL, INDEX IDX_BC91F41641807E1D (teacher_id), INDEX IDX_BC91F4162C94069F (directory_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE service (id INT AUTO_INCREMENT NOT NULL, teacher_id INT DEFAULT NULL, length SMALLINT NOT NULL, price NUMERIC(15, 4) NOT NULL, lessons SMALLINT NOT NULL, newStudentsOnly TINYINT(1) NOT NULL, created_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_E19D9AD241807E1D (teacher_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE purchase (id INT AUTO_INCREMENT NOT NULL, student_id INT DEFAULT NULL, teacher_id INT DEFAULT NULL, service_id INT DEFAULT NULL, payment_transaction_id INT DEFAULT NULL, language_id INT DEFAULT NULL, price NUMERIC(15, 4) NOT NULL, lesson_length SMALLINT NOT NULL, lessons SMALLINT NOT NULL, lessons_left SMALLINT NOT NULL, commission SMALLINT NOT NULL, status SMALLINT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, INDEX IDX_6117D13BCB944F1A (student_id), INDEX IDX_6117D13B41807E1D (teacher_id), INDEX IDX_6117D13BED5CA9E6 (service_id), INDEX IDX_6117D13BCAE8710B (payment_transaction_id), INDEX IDX_6117D13B82F1BAF4 (language_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE langu_transaction (id INT AUTO_INCREMENT NOT NULL, linked_transaction_id INT DEFAULT NULL, debited_wallet_id INT DEFAULT NULL, credited_wallet_id INT DEFAULT NULL, author_id INT DEFAULT NULL, recipient_id INT DEFAULT NULL, transaction_id VARCHAR(40) NOT NULL, amount NUMERIC(15, 4) NOT NULL, fee NUMERIC(15, 4) NOT NULL, status SMALLINT NOT NULL, type SMALLINT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_EE2145C2FC0CB0F (transaction_id), INDEX IDX_EE2145C692265F7 (linked_transaction_id), INDEX IDX_EE2145CB7AB54DE (debited_wallet_id), INDEX IDX_EE2145C868D859B (credited_wallet_id), INDEX IDX_EE2145CF675F31B (author_id), INDEX IDX_EE2145CE92F8F78 (recipient_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE feedback (id INT AUTO_INCREMENT NOT NULL, lesson_id INT DEFAULT NULL, language_id INT DEFAULT NULL, status SMALLINT NOT NULL, langu_rating SMALLINT NOT NULL, lesson_rating SMALLINT NOT NULL, teacher_rating SMALLINT NOT NULL, teacher_comment VARCHAR(500) DEFAULT NULL, lesson_comment VARCHAR(500) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_D2294458CDF80196 (lesson_id), INDEX IDX_D229445882F1BAF4 (language_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE lesson (id INT AUTO_INCREMENT NOT NULL, payment_transaction_id INT DEFAULT NULL, purchase_id INT DEFAULT NULL, status SMALLINT NOT NULL, drive_file_id VARCHAR(100) DEFAULT NULL, opentok_session_id VARCHAR(100) DEFAULT NULL, ordinal SMALLINT NOT NULL, start_date DATETIME NOT NULL, objectives LONGTEXT NOT NULL COMMENT \'(DC2Type:array)\', created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, reminder_at DATETIME DEFAULT NULL, INDEX IDX_F87474F3CAE8710B (payment_transaction_id), INDEX IDX_F87474F3558FBEB9 (purchase_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE lesson__slot (lesson_id INT NOT NULL, slot_id INT NOT NULL, INDEX IDX_BBCD84FBCDF80196 (lesson_id), INDEX IDX_BBCD84FB59E5119C (slot_id), PRIMARY KEY(lesson_id, slot_id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE lesson__resource (lesson_id INT NOT NULL, resource_id INT NOT NULL, INDEX IDX_F6725EFCCDF80196 (lesson_id), INDEX IDX_F6725EFC89329D25 (resource_id), PRIMARY KEY(lesson_id, resource_id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE lesson_log (id INT AUTO_INCREMENT NOT NULL, lesson_id INT DEFAULT NULL, date DATETIME NOT NULL, type SMALLINT NOT NULL, INDEX IDX_FD4ACE26CDF80196 (lesson_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('ALTER TABLE langu_feedback ADD CONSTRAINT FK_16FA03FFA76ED395 FOREIGN KEY (user_id) REFERENCES langu_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mangopay_wallet ADD CONSTRAINT FK_5BB5BE6A76ED395 FOREIGN KEY (user_id) REFERENCES langu_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mangopay_wallet ADD CONSTRAINT FK_5BB5BE638248176 FOREIGN KEY (currency_id) REFERENCES currency (id)');
        $this->addSql('ALTER TABLE langu_user ADD CONSTRAINT FK_A54214B038248176 FOREIGN KEY (currency_id) REFERENCES currency (id)');
        $this->addSql('ALTER TABLE langu_user ADD CONSTRAINT FK_A54214B097B41181 FOREIGN KEY (language_to_learn_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE langu_user__native_languages ADD CONSTRAINT FK_10831A9EA52735B9 FOREIGN KEY (native_language_user_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE langu_user__native_languages ADD CONSTRAINT FK_10831A9EF44D7B10 FOREIGN KEY (native_language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE langu_user__languages_spoken ADD CONSTRAINT FK_236EB59C1319B42D FOREIGN KEY (spoken_language_user_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE langu_user__languages_spoken ADD CONSTRAINT FK_236EB59C50D2B87B FOREIGN KEY (spoken_language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE langu_user__languages_taught ADD CONSTRAINT FK_1D99F2BE89962CA6 FOREIGN KEY (taught_language_user_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE langu_user__languages_taught ADD CONSTRAINT FK_1D99F2BE4FC87DD0 FOREIGN KEY (taught_language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE teaching_qualification ADD CONSTRAINT FK_98D1AC1AA76ED395 FOREIGN KEY (user_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE mp_bank_account ADD CONSTRAINT FK_85019D6CA76ED395 FOREIGN KEY (user_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE exchange_rate ADD CONSTRAINT FK_E9521FAB45BD1D6 FOREIGN KEY (source_currency_id) REFERENCES currency (id)');
        $this->addSql('ALTER TABLE exchange_rate ADD CONSTRAINT FK_E9521FABBF1ECE7C FOREIGN KEY (target_currency_id) REFERENCES currency (id)');
        $this->addSql('ALTER TABLE slot_reservation ADD CONSTRAINT FK_DA7AB0B0CB944F1A FOREIGN KEY (student_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE slot_reservation ADD CONSTRAINT FK_DA7AB0B059E5119C FOREIGN KEY (slot_id) REFERENCES slot (id)');
        $this->addSql('ALTER TABLE slot ADD CONSTRAINT FK_AC0E206741807E1D FOREIGN KEY (teacher_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307FE2904019 FOREIGN KEY (thread_id) REFERENCES message (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307FF675F31B FOREIGN KEY (author_id) REFERENCES langu_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307FE92F8F78 FOREIGN KEY (recipient_id) REFERENCES langu_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE directory ADD CONSTRAINT FK_467844DA41807E1D FOREIGN KEY (teacher_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE directory ADD CONSTRAINT FK_467844DA727ACA70 FOREIGN KEY (parent_id) REFERENCES directory (id)');
        $this->addSql('ALTER TABLE resource ADD CONSTRAINT FK_BC91F41641807E1D FOREIGN KEY (teacher_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE resource ADD CONSTRAINT FK_BC91F4162C94069F FOREIGN KEY (directory_id) REFERENCES directory (id)');
        $this->addSql('ALTER TABLE service ADD CONSTRAINT FK_E19D9AD241807E1D FOREIGN KEY (teacher_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE purchase ADD CONSTRAINT FK_6117D13BCB944F1A FOREIGN KEY (student_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE purchase ADD CONSTRAINT FK_6117D13B41807E1D FOREIGN KEY (teacher_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE purchase ADD CONSTRAINT FK_6117D13BED5CA9E6 FOREIGN KEY (service_id) REFERENCES service (id)');
        $this->addSql('ALTER TABLE purchase ADD CONSTRAINT FK_6117D13BCAE8710B FOREIGN KEY (payment_transaction_id) REFERENCES langu_transaction (id)');
        $this->addSql('ALTER TABLE purchase ADD CONSTRAINT FK_6117D13B82F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE langu_transaction ADD CONSTRAINT FK_EE2145C692265F7 FOREIGN KEY (linked_transaction_id) REFERENCES langu_transaction (id)');
        $this->addSql('ALTER TABLE langu_transaction ADD CONSTRAINT FK_EE2145CB7AB54DE FOREIGN KEY (debited_wallet_id) REFERENCES mangopay_wallet (id)');
        $this->addSql('ALTER TABLE langu_transaction ADD CONSTRAINT FK_EE2145C868D859B FOREIGN KEY (credited_wallet_id) REFERENCES mangopay_wallet (id)');
        $this->addSql('ALTER TABLE langu_transaction ADD CONSTRAINT FK_EE2145CF675F31B FOREIGN KEY (author_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE langu_transaction ADD CONSTRAINT FK_EE2145CE92F8F78 FOREIGN KEY (recipient_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE feedback ADD CONSTRAINT FK_D2294458CDF80196 FOREIGN KEY (lesson_id) REFERENCES lesson (id)');
        $this->addSql('ALTER TABLE feedback ADD CONSTRAINT FK_D229445882F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE lesson ADD CONSTRAINT FK_F87474F3CAE8710B FOREIGN KEY (payment_transaction_id) REFERENCES langu_transaction (id)');
        $this->addSql('ALTER TABLE lesson ADD CONSTRAINT FK_F87474F3558FBEB9 FOREIGN KEY (purchase_id) REFERENCES purchase (id)');
        $this->addSql('ALTER TABLE lesson__slot ADD CONSTRAINT FK_BBCD84FBCDF80196 FOREIGN KEY (lesson_id) REFERENCES lesson (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE lesson__slot ADD CONSTRAINT FK_BBCD84FB59E5119C FOREIGN KEY (slot_id) REFERENCES slot (id)');
        $this->addSql('ALTER TABLE lesson__resource ADD CONSTRAINT FK_F6725EFCCDF80196 FOREIGN KEY (lesson_id) REFERENCES lesson (id)');
        $this->addSql('ALTER TABLE lesson__resource ADD CONSTRAINT FK_F6725EFC89329D25 FOREIGN KEY (resource_id) REFERENCES resource (id)');
        $this->addSql('ALTER TABLE lesson_log ADD CONSTRAINT FK_FD4ACE26CDF80196 FOREIGN KEY (lesson_id) REFERENCES lesson (id) ON DELETE CASCADE');
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE langu_transaction DROP FOREIGN KEY FK_EE2145CB7AB54DE');
        $this->addSql('ALTER TABLE langu_transaction DROP FOREIGN KEY FK_EE2145C868D859B');
        $this->addSql('ALTER TABLE langu_feedback DROP FOREIGN KEY FK_16FA03FFA76ED395');
        $this->addSql('ALTER TABLE mangopay_wallet DROP FOREIGN KEY FK_5BB5BE6A76ED395');
        $this->addSql('ALTER TABLE langu_user__native_languages DROP FOREIGN KEY FK_10831A9EA52735B9');
        $this->addSql('ALTER TABLE langu_user__languages_spoken DROP FOREIGN KEY FK_236EB59C1319B42D');
        $this->addSql('ALTER TABLE langu_user__languages_taught DROP FOREIGN KEY FK_1D99F2BE89962CA6');
        $this->addSql('ALTER TABLE teaching_qualification DROP FOREIGN KEY FK_98D1AC1AA76ED395');
        $this->addSql('ALTER TABLE mp_bank_account DROP FOREIGN KEY FK_85019D6CA76ED395');
        $this->addSql('ALTER TABLE slot_reservation DROP FOREIGN KEY FK_DA7AB0B0CB944F1A');
        $this->addSql('ALTER TABLE slot DROP FOREIGN KEY FK_AC0E206741807E1D');
        $this->addSql('ALTER TABLE message DROP FOREIGN KEY FK_B6BD307FF675F31B');
        $this->addSql('ALTER TABLE message DROP FOREIGN KEY FK_B6BD307FE92F8F78');
        $this->addSql('ALTER TABLE directory DROP FOREIGN KEY FK_467844DA41807E1D');
        $this->addSql('ALTER TABLE resource DROP FOREIGN KEY FK_BC91F41641807E1D');
        $this->addSql('ALTER TABLE service DROP FOREIGN KEY FK_E19D9AD241807E1D');
        $this->addSql('ALTER TABLE purchase DROP FOREIGN KEY FK_6117D13BCB944F1A');
        $this->addSql('ALTER TABLE purchase DROP FOREIGN KEY FK_6117D13B41807E1D');
        $this->addSql('ALTER TABLE langu_transaction DROP FOREIGN KEY FK_EE2145CF675F31B');
        $this->addSql('ALTER TABLE langu_transaction DROP FOREIGN KEY FK_EE2145CE92F8F78');
        $this->addSql('ALTER TABLE mangopay_wallet DROP FOREIGN KEY FK_5BB5BE638248176');
        $this->addSql('ALTER TABLE langu_user DROP FOREIGN KEY FK_A54214B038248176');
        $this->addSql('ALTER TABLE exchange_rate DROP FOREIGN KEY FK_E9521FAB45BD1D6');
        $this->addSql('ALTER TABLE exchange_rate DROP FOREIGN KEY FK_E9521FABBF1ECE7C');
        $this->addSql('ALTER TABLE langu_user DROP FOREIGN KEY FK_A54214B097B41181');
        $this->addSql('ALTER TABLE langu_user__native_languages DROP FOREIGN KEY FK_10831A9EF44D7B10');
        $this->addSql('ALTER TABLE langu_user__languages_spoken DROP FOREIGN KEY FK_236EB59C50D2B87B');
        $this->addSql('ALTER TABLE langu_user__languages_taught DROP FOREIGN KEY FK_1D99F2BE4FC87DD0');
        $this->addSql('ALTER TABLE purchase DROP FOREIGN KEY FK_6117D13B82F1BAF4');
        $this->addSql('ALTER TABLE feedback DROP FOREIGN KEY FK_D229445882F1BAF4');
        $this->addSql('ALTER TABLE slot_reservation DROP FOREIGN KEY FK_DA7AB0B059E5119C');
        $this->addSql('ALTER TABLE lesson__slot DROP FOREIGN KEY FK_BBCD84FB59E5119C');
        $this->addSql('ALTER TABLE message DROP FOREIGN KEY FK_B6BD307FE2904019');
        $this->addSql('ALTER TABLE directory DROP FOREIGN KEY FK_467844DA727ACA70');
        $this->addSql('ALTER TABLE resource DROP FOREIGN KEY FK_BC91F4162C94069F');
        $this->addSql('ALTER TABLE lesson__resource DROP FOREIGN KEY FK_F6725EFC89329D25');
        $this->addSql('ALTER TABLE purchase DROP FOREIGN KEY FK_6117D13BED5CA9E6');
        $this->addSql('ALTER TABLE lesson DROP FOREIGN KEY FK_F87474F3558FBEB9');
        $this->addSql('ALTER TABLE purchase DROP FOREIGN KEY FK_6117D13BCAE8710B');
        $this->addSql('ALTER TABLE langu_transaction DROP FOREIGN KEY FK_EE2145C692265F7');
        $this->addSql('ALTER TABLE lesson DROP FOREIGN KEY FK_F87474F3CAE8710B');
        $this->addSql('ALTER TABLE feedback DROP FOREIGN KEY FK_D2294458CDF80196');
        $this->addSql('ALTER TABLE lesson__slot DROP FOREIGN KEY FK_BBCD84FBCDF80196');
        $this->addSql('ALTER TABLE lesson__resource DROP FOREIGN KEY FK_F6725EFCCDF80196');
        $this->addSql('ALTER TABLE lesson_log DROP FOREIGN KEY FK_FD4ACE26CDF80196');
        $this->addSql('DROP TABLE transaction');
        $this->addSql('DROP TABLE langu_feedback');
        $this->addSql('DROP TABLE page');
        $this->addSql('DROP TABLE mangopay_wallet');
        $this->addSql('DROP TABLE langu_user');
        $this->addSql('DROP TABLE langu_user__native_languages');
        $this->addSql('DROP TABLE langu_user__languages_spoken');
        $this->addSql('DROP TABLE langu_user__languages_taught');
        $this->addSql('DROP TABLE teaching_qualification');
        $this->addSql('DROP TABLE mp_bank_account');
        $this->addSql('DROP TABLE exchange_rate');
        $this->addSql('DROP TABLE currency');
        $this->addSql('DROP TABLE language');
        $this->addSql('DROP TABLE slot_reservation');
        $this->addSql('DROP TABLE slot');
        $this->addSql('DROP TABLE message');
        $this->addSql('DROP TABLE directory');
        $this->addSql('DROP TABLE resource');
        $this->addSql('DROP TABLE service');
        $this->addSql('DROP TABLE purchase');
        $this->addSql('DROP TABLE langu_transaction');
        $this->addSql('DROP TABLE feedback');
        $this->addSql('DROP TABLE lesson');
        $this->addSql('DROP TABLE lesson__slot');
        $this->addSql('DROP TABLE lesson__resource');
        $this->addSql('DROP TABLE lesson_log');
    }
}
