<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20170424100306 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE speciality_translations (id INT AUTO_INCREMENT NOT NULL, object_id INT DEFAULT NULL, locale VARCHAR(8) NOT NULL, field VARCHAR(32) NOT NULL, content LONGTEXT DEFAULT NULL, INDEX IDX_6389EF33232D562B (object_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE speciality (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(50) NOT NULL, deleted_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_F3D7A08E5E237E06 (name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE teacher_speciality (id INT AUTO_INCREMENT NOT NULL, speciality_id INT NOT NULL, teacher_id INT NOT NULL, priority SMALLINT NOT NULL, INDEX IDX_79C8022E3B5A08D7 (speciality_id), INDEX IDX_79C8022E41807E1D (teacher_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('ALTER TABLE speciality_translations ADD CONSTRAINT FK_6389EF33232D562B FOREIGN KEY (object_id) REFERENCES speciality (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE teacher_speciality ADD CONSTRAINT FK_79C8022E3B5A08D7 FOREIGN KEY (speciality_id) REFERENCES speciality (id)');
        $this->addSql('ALTER TABLE teacher_speciality ADD CONSTRAINT FK_79C8022E41807E1D FOREIGN KEY (teacher_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE langu_user CHANGE short_summary short_summary VARCHAR(110) DEFAULT NULL');
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE speciality_translations DROP FOREIGN KEY FK_6389EF33232D562B');
        $this->addSql('ALTER TABLE teacher_speciality DROP FOREIGN KEY FK_79C8022E3B5A08D7');
        $this->addSql('DROP TABLE speciality_translations');
        $this->addSql('DROP TABLE speciality');
        $this->addSql('DROP TABLE teacher_speciality');
        $this->addSql('ALTER TABLE langu_user CHANGE short_summary short_summary VARCHAR(100) DEFAULT NULL COLLATE utf8_unicode_ci');
    }
}
