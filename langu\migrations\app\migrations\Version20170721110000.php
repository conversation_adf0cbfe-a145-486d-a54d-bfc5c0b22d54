<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20170721110000 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE blog_category ADD seo_title VARCHAR(255) DEFAULT NULL, ADD seo_description VARCHAR(255) DEFAULT NULL');
        $this->addSql('DROP INDEX UNIQ_BA5AE01DDA1E4C39 ON blog_post');
        $this->addSql('DROP INDEX UNIQ_BA5AE01DA98369AA ON blog_post');
        $this->addSql('ALTER TABLE blog_post CHANGE seo_title seo_title VARCHAR(255) NOT NULL, CHANGE seo_description seo_description VARCHAR(255) NOT NULL');
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE blog_category DROP seo_title, DROP seo_description');
        $this->addSql('ALTER TABLE blog_post CHANGE seo_title seo_title VARCHAR(255) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE seo_description seo_description VARCHAR(255) DEFAULT NULL COLLATE utf8_unicode_ci');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_BA5AE01DDA1E4C39 ON blog_post (seo_title)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_BA5AE01DA98369AA ON blog_post (seo_description)');
    }
}
