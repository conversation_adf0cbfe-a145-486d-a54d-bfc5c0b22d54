<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20170802112339 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE landing_page (id INT AUTO_INCREMENT NOT NULL, teacher1_id INT DEFAULT NULL, teacher2_id INT DEFAULT NULL, teacher3_id INT DEFAULT NULL, path VARCHAR(255) NOT NULL, updated_at DATETIME NOT NULL, UNIQUE INDEX UNIQ_87A7C899B548B0F (path), INDEX IDX_87A7C899B52BC134 (teacher1_id), INDEX IDX_87A7C899A79E6EDA (teacher2_id), INDEX IDX_87A7C8991F2209BF (teacher3_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('ALTER TABLE landing_page ADD CONSTRAINT FK_87A7C899B52BC134 FOREIGN KEY (teacher1_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE landing_page ADD CONSTRAINT FK_87A7C899A79E6EDA FOREIGN KEY (teacher2_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE landing_page ADD CONSTRAINT FK_87A7C8991F2209BF FOREIGN KEY (teacher3_id) REFERENCES langu_user (id)');
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE landing_page');
    }
}
