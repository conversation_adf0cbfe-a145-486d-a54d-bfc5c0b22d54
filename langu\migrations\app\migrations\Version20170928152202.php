<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20170928152202 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE seo_page (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, path VARCHAR(240) COLLATE utf8_unicode_ci NOT NULL, seo_title VARCHAR(255) NOT NULL, seo_description VARCHAR(255) NOT NULL, feature_1_heading VARCHAR(255) NOT NULL, feature_1_icon_url VARCHAR(2083) NOT NULL, feature_1_content LONGTEXT NOT NULL, feature_2_heading VARCHAR(255) DEFAULT NULL, feature_2_icon_url VARCHAR(2083) DEFAULT NULL, feature_2_content LONGTEXT DEFAULT NULL, feature_3_heading VARCHAR(255) DEFAULT NULL, feature_3_icon_url VARCHAR(2083) DEFAULT NULL, feature_3_content LONGTEXT DEFAULT NULL, feature_4_heading VARCHAR(255) DEFAULT NULL, feature_4_icon_url VARCHAR(2083) DEFAULT NULL, feature_4_content LONGTEXT DEFAULT NULL, feature_5_heading VARCHAR(255) DEFAULT NULL, feature_5_icon_url VARCHAR(2083) DEFAULT NULL, feature_5_content LONGTEXT DEFAULT NULL, feature_6_heading VARCHAR(255) DEFAULT NULL, feature_6_icon_url VARCHAR(2083) DEFAULT NULL, feature_6_content LONGTEXT DEFAULT NULL, feature_7_heading VARCHAR(255) DEFAULT NULL, feature_7_icon_url VARCHAR(2083) DEFAULT NULL, feature_7_content LONGTEXT DEFAULT NULL, feature_8_heading VARCHAR(255) DEFAULT NULL, feature_8_icon_url VARCHAR(2083) DEFAULT NULL, feature_8_content LONGTEXT DEFAULT NULL, title VARCHAR(255) NOT NULL, sub_title VARCHAR(500) NOT NULL, main_heading VARCHAR(1000) NOT NULL, lang VARCHAR(4) NOT NULL, UNIQUE INDEX UNIQ_E8DCA6F1B548B0F (path), INDEX search_idx (lang, path), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE seo_page');
    }
}
