<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20171006163738 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE about_us_page (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, seo_title VARCHAR(255) NOT NULL, seo_description VARCHAR(255) NOT NULL, title VARCHAR(255) NOT NULL, intro TEXT NOT NULL, member_1_name VARCHA<PERSON>(255) DEFAULT NULL, member_1_title VARCHAR(255) DEFAULT NULL, member_1_photo_url VARCHAR(2083) DEFAULT NULL, member_1_content TEXT DEFAULT NULL, member_2_name VARCHAR(255) DEFAULT NULL, member_2_title VARCHAR(255) DEFAULT NULL, member_2_photo_url VARCHAR(2083) DEFAULT NULL, member_2_content TEXT DEFAULT NULL, member_3_name VARCHAR(255) DEFAULT NULL, member_3_title VARCHAR(255) DEFAULT NULL, member_3_photo_url VARCHAR(2083) DEFAULT NULL, member_3_content TEXT DEFAULT NULL, member_4_name VARCHAR(255) DEFAULT NULL, member_4_title VARCHAR(255) DEFAULT NULL, member_4_photo_url VARCHAR(2083) DEFAULT NULL, member_4_content TEXT DEFAULT NULL, member_5_name VARCHAR(255) DEFAULT NULL, member_5_title VARCHAR(255) DEFAULT NULL, member_5_photo_url VARCHAR(2083) DEFAULT NULL, member_5_content TEXT DEFAULT NULL, member_6_name VARCHAR(255) DEFAULT NULL, member_6_title VARCHAR(255) DEFAULT NULL, member_6_photo_url VARCHAR(2083) DEFAULT NULL, member_6_content TEXT DEFAULT NULL, member_7_name VARCHAR(255) DEFAULT NULL, member_7_title VARCHAR(255) DEFAULT NULL, member_7_photo_url VARCHAR(2083) DEFAULT NULL, member_7_content TEXT DEFAULT NULL, member_8_name VARCHAR(255) DEFAULT NULL, member_8_title VARCHAR(255) DEFAULT NULL, member_8_photo_url VARCHAR(2083) DEFAULT NULL, member_8_content TEXT DEFAULT NULL, main_image VARCHAR(255) NOT NULL, team TEXT DEFAULT NULL, teachers_info TINYTEXT DEFAULT NULL, teachers_content TEXT DEFAULT NULL, methodology TEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB ROW_FORMAT = COMPRESSED');
        $this->addSql('DROP TABLE transaction');
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE transaction (id INT AUTO_INCREMENT NOT NULL, authorId INT NOT NULL, creditedUserId INT NOT NULL, creditedWalletId INT NOT NULL, debitedFunds INT NOT NULL, creditedFunds INT NOT NULL, fees INT NOT NULL, status VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci, resultCode INT NOT NULL, resultMessage VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci, executionDate DATETIME NOT NULL, type VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci, nature VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci, cardType VARCHAR(255) NOT NULL COLLATE utf8_unicode_ci, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('DROP TABLE about_us_page');
    }
}
