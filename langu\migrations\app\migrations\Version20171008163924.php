<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20171008163924 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE about_us_page ADD box_1_icon TEXT NOT NULL, ADD box_1_headline TEXT NOT NULL, ADD box_1_text TEXT NOT NULL, ADD box_2_icon TEXT NOT NULL, ADD box_2_headline TEXT NOT NULL, ADD box_2_text TEXT NOT NULL, ADD box_3_icon TEXT NOT NULL, ADD box_3_headline TEXT NOT NULL, ADD box_3_text TEXT NOT NULL, CHANGE member_1_photo_url member_1_photo_url TEXT DEFAULT NULL, CHANGE member_2_photo_url member_2_photo_url TEXT DEFAULT NULL, CHANGE member_3_photo_url member_3_photo_url TEXT DEFAULT NULL, CHANGE member_4_photo_url member_4_photo_url TEXT DEFAULT NULL, CHANGE member_5_photo_url member_5_photo_url TEXT DEFAULT NULL, CHANGE member_6_photo_url member_6_photo_url TEXT DEFAULT NULL, CHANGE member_7_photo_url member_7_photo_url TEXT DEFAULT NULL, CHANGE member_8_photo_url member_8_photo_url TEXT DEFAULT NULL, CHANGE team team TEXT NOT NULL, CHANGE teachers_info teachers_info TINYTEXT NOT NULL, CHANGE teachers_content teachers_content TEXT NOT NULL, CHANGE methodology methodology TEXT NOT NULL');
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE about_us_page DROP box_1_icon, DROP box_1_headline, DROP box_1_text, DROP box_2_icon, DROP box_2_headline, DROP box_2_text, DROP box_3_icon, DROP box_3_headline, DROP box_3_text, CHANGE member_1_photo_url member_1_photo_url VARCHAR(2083) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE member_2_photo_url member_2_photo_url VARCHAR(2083) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE member_3_photo_url member_3_photo_url VARCHAR(2083) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE member_4_photo_url member_4_photo_url VARCHAR(2083) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE member_5_photo_url member_5_photo_url VARCHAR(2083) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE member_6_photo_url member_6_photo_url VARCHAR(2083) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE member_7_photo_url member_7_photo_url VARCHAR(2083) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE member_8_photo_url member_8_photo_url VARCHAR(2083) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE team team TEXT DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE teachers_info teachers_info TINYTEXT DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE teachers_content teachers_content TEXT DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE methodology methodology TEXT DEFAULT NULL COLLATE utf8_unicode_ci');
    }
}
