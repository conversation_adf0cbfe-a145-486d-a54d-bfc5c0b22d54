<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20171009103751 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE methodology_translations (id INT AUTO_INCREMENT NOT NULL, object_id INT DEFAULT NULL, locale VARCHAR(8) NOT NULL, field VARCHAR(32) NOT NULL, content LONGTEXT DEFAULT NULL, INDEX IDX_B3BA08B4232D562B (object_id), UNIQUE INDEX lookup_unique_idx (locale, object_id, field), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB');
        $this->addSql('CREATE TABLE methodology_page (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, seo_title VARCHAR(255) NOT NULL, seo_description VARCHAR(255) NOT NULL, title VARCHAR(255) NOT NULL, intro VARCHAR(1000) NOT NULL, main_image VARCHAR(255) NOT NULL, philosophy TEXT NOT NULL, box_1_icon VARCHAR(2083) NOT NULL, box_1_headline VARCHAR(300) NOT NULL, box_1_text TEXT NOT NULL, box_2_icon VARCHAR(2083) NOT NULL, box_2_headline VARCHAR(300) NOT NULL, box_2_text TEXT NOT NULL, box_3_icon VARCHAR(2083) NOT NULL, box_3_headline VARCHAR(300) NOT NULL, box_3_text TEXT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB ROW_FORMAT = COMPRESSED');
        $this->addSql('ALTER TABLE methodology_translations ADD CONSTRAINT FK_B3BA08B4232D562B FOREIGN KEY (object_id) REFERENCES methodology_page (id) ON DELETE CASCADE');
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE methodology_translations DROP FOREIGN KEY FK_B3BA08B4232D562B');
        $this->addSql('DROP TABLE methodology_translations');
        $this->addSql('DROP TABLE methodology_page');
    }
}
