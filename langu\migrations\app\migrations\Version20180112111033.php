<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

class Version20180112111033 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql("
            CREATE TABLE `faq_category` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
                `locale` varchar(8) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'en',
                PRIMARY KEY (`id`),
                UNIQUE KEY `name` (`name`)
            ) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;        
        ");        
        $this->addSql("
            CREATE TABLE `faq_item` ( `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
            `question` varchar(255) NOT NULL DEFAULT '',
            `answer` longtext NOT NULL,
            `faq_category_id` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `faq_category_id` (`faq_category_id`),
            CONSTRAINT `faq_item_ibfk_1` FOREIGN KEY (`faq_category_id`) REFERENCES `faq_category` (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;      
        ");
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('DROP TABLE faq_item');                            
        $this->addSql('DROP TABLE faq_category');    
    }
}
