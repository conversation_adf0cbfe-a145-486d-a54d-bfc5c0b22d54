<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

class Version20180116111422 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql("
            CREATE TABLE `faq_page` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `sidebar_title` varchar(255) DEFAULT '',
                `sidebar_content` text,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
        ");
        $this->addSql("
            CREATE TABLE `faq_page_translations` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` int(11) DEFAULT NULL,
                `locale` varchar(8) COLLATE utf8_unicode_ci NOT NULL,
                `field` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
                `content` longtext COLLATE utf8_unicode_ci,
                PRIMARY KEY (`id`),
                UNIQUE KEY `lookup_unique_idx` (`locale`,`object_id`,`field`),
                KEY `IDX_96A4F040232D562BFAQ` (`object_id`),
                CONSTRAINT `FK_96A4F040232D562BFAQ` FOREIGN KEY (`object_id`) REFERENCES `faq_page` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;        
        ");
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('DROP TABLE faq_page');
        $this->addSql('DROP TABLE faq_page_translations');        
    }
}
