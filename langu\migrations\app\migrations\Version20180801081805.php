<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20180801081805 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE langu_user CHANGE google_email google_email VARCHAR(255) DEFAULT NULL, CHANGE google_id google_id VARCHAR(255) DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A54214B0E7927C74 ON langu_user (email)');
        $this->addSql('ALTER TABLE faq_category CHANGE name name VARCHAR(255) NOT NULL, CHANGE locale locale VARCHAR(8) NOT NULL');
        $this->addSql('ALTER TABLE `faq_category` DROP INDEX `name`, ADD UNIQUE INDEX `UNIQ_FAEEE0D65E237E06` (`name` ASC)');
        $this->addSql('ALTER TABLE faq_page CHANGE sidebar_title sidebar_title VARCHAR(255) NOT NULL, CHANGE sidebar_content sidebar_content LONGTEXT NOT NULL');

        $this->addSql('ALTER TABLE `faq_item` DROP FOREIGN KEY `faq_item_ibfk_1`');
        $this->addSql('ALTER TABLE faq_item CHANGE id id INT AUTO_INCREMENT NOT NULL, CHANGE faq_category_id faq_category_id INT NOT NULL, CHANGE question question VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE `faq_item` ADD CONSTRAINT `faq_item_ibfk_1` FOREIGN KEY (`faq_category_id`) REFERENCES `faq_category` (`id`)');

        $this->addSql('DROP INDEX lookup_unique_idx ON faq_page_translations');
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE faq_category CHANGE name name VARCHAR(255) DEFAULT \'\' NOT NULL COLLATE utf8_unicode_ci, CHANGE locale locale VARCHAR(8) DEFAULT \'en\' NOT NULL COLLATE utf8_unicode_ci');
        $this->addSql('ALTER TABLE `faq_category` DROP INDEX `UNIQ_FAEEE0D65E237E06`, ADD UNIQUE INDEX `name` (`name` ASC)');

        $this->addSql('ALTER TABLE faq_item CHANGE id id INT UNSIGNED AUTO_INCREMENT NOT NULL, CHANGE faq_category_id faq_category_id INT DEFAULT NULL, CHANGE question question VARCHAR(255) DEFAULT \'\' NOT NULL COLLATE utf8_unicode_ci');
        $this->addSql('ALTER TABLE faq_item RENAME INDEX idx_1a054d78f689b0db TO faq_category_id');
        $this->addSql('ALTER TABLE faq_page CHANGE sidebar_title sidebar_title VARCHAR(255) DEFAULT \'\' COLLATE utf8_general_ci, CHANGE sidebar_content sidebar_content TEXT DEFAULT NULL COLLATE utf8_general_ci');
        $this->addSql('CREATE UNIQUE INDEX lookup_unique_idx ON faq_page_translations (locale, object_id, field)');
        $this->addSql('DROP INDEX UNIQ_A54214B0E7927C74 ON langu_user');
        $this->addSql('ALTER TABLE langu_user CHANGE google_email google_email VARCHAR(255) NOT NULL COLLATE utf8_general_ci, CHANGE google_id google_id VARCHAR(255) NOT NULL COLLATE utf8_general_ci');
    }
}
