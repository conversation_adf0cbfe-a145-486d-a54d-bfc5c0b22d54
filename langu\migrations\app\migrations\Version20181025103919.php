<?php

namespace DoctrineMigrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
class Version20181025103919 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE feedback ADD skip_rating SMALLINT NOT NULL DEFAULT 0 ');
        $this->addSql('ALTER TABLE `feedback` CHANGE `lesson_rating` `lesson_rating` SMALLINT(6) NULL DEFAULT NULL');
        $this->addSql('ALTER TABLE `feedback` CHANGE `langu_rating` `langu_rating` SMALLINT(6) NULL DEFAULT NULL');
        $this->addSql('ALTER TABLE `feedback` CHANGE `teacher_rating` `teacher_rating` SMALLINT(6) NULL DEFAULT NULL');

    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE feedback DROP COLUMN skip_rating');
        $this->addSql('ALTER TABLE `feedback` CHANGE `lesson_rating` `lesson_rating` SMALLINT(6) NOT NULL');
        $this->addSql('ALTER TABLE `feedback` CHANGE `langu_rating` `langu_rating` SMALLINT(6) NOT NULL');
        $this->addSql('ALTER TABLE `feedback` CHANGE `teacher_rating` `teacher_rating` SMALLINT(6) NOT NULL');

    }
}
