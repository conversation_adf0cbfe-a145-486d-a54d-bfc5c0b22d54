<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210504114937 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE resource_canvas (id INT AUTO_INCREMENT NOT NULL, directory_id INT DEFAULT NULL, lesson INT DEFAULT NULL, display_name VARCHAR(1024) NOT NULL, name VARCHAR(1024) NOT NULL, path VARCHAR(2083) NOT NULL, access SMALLINT NOT NULL, type SMALLINT NOT NULL, mime VARCHAR(255) NOT NULL, createdAt DATETIME NOT NULL, deletedAt DATETIME DEFAULT NULL, INDEX IDX_6D6386E22C94069F (directory_id), INDEX IDX_6D6386E2F87474F3 (lesson), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB');
        $this->addSql('ALTER TABLE resource_canvas ADD CONSTRAINT FK_6D6386E22C94069F FOREIGN KEY (directory_id) REFERENCES directory (id)');
        $this->addSql('ALTER TABLE resource_canvas ADD CONSTRAINT FK_6D6386E2F87474F3 FOREIGN KEY (lesson) REFERENCES lesson (id)');
    }

    public function down(Schema $schema) : void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE resource_canvas');
    }
}
