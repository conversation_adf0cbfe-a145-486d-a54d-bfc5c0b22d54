<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211108134820 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'This migration adds a new is_course and language_id field in the Service entity';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE service ADD language_id INT DEFAULT NULL, ADD is_course LONGTEXT NOT NULL');
        $this->addSql('ALTER TABLE service ADD CONSTRAINT FK_E19D9AD282F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)');
        $this->addSql('CREATE INDEX IDX_E19D9AD282F1BAF4 ON service (language_id)');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE service DROP FOREIGN KEY FK_E19D9AD282F1BAF4');
        $this->addSql('DROP INDEX IDX_E19D9AD282F1BAF4 ON service');
        $this->addSql('ALTER TABLE service DROP language_id, DROP is_course');
    }
}
