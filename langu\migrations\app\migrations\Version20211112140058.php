<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211112140058 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'This migration adds a new field, course_name and image, in the Service entity';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE service ADD course_name VARCHAR(255) NOT NULL, ADD image LONGTEXT NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE service DROP course_name, DROP image');
    }
}
