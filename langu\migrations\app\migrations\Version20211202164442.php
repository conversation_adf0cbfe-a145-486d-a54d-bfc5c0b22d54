<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211202164442 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE service DROP is_course');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE service ADD is_course LONGTEXT DEFAULT NULL COLLATE utf8_unicode_ci');
    }
}
