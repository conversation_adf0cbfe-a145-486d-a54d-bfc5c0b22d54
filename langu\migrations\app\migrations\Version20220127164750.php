<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220127164750 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE langu_user CHANGE youtube_video_id youtube_video_id VARCHAR(500) DEFAULT NULL, CHANGE short_summary short_summary VARCHAR(120) DEFAULT NULL, CHANGE long_summary long_summary VARCHAR(500) DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE langu_user CHANGE youtube_video_id youtube_video_id VARCHAR(16) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE short_summary short_summary VARCHAR(110) DEFAULT NULL COLLATE utf8_unicode_ci, CHANGE long_summary long_summary VARCHAR(400) DEFAULT NULL COLLATE utf8_unicode_ci');
    }
}
