<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220524095955 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'This migration add new filed file_name to table message';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE message ADD file_name VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE message DROP file_name');
    }
}
