<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220524154820 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'This migration add new filed last_message_date to table message';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE message ADD last_message_date DATETIME DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE message DROP last_message_date');
    }
}
