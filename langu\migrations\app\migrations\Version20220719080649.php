<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220719080649 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'This migration add new fields refund_method, refund_amount, type_service and price_for_langu to table purchase';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE purchase ADD refund_method VARCHAR(255) DEFAULT NULL, ADD refund_amount NUMERIC(15, 4) DEFAULT NULL');
        $this->addSql('ALTER TABLE purchase ADD type_service VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE purchase ADD price_for_langu NUMERIC(15, 4) DEFAULT NULL');

    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE purchase DROP refund_method, DROP refund_amount');
        $this->addSql('ALTER TABLE purchase DROP type_service');
        $this->addSql('ALTER TABLE purchase DROP price_for_langu');
    }
}
