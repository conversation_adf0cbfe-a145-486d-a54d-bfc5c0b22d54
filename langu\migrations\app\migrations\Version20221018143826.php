<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221018143826 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'This migration create new table teacher_sorting_data_feedback_tag';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('CREATE TABLE teacher_sorting_data_feedback_tag (id INT AUTO_INCREMENT NOT NULL, teacher_id INT NOT NULL, feedback_id INT NOT NULL, count_feedbacks INT NOT NULL, INDEX IDX_708DDD8E41807E1D (teacher_id), INDEX IDX_708DDD8ED249A887 (feedback_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB');
        $this->addSql('ALTER TABLE teacher_sorting_data_feedback_tag ADD CONSTRAINT FK_708DDD8E41807E1D FOREIGN KEY (teacher_id) REFERENCES langu_user (id)');
        $this->addSql('ALTER TABLE teacher_sorting_data_feedback_tag ADD CONSTRAINT FK_708DDD8ED249A887 FOREIGN KEY (feedback_id) REFERENCES feedback_tag (id)');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP TABLE teacher_sorting_data_feedback_tag');
    }
}
