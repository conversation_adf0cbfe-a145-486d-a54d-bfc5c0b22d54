<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221109114450 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'This migration add new field is_most_popular_user_tag in table teacher_sorting_data_feedback_tag';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE teacher_sorting_data_feedback_tag ADD is_most_popular_user_tag TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE teacher_sorting_data_feedback_tag DROP is_most_popular_user_tag');
    }
}
