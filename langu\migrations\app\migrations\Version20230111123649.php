<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230111123649 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'This migration add new field updated_at to table service';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE service ADD updated_at DATETIME DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE service DROP updated_at');
    }
}
