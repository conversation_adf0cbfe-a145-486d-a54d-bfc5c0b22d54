<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230504104002 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'This migration add transaltions for user_Tag';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE user_tag_translations (id INT AUTO_INCREMENT NOT NULL, object_id INT DEFAULT NULL, locale VARCHAR(8) NOT NULL, field VARCHAR(32) NOT NULL, content LONGTEXT DEFAULT NULL, INDEX IDX_1BB4B358232D562B (object_id), UNIQUE INDEX lookup_unique_idx (locale, object_id, field), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE user_tag_translations ADD CONSTRAINT FK_1BB4B358232D562B FOREIGN KEY (object_id) REFERENCES user_tag (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_tag DROP FOREIGN KEY FK_E89FD608F3D7A08E');
        $this->addSql('DROP INDEX UNIQ_E89FD608F3D7A08E ON user_tag');
        $this->addSql('ALTER TABLE user_tag ADD description VARCHAR(255) NOT NULL, DROP speciality, CHANGE name name VARCHAR(50) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE user_tag_translations DROP FOREIGN KEY FK_1BB4B358232D562B');
        $this->addSql('DROP TABLE user_tag_translations');
        $this->addSql('ALTER TABLE user_tag ADD speciality INT DEFAULT NULL, DROP description, CHANGE name name VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE user_tag ADD CONSTRAINT FK_E89FD608F3D7A08E FOREIGN KEY (speciality) REFERENCES speciality (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_E89FD608F3D7A08E ON user_tag (speciality)');
    }
}
