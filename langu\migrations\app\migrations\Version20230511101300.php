<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230511101300 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'This migration add new field logo and headline';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE user_tag ADD head_line VARCHAR(255) NOT NULL, ADD logo VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE user_tag DROP head_line, DROP logo');
    }
}
