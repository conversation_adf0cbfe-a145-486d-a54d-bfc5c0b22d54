<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230619075838 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'This migration add new field recommended_points for table teacher_sorting_data';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE teacher_sorting_data ADD recommended_points INT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE teacher_sorting_data DROP recommended_points');
    }
}
