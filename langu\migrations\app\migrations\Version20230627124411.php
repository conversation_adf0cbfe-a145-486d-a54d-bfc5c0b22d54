<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230627124411 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'This migration add new field is_visible_in_order_cache in table teacher_sorting_data';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE teacher_sorting_data ADD is_visible_in_order_cache DOUBLE PRECISION NOT NULL');
    }

    public function down(Schema $schema): void
    {

        $this->addSql('ALTER TABLE teacher_sorting_data DROP is_visible_in_order_cache');

    }
}
