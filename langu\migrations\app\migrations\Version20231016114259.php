<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231016114259 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'This migration create new table google_event_calendar';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE google_event_calendar (id INT AUTO_INCREMENT NOT NULL, channel_id VARCHAR(255) NOT NULL, resource_id VARCHAR(255) NOT NULL, resource_uri VARCHAR(255) NOT NULL, token VARCHAR(255) DEFAULT NULL, expiration DATETIME NOT NULL, user_id INT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE google_event_calendar');
    }
}
