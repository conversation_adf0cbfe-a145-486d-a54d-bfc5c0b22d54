<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231016214249 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'This migration add new table google_calendar_token';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE google_calendar_token (id INT AUTO_INCREMENT NOT NULL, user INT NOT NULL, access_token LONGTEXT NOT NULL, refresh_token LONGTEXT NOT NULL, UNIQUE INDEX UNIQ_AB8BF6F38D93D649 (user), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE google_calendar_token ADD CONSTRAINT FK_AB8BF6F38D93D649 FOREIGN KEY (user) REFERENCES langu_user (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE google_calendar_token DROP FOREIGN KEY FK_AB8BF6F38D93D649');
        $this->addSql('DROP TABLE google_calendar_token');
    }
}
