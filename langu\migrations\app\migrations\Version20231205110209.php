<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231205110209 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'This migration add new fields for UTM data to langu_user';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE langu_user ADD utm_source VARCHAR(255) NOT NULL, ADD utm_campaign VARCHAR(255) NOT NULL, ADD utm_ad VARCHAR(255) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE langu_user DROP utm_source, DROP utm_campaign, DROP utm_ad');
    }
}
