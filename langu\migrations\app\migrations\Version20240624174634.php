<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240624174634 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'This migration makes UTM fields nullable in langu_user';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE langu_user MODIFY utm_medium VARCHAR(255) DEFAULT NULL, MODIFY utm_term VARCHAR(255) DEFAULT NULL, MODIFY utm_content VARCHAR(255) DEFAULT NULL, MODIFY latest_utmm_source VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE langu_user DROP utm_medium, DROP utm_term, DROP utm_content, DROP latest_utmm_source');
    }
}
