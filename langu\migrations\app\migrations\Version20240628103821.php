<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240628103821 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'This migration renames column from latest_utmm_source to latest_utm_source';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE langu_user CHANGE COLUMN `latest_utmm_source` latest_utm_source VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE langu_user CHANGE COLUMN `latest_utm_source` latest_utmm_source VARCHAR(255) DEFAULT NULL');
    }
}
