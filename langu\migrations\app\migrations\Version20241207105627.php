<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241207105627 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added new table signature_courses_flag and new field to service: is_signature_course ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE signature_courses_flag (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE service ADD is_signature_course TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE service ADD flag_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE service ADD CONSTRAINT FK_E19D9AD2919FE4E5 FOREIGN KEY (flag_id) REFERENCES signature_courses_flag (id) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_E19D9AD2919FE4E5 ON service (flag_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE signature_courses_flag');
        $this->addSql('ALTER TABLE service DROP is_signature_course');
        $this->addSql('ALTER TABLE service DROP FOREIGN KEY FK_E19D9AD2919FE4E5');
        $this->addSql('ALTER TABLE service DROP flag_id');
        $this->addSql('DROP INDEX IDX_E19D9AD2919FE4E5 ON service');
    }
}
