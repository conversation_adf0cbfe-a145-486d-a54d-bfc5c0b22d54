<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;


final class Version20241216093727 extends AbstractMigration
{
    public function up(Schema $schema): void
    {

        $this->addSql('ALTER TABLE service ADD fee NUMERIC(15, 4) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE service DROP fee');
    }
}
