<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250425142015 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added new field finished_at to table lessons';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lesson ADD finished_at DATETIME DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lesson DROP finished_at');
    }
}
